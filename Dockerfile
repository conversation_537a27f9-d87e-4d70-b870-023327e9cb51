

# Utilizziamo Node come immagine base
FROM node:20 as build

# Installiamo Git
RUN apt-get update && apt-get install -y git

# Impostiamo la directory di lavoro
WORKDIR /app

# Metodo 1: Usando un Personal Access Token (PAT) con HTTPS
# Passiamo il token come argomento durante la build
ARG GITHUB_TOKEN
RUN git clone -b m3 https://oauth2:<EMAIL>/galimberti/gali-erp.git .

# Metodo 2: Alternativa usando SSH (richiede configurazione chiavi SSH)
# COPY ./id_rsa /root/.ssh/id_rsa
# RUN chmod 600 /root/.ssh/id_rsa
# RUN mkdir -p /root/.ssh && ssh-keyscan github.com >> /root/.ssh/known_hosts
# RUN <NAME_EMAIL>:username/angular-project.git .

# Installiamo le dipendenze
RUN npm install --legacy-peer-deps

# Installiamo Angular CLI globalmente
RUN npm install -g @angular/cli

# Compilazione dell'applicazione in modalità produzione
RUN ng build --configuration production

# Seconda fase per servire l'applicazione compilata (opzionale)
FROM nginx:alpine

# Copiamo i file compilati dalla fase di build nella directory di Nginx
COPY --from=build /app/dist /usr/share/nginx/html

# Aggiungiamo la configurazione di Nginx personalizzata
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Esposizione della porta 80
EXPOSE 80

# Avvio di Nginx
CMD ["nginx", "-g", "daemon off;"]


#
