import { Injectable, isDevMode } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import {tap, switchMap, catchError, finalize, filter, map, first} from 'rxjs/operators';
import {Router} from '@angular/router';
import { AuthenticationService } from './authentication.service';
import { OdoorpcService } from './odoorpc.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  isRefreshingToken: boolean = false;
  tokenSubject: BehaviorSubject<string> = new BehaviorSubject<string>(null);

  constructor(private router: Router,
    private odoo:OdoorpcService,
    private authService: AuthenticationService) {
    console.log("AUTH INTERCET1")
  }

  


  intercept(req: HttpRequest<any>, next: <PERSON>tt<PERSON><PERSON>and<PERSON>): Observable<HttpEvent<any>> { 

    console.log("intercept ")
    var x:any = next.handle(req)

    x.subscribe(r =>{
      if(r && r.body && r.body.error && r.body.error.code == 100  ){
        window.location.href =  'https://o3.galimberti.eu/m3'
        return
      }
    })
    return x
  }









//   addAuthHeader(request) {
//     // const authHeader = this.authService.getAuthorizationHeader();
//     // if (authHeader) {
//         // return request.clone({
//         //     setHeaders: {
//         //         "Authorization": "Bearer"
//         //     }
//         // });
//     // }
//     return request;
// }
  handle401(req: HttpRequest<any>, next: HttpHandler) {

    // console.log("HANDLE 401")

    // if (!this.isRefreshingToken) {
    //   // this.isRefreshingToken = true

    //   // Reset here so that the following requests wait until the token
    //   // comes back from the refreshToken call.
    //   // this.tokenSubject.next(null);
    //   console.log("REFRESHIIIING TOKEN ")
    //   return this.authService.refreshToken().pipe(map((token) => {
    //     console.log("REFRESH TOKEN ", token)
    //   }))
    // }
  }
}
  