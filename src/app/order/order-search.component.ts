import { Component, OnInit, ElementRef, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { RestapiService, QuerySearchOptions, QueryCriteriaOR, QueryCriteria } from '../shared/services/rest-api.service';

export class ConfigOptionsFilter {
  label : string
  criteria : QueryCriteria[]
}

export class ConfigOptions {
  table: string
  order: string
  titleNavbar: string
  pathChild: string
  criteria?: QueryCriteria[]
  fields?: string[]
  state? : string
  stateMap? : Map<string, [string, string]> 
  picking? : string
  filters? : ConfigOptionsFilter[]
  disableNoFilter? : boolean
  icon?: string 
}

@Injectable({
  providedIn: 'root'
})
export abstract class OrderComponent implements OnInit {

  items: any[]
  loading: boolean
  input: any
  timeout: any
  nameTable : string
  config : ConfigOptions
  titleNavbar : string
  stateQuery : string = null
  filterCriteria: any;
  abstract getConfig(): ConfigOptions
  postProcess(results:any[]):any[] {
    return results
  }


  constructor(
    public restapi : RestapiService,
    public router: Router,
    public elementRef : ElementRef,
  ) { 
    this.config = this.getConfig()
  }
  
  ngOnInit() {
    this.loading = false
    // fix for autofocus
    
    this.input = this.elementRef.nativeElement.querySelector('nav input')

    // Init a timeout variable to be used below
    this.timeout = null
    // Listen for keystroke events
    this.input.onkeyup = (e) => {

        // if it has been less than <MILLISECONDS>
        if (this.timeout)
          clearTimeout(this.timeout);
        // Make a new timeout set to go off in 800ms
        this.timeout = setTimeout( () => {
          this.search()
        }, 500)
    }

    this.search()
  }

  search() {
    this.loading = true
    var q : QuerySearchOptions = {
      table : this.config.table,
      criteria : [],
      resolve : [
        {
          table : this.config.table,
          key : "id",
          fields : this.config.fields ? this.config.fields : []
        }
      ],
      limit  : 20,
      order  : this.config.order
    }
    
    if (this.config.disableNoFilter && !this.filterCriteria)
      this.filterCriteria = this.config.filters[0].criteria

    if(this.input.value.split(" ").join("") != "" ) {
      this.loading = true
      this.input.value.split(/[\s.]/).forEach(v => {
        q.criteria.push(new QueryCriteriaOR())
        q.criteria.push({column: "name", operator: "ilike",  value: v})
        q.criteria.push({column: "partner_id", operator: "ilike",  value: v})
      })
    } else {
      q.criteria.push({column: "name", operator: "ilike",  value: this.input.value})
    }

    if(this.config.criteria)
      q.criteria = q.criteria.concat(this.config.criteria)

    if (this.filterCriteria)
      q.criteria = q.criteria.concat(this.filterCriteria)

    if(this.stateQuery)
      q.criteria.push({column: this.config.state, operator: "ilike",  value: this.stateQuery })

    this.restapi.search(q).then((res) => {
      // console.log("RES22 ", res)
      res.forEach(item => {
        if(item.partner_id)
          item.partnerName  = String( item.partner_id ).split(",")[1]
      })
      this.items = this.postProcess(res)
      this.loading = false
    }).catch((error) =>{
      if(error.indexOf("404") != -1){
        this.loading = false;
        this.items = [];
      }else
        alert(error)
    })
    this.timeout = null
  }

  openSalesCreate(){
    window.open("/sales/0", '_blank');
  }

  redirectOrder(o) {
    this.router.navigate([this.config.pathChild+o.id]);
  }

  setCriteria(criteria) {
    this.filterCriteria = criteria
    this.search()
  }

  
}
