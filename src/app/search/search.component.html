<!-- Navbar -->
<app-navbar [loading]="loading" backroute="..">
  <div class="d-flex justify-content-between align-items-center w-100">
    <a class="navbar-brand">
      <span> Cerca </span>
    </a>
    <div *ngIf="package">
      <button class="btn" (click)="toggleMessage()"
        [ngClass]="{'text-primary': openMessage, 'text-light': !openMessage}">
        <i class="fa-solid fa-lg fa-comment fa-lg text-light"></i>
      </button>
    </div>
  </div>
</app-navbar>

<app-message-widget *ngIf="openMessage" [id]="package.id" [action]="834">
</app-message-widget>

<!-- Scanner -->
<app-scanner style="height: 100vh; z-index: 9999; position: absolute;"  *ngIf="!package && !product" (onCode)="onCode($event)"></app-scanner>

<!-- Product Quantity Editor -->
<app-product-quantity-editor *ngIf="showQuantityEditor" [product]="product" (onSave)="onQuantity($event)"
  (onCancel)="showQuantityEditor = false">
</app-product-quantity-editor>

<!-- Product View -->
<div class="container-fluid px-3 py-4" *ngIf="product">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-start mb-3">
    <div>
      <h1 class="display-6 mb-0">{{ product.display_name }}</h1>
      <p class="text-muted mb-0" *ngIf="productAvailable">{{ quants[0]?.location_id?.name }}</p>
    </div>
    <span class="dropdown" *ngIf="productAvailable">
      <button class="btn btn-primary" type="button" data-bs-toggle="dropdown">
        <i class="fa-solid fa-bars"></i>
      </button>
      <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end">
        <li><h6 class="dropdown-header">Consuma</h6></li>
        <li><a class="dropdown-item" (click)="onConsume(quants[0], O_IDS.picking_type_consume_case_id, O_IDS.location_case_consume)">Case</a></li>
        <li><a class="dropdown-item" (click)="onConsume(quants[0], O_IDS.picking_type_consume_massello_id, O_IDS.location_massello_consume)">Massello</a></li>
        <li><a class="dropdown-item" (click)="onConsume(quants[0], O_IDS.picking_type_consume_destroyed_id, O_IDS.location_campioni_consume)">Campioni Pavimenti</a></li>
        <li><a class="dropdown-item" (click)="onConsume(quants[0], O_IDS.picking_type_consume_montaggio_id, O_IDS.location_montaggio_consume)">Montaggio</a></li>
        <li><a class="dropdown-item" (click)="onConsume(quants[0], O_IDS.picking_type_consume_manutenzioni_id, O_IDS.location_manutenzioni_consume)">Lavori interni</a></li>
        <li><a class="dropdown-item" (click)="onConsume(quants[0], O_IDS.picking_type_consume_destroyed_id, O_IDS.location_destroyed_consume)">Distrutto</a></li>
        <li><h5 class="dropdown-header text-center">Modifica</h5></li>
        <li>
          <a class="dropdown-item" (click)="onCreateScrap(quants[0])">
            Segnala offerta
          </a>
        </li>
      </ul>
    </span>
  </div>

  <hr class="mb-4">

  <!-- Content -->
  <div *ngIf="!productAvailable">
    <h6 class="text-muted">Nessuna disponibilità per il prodotto selezionato</h6>
  </div>

  <div *ngIf="productAvailable">
    <div class="row g-4 mb-3">
      <div class="col-6">
        <p class="text-muted mb-1">Quantità</p>
        <h5 class="mb-1">{{ quants[0]?.quantity | number : '1.0-2':'it-IT' }} {{ product.uom_id.name }}</h5>
        <small class="text-muted">{{ getDescriptiveBarcode(product, quants[0]?.quantity) }}</small>
      </div>
      <div class="col-6">
        <p class="text-muted mb-1">Disponibile</p>
        <h5 class="mb-1">{{ quants[0]?.available_quantity | number : '1.0-2':'it-IT'}} {{ product.uom_id.name }}</h5>
        <small class="text-muted">{{ getDescriptiveBarcode(product, quants[0]?.available_quantity) }}</small>
      </div>
    </div>
  </div>
</div>

<!-- Package Section -->
<div class="container-fluid px-3 py-4" *ngIf="package">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-start mb-3 me-2">
    <div>
      <h1 class="display-6 mb-0">{{ package.name }}</h1>
      <p class="text-muted mb-0">{{ package.location_id.name }}</p>
    </div>
    <div class="dropdown">
      <button (click)="loadLocations()" class="btn btn-dark dropdown-toggle" type="button" data-bs-toggle="dropdown">
        Stocca
      </button>
      <div class="dropdown-menu" style="max-height: calc(100vh - 180px); overflow-y: auto !important; z-index: 9999; position: absolute;">
        <div class="accordion" id="accordionExample">
          <div class="accordion-item" *ngFor="let g of locationGroups | keyvalue">
            <h2 class="accordion-header">
              <button (click)="$event.stopPropagation()" class="accordion-button" type="button"
                data-bs-toggle="collapse" [attr.data-bs-target]="'#loc' + g.key.replaceAll(' ', '')">
                {{ g.key }}
              </button>
            </h2>
            <div id="loc{{ g.key.replaceAll(' ', '') }}" class="accordion-collapse collapse"
              data-bs-parent="#accordionExample">
              <div class="accordion-body p-0">
                <ul class="list-group list-group-flush">
                  <li class="list-group-item" *ngFor="let v of g.value" (click)="move(v)">
                    {{ v.display_name }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <hr class="mb-4">

  <!-- Mobile View - Cards -->
  <div *ngIf="isMobileView" class="row g-3">
    <div class="col-12" *ngFor="let q of package?.quant_ids?.values">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start mb-3">
            <div class="d-flex flex-column mb-2">
              <h6 class="mb-1 me-2">{{ getBaseProductName(q.product_id?.name) }}</h6>
              <small class="text-muted" *ngIf="hasParentheses(q.product_id?.name)">
                {{ getParenthesesContent(q.product_id?.name) }}
              </small>
            </div>
            <div class="dropdown">
              <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="dropdown">
                <i class="fa-solid fa-bars"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end">
                <li><h5 class="dropdown-header text-center">Consuma</h5></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_case_id, O_IDS.location_case_consume, package)">Case</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_massello_id, O_IDS.location_massello_consume, package)">Massello</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_campioni_id, O_IDS.location_campioni_consume, package)">Campioni Pavimenti</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_montaggio_id, O_IDS.location_montaggio_consume, package)">Montaggio</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_manutenzioni_id, O_IDS.location_manutenizoni_consume, package)">Lavori interni</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_destroyed_id, O_IDS.location_destroyed_consume, package)">Distrutto</a></li>
                <li><h5 class="dropdown-header text-center">Modifica</h5></li>
                <li>
                  <a class="dropdown-item" [class.disabled]="!isModifiableProduct(q.product_id.value)"
                    [style.text-decoration]="!isModifiableProduct(q.product_id.value) ? 'line-through' : 'none'"
                    (click)="isModifiableProduct(q.product_id.value) && onModifyAttrs(q, package)">
                    Attributi
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" (click)="onCreateScrap(q, package)">
                    Segnala offerta
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div class="row g-3">
            <div class="col-6">
              <p class="text-muted mb-1">Quantità</p>
              <h6 class="mb-1">{{ q.quantity | number:'1.0-2':'it-IT' }} {{ q.product_uom_id.name }}</h6>
              <small class="text-muted">{{ getDescriptive(q, q.quantity) }}</small>
            </div>
            <div class="col-6">
              <p class="text-muted mb-1">Disponibile</p>
              <h6 class="mb-1">{{ q.available_quantity | number:'1.0-2':'it-IT' }} {{ q.product_uom_id.name }}</h6>
              <small class="text-muted">{{ getDescriptive(q, q.available_quantity) }}</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Desktop View - Table -->
  <div *ngIf="!isMobileView" class="table-container">
    <p-table [value]="package?.quant_ids?.values" [paginator]="true" [rows]="10" 
             [showCurrentPageReport]="true" [tableStyle]="{'min-width': '50rem'}"
             currentPageReportTemplate="Mostrando {first} a {last} di {totalRecords} articoli"
             [rowsPerPageOptions]="[10, 25, 50]"
             styleClass="table-with-dropdowns">
      
      <ng-template pTemplate="header">
        <tr>
          <th>Prodotto</th>
          <th>Varianti</th>
          <th>Quantità</th>
          <th>Quantità Descrittiva</th>
          <th>Disponibile</th>
          <th>Disponibile Descrittiva</th>
          <th>UdM</th>
          <th style="width: 120px;">Azioni</th>
        </tr>
      </ng-template>
      
      <ng-template pTemplate="body" let-q>
        <tr>
          <td>
            <strong>{{ getBaseProductName(q.product_id?.name) }}</strong>
          </td>
          <td>
            <span class="text-muted" *ngIf="hasParentheses(q.product_id?.name)">
              {{ getParenthesesContent(q.product_id?.name) }}
            </span>
            <span *ngIf="!hasParentheses(q.product_id?.name)" class="text-muted">-</span>
          </td>
          <td>
            <strong>{{ q.quantity | number:'1.0-2':'it-IT' }}</strong>
          </td>
          <td>
            <small class="text-muted">{{ getDescriptive(q, q.quantity) }}</small>
          </td>
          <td>
            <strong>{{ q.available_quantity | number:'1.0-2':'it-IT' }}</strong>
          </td>
          <td>
            <small class="text-muted">{{ getDescriptive(q, q.available_quantity) }}</small>
          </td>
          <td>
            {{ q.product_uom_id.name }}
          </td>
          <td>
            <div class="dropdown table-dropdown position-static">
              <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="dropdown" 
                      aria-expanded="false" data-bs-auto-close="true">
                <i class="fa-solid fa-bars"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end shadow-lg" 
                  style="z-index: 9999; position: absolute;">
                <li><h6 class="dropdown-header">Consuma</h6></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_case_id, O_IDS.location_case_consume, package)">Case</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_massello_id, O_IDS.location_massello_consume, package)">Massello</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_campioni_id, O_IDS.location_campioni_consume, package)">Campioni Pavimenti</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_montaggio_id, O_IDS.location_montaggio_consume, package)">Montaggio</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_manutenzioni_id, O_IDS.location_manutenizoni_consume, package)">Lavori interni</a></li>
                <li><a class="dropdown-item" (click)="onConsume(q, O_IDS.picking_type_consume_destroyed_id, O_IDS.location_destroyed_consume, package)">Distrutto</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><h6 class="dropdown-header">Modifica</h6></li>
                <li>
                  <a class="dropdown-item" [class.disabled]="!isModifiableProduct(q.product_id.value)"
                    [style.text-decoration]="!isModifiableProduct(q.product_id.value) ? 'line-through' : 'none'"
                    (click)="isModifiableProduct(q.product_id.value) && onModifyAttrs(q, package)">
                    Attributi
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" (click)="onCreateScrap(q, package)">
                    Segnala offerta
                  </a>
                </li>
              </ul>
            </div>
          </td>
        </tr>
      </ng-template>
      
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="text-center">
            <div class="p-4">
              <i class="fa-solid fa-box-open fa-3x text-muted mb-3"></i>
              <p class="text-muted">Nessun prodotto trovato nel pacco</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<app-modify-attributes (onDone)="onAttrsEditDone($event)" *ngIf="modifyingAttrsOfQuant" [quant]="modifyingAttrsOfQuant"
  (onCancel)="onCancel($event)"></app-modify-attributes>

<app-scraps *ngIf="showingScraps"
  [quant]="scrapQuant"
  [sourcePackage]="scrapSourcePackage"
  (onCancel)="showingScraps = false"
  (onConfirm)="onScrapConfirm($event)">
</app-scraps>

<!-- Transfers Section -->
<div class="container-fluid px-3 pb-5" *ngIf="package || product">
  <div class="d-flex justify-content-end mb-3">
    <button class="btn btn-outline-primary btn-sm" (click)="toggleTransfers()">
      {{ showTransfers ? 'Nascondi' : 'Mostra' }} Movimenti
    </button>
  </div>
  
  <!-- Updated Transfers Section - Replace in your search.component.html -->

<!-- Package Transfers - Mobile -->
<div *ngIf="showTransfers && moveLines?.length > 0 && package && isMobileView">
  <hr class="mb-4">
  <div class="row g-3">
    <div class="col-12" *ngFor="let t of moveLines">
      <div class="card">
        <div class="card-body">
          <div class="d-flex flex-column mb-2">
            <h6 class="mb-1 me-2">{{ getBaseProductName(t.product_id?.name) }}</h6>
            <small class="text-muted" *ngIf="hasParentheses(t.product_id?.name)">
              {{ getParenthesesContent(t.product_id?.name) }}
            </small>
          </div>
          
          <!-- Package Info -->
          <div class="row mb-2" *ngIf=" t.package_id?.name !== '-' ||  t.result_package_id?.name !== '-'">
            <div class="col-6" *ngIf="t.package_id?.name !== '-'">
              <small class="text-muted">Da Pacco:</small>
              <div class="fw-bold">{{ t.package_id?.name }}</div>
            </div>
            <div class="col-6" *ngIf=" t.result_package_id?.name !== '-'">
              <small class="text-muted">A Pacco:</small>
              <div class="fw-bold">{{  t.result_package_id?.name }}</div>
            </div>
          </div>
          
          <!-- Quantity and Operation -->
          <div class="d-flex justify-content-between mb-2">
            <p class="mb-0">
              {{ getMLQuantity(t) | number:'1.0-2':'it-IT' }}
              <small class="text-muted">{{ t.product_uom_id?.name }}</small>
            </p>
            <span class="text-muted">{{t.picking_type_id?.name?.replace('LOMAGNA: ', '') }}</span>
          </div>
          
          <!-- Group/Partner and Status -->
          <div class="d-flex justify-content-between mb-2">
            <p class="mb-0 text-muted">
              <a (click)="redirectTo(t)" class="text-decoration-none"> 
              <small>
                {{ t.move_id?.value?.group_id?.name || '' }}
                {{ t.move_id?.value?.group_id?.name ? ' - ' : '' }}
                {{ t.move_id?.value?.partner_id?.name ? '- ' + t.move_id?.value?.partner_id?.name : '' }}
              </small>
              </a>
            </p>
            <span class="badge" [ngClass]="t.state === 'done' ? 'bg-success' : 'bg-warning'">
              {{  t.state }}
            </span>
          </div>
          
          <!-- Date and Done By -->
          <div class="row">
            <div class="col-6">
              <small class="text-muted">Completato:</small>
              <div class="small">{{ t.write_date | date:'dd/MM/yyyy HH:mm'  }}</div>
            </div>
            <div class="col-6">
              <small class="text-muted">Da:</small>
              <div class="small">{{   t.create_uid?.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Package Transfers - Desktop -->
<div *ngIf="showTransfers && moveLines?.length > 0 && package && !isMobileView" class="table-container">
  <hr class="mb-4">
  <h5>Impegni del Pacco</h5>
  <p-table [value]="moveLines" [paginator]="true" [rows]="10" 
           [showCurrentPageReport]="true" [tableStyle]="{'min-width': '80rem'}"
           [rowsPerPageOptions]="[10, 25, 50]"
           currentPageReportTemplate="Mostrando {first} a {last} di {totalRecords} impegni"
           styleClass="table-with-dropdowns">
    
    <ng-template pTemplate="header">
      <tr>
        <th>Prodotto</th>
        <th>Da Pacco</th>
        <th>A Pacco</th>
        <th>Varianti</th>
        <th>Quantità</th>
        <th>Operazione</th>
        <th>Gruppo/Partner</th>
        <th>Data Completato</th>
        <th>Stato</th>
        <th>Completato Da</th>
      </tr>
    </ng-template>
    
    <ng-template pTemplate="body" let-t>
      <tr>
        <td>{{ getBaseProductName(t.product_id?.name) }}</td>
        <td>{{ t.package_id?.name }}</td>
        <td>{{ t.result_package_id?.name }}</td>
        <td>
          <span class="text-muted" *ngIf="hasParentheses(t.product_id?.name)">
            {{ getParenthesesContent(t.product_id?.name) }}
          </span>
          <span *ngIf="!hasParentheses(t.product_id?.name)" class="text-muted">-</span>
        </td>
        <td>{{ getMLQuantity(t) | number:'1.0-2':'it-IT' }} {{ t.product_uom_id?.name }}</td>
        <td>{{ t.picking_type_id?.name?.replace('LOMAGNA: ', '') }}</td>
        <td>
          <a (click)="redirectTo(t)" class="text-decoration-none"> 
          {{ t.move_id?.value?.group_id?.name || '' }}
          {{ t.move_id?.value?.group_id?.name ? ' - ' : '' }}
          {{ t.move_id?.value?.partner_id?.name ? '- ' + t.move_id?.value?.partner_id?.name : '' }}
          {{ t.picking_id?.name }}
          </a>
        </td>
        <td>{{ t.write_date | date:'dd/MM/yyyy HH:mm'    }}</td>
        <td>
          <span class="badge" [ngClass]="t.state === 'done' ? 'bg-success' : 'bg-warning'">
            {{ t.state }}
          </span>
        </td>
        <td>{{ t.create_uid?.name }}</td>
      </tr>
    </ng-template>
  </p-table>
</div>

<!-- Footer -->
<div class="navbar-footer bg-dark fixed-bottom d-flex p-2 gap-2" *ngIf="!modifyingAttrsOfQuant">
  <button class="btn btn-primary flex-grow-1 text-white" (click)="package = null; product = null ; moveLines = null; showTransfers = false">
    Scansiona ancora
  </button>
</div>