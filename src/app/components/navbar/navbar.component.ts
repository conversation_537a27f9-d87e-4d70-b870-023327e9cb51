import { Component, Input, OnInit } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { Task } from 'src/app/models/project.model';
import { User } from 'src/app/models/user.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';


//chrismaaassss

interface SnowflakeConfig {
  delay: number;
  duration: number;
  left: number;
  size: number;
}



@Component({
    selector: 'app-navbar',
    templateUrl: './navbar.component.html',
    styleUrls: ['./navbar.component.scss'],
    standalone: false
})
export class NavbarComponent implements OnInit {

  @Input() backroute?:string
  @Input() loading
  @Input() leadId?:string

  unreadNotificationsLength?: number;
  activitiesLength?: number;
  userId: number;
  user: User;
  isMobileView: boolean = true;
  hideCalendar: boolean = false;
  backupStatus: 'none' | 'old' | 'error' | 'recent' = 'recent';
  notifyBackup: boolean = true;  // notify user for backup problems
  version: string = '';


  // TICKET REQUEST
  showRequestDialog: boolean = false;
  requestName: string = '';
  requestDescription: string = '';
  requestLoading: boolean = false;
  requestCreated: boolean = false;
  createdRequestId: number | null = null;

  // chrismaaaassss
  // showSnowAnimation = false;
  // snowflakeConfigs: SnowflakeConfig[] = [];
  // pressTimer: any = null;
  // progressInterval: any = null;
  // isPressing = false;
  // pressProgress = 0;
  // readonly PRESS_DURATION = 2000;



  constructor(private odooEm: OdooEntityManager) {
    // Generate random configurations for 50 snowflakes
    // this.generateSnowflakes();
  }

  //hide unnecessary eleemnts on mobile that load up 

  async ngOnInit()  {
   await this.loadUserInfo();
    this.checkScreenSize();
    this.checkBackupStatus();
    this.getVersion();
  }

  async loadUserInfo() {
    const result: any = await this.odooEm.odoorpcService.getSessionInfo();
      this.userId = result.result.user_id[0];
      console.log("user id" ,this.userId)
  }

  private checkScreenSize() {
    this.isMobileView = window.innerWidth < 768; // Bootstrap's md breakpoint
    console.log('isMobileView', this.isMobileView);
  }

  async checkBackupStatus() {
    let status = this.backupStatus;
    //we show alerts only for some users: 
    let okUser = [13, 103].includes(this.userId);
    console.log('okUser', okUser);
    // based on status we switch the notifyBackup flag
    if (status === 'recent' || !okUser) {
      this.notifyBackup = false;
    } else {
      this.notifyBackup = okUser;
      alert('there seems to be a problem with the backup');
    }
  }

  async getVersion() {
    this.version = '29/05/2025 - 08:30';
  }


// TICKET REQUEST
// Add these methods
openRequestDialog(event: Event) {
  event.preventDefault();
  this.resetRequestForm();
  this.showRequestDialog = true;
}

closeRequestDialog() {
  this.showRequestDialog = false;
}

resetRequestForm() {
  this.requestName = '';
  this.requestDescription = '';
  this.requestLoading = false;
  this.requestCreated = false;
  this.createdRequestId = null;
}

async createRequest() {
  if (!this.requestName) {
    return;
  }
  
  this.requestLoading = true;
  console.log('Creating request task:', this.requestName);
  
  try {
    // Format description with HTML
    let formattedDescription = '';
    
    // Add user's description if present
    if (this.requestDescription) {
      formattedDescription += `<div>${this.requestDescription}</div>`;
    }
    
    // Add spacer
    formattedDescription += '<br><br>';
    
    // Add clickable link to current page
    formattedDescription += `<div><a href="${window.location.href}" target="_blank">${window.location.href}</a></div>`;
    
    const taskData = {
      name: this.requestName,
      project_id: 7, // Project ID as specified
      description: formattedDescription,
      stage_id: 219, // Stage ID as specified
      user_ids: [[6, 0, [this.userId]]] // Assign to current user
    };
    
    console.log('Creating task with data:', taskData);
    
    // Create the task
    const createdTask = await firstValueFrom(
      this.odooEm.create<Task>(new Task(), taskData)
    );
    
    console.log('Task created successfully:', createdTask);
    
    // Update state to show success message
    this.requestCreated = true;
    this.createdRequestId = createdTask.id;
    
  } catch (error) {
    console.error('Error creating request task:', error);
    alert('Errore durante la creazione della richiesta');
  } finally {
    this.requestLoading = false;
  }
}

  // chrismaaaassss
  // generateSnowflakes() {
  //   this.snowflakeConfigs = Array(100).fill(null).map(() => ({
  //     delay: Math.random() * 3,
  //     duration: 8 + Math.random() * 4,
  //     left: Math.random() * 100,
  //     size: 0.8 + Math.random() * 0.8
  //   }));
  // }

  // startPressTimer() {
  //   this.isPressing = true;
  //   this.pressProgress = 0;
  //   const startTime = Date.now();

  //   // Update progress every 50ms
  //   this.progressInterval = setInterval(() => {
  //     const elapsed = Date.now() - startTime;
  //     this.pressProgress = Math.min(elapsed / this.PRESS_DURATION, 1);
      
  //     if (this.pressProgress >= 1) {
  //       this.clearProgressInterval();
  //     }
  //   }, 50);

  //   this.pressTimer = setTimeout(() => {
  //     this.toggleSnowAnimation();
  //     this.isPressing = false;
  //     this.pressProgress = 0;
  //   }, this.PRESS_DURATION);
  // }

  // clearProgressInterval() {
  //   if (this.progressInterval) {
  //     clearInterval(this.progressInterval);
  //     this.progressInterval = null;
  //   }
  // }

  // clearPressTimer() {
  //   if (this.pressTimer) {
  //     clearTimeout(this.pressTimer);
  //     this.pressTimer = null;
  //   }
  //   this.clearProgressInterval();
  //   this.isPressing = false;
  //   this.pressProgress = 0;
  // }

  // toggleSnowAnimation() {
  //   this.showSnowAnimation = true;
  //   this.generateSnowflakes();
  //   setTimeout(() => {
  //     this.showSnowAnimation = false;
  //   }, 8000);
  // }
}