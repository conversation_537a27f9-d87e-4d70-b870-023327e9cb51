<app-navbar backroute=".." [loading]="loading">
  <div class="d-flex justify-content-between align-items-center w-100">
    <span class="navbar-brand">Produzioni</span>

  </div>
</app-navbar>



<p-table #dtext
  [value]="filteredProductions" 
  [columns]="cols" 
  [sortField]="sortField"
  [sortOrder]="sortOrder"
  [globalFilterFields]="['name', 'origin', 'product_id.name', 'user_id.name', 'state']"
  styleClass="p-datatable-gridlines" 
  showGridlines 
  stripedRows 
  dataKey="id"
  [resizableColumns]="true" 
  styleClass="p-datatable-sm"
  [scrollable]="true" 
  scrollHeight="calc(100vh - 210px)" 
  [tableStyle]="{'min-width': '50rem'}" 
  [paginator]="true"
  [rows]="50" 
  [rowsPerPageOptions]="[50, 100,250]" 
  stateStorage="local"
  stateKey="dt-prod-ext"
  >



<!-- Enhanced Filter Section -->
<ng-template pTemplate="caption" #caption>
 
  <div class="bg-light m-0  w-100" >
    <div class="container-fluid m-0">
      <!-- FIRST ROW - Status, Components, User, and Quick Actions -->
      <div class="row my-2 align-items-stretch">
        <!-- Search Input -->
        <div class="col-md-4 d-flex align-items-center">
          <i class="fa fa-filter me-2"></i>
           <input
              class="form-control" 
              (input)="dtext.filterGlobal($event.target.value, 'contains')"
              [ngModel]="dtext.filters['global']? dtext.filters['global'].value : ''"
              placeholder="Cerca prodotto, riferimento, origine...">
          </div>

      <!-- Status Buttons -->
      <div class="col-md-4 d-flex">
        <div class="d-flex align-items-center w-100">
          <span class="me-2 fw-semibold">Stato:</span>
          <div class="btn-group btn-group-sm flex-grow-1" role="group">
            <input type="checkbox" class="btn-check" id="btn-draft" [checked]="selectedStates.includes('draft')"
              (change)="toggleState('draft')">
            <label class="btn btn-outline-muted flex-fill" for="btn-draft">
              <span class="fa fa-circle text-muted me-1"></span> Bozza
            </label>

            <input type="checkbox" class="btn-check" id="btn-confirmed" [checked]="selectedStates.includes('confirmed')"
              (change)="toggleState('confirmed')">
            <label class="btn btn-outline-muted flex-fill" for="btn-confirmed">
              <span class="fa fa-circle text-secondary me-1"></span> Confermato
            </label>

            <input type="checkbox" class="btn-check" id="btn-progress" [checked]="selectedStates.includes('progress')"
              (change)="toggleState('progress')">
            <label class="btn btn-outline-muted flex-fill" for="btn-progress">
              <span class="fa fa-circle text-warning me-1"></span> In Corso
            </label>

            <input type="checkbox" class="btn-check" id="btn-to-close" [checked]="selectedStates.includes('to_close')"
              (change)="toggleState('to_close')">
            <label class="btn btn-outline-muted flex-fill" for="btn-to-close">
              <span class="fa fa-circle text-primary me-1"></span> Da Chiudere
            </label>

            <input type="checkbox" class="btn-check" id="btn-done" [checked]="selectedStates.includes('done')"
              (change)="toggleState('done')">
            <label class="btn btn-outline-muted flex-fill" for="btn-done">
              <span class="fa fa-circle text-success me-1"></span> Completato
            </label>
          </div>
        </div>
      </div>

      <!-- User Selection -->
      <!-- <div class="col-md-2 d-flex">
        <div class="input-group input-group-sm flex-grow-1">
          <span class="input-group-text">Utente</span>
          <select class="form-select" [(ngModel)]="selectedUser" (change)="applyFilters()">
            <option [ngValue]="null">Tutti</option>
            <option *ngFor="let user of users" [ngValue]="user">{{user.name}}</option>
          </select>
          <button class="btn btn-outline-secondary" (click)="toggleMyProductions()"
            [ngClass]="showMyProductions ? 'active' : ''" title="Solo le mie produzioni">
            <i class="fa-solid fa-user"></i>
          </button>
        </div>
      </div> -->

      <!-- Quick Actions -->
      <div class="col-md-4 d-flex">
        <div class="btn-group btn-group-sm w-100" role="group">
          <p-button 
            (click)="createNewProduction()" 
            label="Nuova" 
            icon="fa fa-plus" 
            class="ms-auto p-button-primary"></p-button>

          <!-- <button class="btn btn-info flex-fill" (click)="resetFilters()">
            <i class="fa-solid fa-refresh me-1"></i> Reset
          </button> -->
        </div>
      </div>
      
      </div>
    </div>
  </div>

</ng-template>

  <ng-template #header pTemplate="header" let-columns>
    <tr>
      <th *ngFor="let col of columns" pResizableColumn [pSortableColumn]="col.sortable ? col.field : null"
        [style.min-width]="col.width">
        {{ col.header }}
        <p-sortIcon *ngIf="col.sortable" [field]="col.field"></p-sortIcon>
      </th>
    </tr>
  </ng-template>

  <ng-template pTemplate="body" let-production let-columns="columns">
    <tr (click)="onRowClick(production.id)" class="cursor-pointer" [ngClass]="{'bg-light-warning': isLate(production)}">

      <ng-container *ngFor="let col of columns">
        <!-- Render column based on field type -->
        <td *ngIf="col.field === 'name'">
          <span class="text-fw-bold">{{ production.name }}</span>
          
        </td>

        <td *ngIf="col.field === 'origin'">
          <div class="d-flex align-items-center">
            <div>
              <!-- Display origin code with blue color -->
              <div>
                <span class="text-primary" *ngIf="production.origin">{{ production.origin }}</span>

              </div>
            </div>
          </div>
        </td>

        <td *ngIf="col.field === 'product_id.name'">
          <div>
            <span class="fw-medium">{{ production.product_id?.name?.replaceAll(", -","") }}</span>
            <span *ngIf="production.product_description_variants" class="d-block small text-muted">
              {{ production.product_description_variants?.replaceAll(", -","") | slice:0:40 }}{{
              production.product_description_variants?.length > 40 ? '...' : '' }}
            </span>
          </div>
        </td>

        <td *ngIf="col.field === 'product_qty'">
          <div class="d-flex align-items-center">
            <span [ngClass]="production.qty_producing > 0 ? 'text-primary' : ''">
              {{ production.qty_producing | number : '1.0-3' }}
            </span>
            <span class="mx-1 text-muted">/</span>
            <span class="fw-medium">{{ production.product_qty | number : '1.0-3' }}</span>
            <span class="ms-1 text-muted">{{ production.product_uom_id?.name }}</span>
          </div>
        </td>

        <td *ngIf="col.field === 'date_deadline'">
          <span [ngClass]="isLate(production) ? 'text-danger fw-bold' : ''">
            {{ production.date_deadline | date:'dd/MM/yyyy' }}
          </span>
        </td>

        <td *ngIf="col.field === 'date_planned_start'">
          {{ production.date_planned_start | date:'dd/MM/yyyy' }}
        </td>

        <td *ngIf="col.field === 'user_id.name'">
          <div class="d-flex align-items-center">
            <span *ngIf="production.user_id?.name">
              {{ production.user_id.name }}
            </span>
            <span *ngIf="!production.user_id?.name" class="text-muted">
              Non assegnato
            </span>
          </div>
        </td>

        <td *ngIf="col.field === 'state'">
          <span class="" [ngClass]="getStatusClass(production.state)">
            {{ getStateLabel(production.state) }}
          </span>
          <ng-container *ngIf="production.workorder_ids?.values?.length > 0">
            <div class="mt-1 small text-muted">
              {{ production.workorder_ids.values.length }} lavorazioni
            </div>
          </ng-container>
        </td>

        <td *ngIf="col.field === 'components_availability_state'">
          <div class="d-flex align-items-center">
            <span [ngClass]="getProductionReadinessColor(production)">
              <i class="fa me-2" [ngClass]="getProductionReadinessIcon(production)"></i>
              {{ getProductionReadiness(production) }}
            </span>
          </div>
        </td>

        <!-- Generic field handling for any other columns -->
        <td
          *ngIf="!['name', 'origin', 'product_id.name', 'product_qty', 'date_deadline', 'date_planned_start', 'user_id.name', 'state', 'components_availability_state'].includes(col.field)">
          {{ getFieldValue(production, col.field) }}
        </td>
      </ng-container>

    </tr>
  </ng-template>

  <!-- <ng-template pTemplate="emptymessage">
    <tr>
      <td [attr.colspan]="selectedColumns.length" class="text-center p-4">
        <i class="fa fa-exclamation-circle text-muted mb-2" style="font-size: 2rem"></i>
        <div>Nessuna produzione trovata</div>
        <button class="btn btn-sm btn-outline-primary mt-3" (click)="loadProductions()">
          <i class="fa fa-refresh me-2"></i>Ricarica
        </button>
      </td>
    </tr>
  </ng-template> -->

</p-table>