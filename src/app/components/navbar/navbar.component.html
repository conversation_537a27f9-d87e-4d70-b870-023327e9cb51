<nav class="navbar navbar-expand bg-dark navbar-dark">
  <div class="container-fluid">
    <div class="collapse navbar-collapse px-0" id="navbarSupportedContent">
      <!-- Home Dropdown -->
      <!-- @if (!isMobileView){
      <div class="dropdown me-2">
        <button class="btn btn-link text-white p-1" 
                type="button" 
                id="homeDropdown" 
                data-bs-toggle="dropdown" 
                aria-expanded="false">
          <i class="fas fa-home"></i>
        </button>
        <div class="dropdown-menu dropdown-menu-start p-0" 
             aria-labelledby="homeDropdown" 
             style="min-width: 300px; max-height: 80vh; overflow-y: auto;">
          <app-home-widget></app-home-widget>
        </div>
      </div>
      } -->

      <!-- backroute -->
      <button *ngIf="backroute" class="btn btn-link text-primary" [routerLink]="backroute ? backroute : '..'" queryParamsHandling="preserve">
        <i class="fas fa-chevron-left me-1"></i>
      </button>

      <!-- MERRY CHRISTMAS
      @if (!isMobileView){
      <i class="fa-solid fa-hat-santa me-1 cursor-pointer" 
        (mousedown)="startPressTimer()" 
        (mouseup)="clearPressTimer()"
        (mouseleave)="clearPressTimer()"
        [class.pressing]="isPressing"
        [ngClass]="isPressing ? 'fa-lg text-primary' : 'fa-lg text-white'"
        [style.opacity]="isPressing ? (0.3 + (pressProgress * 0.7)) : 1">
      </i>
      } -->
      <ng-content></ng-content>

      <!-- Added d-flex and ms-auto to push these items right -->
      <div class="d-flex ms-auto align-items-center">
        @if (!isMobileView && !hideCalendar){
          <app-calendar-widget class="d-none d-md-block" ></app-calendar-widget>

          <!-- dropdown with question mark -->
          <div class="dropdown ">
            <button class="btn btn-link position-relative text-white" 
                    type="button" 
                    id="dropdownMenuButton" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false">
                    <i class="fa-solid fa-circle-info"></i>
              
              <!-- Cloud icon for backup status -->
              @if (notifyBackup) {
                <i class="fa-solid fa-triangle-exclamation text-danger position-absolute top-10 start-60 translate-middle" 
                   style="font-size: 0.7 rem;">
                  <span class="visually-hidden">Problemi con i backup</span>
                </i>
              }
            </button>
            <ul class="dropdown-menu dropdown-menu-end shadow-lg" 
                aria-labelledby="helpDropdown" 
                style="min-width: 250px;">
                <a class="dropdown-item d-flex justify-content-between align-items-center" 
                   href="https://trello.com/b/W3EJtffy/910-odoo-manuale" 
                   target="_blank">
                  Manuale
                  <i class="fas fa-external-link-alt text-muted ms-2"></i>
                </a>
 
              <li>
                <a class="dropdown-item d-flex justify-content-between align-items-center" 
                   [ngClass]="{'text-danger': notifyBackup}"
                href="#" 
                   (click)="checkBackupStatus()">
                  <span>Stato backup: {{backupStatus}}</span>
                  <i class="fa-solid fa-cloud-arrow-up text-muted ms-2"></i>
                </a>
              </li>
              <li>
                <a class="dropdown-item d-flex justify-content-between align-items-center">
                  Versione
                  <span class="text-muted">{{version}}</span>
                </a>
              </li>
              <li>
                <a class="dropdown-item d-flex justify-content-between align-items-center" 
                   href="#" 
                   (click)="openRequestDialog($event)">
                  <span>Crea richiesta</span>
                  <i class="fa-solid fa-plus text-muted ms-2"></i>
                </a>
              </li>
            </ul>
          </div>
        }
      </div>
    </div>
  </div>
</nav>

<bar-loader [loading]="loading"></bar-loader>


<!-- TICKET REQUEST -->
<p-dialog 
  [(visible)]="showRequestDialog" 
  [style]="{width: '450px'}" 
  [modal]="true" 
  [draggable]="false" 
  [resizable]="false"
  header="Crea nuova richiesta"
  [closeOnEscape]="true"
  styleClass="bootstrap-dialog">
  
  <div *ngIf="!requestCreated" class="p-2">
    <div class="mb-3">
      <label for="requestName" class="form-label">Titolo</label>
      <input type="text" class="form-control" id="requestName" [(ngModel)]="requestName">
    </div>
    <div class="mb-3">
      <label for="requestDescription" class="form-label">Descrizione (potrai aggiungere altro succesivamente)</label>
      <textarea class="form-control" id="requestDescription" rows="4" [(ngModel)]="requestDescription"></textarea>
    </div>
  </div>
  
  <div *ngIf="requestCreated" class="text-center p-3">
    <div class="mb-3">
      <i class="fa-solid fa-check-circle text-success fa-3x"></i>
    </div>
    <p>Richiesta creata con successo!</p>
  </div>
  
  <ng-template pTemplate="footer">
    <div *ngIf="!requestCreated" class="d-flex justify-content-end">
      <button class="btn btn-light me-2" (click)="closeRequestDialog()">
        <i class="fa fa-times me-1"></i>
        Annulla
      </button>
      <button class="btn btn-primary text-white" 
              [disabled]="requestLoading || !requestName"
              (click)="createRequest()">
        <i class="fa fa-spinner fa-spin" *ngIf="requestLoading"></i>
        <i class="fa fa-check me-1" *ngIf="!requestLoading"></i>
        {{requestLoading ? 'Creazione...' : 'Conferma'}}
      </button>
    </div>
    <div *ngIf="requestCreated" class="d-flex justify-content-center">
      <a class="btn btn-primary text-white"
         [href]="'https://o3.galimberti.eu/web#id=' + createdRequestId + '&cids=1&menu_id=422&action=635&active_id=7&model=project.task&view_type=form'" 
         target="_blank">
        <i class="fa fa-external-link me-1"></i>
        Apri per aggiungere dettagli o screenshot
      </a>
    </div>
  </ng-template>
</p-dialog>

<!-- Snow animation container       MERRY CHRISTMAS
<div *ngIf="showSnowAnimation" class="snowfall-container">
  <div *ngFor="let config of snowflakeConfigs" class="snowflake"
       [style.left.%]="config.left"
       [style.animationDelay.s]="config.delay"
       [style.fontSize.em]="config.size">
    <i class="fa-solid fa-snowflake"></i>
  </div>
</div>
-->
