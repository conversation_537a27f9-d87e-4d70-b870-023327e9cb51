import { OdooModel } from './odoo-model.model';
import { OdooSerializableInterface } from '../shared/interfaces/odoo-serializable-interface';
import { SaleOrder } from './sale-order.model';
import { OdooMultiRelationship } from './odoo-multi-relationship.model';
import { OdooRelationship } from './odoo-relationship.model';

import { StockQuant } from './stock-quant';
import { StockLocation } from './stock-location';
import { IrAttachment, MailMessage } from './mail.message';
import { ProductTag } from './product.tag.model';


export class StockQuantPackage extends OdooModel implements OdooSerializableInterface<StockQuantPackage> {
  public readonly ODOO_MODEL = 'stock.quant.package';

  id: number;
  ga_tag_ids:OdooMultiRelationship<ProductTag> = new OdooMultiRelationship(ProductTag);
  ga_note_pacco:string = "";
  // unit: number;
  // packages: number;
  // barcodes: string[];
  // pacco_from: OdooRelationship = new OdooRelationship()
  // pacco_to: OdooRelationship = new OdooRelationship()
  // righe_from:OdooRelationship<NaPacchiProduct>
  // product_id:OdooRelationship = new OdooRelationship()
  // product_uom_qty
  // product_uom_qty_po
  // move_line_ids: OdooRelationship<StockMoveLine> = new OdooRelationship<StockMoveLine>()
  // result_package_id:OdooRelationship<StockQuantPackage>
  quant_ids:OdooMultiRelationship<StockQuant> = new OdooMultiRelationship(StockQuant)
  location_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>()
  // state = null
  // qty:number = 0
  name:string = ""
  message_ids:OdooMultiRelationship<MailMessage> = new OdooMultiRelationship<MailMessage>(MailMessage)
  message_attachment_count:number = 0
  // write_date:string

  _attachments: IrAttachment[] = [];
 

  create(): StockQuantPackage {
    return new StockQuantPackage();
  }
}
