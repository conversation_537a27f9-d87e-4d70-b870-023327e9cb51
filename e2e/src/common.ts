import { browser, by, element, protractor } from 'protractor';
const EC = protractor.ExpectedConditions;

export class CommonTest {

    navigateToLogin() {
        browser.get('/login');
        var btn = element(by.css('[data-test-id=loginButton]'))
        btn.click()
    }

    navigateToContact() {
        return browser.get('/contact')
    }

    async openDeailContact() {

        // search gilio
        var input = element.all(by.css('[data-test-id=inputSearchContact]')).get(0)
        await browser.driver.executeScript("arguments[0].click();", input.getWebElement());
        await input.sendKeys("giulio seregni")

        // click it 
        element.all(by.css('[data-test-id=searchResult] a')).first().click()
    }

    async testProduct(id, isSalesProduct, objectValue) {

        var key = 2;

        var objectLabel = {}
        if (isSalesProduct) {
            objectLabel = {
                "1": "Lung",
                "2": "<PERSON>rgh",
                "3": "<PERSON><PERSON>",
                "4": "<PERSON>à",
                "5": "<PERSON>",
                "6": "<PERSON><PERSON>",
                "7": "Desc",
                "11": "Prezzo",
                "12": "Sconto",
                "13": "Finale",
                "14": "Totale"
            }
        } else { // purchase
            objectLabel = {
                "1": "Lung",
                "2": "Largh",
                "3": "Spes",
                "4": "Unità",
                "5": "Rich",
                "6": "Lorda",
                "7": "Desc",
                "8": "Prezzo",
                "9": "Sconto",
                "10": "Finale",
                "11": "Totale"
            }
        }

        await browser.wait(EC.presenceOf( element(by.css('[data-test-id=inventoryButton]'))), 15000);
        await browser.driver.executeScript("arguments[0].click();", element.all(by.css('[data-test-id=inventoryButton]')).get(0).getWebElement());

        var input = element(by.css('[data-test-id=search-input]'))
        input.clear()
        input.sendKeys(id)
        element.all(by.css('[data-test-id=tableInventory]')).first().click()
        var close = element(by.css('[data-test-id=inventoryCloseIcon]'))
        browser.driver.executeScript("arguments[0].click();", close.getWebElement());

        var rowProduct = element.all(by.css('[data-test-id=tableProducts]')).all(by.tagName("tr")).get(key)
        var cells = rowProduct.all(by.tagName("td"))

        await browser.executeScript("arguments[0].scrollLeft = 0", element(by.css('[data-test-id=tableProducts]')).getWebElement());
        var inputRich = await cells.get(5).all(by.tagName('input')).get(0)
        // await browser.wait(EC.visibilityOf(inputRich), 20000)
        // set rich field value
        var valueInput = await inputRich.getAttribute('value')
        var i = valueInput.length;
        while (i--)
            await inputRich.sendKeys(protractor.Key.BACK_SPACE)
        await inputRich.sendKeys(objectValue[5] + "\n")

        // set discount
        if (isSalesProduct) {
            var keySconto = 12;
            var word = 'sales';
            var indexEditable = [4, 11, 12];
        } else {
            var keySconto = 9;
            var word = "purchase";
            var indexEditable = [4, 8, 9];
        }
        await browser.executeScript("arguments[0].scrollLeft = 5000", element(by.css('[data-test-id=tableProducts]')).getWebElement());
        // await browser.sleep(2000)

        var inputSconto = await cells.get(keySconto).all(by.tagName('input')).get(0)
        await browser.wait(EC.visibilityOf(inputSconto), 20000)
        var valueInput = await inputSconto.getAttribute('value')
        var i = valueInput.length;
        while (i--)
            await inputSconto.sendKeys(protractor.Key.BACK_SPACE)
        await inputSconto.sendKeys(objectValue[keySconto] + "\n")
        // await browser.sleep(1000)



        for (const key in objectLabel) {
            if (key == "5" || key == "13" || key == "10") continue;
            if (indexEditable.indexOf(Number(key)) == -1) {

                var text = await cells.get(Number(key)).getWebElement().getAttribute('innerText')
                if (parseFloat(text) !== parseFloat(objectValue[key]))
                    await this.failProductTest(" error READ product " + word + " - id " + id + " - '" + objectLabel[key] + "' value " + parseFloat(text), key)
            } else {
                var inputUnità = await cells.get(Number(key)).all(by.tagName('input')).get(0)
                var valueInputUnità = await inputUnità.getAttribute('value')
                if (parseFloat(valueInputUnità) !== parseFloat(objectValue[key]))
                    await this.failProductTest(" error READ product " + word + " - id " + id + " - '" + objectLabel[key] + "' value " + parseFloat(valueInputUnità), key)
            }


        }

        //remove product
        var rowProduct = element.all(by.css('[data-test-id=tableProducts]')).all(by.tagName("tr")).get(key)
        var cellsTh = rowProduct.all(by.tagName("th"));
        var inputCheckBox = await cellsTh.get(0).all(by.tagName('input')).get(0)
        browser.driver.executeScript("arguments[0].click();", inputCheckBox.getWebElement());
        browser.driver.executeScript("arguments[0].click();", element(by.css('[data-test-id=deleteButton]')).getWebElement());
        await browser.sleep(1000)

    }


    failProductTest = async (error, key) => {
        // remove product
        // var rowProduct = element.all(by.css('[data-test-id=tableProducts]')).all(by.tagName("tr")).get(key)
        // var cellsTh = rowProduct.all(by.tagName("th"));
        // var inputCheckBox = await cellsTh.get(0).all(by.tagName('input')).get(0)
        // browser.driver.executeScript("arguments[0].click();", inputCheckBox.getWebElement());
        // browser.driver.executeScript("arguments[0].click();", element(by.css('[data-test-id=deleteButton]')).getWebElement());
        return fail(error)
    }
}