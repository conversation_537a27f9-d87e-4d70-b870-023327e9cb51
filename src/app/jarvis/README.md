# Chat Component con OpenAI Integration

Questo componente fornisce un'interfaccia chat semplice e moderna che si integra con le API di OpenAI tramite il servizio L33tService.

## Caratteristiche

- 🤖 Interfaccia chat moderna e responsive
- 🔑 Configurazione sicura della API Key (solo locale)
- 🎯 Selezione del modello OpenAI (GPT-3.5, GPT-4, etc.)
- ⚙️ Prompt di sistema personalizzabile
- 💬 Cronologia dei messaggi
- 🎨 Design moderno con gradients e animazioni
- 📱 Responsive design per mobile

## Come usare

### 1. Navigazione
Il componente è accessibile tramite la route `/jarvis`

### 2. Configurazione API Key
1. Apri il componente chat
2. Inserisci la tua API Key di OpenAI nel campo di configurazione
3. Clicca "Configura" per iniziare

### 3. Utilizzo
- Scrivi un messaggio nella casella di input
- Premi Invio per inviare (Shift+Invio per andare a capo)
- Seleziona il modello OpenAI desiderato dal dropdown
- Personalizza il prompt di sistema se necessario

## Servizio L33tService

Il componente utilizza il servizio `L33tService` che fornisce un wrapper completo per le API OpenAI:

### Metodi principali:

```typescript
// Configurazione
setApiKey(apiKey: string): void
setBaseUrl(baseUrl: string): void
isConfigured(): boolean

// Chat Completions
sendMessage(message: string, model?: string, systemPrompt?: string): Observable<string>
createChatCompletion(request: OpenAICompletionRequest): Observable<OpenAICompletionResponse>
createConversation(messages: OpenAIMessage[], model?: string): Observable<string>

// Embeddings
getTextEmbedding(text: string, model?: string): Observable<number[]>
createEmbeddings(request: OpenAIEmbeddingRequest): Observable<OpenAIEmbeddingResponse>

// Utility
getModels(): Observable<any>
```

### Esempio di utilizzo del servizio:

```typescript
import { L33tService } from '../l33t.service';

constructor(private l33tService: L33tService) {}

// Configura API Key
this.l33tService.setApiKey('sk-your-api-key-here');

// Invia un messaggio semplice
this.l33tService.sendMessage('Ciao, come stai?')
  .subscribe(response => {
    console.log('Risposta AI:', response);
  });

// Conversazione con più messaggi
const messages = [
  { role: 'system', content: 'Sei un assistente utile' },
  { role: 'user', content: 'Spiegami la fisica quantistica' }
];

this.l33tService.createConversation(messages, 'gpt-4')
  .subscribe(response => {
    console.log('Risposta:', response);
  });
```

## Sicurezza

- La API Key viene utilizzata solo localmente nel browser
- Non viene memorizzata permanentemente
- Tutte le comunicazioni avvengono direttamente con OpenAI
- Nessun dato viene inviato a server intermedi

## Personalizzazione

### Modelli supportati
- gpt-3.5-turbo (default)
- gpt-4
- gpt-4-turbo-preview

### Prompt di sistema
Il prompt di sistema può essere personalizzato per modificare il comportamento dell'AI:
- Linguaggio di risposta
- Stile di comunicazione
- Competenze specifiche
- Limitazioni

## Dipendenze

Il componente richiede:
- Angular HttpClient (già configurato)
- FormsModule per i form
- CommonModule per le direttive comuni

## Installazione pacchetto OpenAI

Per installare il pacchetto OpenAI (opzionale, il wrapper funziona anche senza):

```bash
# Con npm
npm install openai

# Con yarn
yarn add openai
```

Nota: Il wrapper è stato progettato per funzionare anche senza il pacchetto ufficiale OpenAI, utilizzando direttamente le API HTTP.
