import { browser, by, element, protractor } from 'protractor';
const EC = protractor.ExpectedConditions;

export class SalesTest {

  async newSalesOrder() {

    var salesBtn = element.all(by.css('[data-test-id=salesButton]')).get(0)
    await browser.driver.executeScript("arguments[0].click();", salesBtn.getWebElement());

    var createSaleOrderBtn = element.all(by.css('[data-test-id=createSaleOrderButton]')).get(0)
    await browser.driver.executeScript("arguments[0].click();", createSaleOrderBtn.getWebElement());
  }
}