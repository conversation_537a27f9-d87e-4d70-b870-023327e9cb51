<form (ngSubmit)="insert()" #form="ngForm">

    <div class="form-group row">
      <label  class="col-sm-4 col-form-label">Scadenza</label>
      <div class="col-sm-8">
        <input required type="date" name="due" class="form-control"  [(ngModel)]="due">
      </div>
    </div>
  
    <ng-container *ngFor="let field of fields; index as i;">
        <div class="form-group row" *ngIf="(field.type == 'text' || field.type == 'number') && !field.name.endsWith('*')">
            <label for="inputEmail3" class="col-sm-4 col-form-label">{{field.name}}</label>
            <div class="col-sm-8">
              <input required [name]="field.id" class="form-control" [(ngModel)]="field.value">
            </div>
        </div>
        <div class="form-group row" *ngIf="field.type == 'list' && !field.name.endsWith('*')">
          <label class="col-sm-4 col-form-label">{{field.name}}</label>
          <div class="col-sm-8">
            <select required class="form-control" [name]="field.id" [(ngModel)]="field.value">
              <option *ngFor="let o of field.options" [value]="o.id">{{o.value.text}}</option>
            </select>
          </div>
        </div>
  
        <div class="form-group row" *ngIf="field.type == 'checkbox' && !field.name.endsWith('*')">
          <label class="col-sm-4 col-form-label">{{field.name}}</label>
          <div class="col-sm-8 d-flex align-items-center">
            <input type="checkbox"  [name]="field.id" [(ngModel)]="field.value">
            <!-- <select required class="form-control" [name]="field.id" [(ngModel)]="field.value">
              <option *ngFor="let o of field.options" [value]="o.id">{{o.value.text}}</option>
            </select> -->
          </div>
        </div>
  
  
        <div class="form-group row" *ngIf="field.type == 'checkbox' && field.name.endsWith('*')">
          <label class="col-sm-4 col-form-label">{{field.name}}</label>
          <div class="col-sm-8 d-flex align-items-center">
            <input type="checkbox" [name]="field.id" [(ngModel)]="field.value" >
            <!-- <select required class="form-control" [name]="field.id" [(ngModel)]="field.value">
              <option *ngFor="let o of field.options" [value]="o.id">{{o.value.text}}</option>
            </select> -->
          </div>
        </div>
  
        <div class="form-group row" *ngIf="field.type == 'date' && !field.name.endsWith('*')">
          <label class="col-sm-4 col-form-label">{{field.name}}</label>
          <div class="col-sm-8 d-flex align-items-center">
            <input type="date" required [name]="field.id" [(ngModel)]="field.value" class="form-control">
            <!-- <select required class="form-control" [name]="field.id" [(ngModel)]="field.value">
              <option *ngFor="let o of field.options" [value]="o.id">{{o.value.text}}</option>
            </select> -->
          </div>
        </div>
  
        <div class="form-group row" *ngIf="field.type == 'date' && field.name.endsWith('*')">
          <label class="col-sm-4 col-form-label">{{field.name}}</label>
          <div class="col-sm-8 d-flex align-items-center">
            <input type="date" [name]="field.id" [(ngModel)]="field.value" class="form-control">
            <!-- <select required class="form-control" [name]="field.id" [(ngModel)]="field.value">
              <option *ngFor="let o of field.options" [value]="o.id">{{o.value.text}}</option>
            </select> -->
          </div>
        </div>
  
  
        
  
    </ng-container>
      
    <!-- added to standard trello-insert / bad -->

    <div class="form-group row mb-3">
        <label class="col-sm-4 col-form-label">Impresa affidataria</label>
        <div class="col-sm-8">
            <div class="flex-nowrap d-flex">
                <input class="form-control" disabled>
               
                <!-- NUOVO DEAL -->
                <div class="dropdown dropright ms-auto">
                    <button class="btn btn-link px-1" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fal fa-bullseye"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-right" style="min-width: 300px;z-index:9999999">
                    <form class="px-4 py-3" style="min-width: 440px;">
                        
                        <!--<app-contact class="embedded" (onSelect)="onContact($event)"></app-contact>-->
    
                    </form>
                    </div>
                </div>
                <!-- new deal -->
              </div>
        </div>
    </div>



    <div class="form-group row">
        <label class="col-sm-4 col-form-label">Email Impresa affidataria</label>
        <div class="col-sm-8 d-flex align-items-center">
        <input type="date">
        </div>
    </div>
      
    <button [disabled]="!form.form.valid" type="submit" class="btn btn-primary w-100 text-white" >Crea</button>
  </form>
  