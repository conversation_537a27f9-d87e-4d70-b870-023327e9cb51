<table class="table table-flush" >
    <tr *ngFor="let a of array_events ">
        <td class="align-middle w-100 ps-4 lead">

            <a>
            {{a.name}} 
            </a><br/>
            <small class="text-muted">{{a.start.split(" ")[0]}}</small>
        </td>
        <td class="ps-auto">

            <button (click)="openCalendarOdoo()"  class="btn me-3 text-center" >
                <i [ngClass]="a.attendee_status == 'accepted' ? 'fa-calendar' : 'fa-calendar-xmark' " class="fa fa-3x text-primary" ></i>
            </button>

   
            <!-- <a href="leads/{{lead.id}}/production/{{o.id}}" class="btn me-3 text-center">
                <i class="fad fa-clipboard-list text-primary fa-3x"></i>
                <br>Nota
            </a> -->
            <!-- <a href="leads/{{lead.id}}/production/{{o.id}}" class="btn me-3 text-center">
                <i class="fad fa-box-alt text-primary fa-3x"></i>
                <br><PERSON>chi
            </a> -->
        </td>
    </tr>
</table>
