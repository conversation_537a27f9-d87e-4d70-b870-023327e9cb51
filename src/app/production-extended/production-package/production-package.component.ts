import { AfterViewInit, Component, Input, OnInit } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { MrpProduction } from 'src/app/models/mrp-production';
import { StockMove } from 'src/app/models/stock-move';
import { StockMoveLine } from 'src/app/models/stock-move-line';
import { StockQuant } from 'src/app/models/stock-quant';
import { StockQuantPackage } from 'src/app/models/stock-quant-package';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
  selector: 'app-production-package',
  standalone: false,
  templateUrl: './production-package.component.html',
  styleUrl: './production-package.component.scss'
})

// this class handle the output production , use odooem similary
export class ProductionPackageComponent implements OnInit, AfterViewInit {
  @Input() production: MrpProduction;
  @Input() preproduction: MrpProduction;

  packages: StockQuantPackage[] = [];

  constructor(private odooEm: OdooEntityManager) { }
  async ngAfterViewInit(): Promise<void> {

    await this.solvePreproduction()
    await this.solveProduction()
  }


  async solvePreproduction() {
    await firstValueFrom(this.odooEm.resolve(this.preproduction.move_byproduct_ids))
    //solve mov elines
    await firstValueFrom(this.odooEm.resolveArray(new StockMoveLine(), this.preproduction.move_byproduct_ids.values, "move_line_ids"))
    // get all movelines
    let moveLines = this.preproduction.move_byproduct_ids.values.map(move => move.move_line_ids.values).flat()
    // reolve result_packageid on moveline
    await firstValueFrom(this.odooEm.resolveArrayOfSingle(new StockQuantPackage(), moveLines, "result_package_id"))
    // extract all result packages unique
    this.packages = Array.from(new Set(moveLines.map(line => line.result_package_id.value))).filter(p => p != null)
    // this.production.move_byproduct_ids.values.map(move => move.move_line_ids.values.map(line => line.result_package_id)).flat()

    //solve quants of packages
    await firstValueFrom(this.odooEm.resolveArray(new StockQuant(), this.packages, "quant_ids"))
    console.log("PRE PRODUCTION ", this.preproduction);
    console.log("PACKAGES", this.packages)
  }

  async solveProduction() {
    await firstValueFrom(this.odooEm.resolve(this.production.move_finished_ids))
//solve move raw ids
await firstValueFrom(this.odooEm.resolve(this.production.move_raw_ids))
    //sove existing move lines
    await firstValueFrom(this.odooEm.resolveArray(new StockMoveLine(), this.production.move_raw_ids.values, "move_line_ids"))
    console.log("FINAL PRODUCTION ", this.production);
  }

async refetchProduction() {
  await firstValueFrom(this.odooEm.search<MrpProduction>(new MrpProduction(), [['id', '=', this.production.id]]))
  await this.solveProduction()
}

  async onPackageClick(pkg: StockQuantPackage) {
    console.log("Package clicked", pkg);

    // create input move lines with package content


    //create a move and a moveline for each quant ogf the package
    for (const quant of pkg.quant_ids.values) {

      //check if we already have a moveline for this quant
      const existingMoveLine = this.production.move_raw_ids.values.map(move => move.move_line_ids.values).flat().find(line => line.product_id.id == quant.product_id.id && line.package_id.id == quant.package_id.id)
      if (existingMoveLine) {
        console.log("Existing move line", existingMoveLine);
        if (existingMoveLine.qty_done == 0) {
          //this means the package is deselected, we have to reselect it and set the quantity
          await firstValueFrom(this.odooEm.update<StockMoveLine>(existingMoveLine, {
            qty_done: quant.quantity
          }))
          continue;
        }
        else {
          //we are deselecting: set qty_done to 0
          await firstValueFrom(this.odooEm.update<StockMoveLine>(existingMoveLine, {
            qty_done: 0
          }))
        }
      }

      else {
        //if move does not exist, we have to create it!
        var newmove = await firstValueFrom(this.odooEm.create<StockMove>(new StockMove(), {
          name: quant.product_id.name,
          product_id: quant.product_id.id,
          product_uom_qty: quant.quantity,
          product_uom: quant.product_uom_id.id,
          location_id: quant.location_id.id,
          location_dest_id: this.production.location_dest_id.id,
          raw_material_production_id: this.production.id
        }))


        var newmoveline = await firstValueFrom(this.odooEm.create<StockMoveLine>(new StockMoveLine(), {
          product_id: quant.product_id.id,
          qty_done: quant.quantity,
          product_uom_id: quant.product_uom_id.id,
          move_id: newmove.id,
          package_id: quant.package_id.id
        }))


        // await firstValueFrom(this.odooEm.update<StockMove>(newmove, {
        //   move_line_ids: [[4, newmoveline.id]]
        // }))

        await firstValueFrom(this.odooEm.update<MrpProduction>(this.production, {
          move_raw_ids: [[4, newmove.id]]
        }))
      }
    }
    await this.refetchProduction()
  }

  isPackageSelected(pkg: StockQuantPackage) {
    //we basically need to find a moveline with the package that has a quantity > 0
    if (!this.production.move_raw_ids.values) return false
    if (!this.production.move_raw_ids.values[0]?.move_line_ids.values) return false
    return this.production.move_raw_ids.values.map(move => move.move_line_ids?.values ? move.move_line_ids?.values : []).flat().some(line => line.package_id.id == pkg.id && line.qty_done > 0)

  }

  getSelectedPackages(): StockQuantPackage[] {
    return this.packages.filter(pkg => this.isPackageSelected(pkg));
  }
  
  getInventoryPackages(): StockQuantPackage[] {
    return this.packages.filter(pkg => !this.isPackageSelected(pkg));
  }
  
  getTotalQuantity(pkg: StockQuantPackage): number {
    if (!pkg.quant_ids || !pkg.quant_ids.values) return 0;
    return pkg.quant_ids.values.reduce((total, quant) => total + (quant.quantity || 0), 0);
  }


  ngOnInit(): void {
  }



}
