import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { firstValueFrom, refCount } from 'rxjs';
import _ from 'lodash';
import { ODOO_IDS } from '../models/deal';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { StockPicking } from '../models/stock-picking';
import { StockMoveLine } from '../models/stock-move-line';
import { StockMove } from '../models/stock-move';
import { StockQuant } from '../models/stock-quant';
import { Partner } from '../models/partner';
import { SaleOrder } from '../models/sale-order.model';
import { SaleOrderLine } from '../models/sale-order-line.model';

@Component({
  selector: 'app-sell-from-list',
  template: `
    <div class="container-fluid p-4">

      <!-- Selected Quants Summary -->
      <div class="row mb-4" *ngIf="quants && quants.length > 0">
        <div class="col-12">
          <div class="card border-info">
            <div class="card-header bg-light">
              <h5 class="mb-0">
                <i class="fas fa-boxes me-2"></i>
                Righe Selezionate ({{ quants.length }})
              </h5>
            </div>
            <div class="card-body">
              <p-table 
                [value]="quantsTableData" 
                [paginator]="false"
                [scrollable]="true"
                scrollHeight="300px"
                styleClass="p-datatable-sm">
                <ng-template pTemplate="header">
                  <tr>
                    <th>Prodotto</th>
                    <th>Quantità</th>
                    <th>Ubicazione</th>
                    <th>Collo</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-item>
                  <tr>
                    <td>{{ item.product_name }}</td>
                    <td>
                      <span >{{ item.quantity | number : '1.0-3':'it-IT' }} {{ item.product_uom_name }} </span>
                      
                    </td>
                    <td>{{ item.location_name }}</td>
                    <td>{{ item.package_name || '-' }}</td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Selection Section -->
      <div class="row mb-4" *ngIf="ShowContactSector && !selectedContact">
        <div class="col-12">
          <div class="card border-warning">
            <div class="card-header bg-light">
              <h5 class="mb-0">
                <i class="fas fa-user me-2"></i>
                Selezione Cliente
              </h5>
            </div>
            <div class="card-body">
              <div class="mb-4">
                <label class="mb-4 font-weight-bold">Seleziona cliente e indirizzo</label>
                <app-contact-picker2 
                  [showAddresses]="true"
                  class="embedded bg-white" 
                  [mode]="'embedded'" 
                  (onSelect)="onContact($event)">
                </app-contact-picker2>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Selected Contact Info and Products Summary -->
      <div class="row mb-4" *ngIf="selectedContact && !sale">
        <div class="col-12">
          <div class="card border-success">
            <div class="card-header bg-light">
              <h5 class="mb-0">
                <i class="fas fa-check-circle me-2"></i>
                Riepilogo Ordine
              </h5>
            </div>
            <div class="card-body">
              <!-- Customer Info -->
              <div class=" mb-1">
                <h6 class="alert-heading">
                  <i class="fas fa-user me-2"></i>
               <strong>Cliente selezionato: {{ selectedContact.name }}</strong>
                </h6>
              </div>

              <!-- Products Summary -->
              <div class="mb-3">
                <div class="table-responsive">
                  <table class="table table-sm table-striped">
                    <thead>
                      <tr>
                        <th>Prodotto</th>
                        <th class="text-end">Quantità Totale</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let product of productsSummary">
                        <td>{{ product.product_name }}</td>
                        <td class="text-end">
                          <span >{{ product.total_qty | number : '1.0-3':'it-IT' }} {{ product.product_uom_name }}</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="d-flex gap-2">
                <button class="btn btn-outline-muted" (click)="resetSelection()">
                  <i class="fas fa-arrow-left me-2"></i>
                  Cambia Cliente
                </button>
                <button class="btn btn-primary text-white" (click)="confirmAndCreateSale()">
                  <i class="fas fa-shopping-cart me-2"></i>
                  Conferma e Crea Ordine
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Processing Status -->
      <div class="row mb-4" *ngIf="isProcessing">
        <div class="col-12">
          <div class="card border-secondary">
            <div class="card-body text-center">
              <div class="spinner-border text-primary me-3" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <h5>{{ processingMessage }}</h5>
              <p class="text-muted mb-0">Attendere prego...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Sale Completed -->
      <div class="row mb-4" *ngIf="isCompleted && sale">
        <div class="col-12">
          <div class="alert alert-success">
            <h6 class="alert-heading">
              <i class="fas fa-check-circle me-2"></i>
              Ordine di Vendita Completato
            </h6>
            <p class="mb-3">
              L'ordine <strong>{{ sale.name }}</strong> è stato creato e processato con successo per il cliente 
              <strong>{{ selectedContact.name }}</strong>.
            </p>
            <div class="d-flex gap-2">
              <button class="btn btn-primary" (click)="openSaleInNewTab()">
                <i class="fas fa-external-link-alt me-2"></i>
                Apri Ordine
              </button>
              <button class="btn btn-outline-primary" (click)="resetForNewSale()">
                <i class="fas fa-plus me-2"></i>
                Nuovo Ordine
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div class="row mb-4" *ngIf="errorMessage">
        <div class="col-12">
          <div class="alert alert-danger">
            <h6 class="alert-heading">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Errore
            </h6>
            <p class="mb-0">{{ errorMessage }}</p>
            <button class="btn btn-outline-danger btn-sm mt-2" (click)="clearError()">
              <i class="fas fa-times me-1"></i>
              Chiudi
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  standalone: false
})
export class SellFromList implements OnInit {

  @Input() selectedQuantIds: number[];
  @Output() onComplete: EventEmitter<any> = new EventEmitter<any>();

  ShowContactSector = false;
  selectedContact: Partner;
  sale: SaleOrder;
  quants: StockQuant[];
  pickToComplete: StockPicking;
  
  // UI State variables
  isProcessing = false;
  isCompleted = false;
  processingMessage = '';
  errorMessage = '';
  quantsTableData: any[] = [];
  productsSummary: any[] = [];
  

  constructor(private odooEm: OdooEntityManager) {
    console.log('SellFromList component initialized');
  }

  async ngOnInit() {
    console.log('SellFromList ngOnInit - selectedQuantIds:', this.selectedQuantIds);
    
    try {
      // Solve selected quants
      this.quants = await firstValueFrom(
        this.odooEm.search<StockQuant>(new StockQuant(), [['id', 'in', this.selectedQuantIds]])
      );
      
      console.log('Loaded quants:', this.quants);
      
      // Prepare table data for display
      this.prepareQuantsTableData();
      
      // Prepare products summary immediately
      this.prepareProductsSummary();
      
      this.ShowContactSector = true;
    } catch (error) {
      console.error('Error loading quants:', error);
      this.errorMessage = 'Errore nel caricamento dei quants selezionati.';
    }
  }

  private prepareQuantsTableData() {
    this.quantsTableData = this.quants.map(quant => ({
      product_name: quant.product_id?.name || 'N/A',
      product_uom_name: quant.product_uom_id?.name || 'N/A',
      quantity: quant.quantity,
      location_name: quant.location_id?.name.replace('LOM/', '') || 'N/A',
      package_name: quant.package_id?.name
    }));
    
    console.log('Prepared quants table data:', this.quantsTableData);
  }

  private prepareProductsSummary() {
    // Find unique product IDs and calculate total quantities
    let productsIds = this.quants
      .map(q => q.product_id.id)
      .filter((value, index, self) => self.indexOf(value) === index);
    
    console.log('Unique product IDs:', productsIds);
    
    // Prepare products summary for display
    this.productsSummary = [];
    
    for (const productId of productsIds) {
      let qty = this.quants
        .filter(q => q.product_id.id == productId)
        .reduce((acc, q) => acc + q.quantity, 0);
      
      let productName = this.quants.find(q => q.product_id.id == productId)?.product_id?.name || 'N/A';
      
      this.productsSummary.push({
        product_id: productId,
        product_name: productName,
        total_qty: qty,
        product_uom_name: this.quants.find(q => q.product_id.id == productId)?.product_uom_id?.name || 'N/A'
      });
    }
    
    console.log('Products summary prepared:', this.productsSummary);
  }

  async onContact(contact: Partner) {
    console.log('Contact selected:', contact);
    this.selectedContact = contact;
  }

  resetSelection() {
    console.log('Resetting contact selection');
    this.selectedContact = null;
  }

  async confirmAndCreateSale() {
    const confirmMessage = `Confermi la creazione dell'ordine di vendita per il cliente ${this.selectedContact.name} con ${this.productsSummary.length} prodotti?`;
    
    if (confirm(confirmMessage)) {
      await this.createSaleOrder();
    }
  }

  resetForNewSale() {
    console.log('Resetting for new sale');
    this.selectedContact = null;
    this.sale = null;
    this.pickToComplete = null;
    this.isCompleted = false;
    this.isProcessing = false;
    this.processingMessage = '';
    this.errorMessage = '';
  }

  openSaleInNewTab() {
    let url = '';
    if (this.sale && this.sale.id) {
   
        // Since we never have opportunity_id, this will always go to immediate-sale
        url = `/immediate-sale/s/${this.sale.id}`;
      }
      console.log('Opening sale in new tab:', url);
      window.open(url, '_blank');
    
    this.onComplete.emit({ sale: this.sale, picking: this.pickToComplete });

  }

  private async createSaleOrder() {
    try {
      this.isProcessing = true;
      this.processingMessage = 'Creazione ordine di vendita...';
      
      console.log('Creating sale order for contact:', this.selectedContact.id);
      
      // Create sale order
      let r = await firstValueFrom(
        this.odooEm.create<SaleOrder>(new SaleOrder(), {
          partner_id: this.selectedContact.id,
        })
      );
      //refetch
      this.sale = (await firstValueFrom(this.odooEm.search<SaleOrder>(new SaleOrder(), [["id", "=", r.id]])))[0];
      
      console.log('Sale order created:', this.sale);
      
      // Create sale order lines
      await this.createSaleOrderLines();
      
      // Confirm the sale order
      this.processingMessage = 'Conferma ordine di vendita...';
      await this.odooEm
        .call(new SaleOrder(), "action_confirm", this.sale.id)
        .toPromise();
      
      console.log('Sale order confirmed');
      
      // Process pickings
      await this.processPickings();
      
      this.isProcessing = false;
      this.isCompleted = true;
      
    } catch (error) {
      console.error('Error creating sale order:', error);
      this.isProcessing = false;
      this.errorMessage = 'Errore nella creazione dell\'ordine di vendita: ' + error.message;
    }
  }

  private async createSaleOrderLines() {
    this.processingMessage = 'Creazione righe ordine...';
    
    // Create a line for each product, with route_id 71 which is vendita diretta
    for (const product of this.productsSummary) {
      console.log(`Creating sale order line for product ${product.product_id} with qty ${product.total_qty}`);
      
      await firstValueFrom(
        this.odooEm.create<SaleOrderLine>(new SaleOrderLine(), {
          order_id: this.sale.id,
          product_id: product.product_id,
          product_uom_qty: product.total_qty,
          route_id: 71
        })
      );
    }
    
    console.log('Sale order lines created');
  }

  private async processPickings() {
    this.processingMessage = 'Elaborazione picking...';
    
    // Fetch the pickings
    let picks = await firstValueFrom(
      this.odooEm.search<StockPicking>(new StockPicking(), [['sale_id', '=', this.sale.id]])
    );
    
    console.log("Picks:", picks);
    
    // The pick to autofill is the one with source location stock 
    this.pickToComplete = picks.find(p => p.location_id.id == ODOO_IDS.stock_location_stock);
    
    if (!this.pickToComplete) {
      throw new Error('Nessun picking trovato con location di origine stock');
    }
    
    console.log('Pick to complete:', this.pickToComplete);
    
    // Resolve moves 
    await firstValueFrom(
      this.odooEm.resolveArray(new StockMove(), picks, "move_ids")
    );
    
    console.log("Moves:", picks[0].move_ids.values);
    
    // Create move lines for each quant
    await this.createMoveLines();
    
    // Validate picking
    this.processingMessage = 'Validazione picking...';
    await this.odooEm.call2(new StockPicking().ODOO_MODEL, "button_validate", [
      [this.pickToComplete.id],
    ]);    
    console.log('Picking process completed successfully');
  }

  private async createMoveLines() {
    this.processingMessage = 'Creazione righe movimento...';
    
    // It's not auto-reserve so we need to create new move lines for each quant
    const createPromises = this.quants.map(async (q) => {
      // First we search the move with its product id
      let move = this.pickToComplete.move_ids.values.find(m => m.product_id.id == q.product_id.id);
      
      if (!move) {
        console.warn(`No move found for product ${q.product_id.id}`);
        return;
      }
      
      console.log(`Creating move line for quant ${q.id}, product ${q.product_id.id}, qty ${q.quantity}`);
      
      // Then we create the move line
      return firstValueFrom(
        this.odooEm.create<StockMoveLine>(new StockMoveLine(), {
          move_id: move.id,
          product_id: q.product_id.id,
          qty_done: q.quantity,
          picking_id: this.pickToComplete.id,
          location_id: q.location_id.id,
          location_dest_id: this.pickToComplete.location_dest_id.id,
          package_id: q.package_id.id
          // We do not put a result package id cause we don't know if it's emptied
        })
      );
    });
    
    await Promise.all(createPromises);
    console.log('All move lines created');
  }

  clearError() {
    this.errorMessage = '';
    console.log('Error message cleared');
  }
}