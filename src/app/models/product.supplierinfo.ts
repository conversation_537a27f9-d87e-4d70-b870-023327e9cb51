import { OdooModel } from './odoo-model.model';
import { OdooSerializableInterface } from '../shared/interfaces/odoo-serializable-interface';
import { OdooRelationship } from './odoo-relationship.model';
import { Product } from './product.model';
import { ProductTemplate } from './product.template.model';
import { Currency } from './mail.message';

export class ProductSupplierinfo extends OdooModel implements OdooSerializableInterface<ProductSupplierinfo> {
  public readonly ODOO_MODEL = 'product.supplierinfo';

  // name: string;
  display_name: string = "";
  sequence:number = 0;
  product_id:OdooRelationship<Product> = new OdooRelationship<Product>(); 
  product_tmpl_id:OdooRelationship<ProductTemplate> = new OdooRelationship<ProductTemplate>();
  product_name:string = "";
  product_code:string =""
  currency_id:OdooRelationship<Currency> = new OdooRelationship<Currency>();
  delay:number = 0;
  product_uom:number = 0;
  price:number = 0;

  
  constructor(id?: number, name?: string, default_code?: number, codice_fornitore?: number, description_sale?: string,
              prezzo_impresa?: string, qty_available?: number,inventory_qty_needed?:number, list_price?: number, uom_id = new OdooRelationship()) {
    super(id);
    
  }

  create(): ProductSupplierinfo {
    return new ProductSupplierinfo();
  }
}
