<div class="vh-100 d-flex flex-column overflow-hidden bg-light">
  <!-- Top navbar -->
  <app-navbar [loading]="loading" backroute=".." *ngIf="contact" class="sticky-top">
    <a href="" class="navbar-brand">{{contact?.name}}</a>
    <div class="d-flex justify-content-end align-items-center w-100 gap-2">
      <!-- Badge should come first in order but stay right-aligned -->
      <span *ngIf="!contact.active" class="badge bg-primary">In archivio</span>

      <!-- Message toggle button - visible on mobile -->
      <div class="dropdown d-md-none" *ngIf="contact?.id">
        <button class="btn btn-link text-white" type="button" (click)="viewMessage = !viewMessage">
          <i class="fa-solid fa-lg fa-comment text-light" [class.text-primary]="viewMessage"></i>
        </button>
      </div>

      <!-- Menu dropdown -->
      <div class="dropdown">
        <button class="btn btn-link text-white" type="button" data-bs-toggle="dropdown">
          <i class="fa fa-bars"></i>
        </button>
        <ul class="dropdown-menu dropdown-menu-end">
          <li *ngIf="contact.active"><a class="dropdown-item" (click)="onDelete()">Download vcard</a></li>
          <li *ngIf="contact.active"><a class="dropdown-item" (click)="onDelete()">Archivia</a></li>
          <li *ngIf="!contact.active"><a class="dropdown-item" (click)="restore()">Ripristina</a></li>
        </ul>
      </div>
    </div>
  </app-navbar>

  <!-- Main content wrapper -->
  <div class="d-flex gap-3 p-3" style="height: calc(100vh - 56px);">

  <!-- Main content -->
  <div class="flex-grow-1 bg-white rounded shadow-sm d-flex flex-column" 
    [class.d-none]="isMobileView && viewMessage">
    
<!-- Fixed header with drive component prominently displayed -->
<div class="px-3 py-2 border-bottom d-flex align-items-center justify-content-between ">
  <h4 class="m-0">Informazioni contatto</h4>
  
  <!-- Drive component with better styling and size -->
  <div class="d-flex align-items-center gap-2">
    <app-contact-drive 
      *ngIf="contact"
      [contact]="contact">
    </app-contact-drive>
  </div>
</div>


       <!-- Fixed height tabs -->
    <div class="border-bottom bg-light">
      <ul class="nav nav-tabs border-0 px-2 pt-2">
          <li class="nav-item">
            <button class="nav-link rounded-top border" [class.active]="activeTab === 'informazioni'"
              (click)="setActiveTab('informazioni')">
              Anagrafica
            </button>
          </li>
          <li class="nav-item d-flex align-items-center">
            <button class="nav-link" [class.active]="activeTab === 'recapiti'" (click)="setActiveTab('recapiti')"
              type="button">
              Recapiti
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="activeTab === 'commesse'" (click)="setActiveTab('commesse')"
              type="button">
              Commesse
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="activeTab === 'vendite'" (click)="setActiveTab('vendite')"
              type="button">
              Vendite su lista
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-link" [class.active]="activeTab === 'acquisti'" (click)="setActiveTab('acquisti')"
              type="button">
              Acquisti
            </button>
          </li>
        </ul>
      </div>

    <!-- Scrollable content - calculate remaining height -->
    <div class="tab-content m-3 overflow-auto" style="height: calc(100% - 116px);">
        <!-- Anagrafica tab -->
        <!-- Anagrafica tab -->
<div class="tab-pane fade" *ngIf="contact" [class.show]="activeTab === 'informazioni'"
[class.active]="activeTab === 'informazioni'">

<!-- Basic Information Section -->
<div class="mb-4">
<h6 class="text-secondary fw-semibold mb-3 border-bottom pb-1 d-flex align-items-center">
 <span>
   <i class="fa fa-info-circle me-2"></i>
   Informazioni di Base
 </span>
 <button class="btn btn-outline-primary btn-sm ms-auto" (click)="editMode = !editMode">
   <i class="fa me-1" [ngClass]="editMode ? 'fa-times' : 'fa-pencil'"></i>
   <span class="d-none d-sm-inline">{{ editMode ? 'Esci' : 'Modifica' }}</span>
 </button>
</h6>

<!-- Name -->
<div class="mb-3">
 <label class="form-label text-muted small fw-semibold mb-1">
   Nome/Ragione Sociale
   <span *ngIf="editMode && !contact.name" class="badge bg-warning text-dark ms-2">
     <i class="fa fa-exclamation-triangle me-1"></i>Richiesto
   </span>
 </label>
 <input *ngIf="editMode" 
        name="name" 
        [(ngModel)]="contact.name" 
        (change)="update('name')"
        class="form-control"
        [class.border-danger]="!contact.name"
        placeholder="Inserisci nome o ragione sociale">
 <div *ngIf="!editMode" class="fw-medium">
   {{contact.name || 'Non specificato'}}
 </div>
</div>

<!-- VAT -->
<div class="mb-3">
 <label class="form-label text-muted small fw-semibold mb-1">Partita IVA</label>
 <div class="input-group" *ngIf="editMode">
   <input class="form-control" 
          name="vat" 
          [(ngModel)]="contact.vat" 
          (change)="update('vat')"
          [class.border-danger]="!contact.vat"
          placeholder="*************">
   <button class="btn btn-outline-success" 
           type="button" 
           *ngIf="contact.vat"
           (click)="onVAT(contact.vat)"
           title="Verifica partita IVA">
     <i class="fa fa-check"></i>
   </button>
   <span *ngIf="!contact.vat" class="input-group-text bg-danger bg-opacity-25">
     <i class="fa fa-exclamation-triangle text-dark"></i>
   </span>
 </div>
 <div *ngIf="!editMode" class="fw-medium">
   {{contact.vat || 'Non specificata'}}
 </div>
</div>

<!-- ARCA Code -->
<div class="mb-3">
 <label class="form-label text-muted small fw-semibold mb-1">Codice ARCA</label>
 <input *ngIf="editMode" 
        name="arca" 
        [(ngModel)]="contact.ga_arca" 
        (change)="update('ga_arca')"
        class="form-control"
        [class.border-danger]="!contact.ga_arca"
        placeholder="Codice sistema ARCA">
 <div *ngIf="!editMode" class="fw-medium">
   {{contact.ga_arca || 'Non specificato'}}
 </div>
</div>

<!-- Partner Categories -->
<div class="mb-0">
 <label class="form-label text-muted small fw-semibold mb-2">Categorie</label>
 <div class="d-flex flex-wrap gap-1 align-items-center" style="min-height: 2rem;">
   <!-- Display existing categories -->
   <span *ngFor="let category of contact.category_id?.values || []"
         class="badge bg-secondary text-white">
     {{category.name}}
     <button *ngIf="editMode" 
             class="btn btn-link btn-sm p-0 text-white ms-1 lh-1" 
             (click)="removeCategory(category)"
             title="Rimuovi categoria">
       <i class="fa fa-times small"></i>
     </button>
   </span>

   <!-- Add category dropdown -->
   <div class="dropdown" *ngIf="editMode">
     <button class="btn btn-sm btn-outline-secondary px-2 py-1" 
             type="button"
             data-bs-toggle="dropdown" 
             aria-expanded="false" 
             title="Aggiungi categoria">
       <i class="fa fa-plus"></i>
     </button>
     <ul class="dropdown-menu" style="max-height: 200px; overflow-y: auto; min-width: 250px;">
       <li class="dropdown-header">Seleziona una categoria</li>
       <li *ngFor="let availableCategory of availableCategories">
         <a class="dropdown-item"
            [class.active]="hasCategory(availableCategory)"
            (click)="addCategory(availableCategory)">
           <i class="fa fa-check me-2" *ngIf="hasCategory(availableCategory)"></i>
           {{availableCategory.name}}
         </a>
       </li>
     </ul>
   </div>


   <!-- Empty state for read mode -->
   <span *ngIf="!editMode && (!contact.category_id?.values || contact.category_id.values.length === 0)" 
         class="text-muted fst-italic small">
     Nessuna categoria assegnata
   </span>
 </div>
</div>
</div>

<!-- Address Section -->
<div class="mb-4">
<h6 class="text-secondary fw-semibold mb-3 border-bottom pb-1">
 <i class="fa fa-map-marker-alt me-2"></i>
 Indirizzo
</h6>

<!-- Street Address -->
<div class="mb-3">
 <label class="form-label text-muted small fw-semibold mb-1">Via/Indirizzo</label>
 <input *ngIf="editMode" 
        #placesRef="ngx-places" 
        ngx-google-places-autocomplete 
        [options]="placesOptions"
        class="form-control" 
        [class.border-danger]="!contact.street"
        [value]="contact.street" 
        (onAddressChange)="handleAddressChange($event)"
        placeholder="Via, Piazza, Corso...">
 <div *ngIf="!editMode" class="fw-medium">
   {{contact.street || 'Non specificato'}}
 </div>
</div>

<!-- City and ZIP Row -->
<div class="row g-2">
 <div class="col-8">
   <div class="mb-3">
     <label class="form-label text-muted small fw-semibold mb-1">Città</label>
     <input *ngIf="editMode" 
            name="city" 
            [(ngModel)]="contact.city" 
            (change)="update('city')"
            class="form-control"
            [class.border-danger]="!contact.city"
            placeholder="Nome città">
     <div *ngIf="!editMode" class="fw-medium">
       {{contact.city || 'Non specificata'}}
     </div>
   </div>
 </div>
 <div class="col-4">
   <div class="mb-3">
     <label class="form-label text-muted small fw-semibold mb-1">CAP</label>
     <input *ngIf="editMode" 
            name="zip" 
            [(ngModel)]="contact.zip" 
            (change)="update('zip')"
            class="form-control"
            [class.border-danger]="!contact.zip"
            placeholder="00000">
     <div *ngIf="!editMode" class="fw-medium">
       {{contact.zip || 'N/A'}}
     </div>
   </div>
 </div>
</div>

<!-- Country -->
<div class="mb-0">
 <label class="form-label text-muted small fw-semibold mb-1">Nazione</label>
 <select *ngIf="editMode" 
         class="form-select" 
         name="country_id" 
         [class.border-danger]="!contact.country_id?.id"
         [ngModel]="contact.country_id?.id"
         (ngModelChange)="updateCountry($event)">
   <option [ngValue]="null">Seleziona una nazione</option>
   <option *ngFor="let country of countries" [value]="country.id">{{country.name}}</option>
 </select>
 <div *ngIf="!editMode" class="fw-medium">
   {{contact.country_id?.name || 'Non specificata'}}
 </div>
</div>
</div>

<!-- Contact Information Section -->
<div class="mb-4">
<h6 class="text-secondary fw-semibold mb-3 border-bottom pb-1">
 <i class="fa fa-address-book me-2"></i>
 Contatti
</h6>

<div class="row g-3">
 <!-- Phone -->
 <div class="col-md-6">
   <div class="mb-3 mb-md-0">
     <label class="form-label text-muted small fw-semibold mb-1">Telefono</label>
     <input *ngIf="editMode" 
            name="phone" 
            [(ngModel)]="contact.phone" 
            (change)="update('phone')"
            class="form-control"
            [class.border-danger]="!contact.phone"
            placeholder="+39 ************">
     <div *ngIf="!editMode" class="fw-medium">
       <a *ngIf="contact.phone" [href]="'tel:' + contact.phone" class="text-decoration-none">
         <i class="fa fa-phone me-2 text-success"></i>{{contact.phone}}
       </a>
       <span *ngIf="!contact.phone" class="text-muted">Non specificato</span>
     </div>
   </div>
 </div>

 <!-- Email -->
 <div class="col-md-6">
   <div class="mb-3 mb-md-0">
     <label class="form-label text-muted small fw-semibold mb-1">Email</label>
     <input *ngIf="editMode" 
            name="email" 
            [(ngModel)]="contact.email" 
            (change)="update('email')"
            type="email"
            class="form-control"
            [class.border-danger]="!contact.email"
            placeholder="<EMAIL>">
     <div *ngIf="!editMode" class="fw-medium">
       <a *ngIf="contact.email" [href]="'mailto:' + contact.email" class="text-decoration-none">
         <i class="fa fa-envelope me-2 text-primary"></i>{{contact.email}}
       </a>
       <span *ngIf="!contact.email" class="text-muted">Non specificata</span>
     </div>
   </div>
 </div>

 <!-- Credit Limit -->
 <div class="col-12">
   <div class="mb-0">
     <label class="form-label text-muted small fw-semibold mb-1">Fido Creditizio</label>
     <div class="fw-medium">
       <span class="me-2">
         <i class="fa"
            [ngClass]="{'fa-check-circle text-success': contact.use_partner_credit_limit, 
                       'fa-times-circle text-danger': !contact.use_partner_credit_limit}"></i>
       </span>
       <span *ngIf="contact.credit_limit">
         {{contact.credit_limit | currency:'EUR':'symbol':'1.2-2'}}
       </span>
       <span *ngIf="!contact.credit_limit" class="text-muted">
         {{contact.use_partner_credit_limit ? 'Attivo (senza limite)' : 'Non attivo'}}
       </span>
     </div>
   </div>
 </div>
</div>
</div>

<!-- Notes Section -->
<div class="mb-0">
<h6 class="text-secondary fw-semibold mb-3 border-bottom pb-1">
 <i class="fa fa-sticky-note me-2"></i>
 Note e Commenti
</h6>

<div class="mb-0">
 <div *ngIf="editMode" 
      #commentEditor 
      class="form-control" 
      [class.border-danger]="!contact.comment"
      [innerHTML]="contact.comment"
      contenteditable="true" 
      (blur)="updateComment(commentEditor.innerHTML)"
      style="min-height: 120px;"
      placeholder="Aggiungi note o commenti...">
 </div>
 <div *ngIf="!editMode" 
      class="border rounded p-3" 
      style="min-height: 120px;"
      [innerHTML]="contact.comment || '<span class=\'text-muted fst-italic\'>Nessuna nota disponibile</span>'">
 </div>
</div>
</div>
</div>

        <!-- Recapiti (Details) tab content -->
        <div class="tab-pane fade" [class.show]="activeTab === 'recapiti'" [class.active]="activeTab === 'recapiti'"
          id="tabcontatti" role="tabpanel">
          <app-contact-picker *ngIf="contact" #picker (loading)="loading = $event" [contact]="contact"
            [emptyForFalse]="true"></app-contact-picker>
        </div>

        <!-- Commesse (Leads) tab content -->
        <div class="tab-pane fade" [class.show]="activeTab === 'commesse'" [class.active]="activeTab === 'commesse'"
          id="tabcommesse" role="tabpanel">
          <!-- Table to display leads -->
          <div class="table-responsive mt-3">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Fascicolo</th>
                  <th>Città</th>
                  <th>Descrizione</th>
                  <th>Stato</th>
                  <th>Valore contratto</th>
                  <th>Responsabile</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let lead of leads" (click)="redirectDeal(lead.id)" style="cursor: pointer;">
                  <td>{{ lead.tracking_code }}</td>
                  <td>{{ lead.city }}</td>
                  <td>{{ lead.name }}</td>
                  <td>{{ lead.stage_id.name }}</td>
                  <td>{{ lead.expected_revenue | currency:'EUR':'symbol':'1.2-2' }}</td>
                  <td>{{ lead.user_id.name }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div *ngIf="!leads || leads.length === 0" class="alert alert-info mt-3">
            Nessuna commessa trovata per questo contatto.
          </div>
        </div>

        <!-- Vendite su lista tab content -->
        <div class="tab-pane fade" [class.show]="activeTab === 'vendite'" [class.active]="activeTab === 'vendite'"
          id="tabvendite" role="tabpanel">
          <!-- Table to display sales -->
          <div class="table-responsive mt-3">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Vendita</th>
                  <th>Descrizione</th>
                  <th>Stato</th>
                  <th>Importo</th>
                  <th>Responsabile</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let sale of sales" (click)="redirectSale(sale.id)" style="cursor: pointer;">
                  <td>{{ sale.name }}</td>
                  <td>{{ sale.ga_title }}</td>
                  <td>{{ sale._delivery_state }}</td>
                  <td>{{ sale.amount_untaxed | currency:'EUR':'symbol':'1.2-2' }}</td>
                  <td>{{ sale.user_id.name }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div *ngIf="!sales || sales.length === 0" class="alert alert-info mt-3">
            Nessuna vendita trovata per questo contatto.
          </div>
        </div>

        <!-- New Acquisti tab content -->
        <div class="tab-pane fade" [class.show]="activeTab === 'acquisti'" [class.active]="activeTab === 'acquisti'"
          id="tabacquisti" role="tabpanel">
          <div class="table-responsive mt-3">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Numero</th>
                  <th>Data</th>
                  <th>Descrizione</th>
                  <th>Stato</th>
                  <th>Importo</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let purchase of purchases" (click)="redirectPurchase(purchase.id)" style="cursor: pointer;">
                  <td>{{ purchase.name }}</td>
                  <td>{{ purchase.date | date:'shortDate' }}</td>
                  <td>{{ purchase.description }}</td>
                  <td>{{ purchase.state }}</td>
                  <td>{{ purchase.amount | currency:'EUR':'symbol':'1.2-2' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div *ngIf="!purchases || purchases.length === 0" class="alert alert-info mt-3">
            Nessun acquisto trovato per questo contatto.
          </div>
        </div>
      </div>
    </div>
    <!-- Message sidebar -->
    <div [class.d-none]="!viewMessage" [ngClass]="isMobileView ? 'message-sidebar-mobile' : 'message-sidebar'"
      class="bg-white rounded shadow-sm overflow-hidden" [style.width]="isMobileView ? '100%' : '40%'">
      <!-- Changed this line -->
      <!-- Fixed header with flexbox to push drive component to the right -->
<h4 class="p-3 border-bottom m-0 d-flex align-items-center">
  <span>Note e attività</span>

</h4>
      <div style="height: calc(100% - 60px);">
        <app-message-widget [id]="contact.id" [action]="844"></app-message-widget>
      </div>
    </div>
  </div>
</div>