import { Component } from '@angular/core';
import { catchError, from, map, of } from 'rxjs';
import { PriceList } from 'src/app/models/price.list.model';
import { Product } from 'src/app/models/product.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { OdoorpcService } from 'src/app/shared/services/odoorpc.service';

@Component({
    selector: 'app-catalog',
    templateUrl: './catalog.component.html',
    styleUrls: ['./search.component.scss'],
    standalone: false
})
export class CatalogueSearchComponent {

  loading:boolean = false
  activeProduct:Product
  
  
  constructor(private odooEm:OdooEntityManager,private odoorpcService: OdoorpcService) {
    // this.odooEM.call()
    
  }
  
  select(v) {
    console.log("VALLL",v )
    
    
  }  
  
  async loadPrice(res:Product[]) {

    var kwargs = kwargs || {};
    kwargs.context = [];

    var params = {
      model: new PriceList().ODOO_MODEL,
      method: "get_products_price",
      args: [[2],res.map(x => x.id)],
      kwargs: {
        context: null
      },
    };

    var r: any = await this.odoorpcService.sendRequest('/api/web/dataset/call_kw/' + params.model + "/" + params.method, params)

    return r.result ? r.result : []
  }


    // return from(this.odoorpcService.call(
    //   "get_product_price",
    //   new PriceList().ODOO_MODEL,
    //   [[],1],
    //   null
    // )).pipe(
    //   catchError(() => of(null)),
    //   map(res => {
    //     if (res && res.message) {
    //       return res;
    //     }
    //     // if (res && res.id)
    //     //   return serializedObj.deserialize(res);
    //     return res
    //   })
    // );


    // await this.odooEm.call4(new PriceList(), "get_product_price",[2,1844])
    
  // }

}
