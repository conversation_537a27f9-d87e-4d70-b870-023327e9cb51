<nav class="navbar text-white navbar-dark fixed-top px-1" >
    <button class="btn btn-link text-white me-auto" routerLink=".." >
        <i class="fas fa-chevron-left"></i>      
    </button>
    <h1 class="ms-auto me-auto">{{config.titleNavbar}}</h1>
    <div class="ms-auto"></div>

    <div  class="input-group mx-2 my-1">
        <input type="text" class="form-control" id="search-input" placeholder="Cerca">
        <div class="input-group-append"  [hidden]="!config.filters"> 
            <button  [hidden]="config.disableNoFilter" type="button" class="btn btn-dark" (click)="setCriteria(null)">
                x
            </button>  
          <button *ngFor="let c of config.filters" [ngClass]="{'active': c.criteria == filterCriteria}" type="button" class="btn btn-dark" (click)="setCriteria(c.criteria)">
              {{c.label}}
          </button>  
      </div>
        <!-- <div class="input-group-append" *ngIf="config.titleNavbar == 'Ordini di vendita'" > 
            <button type="button" [ngClass]="{'active': stateQuery=='sale'}" (click)="stateQuery = 'sale'; search() " class="btn btn-dark" >
                Confermato
            </button>
            <button type="button" [ngClass]="{'active': stateQuery=='in_progettazione'}" (click)="stateQuery = 'in_progettazione'; search() " class="btn btn-dark" >
                Progettazione
            </button>
            <button type="button" [ngClass]="{'active': stateQuery=='in_produzione'}" (click)="stateQuery = 'in_produzione';  search() "  class="btn btn-dark" >
                Produzione
            </button>
        </div> -->
    </div>
    
    <!-- <div class="input-group mx-2 my-1">
      <input type="text" autofocus class="form-control" id="inlineFormInputGroup" placeholder="Cerca">
    </div> -->
    <bar-loader [loading]="loading"></bar-loader>
  </nav>
  <router-outlet></router-outlet>
  <div [hidden]="loading" class="list-group" style="margin-top:108px;">
    <a (click)="redirectOrder(c)"  class="d-flex align-items-center list-group-item list-group-item-action rounded-0" *ngFor="let c of items"> 
      <div class="mb-1 lead me-auto"  data-test-id="orderTitle">
          {{c.name}}  
          <br>
          <small><small class="text-muted" *ngIf="c.partner_id">{{c.partner_id[1]}}</small></small>
        </div>
        <!-- <div *ngIf="config.icon" class="" ></div> -->
        <div *ngIf="config.state" class="badge badge-muted" ><i class="{{config.stateMap.get(c[config.state])[1]}}"></i> {{config.stateMap.get(c[config.state])[0]}}</div>

        <div *ngIf="config.picking && c.state != 'draft' && c.picking_state == 'spedito'" class="badge badge-success ms-2">
          Spedito
        </div>
        
        <div *ngIf="config.picking && c.state != 'draft' && c.picking_state == 'in_spedizione'" class="badge badge-success ms-2">
          In spedizione
        </div>
    </a>
  </div>
