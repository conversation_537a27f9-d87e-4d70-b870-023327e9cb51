<!-- Filter Controls with Legend -->
<div class="d-flex align-items-center flex-wrap gap-3 m-3">
  <!-- Sector Filter -->
  <div class="d-flex align-items-center">
    <label for="sectorFilter" class="form-label mb-0 me-2 fw-semibold">Settore:</label>
    <select id="sectorFilter" class="form-select form-select-sm" style="min-width: 150px" [(ngModel)]="selectedSector"
      [disabled]="selectedUser !== null" (ngModelChange)="onSectorChange()">
      <option [ngValue]="null">Tutti</option>
      <option *ngFor="let s of sectorOptions" [ngValue]="s">{{ s }}</option>
    </select>
  </div>

  <!-- Responsible Filter -->
  <div class="d-flex align-items-center">
    <label for="userFilter" class="form-label mb-0 me-2 fw-semibold">Responsabile:</label>
    <select id="userFilter" class="form-select form-select-sm" style="min-width: 150px" [(ngModel)]="selectedUser"
      (ngModelChange)="onUserChange()">
      <option [ngValue]="null">Tutti</option>
      <option *ngFor="let u of userOptions" [ngValue]="u">{{ u }}</option>
    </select>
  </div>

  <!-- Reset Filters Button -->
  <button class="btn btn-sm btn-outline-secondary d-flex align-items-center" (click)="resetFilters()"
    [disabled]="!selectedSector && !selectedUser">
    <i class="fa fa-times-circle me-2"></i>
    Reset filtri
  </button>

  <!-- Visual separator -->
  <div class="vr mx-1" style="height: 28px;"></div>

<!-- Legend Button -->
<div>
  <button class="btn btn-sm btn-outline-secondary d-flex align-items-center" type="button" id="legendDropdown"
    data-bs-toggle="dropdown" aria-expanded="false">
    <i class="fa-solid fa-circle-info me-2"></i> Legenda
  </button>
  <div class="dropdown-menu p-3 shadow-sm" style="min-width: 400px; max-width: 600px;">
    <h6 class="dropdown-header border-bottom pb-2 mb-2 text-primary">Navigazione e Filtri</h6>
    <div class="mb-2 d-flex align-items-center">
      <i class="fa fa-caret-right text-primary me-2"></i>
      <span class="fw-semibold">Espandi/Comprimi:</span>
      <span class="ms-1">Clicca sui triangoli per espandere o comprimere le sezioni della tabella</span>
    </div>
    <div class="mb-2 d-flex align-items-center">
      <i class="fa fa-filter text-primary me-2"></i>
      <span class="fw-semibold">Filtri:</span>
      <span class="ms-1">Usa i menu a tendina per filtrare per Settore o Responsabile</span>
    </div>
    <div class="mb-3 ps-4">
      <ul class="list-unstyled small  mb-0">
        <li>• Quando filtri per Settore, vedrai direttamente gli utenti sotto ogni mese</li>
        <li>• Quando filtri per Responsabile, vedrai solo i mesi con i loro totali</li>
        <li>• I filtri attivi vengono mostrati in un banner azzurro sopra la tabella</li>
      </ul>
    </div>

    <h6 class="dropdown-header border-bottom pb-2 mb-2 mt-3 text-primary">Indicatori di Trend</h6>
    <div class="mb-2 fw-bold d-flex align-items-center text-secondary">Il trend è calcolato sui dati degli ultimi 3 mesi confrontati con lo stesso periodo dell'anno precedente:</div>
    <div class="mb-2 d-flex align-items-center">
      <i class="fa fa-arrow-up text-success me-2"></i>
      <span class="fw-semibold">Trend positivo:</span>
      <span class="ms-1">Aumento rispetto allo stesso periodo dell'anno scorso (>5%)</span>
    </div>
    <div class="mb-2 d-flex align-items-center">
      <i class="fa fa-equals text-warning me-2"></i>
      <span class="fw-semibold">Trend stabile:</span>
      <span class="ms-1">Variazione minima rispetto all'anno scorso (±5%)</span>
    </div>
    <div class="mb-2 d-flex align-items-center">
      <i class="fa fa-arrow-down text-danger me-2"></i>
      <span class="fw-semibold">Trend negativo:</span>
      <span class="ms-1">Diminuzione rispetto allo stesso periodo dell'anno scorso (>5%)</span>
    </div>
    <div class="mb-2 d-flex align-items-center">
      <i class="fa fa-circle-question text-muted me-2"></i>
      <span class="fw-semibold">Trend non definito:</span>
      <span class="ms-1">Non ci sono dati per lo stesso periodo dell'anno precedente</span>
    </div>
    <div class="mb-3 ps-4">
      <ul class="list-unstyled small mb-0">
        <li>• Clicca sull'icona del trend per visualizzare i dettagli del confronto</li>
        <li>• Nel popup vedrai i valori mensili di entrambi i periodi e le differenze</li>
      </ul>
    </div>

    <h6 class="dropdown-header border-bottom pb-2 mb-2 mt-3 text-primary">Percentuali di conferme</h6>
    <div class="mb-2 d-flex align-items-center">
      <span class="text-success fw-bold me-2">≥35%</span>
      <span class="">Ottimo tasso di conversione (maggiore della media)</span>
    </div>
    <div class="mb-2 d-flex align-items-center">
      <span class="text-warning fw-bold me-2">≥25%</span>
      <span class="">Buon tasso di conversione (nella media)</span>
    </div>
    <div class="mb-2 d-flex align-items-center">
      <span class="text-danger fw-bold me-2">≤25%</span>
      <span class="">Tasso di conversione insufficiente (sotto la media)</span>
    </div>
  </div>
</div>
</div>


<!-- Information about current filters -->
<div *ngIf="selectedSector || selectedUser" class="alert alert-info py-2 mb-3">
  <div class="d-flex align-items-center">
    <i class="fa fa-filter me-2"></i>
    <strong>Filtri attivi:</strong>
    <span *ngIf="selectedSector" class="ms-2">
      Settore: <span class="badge bg-primary ms-1">{{ selectedSector }}</span>
    </span>
    <span *ngIf="selectedUser" class="ms-2">
      Responsabile: <span class="badge bg-primary ms-1">{{ selectedUser }}</span>
    </span>
  </div>
</div>

<!-- TreeTable with Trend Indicators - 2 Columns Layout -->
<p-treeTable [value]="nodes" [loading]="loading" showGridlines>
  <ng-template pTemplate="header">
    <tr class="bg-light border-bottom">
      <!-- Main column -->
      <th class="fw-semibold ps-3 py-2" style="min-width: 250px; width: 40%">Mese / Area / Responsabile</th>
      
      <!-- First section: Invii + Di cui confermati -->
      <th class="fw-semibold text-center py-2 border-end" style="min-width: 250px; width: 30%">
        Offerte inviate - tasso di trasformazione
      </th>
      
      <!-- Second section: Conferme ricevute -->
      <th class="fw-semibold text-center py-2 border-start" style="min-width: 250px; width: 30%">
        Conferme ricevute - valore contratti
      </th>
    </tr>
  </ng-template>

  <!-- Template for all rows using direct trend data from nodes -->
  <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
    <tr [ngClass]="{
      'border-top': rowNode.level === 0,
      'bg-light': rowNode.level === 0, 
      'bg-light-subtle': rowNode.level === 1,
      'text-muted': rowNode.level === 0 && rowData.sentCount === 0 && rowData.approvedCount === 0
    }">
      <!-- Column 1: Label with color coding -->
      <td class="ps-3">
        <span class="me-2"
          [style.visibility]="rowNode.node.children && rowNode.node.children.length ? 'visible' : 'hidden'">
          <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
        </span>

        <!-- Month row (level 0) -->
        <span *ngIf="rowNode.level === 0" class="fw-bold">
          {{ rowData.label }}
        </span>

        <!-- Area row (level 1) - Color coded by sector -->
        <span *ngIf="rowNode.level === 1" [ngClass]="getAreaTextClass(rowData.label)" class="fw-semibold">
          {{ rowData.label }}
        </span>

        <!-- User row (level 2) -->
        <span *ngIf="rowNode.level === 2" class="fst-italic">
          {{ rowData.label }}
        </span>
      </td>

      <!-- Column 2: Invii with trend icon + Di cui confermati -->
      <td class="text-center border-end">
        <div class="d-flex justify-content-between align-items-center px-3">
          <div>
            <span class="d-block">{{ rowData.sentCount || 0 }}</span>
            <span *ngIf="rowData.sentCount > 0" class="d-block small" [ngClass]="getConfirmationPercentageClass(rowData)">
              <span class="fw-semibold">{{ rowData.sentConfirmedCount }}</span> conf.
              <span class="badge rounded-pill" [ngClass]="{
                'bg-success': getConfirmedPercentage(rowData) >= 35,
                'bg-warning': getConfirmedPercentage(rowData) >= 25 && getConfirmedPercentage(rowData) < 35,
                'bg-danger': getConfirmedPercentage(rowData) < 25
              }">{{ getConfirmedPercentage(rowData) | number:'1.0-0' }}%</span>
            </span>
          </div>
          <div class="d-flex align-items-center">
            <!-- Template per l'icona del trend - Colonna Invii -->
            <div class="dropdown d-inline-block me-1">
              <button class="btn btn-sm btn-link p-0 text-decoration-none" 
                      type="button" 
                      id="sentTrendDropdown{{rowNode.level}}-{{rowNode.index}}" 
                      data-bs-toggle="dropdown"
                      data-bs-auto-close="outside"
                      aria-expanded="false">
                <i class="fa-solid" [ngClass]="getTrendIconClass(rowData.sentTrend.direction, rowData.sentTrend.noHistoricalData)"></i>
              </button>
              <div class="dropdown-menu p-3 shadow dropdown-menu-lg-end" style="min-width: 450px;">
                <h6 class="dropdown-header border-bottom pb-2 mb-3 text-primary fw-bold">
                  Trend Invii – {{ rowData.label }}
                </h6>
                
                <div *ngIf="rowData.sentTrend.noHistoricalData" class="alert alert-secondary">
                  <i class="fa-solid fa-circle-info me-2"></i>
                  Non sono disponibili dati per lo stesso periodo dell'anno precedente.
                  <br>Vengono mostrati solo i dati del periodo corrente.
                </div>
                
                <div class="table-responsive">
                  <table class="table table-sm table-hover mb-3 w-100">
                    <thead class="table-light">
                      <tr>
                        <th class="text-nowrap">Mese</th>
                        <th class="text-end text-nowrap">Precedente</th>
                        <th class="text-end text-nowrap">Corrente</th>
                        <th class="text-end text-nowrap">Diff.</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let cur of rowData.sentTrend.currentBreakdown; let i = index" 
                          [ngClass]="{'table-primary bg-opacity-50': i === 0}">
                        <td class="py-2 fw-medium text-nowrap">{{ cur.month }}</td>
                        <td class="text-end py-2 text-nowrap">
                          {{ rowData.sentTrend.previousBreakdown[i]?.value | number:'1.0-0' }} €
                        </td>
                        <td class="text-end py-2 fw-medium text-nowrap">
                          {{ cur.value | number:'1.0-0' }} €
                        </td>
                        <td class="text-end py-2 text-nowrap" [ngClass]="{
                          'text-success fw-medium': (cur.value - (rowData.sentTrend.previousBreakdown[i]?.value||0)) > 0,
                          'text-danger fw-medium': (cur.value - (rowData.sentTrend.previousBreakdown[i]?.value||0)) < 0
                        }">
                          <span *ngIf="(cur.value - (rowData.sentTrend.previousBreakdown[i]?.value||0)) > 0">+</span>
                          {{ (cur.value - (rowData.sentTrend.previousBreakdown[i]?.value||0)) | number:'1.0-0' }} €
                        </td>
                      </tr>
                      <!-- Totale row -->
                      <tr class="fw-bold table-secondary border-top">
                        <td class="py-2 text-nowrap">Totale</td>
                        <td class="text-end py-2 text-nowrap">{{ rowData.sentTrend.previousPeriodValue | number:'1.0-0' }} €</td>
                        <td class="text-end py-2 text-nowrap">{{ rowData.sentTrend.currentPeriodValue  | number:'1.0-0' }} €</td>
                        <td class="text-end py-2 text-nowrap" [ngClass]="{
                          'text-success': rowData.sentTrend.difference > 0,
                          'text-danger': rowData.sentTrend.difference < 0
                        }">
                          <span *ngIf="rowData.sentTrend.difference > 0">+</span>
                          {{ rowData.sentTrend.difference | number:'1.0-0' }} €
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <div [ngClass]="getTrendTextClass(rowData.sentTrend.direction, rowData.sentTrend.noHistoricalData)" 
                     class="text-center fw-bold py-2 border-top pt-3">
                  <i class="fa-solid" [ngClass]="getTrendIconClass(rowData.sentTrend.direction, rowData.sentTrend.noHistoricalData)"></i>
                  {{ getTrendText(rowData.sentTrend.direction, rowData.sentTrend.percentage, rowData.sentTrend.noHistoricalData) }}
                </div>
              </div>
            </div>
            <div>
              <span class="text-nowrap fw-semibold">{{ rowData.sentValue ? (rowData.sentValue | number:'1.0-0') + ' €' : '0 €' }}</span>
              <span *ngIf="rowData.sentConfirmedValue" class="d-block small text-muted text-end">
                {{ rowData.sentConfirmedValue | number:'1.0-0' }} € conf.
              </span>
            </div>
          </div>
        </div>
      </td>

      <!-- Column 3: Conferme ricevute with trend icon -->
      <td class="text-center border-start">
        <div class="d-flex justify-content-between align-items-center px-3">
          <span>{{ rowData.approvedCount || 0 }}</span>
          <div class="d-flex align-items-center">
            <!-- Template per l'icona del trend - Colonna Conferme ricevute -->
            <div class="dropdown d-inline-block me-1">
              <button class="btn btn-sm btn-link p-0 text-decoration-none" 
                      type="button" 
                      id="approvedTrendDropdown{{rowNode.level}}-{{rowNode.index}}" 
                      data-bs-toggle="dropdown"
                      data-bs-auto-close="outside"
                      aria-expanded="false">
                <i class="fa-solid" [ngClass]="getTrendIconClass(rowData.approvedTrend.direction, rowData.approvedTrend.noHistoricalData)"></i>
              </button>
              <div class="dropdown-menu p-3 shadow dropdown-menu-lg-end" style="min-width: 450px;">
                <h6 class="dropdown-header border-bottom pb-2 mb-3 text-primary fw-bold">
                  Trend Conferme Ricevute – {{ rowData.label }}
                </h6>
                
                <div *ngIf="rowData.approvedTrend.noHistoricalData" class="alert alert-secondary">
                  <i class="fa-solid fa-circle-info me-2"></i>
                  Non sono disponibili dati per lo stesso periodo dell'anno precedente.
                  <br>Vengono mostrati solo i dati del periodo corrente.
                </div>
                
                <div class="table-responsive">
                  <table class="table table-sm table-hover mb-3 w-100">
                    <thead class="table-light">
                      <tr>
                        <th class="text-nowrap">Mese</th>
                        <th class="text-end text-nowrap">Precedente</th>
                        <th class="text-end text-nowrap">Corrente</th>
                        <th class="text-end text-nowrap">Diff.</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let cur of rowData.approvedTrend.currentBreakdown; let i = index" 
                          [ngClass]="{'table-primary bg-opacity-50': i === 0}">
                        <td class="py-2 fw-medium text-nowrap">{{ cur.month }}</td>
                        <td class="text-end py-2 text-nowrap">
                          {{ rowData.approvedTrend.previousBreakdown[i]?.value | number:'1.0-0' }} €
                        </td>
                        <td class="text-end py-2 fw-medium text-nowrap">
                          {{ cur.value | number:'1.0-0' }} €
                        </td>
                        <td class="text-end py-2 text-nowrap" [ngClass]="{
                          'text-success fw-medium': (cur.value - (rowData.approvedTrend.previousBreakdown[i]?.value||0)) > 0,
                          'text-danger fw-medium': (cur.value - (rowData.approvedTrend.previousBreakdown[i]?.value||0)) < 0
                        }">
                          <span *ngIf="(cur.value - (rowData.approvedTrend.previousBreakdown[i]?.value||0)) > 0">+</span>
                          {{ (cur.value - (rowData.approvedTrend.previousBreakdown[i]?.value||0)) | number:'1.0-0' }} €
                        </td>
                      </tr>
                      <!-- Totale row -->
                      <tr class="fw-bold table-secondary border-top">
                        <td class="py-2 text-nowrap">Totale</td>
                        <td class="text-end py-2 text-nowrap">{{ rowData.approvedTrend.previousPeriodValue | number:'1.0-0' }} €</td>
                        <td class="text-end py-2 text-nowrap">{{ rowData.approvedTrend.currentPeriodValue  | number:'1.0-0' }} €</td>
                        <td class="text-end py-2 text-nowrap" [ngClass]="{
                          'text-success': rowData.approvedTrend.difference > 0,
                          'text-danger': rowData.approvedTrend.difference < 0
                        }">
                          <span *ngIf="rowData.approvedTrend.difference > 0">+</span>
                          {{ rowData.approvedTrend.difference | number:'1.0-0' }} €
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <div [ngClass]="getTrendTextClass(rowData.approvedTrend.direction, rowData.approvedTrend.noHistoricalData)" 
                     class="text-center fw-bold py-2 border-top pt-3">
                  <i class="fa-solid" [ngClass]="getTrendIconClass(rowData.approvedTrend.direction, rowData.approvedTrend.noHistoricalData)"></i>
                  {{ getTrendText(rowData.approvedTrend.direction, rowData.approvedTrend.percentage, rowData.approvedTrend.noHistoricalData) }}
                </div>
              </div>
            </div>
            <span class="text-nowrap fw-semibold">{{ rowData.approvedValue ? (rowData.approvedValue | number:'1.0-0') + ' €' : '0 €' }}</span>
          </div>
        </div>
      </td>
    </tr>
  </ng-template>

  <!-- Add a footer to show totals -->
  <ng-template pTemplate="footer">
    <tr class="bg-primary text-white border-top">
      <td class="fw-bold ps-3">Totali</td>
      
      <!-- Invii totals with confirmed info -->
      <td class="text-center fw-bold border-end">
        <div class="d-flex justify-content-between px-3">
          <div>
            <span>{{ getTotalSentCount() }}</span>
            <span class="ms-2 small">({{ getTotalSentConfirmedCount() }} conf. - {{ getOverallConfirmedPercentage() | number:'1.0-0' }}%)</span>
          </div>
          <div>
            <span>{{ getTotalSentValue() | number:'1.0-0' }} €</span>
            <span class="ms-1 small">({{ getTotalSentConfirmedValue() | number:'1.0-0' }} € conf.)</span>
          </div>
        </div>
      </td>
      
      <!-- Conferme ricevute totals -->
      <td class="text-center fw-bold border-start">
        <div class="d-flex justify-content-between px-3">
          <span>{{ getTotalApprovedCount() }}</span>
          <span>{{ getTotalApprovedValue() | number:'1.0-0' }} €</span>
        </div>
      </td>
    </tr>
  </ng-template>

  <!-- Add empty data template -->
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="3" class="text-center py-4">
        <div class="d-flex flex-column align-items-center">
          <i class="fa fa-search text-muted mb-3" style="font-size: 2rem;"></i>
          <span class="text-muted">Nessun dato disponibile</span>
        </div>
      </td>
    </tr>
  </ng-template>
</p-treeTable>