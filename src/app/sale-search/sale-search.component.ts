import { ChangeDetectorRef, Component, OnInit, HostListener  } from '@angular/core';
import { SaleOrderFlash} from '../models/sale-order.model';
import { BehaviorSubject, debounceTime, firstValueFrom, switchMap, of, Observable } from 'rxjs';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { Contact } from '../models/contact.model';
import { AREAS_CFG, ODOO_IDS } from '../models/deal';
import { DriveFolder } from '../models/drive.folder';
import { TrelloCardEntry2 } from '../models/trello-card';
import { MailActivity } from '../models/mail.message';
import { ActivatedRoute, Router } from '@angular/router';
import { Partner } from '../models/partner';
import { StockPicking } from '../models/stock-picking';
import { StockMove } from '../models/stock-move';
import { Lead } from '../models/crm.lead.model';

// Define an interface for sale filters
interface SaleFilters {
  salesStates: string[];
  deliveryStatuses: string[];
  opportunity: boolean;
  noOpportunity: boolean;
  user?: Partner
  orderStartDate: string;
  orderEndDate: string;
  sortField: string;
  sortAscending: boolean;
  justRecents: boolean;
  searchText: string;
}

interface LoadResult {
  sales: SaleOrderFlash[];
  users: string[];
}

@Component({
    selector: 'app-sale-search',
    templateUrl: './sale-search.component.html',
    styleUrls: ['./sale-search.component.scss'],
    standalone: false
})

export class SaleSearchComponent implements OnInit {
  sales: SaleOrderFlash[];
  loading: boolean;
  searchInput: BehaviorSubject<string> = new BehaviorSubject("");
  users = []; // Array to hold HR employees
  selectedUser: Partner | null = null;
  
  // Add a counter to track request sequences
  private loadRequestCounter = 0;
  
  // Multi-selection filter properties
  selectedSalesStates: string[] = [ 'sale', 'draft' ];
  selectedDeliveryStatuses: string[] = ['pending', 'partial', 'started'];
  
  // Filter properties
  filterOpportunity: boolean = false;
  filterNoOpportunity: boolean = true;
  sortField: string = 'date_order'; // Default sort field
  sortAscending: boolean = false; // Default sort direction
  justRecents: boolean = false;
  isMyDealsActive: boolean = false;
  currentUserId: number = null;
  mySalesCheck: boolean = false;
  followers: Partner[] = [];

  // Date filters
  orderStartDate: string = null; // Start date for order filtering
  orderEndDate: string = null; // End date for order filtering
  
  // Legacy filter properties (kept for compatibility)
  filterToShip: boolean = true;
  filterShipped: boolean = false;
  filterList: boolean = true;
  newSale: SaleOrderFlash = new SaleOrderFlash();
  
  cfg: { name: string; src: string; dst: string; preventivo_trello_board: string; preventivo_trello_list: string; project_src: string; project_trello: string; project_trello_board: string; produzione_src: string; produzione_dst: string; produzione_trello: string; produzione_trello_board: string; produzione_link_name: string; pos_src: string; pos_dst: string; pos_trello_board: string; pos_trello_list: string; pos_template_card: string; pos_link_name: string; purchase_drive_src: string; purchase_drive_dst: string; purchase_link_name: string; production_product_id: number; } | { name: string; src: string; dst: string; preventivo_trello_board: string; preventivo_trello_list: string; project_src: string; project_trello: string; project_trello_board: string; produzione_src: string; produzione_dst: string; produzione_trello: string; produzione_trello_board: string; produzione_link_name: string; pos_src: string; pos_dst: string; pos_trello_board: string; pos_trello_list: string; pos_template_card: string; pos_link_name: string; purchase_drive_src: string; purchase_drive_dst: string; purchase_link_name: string; production_product_id?: undefined; } | { name: string; src: string; dst: string; preventivo_trello_list: string; preventivo_trello_board: string; pos_src: string; pos_dst: string; pos_trello_board: string; pos_trello_list: string; pos_template_card: string; pos_link_name: string; project_src?: undefined; project_trello?: undefined; project_trello_board?: undefined; produzione_src?: undefined; produzione_dst?: undefined; produzione_trello?: undefined; produzione_trello_board?: undefined; produzione_link_name?: undefined; purchase_drive_src?: undefined; purchase_drive_dst?: undefined; purchase_link_name?: undefined; production_product_id?: undefined; };
  isMobileView: boolean = false;
  actionsLoaded: boolean = false;
  
  constructor(
    private odooEm: OdooEntityManager,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  // Listen to window resize events
  @HostListener('window:resize', ['$event'])
  onResize() {
    this.checkScreenSize();
  }

  private checkScreenSize() {
    this.isMobileView = window.innerWidth < 768; // Bootstrap's md breakpoint
    this.cdr.detectChanges(); // Ensure view updates
    console.log('isMobileView', this.isMobileView);
  }

  async ngOnInit(): Promise<void> {
    // Check screen size
    this.checkScreenSize()
    this.cfg = AREAS_CFG.filter(a => a.name == 'Tetti')[0];
    
     // Search for partners with @galimberti.eu emails
     const partners = await firstValueFrom(
      this.odooEm.search<Partner>(new Partner(), [
        ['email', 'ilike', '@galimberti.eu']
      ])
    );
    this.followers = partners;
    console.log('followers:', this.followers);

    // Get the current user's name
      const result: any = await this.odooEm.odoorpcService.getSessionInfo();
      console.log('session info:', result);
      this.currentUserId = result.result.partner_id;

    // Load the data initially
    await this.load();
    
    // SOLUTION: Using switchMap to cancel previous requests and keep only the latest response
    this.searchInput.pipe(
      debounceTime(400),
      switchMap(() => this.loadAsObservable())
    ).subscribe({
      next: (result) => {
        // Results are automatically the latest due to switchMap
        this.handleLoadResults(result);
      },
      error: (error) => {
        console.error('Error loading sales:', error);
        this.loading = false;
        this.cdr.detectChanges();
      }
    });
    
    // Listen to route query params for search
    this.route.queryParams.subscribe((params) => {
      if (params.search) {
        this.searchInput.next(params.search);
      }
    });
  }

  // Convert load method to return Observable for use with switchMap
  private loadAsObservable(): Observable<LoadResult> {
    this.loading = true;
    
    return new Observable(observer => {
      this.performLoad().then(result => {
        observer.next(result);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  // Handle the results from the load operation
  private handleLoadResults(result: LoadResult) {
    this.sales = result.sales;
    this.users = result.users;
    this.loading = false;
    this.cdr.detectChanges();
    console.log('Sales loaded:', this.sales);
  }

  // Alternative solution using request counter (if you prefer this approach)
  private async loadWithCounter() {
    const currentRequestId = ++this.loadRequestCounter;
    this.loading = true;
    
    try {
      const result = await this.performLoad();
      
      // Only update UI if this is still the latest request
      if (currentRequestId === this.loadRequestCounter) {
        this.handleLoadResults(result);
      } else {
        console.log(`Discarding stale response for request ${currentRequestId}, current is ${this.loadRequestCounter}`);
      }
    } catch (error) {
      // Only show error if this is still the latest request
      if (currentRequestId === this.loadRequestCounter) {
        console.error('Error loading sales:', error);
        this.loading = false;
        this.cdr.detectChanges();
      }
    }
  }

  // Core load logic extracted to a separate method
  private async performLoad(): Promise<LoadResult> {
    let conditions = [];
    
    // State and delivery conditions
    let filterConditions = [];
    
    // Add non-sale states directly
    const nonSaleStates = this.selectedSalesStates.filter(s => s !== 'sale');
    nonSaleStates.forEach(state => {
      filterConditions.push(['state', '=', state]);
    });
    
    // Add delivery statuses directly (these implicitly mean state = sale)
    this.selectedDeliveryStatuses.forEach(status => {
      filterConditions.push(['delivery_status', '=', status]);
    });
    
    // Combine with OR operators
    if (filterConditions.length > 0) {
      // Add (n-1) OR operators for n conditions
      for (let i = 1; i < filterConditions.length; i++) {
        conditions.push('|');
      }
      
      // Add all filter conditions
      filterConditions.forEach(cond => {
        conditions.push(cond);
      });
    }
    
    // Add opportunity filter
    if (this.filterOpportunity && !this.filterNoOpportunity) {
      conditions.push(['opportunity_id', '!=', false]);
    } else if (!this.filterOpportunity && this.filterNoOpportunity) {
      conditions.push(['opportunity_id', '=', false]);
    } else if (!this.filterOpportunity && !this.filterNoOpportunity) {
      conditions.push(['id', '=', 0]);
    }
    
    // Add user filter
    if (this.selectedUser) {
      conditions.push(['user_id.name', '=', this.selectedUser.name]);
    }
    
    // Add search text filter
    if (this.searchInput.value) {
      const searchValue = this.searchInput.value;
      const saleOrderRegex = /^[vVfF]?\d{4,}$/;
      
      if (saleOrderRegex.test(searchValue)) {
        const numberPart = searchValue.replace(/^[vVfF]/, '');
        conditions.push(['name', 'ilike', numberPart]);
      } else {
        conditions.push('|', '|');
        conditions.push(['name', 'ilike', searchValue]);
        conditions.push(['partner_id.name', 'ilike', searchValue]);
        conditions.push(['ga_title', 'ilike', searchValue]);
      }
    }
    
    // Add date filters
    if (this.orderStartDate) {
      conditions.push(['date_order', '>=', this.orderStartDate]);
    }
    if (this.orderEndDate) {
      conditions.push(['date_order', '<=', this.orderEndDate]);
    }
    
    // Add recent filter
    if (this.justRecents) {
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      const dateString = threeMonthsAgo.toISOString();
      conditions.push(['write_date', '>=', dateString]);
    }
    
    console.log('Search conditions:', JSON.stringify(conditions, null, 2));
    
    this.actionsLoaded = false;
    
    const sales = await firstValueFrom(this.odooEm.search<SaleOrderFlash>(
      new SaleOrderFlash(), 
      conditions, 
      1000, 
    ));

    // Solve opportunity id
    await firstValueFrom(this.odooEm.resolveArrayOfSingle(new Lead(), sales, "opportunity_id"));

    const sortedSales = sales.sort((a, b) => {
      // Handle nested properties like 'partner_id.name' or 'user_id.name'
      const getSortValue = (obj: any, path: string) => {
        return path.split('.').reduce((acc, part) => 
          acc && acc[part] !== undefined ? acc[part] : null, obj);
      };

      const valueA = getSortValue(a, this.sortField);
      const valueB = getSortValue(b, this.sortField);

      // Handle null or undefined values
      if (valueA === null || valueA === undefined) return this.sortAscending ? 1 : -1;
      if (valueB === null || valueB === undefined) return this.sortAscending ? -1 : 1;

      // Compare values
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        // String comparison (case-insensitive)
        const comparison = valueA.localeCompare(valueB, 'it', { sensitivity: 'base' });
        return this.sortAscending ? comparison : -comparison;
      } else if (typeof valueA === 'number' && typeof valueB === 'number') {
        // Numeric comparison
        return this.sortAscending ? valueA - valueB : valueB - valueA;
      } else if (valueA instanceof Date && valueB instanceof Date) {
        // Date comparison
        return this.sortAscending ? 
          valueA.getTime() - valueB.getTime() : 
          valueB.getTime() - valueA.getTime();
      } else {
        // Fallback to string comparison for other types
        const strA = String(valueA);
        const strB = String(valueB);
        const comparison = strA.localeCompare(strB, 'it', { sensitivity: 'base' });
        return this.sortAscending ? comparison : -comparison;
      }
    });
    
    // Continue with processing results
    await firstValueFrom(this.odooEm.resolveArray(new DriveFolder(), sortedSales, "drive_folder_ids"));
    await firstValueFrom(this.odooEm.resolveArray(new TrelloCardEntry2(), sortedSales, "trello_card_ids"));
    await this.loadActivities(sortedSales);
    
    this.actionsLoaded = true;

    const users = sortedSales
      .map(s => s.user_id.name)
      .filter((v, i, a) => a.indexOf(v) === i && v);

    return {
      sales: sortedSales,
      users: users
    };
  }

  // Toggle sales state filter
  toggleSalesState(state: string) {
    // Toggle the state in the array
    this.selectedSalesStates = this.selectedSalesStates.includes(state)
      ? this.selectedSalesStates.filter(s => s !== state)
      : [...this.selectedSalesStates, state];
    
    // Make sure we have at least one state selected
    if (this.selectedSalesStates.length === 0) {
      this.selectedSalesStates = ['draft'];
    }
    
    this.load();
  }

  // Toggle delivery status filter
  toggleDeliveryStatus(status: string) {
    console.log('Toggling delivery status:', status);
    // Toggle the delivery status
    this.selectedDeliveryStatuses = this.selectedDeliveryStatuses.includes(status)
      ? this.selectedDeliveryStatuses.filter(s => s !== status)
      : [...this.selectedDeliveryStatuses, status];
    //if the selection is "partial" also do the same for status "started"
    if (status === 'partial') {
      this.selectedDeliveryStatuses = this.selectedDeliveryStatuses.includes('started')
        ? this.selectedDeliveryStatuses.filter(s => s !== 'started')
        : [...this.selectedDeliveryStatuses, 'started'];
    }
    // If selecting a delivery status, make sure 'sale' is selected in states
    if (this.selectedDeliveryStatuses.length > 0 && !this.selectedSalesStates.includes('sale')) {
      this.selectedSalesStates = [...this.selectedSalesStates, 'sale'];
    }
    
    // If no delivery status is selected, remove 'sale' from states
    if (this.selectedDeliveryStatuses.length === 0 && this.selectedSalesStates.includes('sale')) {
      this.selectedSalesStates = this.selectedSalesStates.filter(s => s !== 'sale');
    }
    this.load();
  }

  toggleJustRecents() {
    this.justRecents = !this.justRecents;
    this.load();
  }

  // Toggle opportunity filters
  toggleOpportunityFilter(hasOpportunity: boolean) {
    if (hasOpportunity) {
      this.filterOpportunity = !this.filterOpportunity;
    } else {
      this.filterNoOpportunity = !this.filterNoOpportunity;
    }
    this.load();
  }

  // Toggle sort direction
  toggleSortDirection() {
    this.sortAscending = !this.sortAscending;
    this.load();
  }

  // Filter for current user's deals
  filterMyDeals() {
    if (this.isMyDealsActive) {
      this.selectedUser = null;
      this.isMyDealsActive = false;
    } else {
        this.selectedUser = this.followers.find(f => f.id === this.currentUserId);
        this.isMyDealsActive = true;
    }
    this.load();
  }

  // Main load method - now triggers the reactive stream
  async load() {
    // Trigger the search input to start the debounced load
    this.searchInput.next(this.searchInput.value);
  }

  async loadActivities(sales: SaleOrderFlash[]) {
    try {
      await firstValueFrom(this.odooEm.resolveArray(new MailActivity(), sales, "activity_ids"));
    } catch (error) {
      console.error('Error loading activities:', error);
    }
  }

  onContact(c:Contact) {
    this.newSale.partner_id.value = c
  }

  async create() {
    this.loading = true
    let sale = await firstValueFrom(this.odooEm.create<SaleOrderFlash>(new SaleOrderFlash(), {
      partner_id: this.newSale.partner_id.value ? this.newSale.partner_id.value.id : null
    }))
    //redirect to the sale (without opportunity)
    let url = `/immediate-sale/s/${sale.id}`;
// Open in a new window or tab
window.open(url, '_blank');
    await this.load()
    this.loading = false
  }

  resetFilters() {
    // Reset all filters
    this.selectedSalesStates = ['sale'];
    this.selectedDeliveryStatuses = ['pending', 'partial', 'started'];
    this.filterOpportunity = false;
    this.filterNoOpportunity = true;
    this.selectedUser = null;
    this.orderStartDate = null;
    this.orderEndDate = null;
    this.justRecents = false;
    this.searchInput.next('');

    // Load data with reset filters
    this.load();
  }

  onRowClick(saleId: number) {
    console.log('Screen width:', window.innerWidth); // Debug log
    console.log('Is mobile view:', this.isMobileView); // Debug log
    
    // Build the URL based on conditions
    let url: string;
    const searchParam = this.searchInput.value ? `?search=${this.searchInput.value}` : '';
    
    if (this.isMobileView) {
      url = `/flash-sale/${saleId}${searchParam}`;
    } else {
      const sale = this.sales.find(s => s.id === saleId);
      
      if (sale.opportunity_id.id) {
        url = `/leads/${sale.opportunity_id.id}/sale/${saleId}${searchParam}`;
      } else {
        url = `/immediate-sale/s/${saleId}${searchParam}`;
      }
    }
    
    // Open in a new window or tab
    window.open(url, '_blank');
  }

  toggleMySalesCheck() {
    this.mySalesCheck = !this.mySalesCheck;
    
    if (this.mySalesCheck) {
      // Apply "Mie aperte" filters
      
      // 1. Only orders non delivered fully, non cancel and non draft
      this.selectedSalesStates = ['sale'];
      this.selectedDeliveryStatuses = ['pending', 'partial', 'started'];
      
      // 2. All commesse and non commesse
      this.filterOpportunity = true;
      this.filterNoOpportunity = true;
      
      // 3. No searches input
      this.searchInput.next('');
      
      // 4. Include all sales (not just recent ones)
      this.justRecents = false;
      
      // 5. Filter for my sales
      this.filterMyDeals();
      
      // 6. Order by date
      this.sortField = 'date_order';
      this.sortAscending = false;
    } else {
      // If turning off "Mie aperte" mode, just reset the user filter
      if (this.isMyDealsActive) {
        this.filterMyDeals(); // Toggle off the user filter
      }
    }
    
    this.load();
  }

  async updateCommitmentDate(s: SaleOrderFlash, date: string) {
    this.loading = true;
    console.log('Updating commitment date for sale order:', s.name, 'to date:', date);
    
    try {
      // First, update the commitment date on the sale order
      await firstValueFrom(this.odooEm.update<SaleOrderFlash>(s, { commitment_date: date }));
      
      // Calculate scheduled_date (3 days before commitment_date)
      const commitmentDate = new Date(date);
      const scheduledDate = new Date(commitmentDate);
      scheduledDate.setDate(commitmentDate.getDate() - 3);
      const scheduledDateStr = scheduledDate.toISOString().split('T')[0];

      // Now update all related pickings
      const pickings = await firstValueFrom(
        this.odooEm.search<StockPicking>(new StockPicking(), 
          [
            ["group_id.sale_id", "=", s.id],
            ["state", "not in", ["done", "cancel"]], // Exclude completed or canceled pickings
            ["picking_type_code", "!=", "incoming"] // Exclude incoming pickings (from vendors)
          ]
        )
      );
      
      if (pickings.length > 0) {
        console.log(`Found ${pickings.length} pickings to update`);
        
        // Use updateMulti to update all pickings at once
        await firstValueFrom(
          this.odooEm.updateMulti<StockPicking>(
            new StockPicking(), 
            pickings, 
            { 
              scheduled_date: scheduledDateStr,
              date_deadline: date 
            }
          )
        );
        
        // Also update all related stock moves
        const moves = await firstValueFrom(
          this.odooEm.search<StockMove>(new StockMove(),
            [
              ["group_id.sale_id", "=", s.id],
              ["state", "not in", ["done", "cancel"]], 
              ["picking_code", "!=", "incoming"],
              ["location_id.id", "!=", ODOO_IDS.stock_location_vendor]
            ]
          )
        );
        
        if (moves.length > 0) {
          console.log(`Found ${moves.length} stock moves to update`);
          
          await firstValueFrom(
            this.odooEm.updateMulti<StockMove>(
              new StockMove(), 
              moves, 
              { 
                date: scheduledDateStr,     // Use the date 3 days before
                date_deadline: date        // Keep the deadline as the actual date
              }
            )
          );
        }
      }
      
      // Update the UI with the new commitment date
      s.commitment_date = date;
      console.log('Commitment date update completed successfully');
      
    } catch (error) {
      console.error('Error updating commitment date:', error);
      alert('Si è verificato un errore durante l\'aggiornamento della data di consegna');
    } finally {
      this.loading = false;
    }
  }
}