

# Metodo 2: Usand<PERSON> l'XML-RPC se lo stai chiamando esternamente
import xmlrpc.client

def call_server_action_xmlrpc():
    url = 'https://m3.galimberti.eu'
    db = 'o16'
    username = 'muletto5'
    password = 'muletto5'
    
    # Connessione ai servizi
    common = xmlrpc.client.ServerProxy('{}/xmlrpc/2/common'.format(url))
    uid = common.authenticate(db, username, password, {})
    models = xmlrpc.client.ServerProxy('{}/xmlrpc/2/object'.format(url))
    print(uid)
    # Esegui l'azione server con ID 800
    result = models.execute_kw(db, uid, password,
        'ir.actions.server', 'run',
        [[8]])
    print(result)
    return result

call_server_action_xmlrpc()