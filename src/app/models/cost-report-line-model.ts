import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { CostReport } from "./cost-report-model";
import { OdooModel } from "./odoo-model.model";
import { ProductCategory } from "./product.category";




// Classe che rappresenta una linea di costo
export class CostReportLine  extends OdooModel implements OdooSerializableInterface<CostReportLine> {

  public ODOO_MODEL = 'ga.cost.report.line';
  category: ProductCategory; // categoria del prodotto
  estimate: number; // costo preventivato della categoria
  shipped: number; // costo spedito della categoria
  forecast: number; // costo previsto della categoria
  gap: number; // differenza tra il costo preventivato e il costo spedito
  coverage: number; // copertura della categoria rispetto al preventivo
  notInvoiced: number; // flag per indicare se ci sono costi non fatturati
  return: number; // costo dei resi
  report_id: CostReport; // report di riferimento
  

  create() {
    return new CostReportLine();
  }

}