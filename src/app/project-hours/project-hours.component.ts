import { Component, OnInit } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { User } from '../models/user.model';
import { Lead } from '../models/crm.lead.model';
import { AnalyticAccount, AnalyticAccountLine } from '../models/analytic-account.model';
import { Partner } from '../models/partner';
import { Task } from '../models/project.model';
declare var bootstrap: any;

@Component({
    selector: 'app-project-hours',
    templateUrl: './project-hours.component.html',
    styleUrls: ['./project-hours.component.scss'],
    standalone: false
})
export class ProjectHoursComponent implements OnInit {
  constructor(
    private odooEm: OdooEntityManager
  ) { }

  // User data
  userData: User;
  dailyHours: number = 8;
  superUserIds = [22, 36, 13, 25]  //ripa, capoccia, locatelli  
  user: User;
  canSeeEveryone: boolean = false;
  watchingResponsableData: boolean = false;
  allUsers: User[] = [];

  
  // View type
  viewType: 'week' | 'month' | 'year' | 'day' = 'month';
  editNotes: boolean = false;
  
  // Period handling
  periods: { label: string, value: string }[] = [];
  selectedPeriod: string;
  periodDays: { date: Date, dayName: string }[] = [];
  singleDay: Date;
  
  // Lead search
  searchTerm: string = '';
  searchResults: Lead[] = [];
  selectedLead: Lead | null = null;
  
  // Accounts and hours
  userAccounts: AnalyticAccount[] = [];
  analyticLines: AnalyticAccountLine[] = []; // All lines for the user
  extraAccounts: Task[] = []; // Extra accounts for progettisti
  offAccounts: Task[] = []; // OFF accounts for progettisti

  
  // Bootstrap modals
  loading: boolean = false;

  //PrimeNG table settings
  extraSectionCollapsed: boolean = false;
  commesseSectionCollapsed: boolean = false;
  confirmedAccountsCollapsed: boolean = false;
  nonConfirmedAccountsCollapsed: boolean = false;
  offSectionCollapsed: boolean = false;

  // Filter for accounts
  accountFilter: string = '';
  filteredConfirmedAccounts: AnalyticAccount[] = [];
  filteredNonConfirmedAccounts: AnalyticAccount[] = [];
  
  
  // Analytics Plan ID for "progettazione"
  readonly PLAN_ID_PROGETTAZIONE = 4;
  readonly PROJECT_ID_PROGETTAZIONE = 10; // Always set project id to activate cost tracking
  readonly ACCOUNT_ID_FOR_EXTRA_TASKS = 31; //all extra tasks are connected to this account

  async ngOnInit() {
    console.log('ProjectHoursComponent initialized');
    this.loading = true;
    await this.loadUserInfo();
    this.initializePeriods();
    this.onPeriodChange();
    this.loading = false;

    this.filterAccounts();
  }
  
  filterAccounts() {
    console.log('Filtering accounts with term:', this.accountFilter);

    //first of all, order accounts based on the last analytic line (date), descending
    this.userAccounts.sort((a, b) => {
      const aDate = this.analyticLines.find(line => line.account_id.id === a.id)?.date;
      const bDate = this.analyticLines.find(line => line.account_id.id === b.id)?.date;
      
      if (aDate && bDate) {
        return new Date(bDate).getTime() - new Date(aDate).getTime();
      }
      return 0;
    });
    
    if (!this.accountFilter || this.accountFilter.trim() === '') {
      // If no filter, show all accounts
      this.filteredConfirmedAccounts = this.getConfirmedAccounts();
      this.filteredNonConfirmedAccounts = this.getNonConfirmedAccounts();
      console.log ('Filtered confirmed accounts:', this.filteredConfirmedAccounts.length);
      console.log ('Filtered non-confirmed accounts:', this.filteredNonConfirmedAccounts.length);
      return;
    }
    
    const filter = this.accountFilter.toLowerCase().trim();
    
    // Filter confirmed accounts
    this.filteredConfirmedAccounts = this.getConfirmedAccounts().filter(account => 
      (account.code && account.code.toLowerCase().includes(filter)) ||
      (account.name && account.name.toLowerCase().includes(filter)) ||
      (account.partner_id && account.partner_id.name && account.partner_id.name.toLowerCase().includes(filter))
    );
    
    // Filter non-confirmed accounts
    this.filteredNonConfirmedAccounts = this.getNonConfirmedAccounts().filter(account => 
      (account.code && account.code.toLowerCase().includes(filter)) ||
      (account.name && account.name.toLowerCase().includes(filter)) ||
      (account.partner_id && account.partner_id.name && account.partner_id.name.toLowerCase().includes(filter))
    );


    
    console.log('Filtered confirmed accounts:', this.filteredConfirmedAccounts.length);
    console.log('Filtered non-confirmed accounts:', this.filteredNonConfirmedAccounts.length);
  }

  getConfirmedAccountsFiltered(): AnalyticAccount[] {
    return this.filteredConfirmedAccounts;
  }
  
  getNonConfirmedAccountsFiltered(): AnalyticAccount[] {
    return this.filteredNonConfirmedAccounts;
  }

  async loadUserInfo() {
    console.log('Loading user info...');
    try {
      const result: any = await this.odooEm.odoorpcService.getSessionInfo();
      const userId = result.result.user_id[0];
      const users = await firstValueFrom(this.odooEm.search<User>(
        new User(),
      ));
      this.user = users.find(u => u.id === userId);
      this.allUsers = users
      console.log('User loaded:', this.user);
      console.log('All users loaded:', this.allUsers);

      //check if the user is a super user
      this.canSeeEveryone = this.superUserIds.includes(this.user.id);
      console.log('User can see everyone? cause he is: ', this.user.name, this.canSeeEveryone);

      this.userData = this.user; //at the beginning the user is the logged user
      //wathcing a repsonsible if the user data id is included in the responsible array
      this.watchingResponsableData = this.superUserIds.includes(this.userData.id);

      // After loading user, load their accounts
      await this.loadUserAccounts();
    } catch (error) {
      console.error('Error loading user info:', error);
    }
  }

  initializePeriods() {
    this.periods = [];
    const today = new Date();

    if (this.viewType === 'day') {
    for (let i = -7; i <= 7; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() + i);
      
      this.periods.push({
        label: this.formatDisplayDate(date),
        value: this.formatDate(date)
      });
    }
    // Set selected period to today
    this.selectedPeriod = this.formatDate(today);
    }
    else if (this.viewType === 'week') {
      const currentWeek = this.getWeekStartDate(today);
      
      for (let i = -12; i <= 12; i++) {
        const weekDate = new Date(currentWeek);
        weekDate.setDate(weekDate.getDate() + (i * 7));
        
        const weekStart = this.formatDate(weekDate);
        const weekEnd = this.formatDate(new Date(weekDate.getTime() + (6 * 24 * 60 * 60 * 1000)));
        
        this.periods.push({
          label: `Settimana ${this.getWeekNumber(weekDate)} (${this.formatDisplayDate(weekDate)} - ${this.formatDisplayDate(new Date(weekDate.getTime() + (6 * 24 * 60 * 60 * 1000)))})`,
          value: weekStart
        });
      }
      
      // Set selected period to current week
      this.selectedPeriod = this.formatDate(currentWeek);
    } 
    else if (this.viewType === 'month') {
      // Generate last 12 months, current month, and next 12 months
      for (let i = -12; i <= 12; i++) {
        const monthDate = new Date(today.getFullYear(), today.getMonth() + i, 1);
        const monthValue = this.formatMonthValue(monthDate);
        
        this.periods.push({
          label: this.formatMonthLabel(monthDate),
          value: monthValue
        });
      }
      
      // Set selected period to current month
      this.selectedPeriod = this.formatMonthValue(new Date(today.getFullYear(), today.getMonth(), 1));
    }
    else if (this.viewType === 'year') {
      // Generate ONLY CURRENT YEAR!!!
      const yearDate = new Date(today.getFullYear(), 0, 1);
      const yearValue = yearDate.getFullYear().toString();
      this.periods.push({
        label: `Anno ${yearValue}`,
        value: yearValue
      });

      // for (let i = -3; i <= 3; i++) {
      //   const yearDate = new Date(today.getFullYear() + i, 0, 1);
      //   const yearValue = yearDate.getFullYear().toString();
        
      //   this.periods.push({
      //     label: `Anno ${yearValue}`,
      //     value: yearValue
      //   });
      // }
      
      // Set selected period to current year
      this.selectedPeriod = today.getFullYear().toString();
    }
    
    console.log(`${this.viewType} periods initialized:`, this.periods);
  }

  changeViewType(type: 'week' | 'month' | 'year' | 'day') {
    console.log('View type changed to:', type);
    this.viewType = type;
    this.initializePeriods();
    this.onPeriodChange();
  }
  
  getViewTypeLabel(): string {
    switch (this.viewType) {
      case 'week': return 'Settimana';
      case 'month': return 'Mese';
      case 'year': return 'Anno';
      case 'day': return 'Giorno';
      default: return 'Periodo';
    }
  }

  onPeriodChange() {
    console.log('Period changed to:', this.selectedPeriod);
    this.updatePeriodDays();
    this.loadHoursForPeriod();
  }

  updatePeriodDays() {
    this.periodDays = [];

    if(this.viewType === 'day') {
    this.periodDays.push({
      date: new Date(this.selectedPeriod),
      dayName: new Date(this.selectedPeriod).toLocaleDateString('it-IT', { weekday: 'short' })
    });
    this.singleDay = new Date(this.selectedPeriod);
    }
  
    if (this.viewType === 'week' ) {
      const startDate = new Date(this.selectedPeriod);
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        
        this.periodDays.push({
          date: date,
          dayName: date.toLocaleDateString('it-IT', { weekday: 'short' })
        });
      }
    }
    else if (this.viewType === 'month') {
      const [year, month] = this.selectedPeriod.split('-').map(Number);
      const startDate = new Date(year, month - 1, 1);
      const lastDay = new Date(year, month, 0).getDate();
      
      for (let i = 0; i < lastDay; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        
        this.periodDays.push({
          date: date,
          dayName: date.toLocaleDateString('it-IT', { weekday: 'short' })
        });
      }
    }
    else if (this.viewType === 'year') {
      const year = parseInt(this.selectedPeriod);
      //start and end date of the year
      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31);

      //create a cell for each day of the year
      for (let i = 0; i <= 365; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        
        this.periodDays.push({
          date: date,
          dayName: date.toLocaleDateString('it-IT', { weekday: 'short' })
        });
      }    
    }
    console.log('Period days updated:', this.periodDays);
  }

  async changeUser(user: User) {
    console.log('User changed to:', user);
    this.userData = user; // Keep track of the new user
    this.loading = true;
    await this.loadUserAccounts();
    await this.loadHoursForPeriod();
    this.watchingResponsableData = this.superUserIds.includes(this.userData.id);
    this.loading = false;
  }


  async loadUserAccounts() {
    console.log('Loading user accounts...');
    
    //inizialization needed for when we change user
    this.userAccounts = [];
    this.analyticLines = [];
    this.extraAccounts = [];

    let currentYear = new Date().getFullYear();
    
    try {
      // Get analytic accounts where the user has recorded hours in current year
      this.analyticLines = await firstValueFrom(this.odooEm.search<AnalyticAccountLine>(
        new AnalyticAccountLine(), [['user_id', '=', this.userData.id], ['date', '>=', `${currentYear}-01-01`]] , 4000 ));

      console.log('Analytic lines loaded:',this.analyticLines);
      
       // Determine if user is a 6-hour or 8-hour worker using the already loaded data
    this.dailyHours = this.determineUserDailyHours();
    console.log(`User ${this.userData.name} is a ${this.dailyHours}-hour worker`);

      // Extract unique account IDs
      const accountIds = Array.from(new Set(this.analyticLines.map(e => e.account_id.id)));

      console.log('Account IDs:', accountIds);
      
      //fetch all  accounts

        let tempAccounts = await firstValueFrom(this.odooEm.search<AnalyticAccount>(
          new AnalyticAccount(),
          [['id', 'in', accountIds]]
        ));
        // Resolve partner relationships for each account
        await firstValueFrom(this.odooEm.resolveArrayOfSingle(new Partner(), tempAccounts, 'partner_id'));
        console.log('Accounts loaded before filtering:', tempAccounts);
      
      
      //remove from the list the accounts that have a code null or empty or false. this removes the accounts that are not related to a lead
      tempAccounts = tempAccounts.filter(account => account.code && account.code.length > 0);
      console.log('Accounts loaded after filtering:', tempAccounts);
      this.userAccounts = tempAccounts;

      //assing the lead to the account for ui data purposes
      //first fetch leads with tracking code = account.code
      let leads = await firstValueFrom(this.odooEm.search<Lead>( new Lead(), [['tracking_code', 'in', tempAccounts.map(account => account.code)]], 2000));
      //assing field _connected_lead to the account
      this.userAccounts.forEach(account => {
        account._connected_lead = leads.find(lead => lead.tracking_code === account.code);
        if (account._connected_lead) {
          // set is confirmed based on stage name of the lead: wether "Confermato" or "Terminata"
          account._is_confirmed = account._connected_lead.stage_id.name !== 'Persa' && account._connected_lead.stage_id.name !== 'Terminata';
        }
      });

      this.filterAccounts();


    

    //now we have to add all the extras for progettisti. these are tasks that are not related to a lead
    //these tasks have project_id = 10 and "P.EXT" in the name
    const extraTasks = await firstValueFrom(this.odooEm.search<Task>(
      new Task(),
      [['project_id', '=', 10], ['name', 'ilike', 'P.EXT']]
    ));
    this.extraAccounts = extraTasks;
    console.log('Extra accounts loaded:', this.extraAccounts);
    
    // Load OFF tasks. these are also extra tasks that contains "P.OFF" in the name
    const offTasks = await firstValueFrom(this.odooEm.search<Task>(
      new Task(),
      [['project_id', '=', 10], ['name', 'ilike', 'P.OFF']]
    ));
    this.offAccounts = offTasks;
    console.log('OFF accounts loaded:', this.offAccounts);
    
    console.log('User accounts loaded:', this.userAccounts);
    
  } catch (error) {
    console.error('Error loading user accounts:', error);
  }
}

// Add this function to calculate the user's daily hours based on existing loaded data
determineUserDailyHours(): number {
  console.log('Determining daily hours for user:', this.userData.name);
  
  if (!this.analyticLines || this.analyticLines.length === 0) {
    console.log('No time entries found, defaulting to 8 hours');
    return 8; // Default to 8 hours if no data available
  }
  
  // Group entries by day
  const dailyHours = {};
  this.analyticLines.forEach(entry => {
    // Make sure we only count entries for the PROJECT_ID_PROGETTAZIONE
    if (entry.project_id && entry.project_id.id === this.PROJECT_ID_PROGETTAZIONE) {
      const dateKey = entry.date.split('T')[0]; // Get date part only
      if (!dailyHours[dateKey]) {
        dailyHours[dateKey] = 0;
      }
      dailyHours[dateKey] += entry.unit_amount;
    }
  });
  
  // Filter only "full days" (days with more than 5 hours)
  const fullDays = Object.keys(dailyHours)
    .filter(date => dailyHours[date] >= 5)
    .sort((a, b) => new Date(b).getTime() - new Date(a).getTime()) // Most recent first
    .slice(0, 20); // Take only last 20 full days
  
  if (fullDays.length === 0) {
    console.log('No full days found, defaulting to 8 hours');
    return 8; // Default to 8 hours if no full days
  }
  
  console.log(`Found ${fullDays.length} full days of work`);
  
  // Calculate total hours for these days
  const totalHours = fullDays.reduce((sum, date) => sum + dailyHours[date], 0);
  const averageHours = totalHours / fullDays.length;
  
  console.log(`Average hours for full days: ${averageHours.toFixed(2)}`);
  
  // Determine if user is a 6-hour or 8-hour worker based on average
  // If average is closer to 6 than 8, consider them a 6-hour worker
  const isLikelySixHourWorker = Math.abs(averageHours - 6) < Math.abs(averageHours - 8);
  const dailyHoursValue = isLikelySixHourWorker ? 6 : 8;
  
  console.log(`User determined to be a ${dailyHoursValue}-hour worker`);
  return dailyHoursValue;
}

  

  getConfirmedAccounts(): AnalyticAccount[] {
    return this.userAccounts.filter(account => account._is_confirmed === true);
  }

  getNonConfirmedAccounts(): AnalyticAccount[] {
    return this.userAccounts.filter(account => account._is_confirmed === false);
 
  }

  getBadgeClassForStage(stageName: string): string {
    if (!stageName) return 'bg-warning text-dark';
    
    // Convert to lowercase and trim for reliable comparison
    const normalizedStageName = stageName.toLowerCase().trim();
    
    // Return appropriate badge class based on stage name
    if (normalizedStageName.includes('confermat')) {
      return 'bg-success text-white';
    } else if (normalizedStageName.includes('pers') || normalizedStageName.includes('annullat')) {
      return 'bg-danger text-white';
    } else if (normalizedStageName.includes('terminat') || normalizedStageName.includes('complet')) {
      return 'bg-secondary text-white';
    } else {
      return 'bg-warning text-dark';
    }
  }

  async loadHoursForPeriod() {
    console.log('Loading hours for period:', this.selectedPeriod);
    try {
      let startDate: Date, endDate: Date;
      if(this.viewType === 'day') {
        startDate = new Date(this.selectedPeriod);
        endDate = new Date(this.selectedPeriod);
      }
      else if (this.viewType === 'week') {
        startDate = new Date(this.selectedPeriod);
        endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 6);
      }
      else if (this.viewType === 'month') {
        const [year, month] = this.selectedPeriod.split('-').map(Number);
        startDate = new Date(year, month - 1, 1);
        endDate = new Date(year, month, 0);
      }
      else if (this.viewType === 'year') {
        const year = parseInt(this.selectedPeriod);
        startDate = new Date(year, 0, 1);
        endDate = new Date(year, 11, 31);
      }
      
      console.log('Date range:', startDate, 'to', endDate);
      
    
      for (const entry of this.analyticLines) {
        if (!entry.account_id || !entry.account_id.id) {
          console.error('Entry missing account_id:', entry);
        }
      }
    } catch (error) {
      console.error('Error loading hours for period:', error);
    }
  }

  isDateInRange(date: Date, startDate: Date, endDate: Date): boolean {
    // Normalize all dates to midnight
    const normalizedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const normalizedStart = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const normalizedEnd = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());
    
    // Compare the timestamps
    return normalizedDate >= normalizedStart && normalizedDate <= normalizedEnd;
  }

  async searchLeads() {
    if (!this.searchTerm || this.searchTerm.length < 3) {
      this.searchResults = [];
      return;
    }
    
    console.log('Searching leads with term:', this.searchTerm);
    try {
      // Search by tracking code, name, or partner name
      this.searchResults = await firstValueFrom(this.odooEm.search<Lead>(
        new Lead(),
        [
          '|',
          '|',
          ['tracking_code', 'ilike', this.searchTerm],
          ['name', 'ilike', this.searchTerm],
          ['partner_id.name', 'ilike', this.searchTerm]
        ],
        10
      ));
      
      // Resolve partner relationships
      await Promise.all(this.searchResults.map(lead => 
        firstValueFrom(this.odooEm.resolveSingle(new Partner(), lead.partner_id))
      ));
      
      console.log('Search results:', this.searchResults);
    } catch (error) {
      console.error('Error searching leads:', error);
      this.searchResults = [];
    }
  }

  selectLead(lead: Lead) {
    console.log('Lead selected:', lead);
    this.selectedLead = lead;
    this.searchTerm = `${lead.tracking_code} - ${lead.name} - ${lead.city? lead.city : ''}`;
    this.searchResults = [];
  }

  async addSelectedLead() {
    if (!this.selectedLead) return;

    this.loading = true;
    
    console.log('Adding selected lead as account:', this.selectedLead);
    
    // Check if account already exists
    const existingAccount = await firstValueFrom(this.odooEm.search<AnalyticAccount>(
      new AnalyticAccount(),
      [['code', '=', this.selectedLead.tracking_code]]
    ));
    
    let account: AnalyticAccount;
    
    if (existingAccount.length > 0) {
      account = existingAccount[0];
      console.log('Using existing account:', account);
    } else {
      // Create new analytic account

      const newAccount = {
        code: this.selectedLead.tracking_code,
        name: this.selectedLead.name + (this.selectedLead.city ? ` - ${this.selectedLead.city}` : ''),
        partner_id: this.selectedLead.partner_id.id,
        plan_id: this.PLAN_ID_PROGETTAZIONE,
        project_ids: [[6, false, [this.PROJECT_ID_PROGETTAZIONE]]]
      };
      
      account = await firstValueFrom(this.odooEm.create<AnalyticAccount>(
        new AnalyticAccount(),
        newAccount
      ));
      
      console.log('Created new account:', account);
    }

    //refetch it
    const accounts = await firstValueFrom(this.odooEm.search<AnalyticAccount>( new AnalyticAccount(), [['id', '=', account.id]]))
    account = accounts[0];

    //aasing the lead to the account for ui data purposes
    account._connected_lead = this.selectedLead;
    // set is confirmed based on stage name of the lead: wether "Confermato" or "Terminata"
    account._is_confirmed = this.selectedLead.stage_id.name !== 'Persa' && this.selectedLead.stage_id.name !== 'Terminata';

    
    // Add to user accounts if not already present
    if (!this.userAccounts.some(a => a.id === account.id)) {
      this.userAccounts.push(account);
      account._forEdit = true; // Mark as newly added for highlighting
      
      setTimeout(() => {
        account._forEdit = false; // Remove highlighting after a delay
      }, 3000);
    }
    
    // Reset selection
    this.selectedLead = null;
    this.searchTerm = '';
    this.loading= false;

    this.filterAccounts();
  }

  getEntryForAccountAndDay(account: AnalyticAccount, day: Date | any): AnalyticAccountLine | null {
    
    // Ensure day is a Date object
    let dayDate: Date;
    if (day instanceof Date) {
      dayDate = new Date(day); // Create a copy to avoid modifying the original
    } else if (day && day.date instanceof Date) {
      dayDate = new Date(day.date); // Create a copy
    } else if (typeof day === 'string') {
      dayDate = new Date(day);
    } else if (day && typeof day.date === 'string') {
      dayDate = new Date(day.date);
    } else {
      console.error('Invalid day format in getEntryForAccountAndDay:', day);
      return null;
    }
    
    const normalizedDay = new Date(dayDate.getFullYear(), dayDate.getMonth(), dayDate.getDate());

    
    // Find matching entry with the same date (ignoring time)
    const entry = this.analyticLines.find(e => {
      const entryDate = new Date(e.date);
      const normalizedEntryDate = new Date(entryDate.getFullYear(), entryDate.getMonth(), entryDate.getDate());
      
      const matches = e.account_id.id === account.id && 
                     normalizedEntryDate.getTime() === normalizedDay.getTime();
      return matches;
    });

    return entry || null;
  }
  
  getEntryForTaskAndDay(task: Task, day: Date | any): AnalyticAccountLine | null {

    // Ensure day is a Date object
    let dayDate: Date;
    if (day instanceof Date) {
      dayDate = new Date(day); // Create a copy
    } else if (day && day.date instanceof Date) {
      dayDate = new Date(day.date); // Create a copy
    } else if (typeof day === 'string') {
      dayDate = new Date(day);
    } else if (day && typeof day.date === 'string') {
      dayDate = new Date(day.date);
    } else {
      console.error('Invalid day format in getEntryForTaskAndDay:', day);
      return null;
    }
    
    const normalizedDay = new Date(dayDate.getFullYear(), dayDate.getMonth(), dayDate.getDate());
    
    // Find matching entry with the same date (ignoring time)
    const entry = this.analyticLines.find(e => {
      const entryDate = new Date(e.date);
      const normalizedEntryDate = new Date(entryDate.getFullYear(), entryDate.getMonth(), entryDate.getDate());
      
      const matches = e.task_id && e.task_id.id === task.id && 
                     normalizedEntryDate.getTime() === normalizedDay.getTime();
      
      return matches;
    });
    
    return entry || null;
  }

  getHoursForAccountAndDay(account: AnalyticAccount, day: any): number {
    // Ensure we pass a proper date to getEntryForAccountAndDay
    const entry = this.getEntryForAccountAndDay(account, day);
    return entry ? entry.unit_amount : null;
  }
  
  // Helper to check if two dates are the same day
  isSameDay(date1: Date | any, date2: Date | any): boolean {
    // Ensure both are Date objects or create new ones
    const normalizedDate1 = date1 instanceof Date ? new Date(date1) : new Date(date1);
    const normalizedDate2 = date2 instanceof Date ? new Date(date2) : new Date(date2);
    
    // Compare just the date portions (year/month/day)
    return normalizedDate1.getFullYear() === normalizedDate2.getFullYear() &&
           normalizedDate1.getMonth() === normalizedDate2.getMonth() &&
           normalizedDate1.getDate() === normalizedDate2.getDate();
  }

  
async tooLatetoEdit(date: Date): Promise<boolean> {
  if(this.canSeeEveryone) return false;
  
  const today = new Date();
  
  // Count only business days (excluding weekends)
  let workingDays = 0;
  const currentDate = new Date(date);
  
  // Set both dates to beginning of day to avoid time issues
  currentDate.setHours(0, 0, 0, 0);
  const endDate = new Date(today);
  endDate.setHours(0, 0, 0, 0);
  
  // If the target date is in the future, editing is always allowed
  if (currentDate > endDate) return false;
  
  // Loop through each day from the target date to today (exclusive)
  while (currentDate < endDate) {
    const dayOfWeek = currentDate.getDay();
    // If not weekend (0 = Sunday, 6 = Saturday)
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      workingDays++;
    }
    
    // Add one day
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  console.log(`Working days between dates: ${workingDays}`);
  return workingDays > 3;
}

  async updateHours(
    account: AnalyticAccount, 
    day: any, 
    hours: number, 
    entry?: AnalyticAccountLine
  ) {
    console.log('updateHours called with:', { 
      account: account?.id, 
      day, 
      hours,
      existingEntry: !!entry 
    });
  
    try {
      // Extract and normalize date from day parameter
      let dateObj: Date;
      if (day instanceof Date) {
        dateObj = new Date(day);
      } else if (day && day.date instanceof Date) {
        dateObj = new Date(day.date);
      } else if (day && typeof day.date === 'string') {
        dateObj = new Date(day.date);
      } else if (typeof day === 'string') {
        dateObj = new Date(day);
      } else {
        console.error('Invalid day format:', day);
        return;
      }

      //read input as a number. input could be with comma or dot as a decimal separator
      hours = parseFloat(hours.toString().replace(',', '.'));
      //turn hours to the closest 0.5
      hours = Math.round(hours * 2) / 2;
      //if no hours, alert
      console.log('Parsed hours:', hours);




      if (await this.tooLatetoEdit(dateObj)) {
        alert('Puoi modificare solo le ore degli ultimi 3 giorni lavorativi. contatta il tuo responsabile');
        this.updatePeriodDays();
        return;
      }
      
      // Format date for Odoo
      const dateStr = this.formatDateForOdoo(dateObj);
      console.log('Formatted date for entry:', dateStr);
  
      // Find existing entry if not provided
      if (!entry) {
        entry = this.getEntryForAccountAndDay(account, dateObj);
        console.log('Looking for existing entry - Found:', entry);
      }
  
      if (entry) {
        
        if (hours > 0) {
          // Update existing entry
          await firstValueFrom(this.odooEm.update<AnalyticAccountLine>(
            entry,
            { unit_amount: hours }
          ));
          entry.unit_amount = hours;
          
          console.log('Updated existing entry:', entry);
        } else {
          // Delete entry if hours set to 0

          await firstValueFrom(this.odooEm.delete<AnalyticAccountLine>(
            new AnalyticAccountLine(),
            [entry.id]
          ));
          
         
          const indexAll = this.analyticLines.findIndex(line => line.id === entry.id);
          if (indexAll > -1) {
            this.analyticLines.splice(indexAll, 1);
          }
          console.log('Deleted entry with 0 hours');
        }
      } else if (hours > 0) {
        // Create new entry
        const newEntry = {
          name: ``,
          account_id: account.id,
          date: dateStr,
          user_id: this.user.id, //logged
          plan_id: this.PLAN_ID_PROGETTAZIONE,
          employee_id: this.userData.employee_id?.id, // selected user
          unit_amount: hours,
          project_id: this.PROJECT_ID_PROGETTAZIONE
        };
        
        console.log('Creating new entry with data:', newEntry);
        
        const entry = await firstValueFrom(this.odooEm.create<AnalyticAccountLine>(
          new AnalyticAccountLine(),
          newEntry
        ));
        //fetch the created entry to resolve the account relationship

        const createdEntrys = await firstValueFrom(this.odooEm.search<AnalyticAccountLine>( new AnalyticAccountLine(), [['id', '=', entry.id]]))
        const createdEntry = createdEntrys[0];

        // Also add to analyticLines (all user entries)
        this.analyticLines.push(createdEntry);
        
        console.log('Created new entry:', createdEntry);
      }
  
      // Update UI totals
      this.updateTotals();
    } catch (error) {
      console.error('Error updating hours:', error);
      console.error('Detailed error:', JSON.stringify(error, null, 2));
    }
  }
  

  async updateTaskHours(
    task: Task, 
    day: any, 
    hours: number, 
    entry?: AnalyticAccountLine
  ) {
    console.log('updateTaskHours called with:', { 
      task: task?.id, 
      day, 
      hours,
      existingEntry: !!entry 
    });
  
    try {
      // Extract and normalize date from day parameter
      let dateObj: Date;
      if (day instanceof Date) {
        dateObj = new Date(day);
      } else if (day && day.date instanceof Date) {
        dateObj = new Date(day.date);
      } else if (day && typeof day.date === 'string') {
        dateObj = new Date(day.date);
      } else if (typeof day === 'string') {
        dateObj = new Date(day);
      } else {
        console.error('Invalid day format:', day);
        return;
      }

          //read input as a number. input could be with comma or dot as a decimal separator
          hours = parseFloat(hours.toString().replace(',', '.'));
          //turn hours to the closest 0.5
          hours = Math.round(hours * 2) / 2;
          //if no hours, alert
          console.log('Parsed hours:', hours);
    
    

      if (await this.tooLatetoEdit(dateObj)) {
        alert('Puoi modificare solo le ore degli ultimi 3 giorni lavorativi. contatta il tuo responsabile');
        this.updatePeriodDays();
        return;
      }
      
      // Format date for Odoo
      const dateStr = this.formatDateForOdoo(dateObj);
      console.log('Formatted date for task entry:', dateStr);
  
      // Find existing entry if not provided
      if (!entry) {
        entry = this.getEntryForTaskAndDay(task, dateObj);
        console.log('Looking for existing task entry - Found:', entry);
      }
  
      if (entry) {
        if (hours > 0) {
          // Update existing entry
          await firstValueFrom(this.odooEm.update<AnalyticAccountLine>(
            entry,
            { unit_amount: hours }
          ));
          entry.unit_amount = hours;
          console.log('Updated existing task entry:', entry);
        } else {
          // Delete entry if hours set to 0
          await firstValueFrom(this.odooEm.delete<AnalyticAccountLine>(
            new AnalyticAccountLine(),
            [entry.id]
          ));
          
          // Also remove from analyticLines
          const indexAll = this.analyticLines.findIndex(line => line.id === entry.id);
          if (indexAll > -1) {
            this.analyticLines.splice(indexAll, 1);
          }
          
          console.log('Deleted task entry with 0 hours');
        }
      } else if (hours > 0) {
        // Create new entry
        const newEntry = {
          name: ``,
          account_id: this.ACCOUNT_ID_FOR_EXTRA_TASKS,
          task_id: task.id,
          date: dateStr,
          user_id: this.user.id, //logged
          plan_id: this.PLAN_ID_PROGETTAZIONE,
          employee_id: this.userData.employee_id?.id, // selected user
          unit_amount: hours,
          project_id: this.PROJECT_ID_PROGETTAZIONE
        };
        
        console.log('Creating new task entry with data:', newEntry);
        
        const entry = await firstValueFrom(this.odooEm.create<AnalyticAccountLine>(
          new AnalyticAccountLine(),
          newEntry
        ));
        
        const createdEntrys = await firstValueFrom(this.odooEm.search<AnalyticAccountLine>( new AnalyticAccountLine(), [['id', '=', entry.id]]))
        const createdEntry = createdEntrys[0];
      
        // Also add to analyticLines (all user entries)
        this.analyticLines.push(createdEntry);
        
        console.log('Created new task entry:', createdEntry);
      }
  
      // Update UI totals
      this.updateTotals();
    } catch (error) {
      console.error('Error updating task hours:', error);
      console.error('Detailed error:', JSON.stringify(error, null, 2));
    }
  }
  
  updateTotals() {
    console.log('Updating totals in UI');
    // Force change detection by creating a new periodDays array with the same content
    this.periodDays = [...this.periodDays];
 
  }

  async updateLineName(account: AnalyticAccount, day: any, entry: AnalyticAccountLine) {
    console.log('Updating line name:', { account, day, entry });
    
    try {
      if (entry.id) {
        // Update existing entry
        await firstValueFrom(this.odooEm.update<AnalyticAccountLine>(
          entry,
          { name: entry.name }
        ));
        console.log('Updated line name:', entry.name);
      }
    } catch (error) {
      console.error('Error updating line name:', error);
    }
  }


  async updateTaskLineName(task: Task, day: any, entry: AnalyticAccountLine) {
    console.log('Updating task line name:', { task, day, entry });
    
    try {
      if (entry.id) {
        // Update existing entry
        await firstValueFrom(this.odooEm.update<AnalyticAccountLine>(
          entry,
          { name: entry.name }
        ));
        console.log('Updated task line name:', entry.name);
      }
    } catch (error) {
      console.error('Error updating task line name:', error);
    }
  }


  loadPreviousPeriod() {
    this.loading = true;
    const currentIndex = this.periods.findIndex(p => p.value === this.selectedPeriod);
    if (currentIndex > 0) {
      this.selectedPeriod = this.periods[currentIndex - 1].value;
      this.onPeriodChange();
    }
    this.loading = false;
  }

  loadNextPeriod() {
    this.loading = true;
    const currentIndex = this.periods.findIndex(p => p.value === this.selectedPeriod);
    if (currentIndex < this.periods.length - 1) {
      this.selectedPeriod = this.periods[currentIndex + 1].value;
      this.onPeriodChange();
    }
    this.loading = false;
  }

  // Helper methods for date handling
  getWeekStartDate(date: Date): Date {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Sunday
    return new Date(date.setDate(diff));
  }

  getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  formatDateForOdoo(date: Date): string {
    // Create a new date object to avoid modifying the original
    const adjustedDate = new Date(date);
    // Add timezone offset to ensure consistency
    adjustedDate.setHours(3); // Set to 3 AM to avoid timezone issues
    console.log('Formatting date for Odoo:', date, '→', adjustedDate.toISOString().split('T')[0]);
    return adjustedDate.toISOString().split('T')[0];
  }
  
  formatDisplayDate(date: Date): string {
    return date.toLocaleDateString('it-IT', { day: '2-digit', month: '2-digit', year: 'numeric' });
  }
  
  formatMonthValue(date: Date): string {
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
  }
  
  formatMonthLabel(date: Date): string {
    return date.toLocaleDateString('it-IT', { month: 'long', year: 'numeric' });
  }

  // totals calculation
  getAccountTotalHours(account: AnalyticAccount): number {
    // Calculate total hours for this account in the selected period
    return this.analyticLines
      .filter(e => e.account_id.id === account.id)
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }

  getTotalExtraHours(): number {
    // Calculate total hours for extra tasks in the selected period for project_id = PROJECT_ID_PROGETTAZIONE
    return this.analyticLines
      .filter(e => 
        e.task_id && this.extraAccounts.some(task => task.id === e.task_id.id)
      )
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }

  getTotalOffHours(): number {
    // Calculate total hours for off tasks in the selected period for project_id = PROJECT_ID_PROGETTAZIONE
    return this.analyticLines
      .filter(e => 
        e.task_id && this.offAccounts.some(task => task.id === e.task_id.id)
      )
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }

  getTotalHours(): number {
    // Calculate total hours for all accounts in the selected period, only for project_id = PROJECT_ID_PROGETTAZIONE  
    return this.analyticLines
      .filter(e => e.project_id && e.project_id.id === this.PROJECT_ID_PROGETTAZIONE)
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }

  getTotaleCommesseHours(): number {
    // Calculate total hours for the selected period, for project_id = PROJECT_ID_PROGETTAZIONE and account_id != ACCOUNT_ID_FOR_EXTRA_TASKS
    return this.analyticLines
      .filter(e => 
        e.account_id.id !== this.ACCOUNT_ID_FOR_EXTRA_TASKS && 
        e.project_id && e.project_id.id === this.PROJECT_ID_PROGETTAZIONE
      )
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }

  getDayTotalHours(date : Date): number {
    // Calculate total hours for all accounts in the selected period, for project_id = PROJECT_ID_PROGETTAZIONE
    return this.analyticLines
      .filter(e => 
        e.project_id && e.project_id.id === this.PROJECT_ID_PROGETTAZIONE &&
        this.isSameDay(new Date(e.date), date)
      )
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }

  getDayTotalHoursClass(date: Date): string {
    const hours = this.getDayTotalHours(date);

    // Determine class based on hours
    if (hours === 0) {
      return 'text-muted';
    }
    if (hours >= this.dailyHours) {
      return 'text-success';
    }
    if (hours < this.dailyHours) {
      return 'text-warning';
    }
    return '';
    // Add more conditions as needed
  }

  getDayTotalExtraHours(date: Date): number {
    // Calculate total hours for extra tasks on a specific day, for project_id = PROJECT_ID_PROGETTAZIONE
    return this.analyticLines
      .filter(e => 
        e.task_id && this.extraAccounts.some(task => task.id === e.task_id.id) &&
        this.isSameDay(new Date(e.date), date)
      )
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }

  getDayTotalOffHours(date: Date): number {
    // Calculate total hours for off tasks on a specific day, for project_id = PROJECT_ID_PROGETTAZIONE
    return this.analyticLines
      .filter(e => 
        e.task_id && this.offAccounts.some(task => task.id === e.task_id.id) &&
        this.isSameDay(new Date(e.date), date)
      )
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }

  getDayCommesseTotalHours(date: Date): number {
    // account is not ACCOUNT_ID_FOR_EXTRA_TASKS and is on the selected day, with project_id = PROJECT_ID_PROGETTAZIONE
    return this.analyticLines
      .filter(e => 
        e.account_id.id !== this.ACCOUNT_ID_FOR_EXTRA_TASKS && 
        e.project_id && e.project_id.id === this.PROJECT_ID_PROGETTAZIONE &&
        this.isSameDay(new Date(e.date), date)
      )
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }
  
  getTaskTotalHours(task: Task): number {
    // Calculate total hours for a specific task
    return this.analyticLines
      .filter(e => e.task_id && e.task_id.id === task.id)
      .reduce((sum, entry) => sum + entry.unit_amount, 0);
  }
  

  isWeekend(date: Date | any): boolean {
    if (!date) return false;
    
    // Ensure we have a Date object
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // 0 is Sunday, 6 is Saturday in JavaScript's getDay()
    const day = dateObj.getDay();
    return day === 0 || day === 6;
  }

  isSunday(date: Date | any): boolean {
    if (!date) return false;
    
    // Ensure we have a Date object
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // 0 is Sunday in JavaScript's getDay()
    return dateObj.getDay() === 0;
  }

  isSaturday(date: Date | any): boolean {
    if (!date) return false;
    
    // Ensure we have a Date object
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // 6 is Saturday in JavaScript's getDay()
    return dateObj.getDay() === 6;
  }
  
}