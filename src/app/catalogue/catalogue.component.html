<app-navbar [loading]="loading2" backroute=".." class="sticky-top">
  <!-- Main container -->
  <div class="d-flex align-items-center w-100 gap-3">
    <!-- Brand -->
    <a class="navbar-brand text-nowrap mb-0">
      Disponibilità e prezzi
    </a>

    <div class="input-group">
      <!-- Main template button -->
      <button type="button" class="btn btn-outline-primary text-white text-nowrap"
        [ngClass]="{'bg-primary': activeTemplate?.id}" [disabled]="!loadedTemplates"
        (click)="toggleProductTemplate(productToShow.id)">
        {{productToShow.name}}
        <span *ngIf="!loadedTemplates">
          <i class="fa fa-spinner fa-spin"></i>
        </span>
      </button>

      <!-- Loading/Dropdown toggle button -->
      <button type="button" class="btn btn-outline-primary text-white dropdown-toggle dropdown-toggle-split"
        [disabled]="!loadedTemplates" data-bs-toggle="dropdown" aria-expanded="false">
      </button>

      <!-- Dropdown menu (unchanged) -->
      <ul class="dropdown-menu p-0 shadow" style="z-index: 300000;">
        <div class="accordion border-0">
          @for (category of productTemplates | keyvalue; track category.key) {
          <div class="accordion-item border-0">
            <!-- Category header -->
            <button
              class="accordion-header link-underline-opacity-0 accordion-button collapsed text-muted w-100 text-start"
              (click)="$event.stopPropagation()" data-bs-toggle="collapse"
              [attr.data-bs-target]="'#collapse' + category.key.replaceAll(' / ','')">
              {{category.key}}
            </button>

            <!-- Category content -->
            <div [id]="'collapse' + category.key.replaceAll(' / ','')" class="accordion-collapse collapse"
              data-bs-parent="#accordionExample">
              <div class="accordion-body p-0">
                <ul class="list-group list-group-flush">
                  @for (product of category.value; track product.id) {
                  <li class="list-group-item">
                    <a class="d-block text-decoration-none text-body py-1 px-2" href="javascript:void(0)"
                      (mouseup)="$event.stopPropagation(); toggleProductTemplate(product.id)">
                      {{product.name}}
                    </a>
                  </li>
                  }
                </ul>
              </div>
            </div>
          </div>
          }
        </div>
      </ul>

      <!-- Fondi filter button -->
      <button type="button" class="btn btn-outline-primary text-white text-nowrap ms-2" (click)="toggleFondi()"
        title="Mostra solo i fondi di magazzino" [class.btn-primary]="onlyFondi">
        <i class="fa-regular fa-trash-can"></i>
      </button>

      <!-- area tag filter button with dropdown to check multiple -->
      <div class="btn-group ms-2" role="group" aria-label="Area filter toggle">
        <button type="button" class="btn text-white text-nowrap" [ngClass]="{
            'btn-primary': selectedAreaFilter === 'EDI',
            'btn-outline-primary': selectedAreaFilter !== 'EDI'
          }" (click)="selectedAreaFilter = 'EDI'; refresh()" title="Modalità EDI - Esclude tag 2, 3">
          Edilizia
        </button>

        <button type="button" class="btn text-white text-nowrap" [ngClass]="{
            'btn-primary': selectedAreaFilter === 'HD',
            'btn-outline-primary': selectedAreaFilter !== 'HD'
          }" (click)="selectedAreaFilter = 'HD'; refresh()" title="Modalità HD - Esclude tag 5, 9, 10">
          Home Design
        </button>
      </div>


      <!-- Search input -->
      <input class="form-control border-primary ms-2" type="search" placeHolder="Cerca per nome/categoria"
        id="search-input" autocomplete="off" (ngModelChange)="inputSearch.next($event)" [ngModel]="inputSearch | async"
        name="s1">
    </div>
  </div>


</app-navbar>

<div class="card rounded-0">
  <!-- Table wrapper with sticky header -->
  <div class="table-responsive w-100" style="height: calc(100vh - 100px); ">
    <table class="table table-hover table-bordered mb-0">
      <thead class="sticky-top" style="z-index: 10;">
        <!-- attribute sections  -->
        @if (activeTemplate?.attribute_line_ids.values.length > 0) {
        <tr class="bg-white border-bottom shadow-sm">
          <th class="border-0" colspan="10">
            <div class="w-100 d-flex flex-column ">

              <!-- Dimensional Attributes Section -->
              <div class="card border-muted mb-2" *ngIf="getDimensionalAttrs(activeTemplate).length > 0">
                <div class="card-header bg-muted py-1 border-0 cursor-pointer" (click)="toggleMisureSection()">
                  <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                      <i class="fa-solid fa-ruler-triangle text-dark me-2"></i>
                      <h6 class="mb-0 fw-bold">Misure</h6>
                    </div>
                    <i class="fa-solid text-dark transition-all"
                      [ngClass]="misureExpanded ? 'fa-caret-up' : 'fa-caret-down'"></i>
                  </div>
                </div>
                <div class="card-body p-1" [ngClass]="{'d-none': !misureExpanded}">
                  <!-- Custom layout for the three dimensional attributes -->
                  <div class="row g-1">
                    <!-- First column for Larghezza and Lunghezza -->
                    <div class="col-md-3">
                      <!-- Find and render Larghezza -->
                      <ng-container *ngFor="let a of activeTemplate?.attribute_line_ids.values">
                        <div
                          *ngIf="a.value_ids.ids.length != 1 && getDimensionalAttrs(activeTemplate).includes(a) && a.attribute_id.name.startsWith('Larghezza')"
                          class="h-100">
                          <div class="card h-100">
                            <!-- Attribute name and check icon -->
                            <div
                              class="card-header py-1 px-2 bg-light d-flex justify-content-between align-items-center border-0">
                              <small [ngClass]="{'text-danger': getIconClass(a).includes('text-danger'), 
          'text-success': getIconClass(a).includes('text-success'),
          'text-primary': getIconClass(a).includes('text-primary')}">
                                {{a.display_name}}
                              </small>
                              <i class="fa-solid" [ngClass]="getIconClass(a)"></i>
                            </div>

                            <!-- Fixed-width badges for Larghezza values -->
                            <div class="card-body p-1">
                              <div class="d-flex flex-wrap justify-content-start">
                                <div class="badge-container" *ngFor="let v of getSortedValues(a.value_ids.values)"
                                  style="width: 45px; margin: 2px;">
                                  <span class="badge w-100 text-center py-1 border-light"
                                    (click)="toggleCriteria({attributeLine: a, attributeValue: v}); refresh$.next(true)"
                                    [ngClass]="{'bg-primary text-white': hasCriteria({attributeLine: a, attributeValue: v}), 
                          'text-dark bg-light': !hasCriteria({attributeLine: a, attributeValue: v})}"
                                    (mouseover)="mouseover($event)">
                                    {{v?.name}}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>

                    <!-- Second column for Altezza -->
                    <div class="col-md-6 ">
                      <!-- Find and render Altezza -->
                      <ng-container *ngFor="let a of activeTemplate?.attribute_line_ids.values">
                        <div
                          *ngIf="a.value_ids.ids.length != 1 && getDimensionalAttrs(activeTemplate).includes(a) && a.attribute_id.name.startsWith('Altezza')"
                          class="h-100">
                          <div class="card h-100">
                            <!-- Attribute name and check icon -->
                            <div
                              class="card-header py-1 px-2 bg-light d-flex justify-content-between align-items-center border-0">
                              <small [ngClass]="{'text-danger': getIconClass(a).includes('text-danger'), 
          'text-success': getIconClass(a).includes('text-success'),
          'text-primary': getIconClass(a).includes('text-primary')}">
                                {{a.display_name}}
                              </small>
                              <i class="fa-solid" [ngClass]="getIconClass(a)"></i>
                            </div>

                            <!-- Fixed-width badges for Altezza values -->
                            <div class="card-body p-1 ">
                              <div class="d-flex flex-wrap justify-content-start">
                                <div class="badge-container" *ngFor="let v of getSortedValues(a.value_ids.values)"
                                  style="width: 45px; margin: 2px;">
                                  <span class="badge w-100 text-center py-1 border-light"
                                    (click)="toggleCriteria({attributeLine: a, attributeValue: v}); refresh$.next(true)"
                                    [ngClass]="{'bg-primary text-white': hasCriteria({attributeLine: a, attributeValue: v}), 
                          'text-dark bg-light': !hasCriteria({attributeLine: a, attributeValue: v})}"
                                    (mouseover)="mouseover($event)">
                                    {{v?.name}}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>
                    <!-- Second column for Lunghezza -->
                    <div class="col-md-3">
                      <!-- Find and render Lunghezza -->
                      <ng-container *ngFor="let a of activeTemplate?.attribute_line_ids.values">
                        <div
                          *ngIf="a.value_ids.ids.length != 1 && getDimensionalAttrs(activeTemplate).includes(a) && a.attribute_id.name.startsWith('Lunghezza')"
                          class="h-100">
                          <div class="card h-100">
                            <!-- Attribute name and check icon -->
                            <div
                              class="card-header py-1 px-2 bg-light d-flex justify-content-between align-items-center border-0">
                              <small [ngClass]="{'text-danger': getIconClass(a).includes('text-danger'), 
                'text-success': getIconClass(a).includes('text-success'),
                'text-primary': getIconClass(a).includes('text-primary')}">
                                {{a.display_name}}
                              </small>
                              <i class="fa-solid" [ngClass]="getIconClass(a)"></i>
                            </div>

                            <!-- Input for Lunghezza -->
                            <div class="card-body p-2">
                              <div class="input-group input-group-sm">
                                <input type="number" class="form-control form-control-sm" [ngModel]="getCriteriaName(a)"
                                  (blur)="setLunghezza(a, $event.target.value)"
                                  [placeholder]="'cerca lunghezza richiesta'">
                                <span class="input-group-text py-0 px-1">mm</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>

                  </div>
                </div>
              </div>

              <!-- Other Attributes Section -->
              <div class="card border-muted">
                <div class="card-header bg-muted py-1 border-0 cursor-pointer"
                  (click)="toggleCaratteristicheTecnicheSection()">
                  <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                      <i class="fa-solid fa-list-check text-dark me-2"></i>
                      <h6 class="mb-0 fw-bold">Caratteristiche Tecniche</h6>
                    </div>
                    <i class="fa-solid text-dark transition-all"
                      [ngClass]="caratteristicheTecnicheExpanded ? 'fa-caret-up' : 'fa-caret-down'"></i>
                  </div>
                </div>
                <div class="card-body p-1" [ngClass]="{'d-none': !caratteristicheTecnicheExpanded}">
                  <div class="row g-1">
                    <ng-container *ngFor="let a of activeTemplate?.attribute_line_ids.values">
                      <div *ngIf="a.value_ids.ids.length != 1 && !getDimensionalAttrs(activeTemplate).includes(a)"
                        class="col-md-3 col-sm-6">
                        <div class="card h-100 ">
                          <!-- Attribute name and check icon -->
                          <div
                            class="card-header  border-0 py-1 px-2 bg-light d-flex justify-content-between align-items-center">
                            <small [ngClass]="{'text-danger': getIconClass(a).includes('text-danger'), 
        'text-success': getIconClass(a).includes('text-success'),
        'text-primary': getIconClass(a).includes('text-primary')}">
                              {{a.display_name}}
                            </small>
                            <i class="fa-solid" [ngClass]="getIconClass(a)"></i>
                          </div>

                          <!-- Pills for ≤ 10 values, dropdown for > 10 values -->
                          <div class="card-body p-1">
                            <!-- Pills for ≤ 10 values -->
                            <div class="d-flex flex-wrap justify-content-start" *ngIf="a.value_ids.values.length <= 10">
                              <span class="badge m-1 border-light text-center py-1"
                                *ngFor="let v of getSortedValues(a.value_ids.values)"
                                (click)="toggleCriteria({attributeLine: a, attributeValue: v}); refresh$.next(true)"
                                [ngClass]="{'bg-primary text-white': hasCriteria({attributeLine: a, attributeValue: v}), 
                      'text-dark bg-light': !hasCriteria({attributeLine: a, attributeValue: v})}"
                                (mouseover)="mouseover($event)">
                                {{v?.name}}
                              </span>
                            </div>

                            <!-- Dropdown for > 10 values -->
                            <div class="p-1" *ngIf="a.value_ids.values.length > 10">
                              <select class="form-select form-select-sm" (change)="onSelectChange(a, $event)">
                                <option value="">Seleziona</option>
                                <option *ngFor="let v of getSortedValues(a.value_ids.values)" [value]="v.id"
                                  [selected]="hasCriteria({attributeLine: a, attributeValue: v})">
                                  {{v?.name}}
                                </option>
                              </select>
                            </div>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
        }

        @if (loadingAttributes) {
        <tr class="bg-white border-bottom shadow-sm">
          <th class="border-0" colspan="10">
            <div class="w-100 d-flex flex-wrap align-items-start p-2">
              <div class="spinner-border spinner-border-sm me-4" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              Carico attributi dell'articolo selezionato...
            </div>
          </th>
        </tr>
        }

        <!-- intestazione della tabella risultati-->
        <tr class="bg-muted border-bottom shadow-sm align-middle w-100">
          <th>
            <!-- dropdown to show legend of getReorderingClass  -->

            <div class="dropdown">
              <button class="btn btn-link btn-sm text-dark" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fa-solid fa-info-circle"></i>
              </button>
              <ul class="dropdown-menu ">
                <!-- dropdown title -->
                <li class="dropdown-item ">
                  <h6> Legenda icone</h6>
                </li>

                <li class="dropdown-item">
                  <i class="fa-solid fa-trash-clock text-danger"></i>
                  <span class="ms-2">Articolo in offerta da terminare con priorità - Verificare qualità </span>
                </li>
                <li class="dropdown-item">
                  <i class="fa-solid fa-trash text-primary"></i>
                  <span class="ms-2">Articolo da terminare con priorità</span>
                </li>
                <!-- <li class="dropdown-item">
                  <i class="fa-solid fa-down-to-line text-warning"></i>
                  <span class="ms-2">Materiale non standard disponibile</span>
                </li> -->
                <li class="dropdown-item">
                  <i class="fa-solid fa-star text-success"></i>
                  <span class="ms-2">Articolo standard, con giacenza minima a magazzino</span>
                </li>
                <li class="dropdown-item">
                  <i class="fa-solid fa-cart-shopping text-body-secondary"></i>
                  <span class="ms-2">Articolo non standard - ordinato su richiesta </span>
                </li>

              </ul>
            </div>
          </th>
          <th>ID</th>
          <th title="Descrizione prodotto">Prodotto</th>

          <th class=" text-center" title="Pacchi e foto">
            <!-- Toggle button for Colli with clear visual feedback -->
            <button type="button"
              class="btn btn-sm px-2 py-1 d-flex align-items-center justify-content-center w-100 btn-primary"
              (click)="togglePhotos()" [attr.aria-pressed]="showPhotos">

              <!-- Icon that changes based on state -->
              <i class="fa-solid me-1" [ngClass]="{
                   'fa-eye': showPhotos,
                   'fa-eye-slash': !showPhotos
                 }"></i>

              <!-- Button text -->
              <span class="fw-medium small">Colli</span>

              <!-- Loading spinner when needed -->
              @if (!loaded && products && products.length > 0) {
              <div class="spinner-border spinner-border-sm ms-1" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              }
            </button>
          </th>

          <th class=" text-center" title="Costo di acqusito del prodotto">
            {{selectedAreaFilter === 'EDI' ? 'Costo rif' : 'Costo mag'}}  
            </th>
          <th class="text-center" title="Prezzo di vendita consigliato secondo listino standard">
            Vendita</th>
          <th class=" text-center">UdM</th>
          <th>Qtà</th>
          <th colspan="2">Qtà libera</th>
        </tr>


        <!-- Totals row - only show when activeTemplate is set and products are loaded -->
        @if (activeTemplate && loaded && products && products.length > 0) {
        <tr class="bg-muted  border-bottom   ">
          <th></th>
          <th></th>
          <th class="border-0 mb-0 fw-bold">
            {{getTotals().totalVariants}} varianti disponibili per {{activeTemplate.name}}

          </th>
          <th></th>
          <th></th>
          <th></th>
          <th class="border-0 mb-0 fw-bold">
            {{activeTemplate?.uom_id?.name}}
          </th>
          <th class="border-0 mb-0 fw-bold">
            {{getTotals().totalQty | number : '1.1-1':'it-IT'}}
          </th>
          <th class="border-0 mb-0 fw-bold text-center" colspan="2">
            {{getTotals().totalFreeQty | number : '1.1-1':'it-IT'}}
          </th>




        </tr>
        }
      </thead>
      <!-- Table body -->
      <tbody cdkDropList id="inventoryList" [cdkDropListConnectedTo]="saleIds" class="h-100"
        (cdkDropListDropped)="drop($event)" >
        <!-- Empty state -->
        @if (products && products.length == 0 && loaded ) {
        <tr>
          <td colspan="9" class="text-center py-3">
            <i class="fa fa-search fa-2x mb-2 text-muted"></i>
            <p class="text-muted mb-0">Nessun prodotto trovato</p>
            @if (activeTemplate && !canCreateVariant()) {
            <p class="text-muted mb-0 text-center">
              <i class="fa-solid fa-times text-danger"></i>
              Per creare una nuova variante, seleziona tutti gli attributi che vuoi usare
            </p>
            }
          </td>
        </tr>
        }

        <!-- VARIANT -->
        <tr *ngIf="canCreateVariant() && products.length == 0">
          <td colspan="9">
            <button class="btn bg-primary" (click)="createVariant()">Crea nuova variante</button>
          </td>
        </tr>

        <tr  *ngFor="let p of products" class="cursor-pointer "
          [ngClass]="{'bg-light text-info': p.qty_available <= 0}">
          <td class="text-center align-middle ">
            <i class="fa-solid" [ngClass]="getReorderingClass(p)"></i>
          </td>
          <td class="text-wrap align-middle">
            {{p.id}}
          </td>
          <td class="position-relative text-start align-middle  ">
            <div class="d-flex align-items-center justify-content-between">

              <a class="position-absolute top-0 end-0 mt-1 me-1 text-primary" title="apri in Odoo"
                href="https://o3.galimberti.eu/web?debug=1#id={{p.id}}&cids=1&menu_id=223&action=393&model=product.product&view_type=form"
                target="_blank" style="font-size: 0.68rem;">
                <i class="fa fa-magnifying-glass ms-1"></i>
              </a>
              <!-- Product display name (non-editable) -->
              <div class="mx-1  text-wrap me-2">
                {{p.display_name?.replaceAll(', -','')}}
              </div>


              <!-- Image dropdown -->
              @if (p.image_1920) {
              <div class="dropdown me-5" (click)="$event.stopPropagation()">
                <img [src]="'data:image/jpeg;base64,' + p.image_1920" class="cursor-pointer dropdown-toggle"
                  data-bs-toggle="dropdown" style="width: 32px; height: 32px; object-fit: cover;" aria-expanded="false">
                <div class="dropdown-menu dropdown-menu-end p-2">
                  <img [src]="'data:image/jpeg;base64,' + p.image_1920" class="img-fluid"
                    style="max-width: 800px; max-height: 800px;">
                </div>
              </div>
              }
            </div>

          </td>

          <!-- Packs and photos -->

          <td class="text-nowrap align-middle text-center  position-relative" (click)="$event.stopPropagation()">
            @if (loaded && showPhotos) {
            <app-packs-photos [productWithQuants]="p" [from]="'product'">

            </app-packs-photos>
            }
          </td>

          <!-- pricing and quantitites-->
          <td class=" align-middle text-center ">
            <span *ngIf="!isOffer(p)">
              {{getCostBasedOnSector(p) | number : '1.2-2':'it-IT'}} €
            </span>

            <span *ngIf="isOffer(p)">
              <span class="text-decoration-line-through">
                {{getOriginalCost (p)| number : '1.2-2':'it-IT'}} €
              </span>
              <br>
              <span class="text-primary">
                <i class="fa-solid fa-chevrons-down"></i>
                {{p.list_price| number : '1.2-2':'it-IT'}} €
              </span>
            </span>
          </td>
          <td class=" align-middle text-center " [ngClass]="{'text-primary': isOffer(p)}">

            {{p._lst_price ? (p._lst_price | number :
            '1.2-2':'it-IT') : '-'}} €
          </td>
          <td class=" align-middle text-center ">{{p.uom_id.name}} </td>
          <td class=" align-middle text-body-tertiary">{{p.qty_available | number :
            '1.1-1':'it-IT'}}
          </td>
          <td class="align-middle fw-bold"> {{ p._freeForInventory | number : '1.1-1':'it-IT' }}
          </td>
          <td class=" align-middle fw-bold">{{getInPzFast(p)}}</td>
        </tr>



      </tbody>
    </table>
  </div>
</div>