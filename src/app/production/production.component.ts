import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { MrpProduction, MrpWorkorder } from '../models/mrp-production';

import { StockMove } from '../models/stock-move';
import { Product } from '../models/product.model';

import { ProductionGroupedMove, GroupLineMove } from '../models/ga_productions.model';
import { ProductTemplate } from '../models/product.template.model';
import { ProductTemplateAttributeValue } from '../models/product.template.attribute.value.model';
import { ProductPackaging } from '../models/product.packaging.model';

interface StockMoveWithPackaging extends StockMove {
  _selectablePackagings?: ProductPackaging[];
  _showPackagingDropdown?: boolean;
}


@Component({
    selector: 'app-production',
    templateUrl: './production.component.html',
    standalone: false
})
export class ProductionComponent implements OnInit {
  // Tab Management
  activeTab: 'products' | 'workorders' | 'components' = 'products';

  // Production Data
  id: number;
  production: MrpProduction;
  workOrders: MrpWorkorder[] = [];

  // Grouped Products
  groupedMoves: ProductionGroupedMove[] = [];
  
  // UI State
  loading = false;
  showProductList = false;

  constructor(
    private route: ActivatedRoute,
    private odooEm: OdooEntityManager
  ) {}

  async ngOnInit() {
    this.loading = true;
    try {
      this.id = Number(this.route.snapshot.paramMap.get('id'));
      await this.loadProduction();
      // await this.loadComponents();
      await this.groupMoves();
      // await this.checkAllCosts();
    } catch (error) {
      console.error('Error initializing production:', error);
    } finally {
      this.loading = false;
    }
  }

  // Navigation
  setActiveTab(tab: 'products' | 'workorders' | 'components') {
    this.activeTab = tab;
  }

  // Data Loading
  private async loadProduction() {
    const productions = await firstValueFrom(this.odooEm.search<MrpProduction>(new MrpProduction(), 
        [['id', '=', this.id]]));
    
    if (!productions.length) {
      throw new Error('Production not found');
    }

    //solve byproducts moves and compoenents
    await firstValueFrom(this.odooEm.resolve(this.production.move_byproduct_ids));
    await firstValueFrom(this.odooEm.resolve(this.production.move_raw_ids));

    //store all products: uotput, byproducts and components
    let productIds: number[] = [];
    productIds.push(this.production.product_id.id);
    productIds.push(...this.production.move_byproduct_ids.ids);
    productIds.push(...this.production.move_raw_ids.ids);

    //search for all products
    let products  = await firstValueFrom(this.odooEm.search<Product>(new Product(), [['id', 'in', productIds]]));

    //resolve all products: templates and packagings + attributes
    await firstValueFrom(this.odooEm.resolveArrayOfSingle(new ProductTemplate(), products, "product_tmpl_id"));
    await firstValueFrom(this.odooEm.resolveArrayOfSingle(new ProductPackaging(), products, "packaging_ids"));
    await firstValueFrom(this.odooEm.resolveArray(new ProductTemplateAttributeValue(), products, "product_template_attribute_value_ids"));

    console.log('products', products);

  }

  private async groupMoves() {
    // Create first group with production's main product
    let firstMove: GroupLineMove = {
      _productTemplate: this.production.product_id.value.product_tmpl_id,
      _product: this.production.product_id,
      _uom: this.production.product_uom_id,
      _costShare: 0,
      _quantity: this.production.product_qty, // Use production quantity
      _totalCost: 0,
      _unitCost: 0,
      packaging_qty: 0,
      is_main_product: true
    };

    this.groupedMoves = [{
      product_tmpl_id: this.production.product_id.value.product_tmpl_id,
      uom_id: this.production.product_uom_id,
      total_quantity: this.production.product_qty,
      cost_share: 100, // Main product starts with 100%
      total_cost: 0,
      unit_cost: 0,
      is_expanded: true,
      is_component: false,
      moves: [firstMove]
    }];

    // Create a map to track groups by template ID
    const templateGroups = new Map<number, ProductionGroupedMove>();
    templateGroups.set(this.production.product_id.value.product_tmpl_id.id, this.groupedMoves[0]);

    // Handle byproducts
    for (const move of this.production.move_byproduct_ids.values) {
      const templateId = move.product_id.value.product_tmpl_id.id;
      
      // Create move line
      const lineMove: GroupLineMove = {
        stockMove: move,
        _productTemplate: move.product_id.value.product_tmpl_id,
        _product: move.product_id,
        _uom: move.product_uom,
        _costShare: move.cost_share || 0,
        _quantity: move.product_uom_qty,
        _totalCost: 0,
        _unitCost: 0,
        packaging_qty: move._product_packaging_qty || 0,
        is_main_product: false
      };

      // Check if group exists for this template
      if (!templateGroups.has(templateId)) {
        // Create new group
        const newGroup: ProductionGroupedMove = {
          product_tmpl_id: move.product_id.value.product_tmpl_id,
          uom_id: move.product_uom,
          total_quantity: move.product_uom_qty,
          cost_share: move.cost_share || 0,
          total_cost: 0,
          unit_cost: 0,
          is_expanded: true,
          is_component: false,
          moves: [lineMove]
        };
        templateGroups.set(templateId, newGroup);
        this.groupedMoves.push(newGroup);
      } else {
        // Add to existing group
        const existingGroup = templateGroups.get(templateId);
        existingGroup.moves.push(lineMove);
        existingGroup.total_quantity += move.product_uom_qty;
        existingGroup.cost_share += move.cost_share || 0;
      }
    }

    // Handle components (move_raw_ids)
    for (const move of this.production.move_raw_ids.values) {
      const templateId = move.product_id.value.product_tmpl_id.id;
      
      // Create move line
      const lineMove: GroupLineMove = {
        stockMove: move,
        _productTemplate: move.product_id.value.product_tmpl_id,
        _product: move.product_id,
        _uom: move.product_uom,
        _costShare: 0, // Components don't have cost share
        _quantity: move.product_uom_qty,
        _totalCost: 0,
        _unitCost: 0,
        packaging_qty: move._product_packaging_qty || 0,
        is_main_product: false
      };

      // Check if group exists for this template
      if (!templateGroups.has(templateId)) {
        // Create new group
        const newGroup: ProductionGroupedMove = {
          product_tmpl_id: move.product_id.value.product_tmpl_id,
          uom_id: move.product_uom,
          total_quantity: move.product_uom_qty,
          cost_share: 0, // Components don't have cost share
          total_cost: 0,
          unit_cost: 0,
          is_expanded: true,
          is_component: true, // Mark as component
          moves: [lineMove]
        };
        templateGroups.set(templateId, newGroup);
        this.groupedMoves.push(newGroup);
      } else {
        // Add to existing group
        const existingGroup = templateGroups.get(templateId);
        existingGroup.moves.push(lineMove);
        existingGroup.total_quantity += move.product_uom_qty;
      }
    }

    // Initialize packagings for all moves
    for (const group of this.groupedMoves) {
      for (const move of group.moves) {
        if (move.stockMove) {
          await this.initializeMovesPackaging(move);
        }
      }
    }
}

private async initializeMovesPackaging(move: GroupLineMove) {
    if (!move._product.value?.packaging_ids) return;

    await firstValueFrom(this.odooEm.resolve(move._product.value.packaging_ids));
    
    if (move._product.value.packaging_ids.ids.length > 0) {
        const firstPackaging = move._product.value.packaging_ids.values[0];
        move.packaging_qty = move._quantity / firstPackaging.qty;
    }
}
  
    // UI Actions
  toggleExpand(group: ProductionGroupedMove) {
    group.is_expanded = !group.is_expanded;
  }


  getVariantAttribute(move: StockMove, name: string) {
    if (!move.product_id?.value?.product_template_attribute_value_ids?.values) return null;
  
    const matches = move.product_id.value.product_template_attribute_value_ids.values
      .filter(value => value.attribute_id.name.startsWith(name));
  
    return matches.length > 0 ? matches[0] : null;
  }
    


}