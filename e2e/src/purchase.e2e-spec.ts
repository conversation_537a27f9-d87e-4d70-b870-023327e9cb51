import { PurchaseTest } from './purchase';
import { CommonTest } from './common';

import { browser, logging, by } from 'protractor';

describe('purchase test', () => {
    let test: PurchaseTest;
    let commonTest: CommonTest

    let barcode = "bar code name"
    let productId = "0016";

    beforeEach(() => {
        test = new PurchaseTest();
        commonTest = new CommonTest()
    })

    it('login', () => {
        commonTest.navigateToLogin()
    })

    it('open contact', () => {
        commonTest.navigateToContact()
    })

    it('open detail user giulio', () => {
        commonTest.openDeailContact()
    })

    it('new purchases order', () => {
        test.newPurchasesOrder()
    })

    it('add product 0016', () => {
        test.addProduct(productId)
    })

    it('confirm order', () => {
        test.confirmPurchasesOrder()
    })

    it('open receipt', async () => {
        await browser.sleep(2 * 1000)
        var url = await browser.getCurrentUrl()
        var id = url.split("/")[url.split("/").length - 1]
        test.navigateToReceipt(id)
    })

    it('add bar code', () => {
        test.addBarCode(barcode)
    })

    // copia id dall'url e controlla che sia nei rifornimenti 
    it('open load', async () => {
        await browser.sleep(2 * 1000)
        var url = await browser.getCurrentUrl()
        var id = url.split("/")[url.split("/").length - 1]
        test.navigateToload(id)
    })

    it('check bar code previously added', () => {
        test.checkBarCode(barcode)
    })

    it('check load picking', () => {
        test.confirmkLoadPicking(productId)
    })

    it('check product load is success', () => {
        test.checProductLoad()
    })

    // fail on Lorda && Desc
    it('read values of purchases product 0113', () => {
        commonTest.navigateToContact()
        commonTest.openDeailContact()
        test.newPurchasesOrder()

        var object = {
            "1": "160",
            "2": "0",
            "3": "0",
            "4": "52",
            "5": "52",
            "6": "100",
            "7": "2",
            "8": "0.114",
            "9": "10",
            "10": "0.1026",
            "11": "10.26"
        }
        commonTest.testProduct("0113", false, object)
    })

    // it('read values of purchases product 0018', () => {
    //   var object = { 
    //     "1": "3000",
    //     "2": "0",
    //     "3": "0",
    //     "4": "6",
    //     "5" : "16",
    //     "6" : "18",
    //     "7" : "6",
    //     "8" : "2.34",
    //     "9" : "10",
    //     "10" : "2.106",
    //     "11" : "37.908"
    //   }
    //   page.testProduct("0018", false, object)
    // })

    // it('read values of purchases product 0037', () => {
    //   var object = { 
    //     "1": "0",
    //     "2": "0",
    //     "3": "0",
    //     "4": "101",
    //     "5" : "101",
    //     "6" : "200",
    //     "7" : "2",
    //     "8" : "0.62",
    //     "9" : "10",
    //     "10" : "0.558",
    //     "11" : "111.60"
    //   }
    //   page.testProduct("0037",false, object)
    // })

    // afterEach(async () => {
    //   // Assert that there are no errors emitted from the browser
    //   const logs = await browser.manage().logs().get(logging.Type.BROWSER);
    //   expect(logs).not.toContain(jasmine.objectContaining({
    //     level: logging.Level.SEVERE,
    //   }))
    // })
})