<form [formGroup]="productForm" (ngSubmit)="onSubmit()" class="p-fluid">
  <p-panel [header]="baseTemplate ? 'Create Product Variant' : 'Create New Product'">
    <div class="grid">
      <!-- Basic Product Information -->
      <div class="col-12 md:col-6">
        <div class="field">
          <label for="name">Product Name</label>
          <input id="name" type="text" pInputText formControlName="name">
        </div>
      </div>

      <div class="col-12 md:col-6">
        <div class="field">
          <label for="list_price">List Price</label>
          <p-inputNumber id="list_price" formControlName="list_price" mode="decimal" [minFractionDigits]="2"></p-inputNumber>
        </div>
      </div>

      <div class="col-12 md:col-6">
        <div class="field">
          <label for="standard_price">Cost Price</label>
          <p-inputNumber id="standard_price" formControlName="standard_price" mode="decimal" [minFractionDigits]="2"></p-inputNumber>
        </div>
      </div>

      <!-- Product Type and Options -->
      <div class="col-12 md:col-6">
        <div class="field-checkbox">
          <p-checkbox formControlName="sale_ok" [binary]="true" inputId="sale_ok"></p-checkbox>
          <label for="sale_ok">Can be Sold</label>
        </div>
      </div>

      <div class="col-12 md:col-6">
        <div class="field-checkbox">
          <p-checkbox formControlName="purchase_ok" [binary]="true" inputId="purchase_ok"></p-checkbox>
          <label for="purchase_ok">Can be Purchased</label>
        </div>
      </div>

      <!-- Dynamic Attributes -->
      <div class="col-12" formGroupName="attributes" *ngIf="attributeLines.length > 0">
        <p-divider></p-divider>
        <h3>Product Attributes</h3>
        
        <div class="grid" *ngFor="let line of attributeLines">
          <div class="col-12 md:col-6">
            <div class="field">
              <label [for]="line.attribute_id.name">{{line.attribute_id.name}}</label>
              <p-dropdown [id]="line.attribute_id.name"
                         [formControlName]="line.attribute_id.name"
                         [options]="line.value_ids.values"
                         optionLabel="name"
                         [placeholder]="'Select ' + line.attribute_id.name">
              </p-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-content-end">
      <p-button type="submit" 
                [label]="baseTemplate ? 'Create Variant' : 'Create Product'"
                [loading]="loading"
                [disabled]="productForm.invalid || loading">
      </p-button>
    </div>
  </p-panel>
</form>