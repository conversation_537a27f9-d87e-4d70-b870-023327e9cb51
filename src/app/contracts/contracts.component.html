<div class="card card-stage card-stage-sibling mb-4 p-0" >
                   
  <div class="card-body p-0">


<div class="card-header px-3 d-flex align-items-center justify-content-between">
  <span><strong>Contratti e varianti</strong></span>
  <div>
    <button class="btn btn-link " title="Espandi" (click)="toggle<PERSON><PERSON>ratti()">
      <i class="fa-solid fa-arrows-up-down fa-lg"></i>
    </button>
    <button class="btn btn-link text-primary " type="button" (click)="createInvoice(O_ids.contratti_id)"
      title="Crea Nuovo Contratto">
      <i class="fa-solid fa-plus fa-lg"></i>
    </button>
  </div>

</div>

<!-- show this only if contrattiInvoices.length>0 -->
@if (contrattiInvoices && contrattiInvoices.length > 0) {

<div>

  <!-- Contratti e varianti section -->
  <div class="d-flex flex-column">

  
    <div class="table-responsive">
      <table class="table  table-hover align-middle mb-0">
        <thead>
          <tr>
            <th class="text-start"></th>
            <td class="text-start">Descrizione</td>
            <td class="text-start">Data conferma</td>
            <th class="text-end">Imponibile</th>

            <td colspan="2" class="text-end">IVA</td>
            <th class="text-end">Totale</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <ng-container *ngFor="let invoice of contrattiInvoices; let i = index">
            <!-- Invoice Row -->
            <tr class="bg-light">
              <td class="text-center">
                <i class="fa-solid"
                  [ngClass]="{'fa-caret-down': invoice._expanded, 'fa-caret-right': !invoice._expanded}"
                  (click)="toggleInvoiceDetails(invoice)" style="cursor: pointer;"></i>
              </td>

              <td>
                <div class="d-flex align-items-center fw-bold"
                  [innerHTML]="invoice.narration || 'Aggiungi una descrizione...'" contenteditable="true"
                  (blur)="updateNarration(invoice, $event.target.innerHTML)"
                  style="min-height: 31px; max-height: 31px; overflow: hidden; outline: none;"
                  [ngClass]="{'text-muted': !invoice.narration}">
                </div>
              </td>


              <td>
                <input type="date" class="form-control form-control-sm bg-white border-0"
                  [ngModel]="invoice.invoice_date | date:'yyyy-MM-dd'"
                  (ngModelChange)="updateInvoiceDate(invoice, $event)">
              </td>



              <!-- <td></td>
                  <td></td> -->
              <th class="text-end fw-bold">{{invoice.amount_untaxed | currency:'EUR'}}</th>
              <td> </td>
              <td class="text-end text-muted">{{invoice.amount_tax | currency:'EUR'}}</td>
              <th class="text-end text-muted">{{invoice.amount_total | currency:'EUR'}}</th>
              <td class="text-end">

                <button class="btn btn-link p-0 me-3" (click)="addInvoiceLine(invoice)"
                  [disabled]="invoice.state === 'posted'"
                  [ngClass]="{'text-primary': invoice.state !== 'posted', 'text-muted': invoice.state === 'posted'}"
                  title="Aggiungi Riga">
                  <i class="fa-solid fa-plus"></i>
                </button>
                <button class="btn btn-link p-0 me-2" (click)="removeInvoice(invoice)" title="Rimuovi Fattura">
                  <i class="fa-solid fa-trash text-primary"></i>
                </button>

              </td>
            </tr>

            <!-- Invoice Lines (expanded view) -->
            <ng-container *ngIf="invoice._expanded">
              <tr *ngFor="let line of invoice.invoice_line_ids.values" class="bg-white">
                <td></td>
                <td colspan="2">
                  <select class="form-select form-select-sm bg-white border-0" [ngModel]="line.product_id?.id"
                    (ngModelChange)="updateProduct(line, $event)">
                    <option [ngValue]="null">Seleziona Prodotto</option>
                    <option *ngFor="let product of contractProducts" [ngValue]="product.id">
                      {{product.name}}
                    </option>
                  </select>
                </td>
                <td class="text-end">
                  <div class="d-flex align-items-center justify-content-end">
                    <input-number [ngModel]="line.price_unit | number : '1.0-2':'en-EN'"
                      (ngModelChange)="updatePrice(line, $event)">
                    </input-number>
                    <span class="ms-1">€</span>
                  </div>
                </td>
                <!-- <td class="text-end">
                      <div class="d-flex align-items-center justify-content-end">
                        <input-number [ngModel]="line.discount | number : '1.0-2':'en-EN'"
                          (ngModelChange)="updateDiscount(line, $event)">
                        </input-number>
                        <span class="ms-1">%</span>
                      </div>
                    </td>
                    <td class="text-end text-muted">{{line.price_subtotal | currency:'EUR'}}</td> -->
                <td colspan="2">
                  <select class="form-select form-select-sm bg-white border-0"
                    [ngModel]="(invoice?.invoice_line_ids.values?.length > 0 &&  line.tax_ids?.values?.length > 0) ? line.tax_ids?.values[0].id : []"
                    (ngModelChange)="updateTax(line, $event)">
                    <option [ngValue]="null">0%</option>
                    <option *ngFor="let tax of taxes" [ngValue]="tax.id"> {{tax.name}}</option>
                  </select>
                </td>


                <!-- <td class="text-end text-muted">{{line._tax_amount | currency:'EUR'}}</td> -->

                <td class="text-end text-muted">{{line.price_total | currency:'EUR'}}</td>


                <td class="text-center">
                  <button class="btn btn-link p-0" (click)="removeLine(invoice, line)" title="Rimuovi Riga">
                    <i class="fa-solid fa-trash text-primary"></i>
                  </button>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </tbody>




      </table>
    </div>
  </div>
</div>

}




  </div>
</div>

<div class="card card-stage card-stage-sibling mb-4 p-0" >
                   
  <div class="card-body p-0">

<div class="card-header px-3 d-flex align-items-center justify-content-between">
  <span><strong>Fatture e SAL</strong></span>
  all {{allInvoices?.length}}
  sal {{salInvoices?.length}}
  contracts {{contrattiInvoices?.length}}
  <div>
    <button class="btn btn-link " title="Espandi" (click)="toggleSal()">
      <i class="fa-solid fa-arrows-up-down fa-lg"></i>
    </button>

    <button class="btn btn-link text-primary " type="button" (click)="createInvoice(O_ids.sal_id)"
      title="Crea Nuova Fattura SAL">
      <i class="fa-solid fa-plus fa-lg"></i>
    </button>
  </div>
</div>

<!-- show this only if salinvoices.length>0 -->

@if (salInvoices && salInvoices.length > 0) {
<div class="d-flex flex-column">
  <div class="table-responsive" id="aaa">
    <table class="table table-hover align-middle mb-0">
      <thead>
        <tr class="text-center">
          <th></th>
          <td class="text-start">Data</td>
          <td class="text-start">Descrizione</td>
          <th class="text-end">Imponibile</th>
          <!-- <th>Sconto</th>
              <th>Imponibile</th> -->
          <td class="text-end" colspan="2">IVA</td>
          <th class="text-end">Totale</th>
          <th class="text-end"></th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let invoice of salInvoices; let i = index">
          <!-- Invoice Row -->
          <tr class="bg-light">
            <td class="text-center">
              <i class="fa-solid" [ngClass]="{'fa-caret-down': invoice._expanded, 'fa-caret-right': !invoice._expanded}"
                (click)="toggleInvoiceDetails(invoice)" style="cursor: pointer;"></i>
            </td>
            <td>
              <!-- Date Input (Invoice Date) -->
              <input type="date" class="form-control form-control-sm bg-white border-0"
                [ngModel]="invoice.invoice_date | date:'yyyy-MM-dd'"
                (ngModelChange)="updateInvoiceDate(invoice, $event)" [disabled]="invoice.state === 'posted'">
            </td>
            <td>
              <!-- Narration (remains editable) -->
              <div class="d-flex align-items-center fw-bold"
                [innerHTML]="invoice.narration || 'Aggiungi una descrizione...'" contenteditable="true"
                (blur)="updateNarration(invoice, $event.target.innerHTML)"
                style="min-height: 31px; max-height: 31px; overflow: hidden; outline: none;"
                [ngClass]="{'text-muted': !invoice.narration}">
              </div>

            </td>
            <!-- <td></td>
                <td></td> -->
            <th class="text-end fw-bold">{{invoice.amount_untaxed | currency:'EUR'}}</th>
            <td> </td>
            <td class="text-center text-muted">{{invoice.amount_tax | currency:'EUR'}}</td>
            <th class="text-end text-muted">{{invoice.amount_total | currency:'EUR'}}</th>
            <td class="text-center">
              <!-- Add Invoice Line Button -->
              <button class="btn btn-link p-0 me-2" (click)="addInvoiceLine(invoice)"
                [disabled]="invoice.state === 'posted'"
                [ngClass]="{'text-primary': invoice.state !== 'posted', 'text-muted': invoice.state === 'posted'}"
                title="Aggiungi Riga">
                <i class="fa-solid fa-plus"></i>
              </button>
              <button class="btn btn-sm me-1" (click)="toggleInvoiceActivity(invoice)"
                [disabled]="invoice.state === 'posted'" [ngClass]="{
                      'btn-primary': invoice.amount_total > 0 && !invoice.activity_ids?.ids?.length && invoice.state !== 'posted',
                      'btn-success': invoice.activity_ids?.ids?.length || invoice.state === 'posted',
                      'btn-muted': invoice.amount_total === 0 && !invoice.activity_ids?.ids?.length || invoice.state === 'posted'
              }">
                <span
                  *ngIf="invoice.amount_total === 0 && !invoice.activity_ids?.ids?.length || invoice.state === 'posted'">
                  Da fatturare
                </span>
                <span
                  *ngIf="invoice.amount_total > 0 && !invoice.activity_ids?.ids?.length && invoice.state !== 'posted'">
                  Da fatturare &nbsp;<i class="fa-solid fa-plus"></i>
                </span>
                <span *ngIf="invoice.activity_ids?.ids?.length && invoice.state !== 'posted'">
                  Non fatturare &nbsp;<i class="fa-solid fa-times"></i>
                </span>

              </button>
              <button class="btn btn-sm me-1" (click)="completeInvoiceActivity(invoice)"
                [disabled]="!invoice.activity_ids?.ids?.length || invoice.state === 'posted'" [ngClass]="{
                      'btn-muted': !invoice.activity_ids?.ids?.length && invoice.state !== 'posted',
                      'btn-primary': invoice.activity_ids?.ids?.length && invoice.state !== 'posted',
                      'btn-success': invoice.state === 'posted'
                    }">
                Fatturato
              </button>
              <button class="btn btn-link p-0 me-2" (click)="removeInvoice(invoice)"
                [disabled]="invoice.activity_ids?.ids?.length || invoice.state === 'posted'" [ngClass]="{
    'text-primary': !(invoice.activity_ids?.ids?.length || invoice.state === 'posted'),
    'text-muted': invoice.activity_ids?.ids?.length || invoice.state === 'posted'
  }" title="Rimuovi Fattura">
                <i class="fa-solid fa-trash"></i>
              </button>
            </td>
          </tr>

          <!-- Invoice Lines (expanded view) -->
          <ng-container *ngIf="invoice._expanded">
            <tr *ngFor="let line of invoice.invoice_line_ids.values" class="bg-white">
              <td></td>
              <td></td>
              <td>
                <!-- Product Select -->
                <select class="form-select form-select-sm bg-white border-0" [ngModel]="line.product_id?.id"
                  (ngModelChange)="updateProduct(line, $event)" [disabled]="invoice.state === 'posted'">
                  <option [ngValue]="null">Seleziona Prodotto</option>
                  <option *ngFor="let product of contractProducts" [ngValue]="product.id">
                    {{product.name}}
                  </option>
                </select>
              </td>
              <td class="text-end">
                <!-- Price Input -->
                <div class="d-flex align-items-center justify-content-end">
                  <input-number [ngModel]="line.price_unit | number : '1.0-2':'en-EN'"
                    (ngModelChange)="updatePrice(line, $event)" [disabled]="invoice.state === 'posted'">
                  </input-number>
                  <span class="ms-1">€</span>
                </div>

              </td>
              <!-- <td class="text-end">
                    <div class="d-flex align-items-center justify-content-end">
                      <input-number [ngModel]="line.discount | number : '1.0-2':'en-EN'"
                        (ngModelChange)="updateDiscount(line, $event)">
                      </input-number>
                      <span class="ms-1">%</span>
                    </div>
                  </td>
                  <td class="text-end text-muted">{{line.price_subtotal | currency:'EUR'}}</td> -->
              <td colspan="2">
                <!-- Tax Select -->
                <select class="form-select form-select-sm bg-white border-0"
                  [ngModel]="(invoice?.invoice_line_ids.values?.length > 0 && line.tax_ids?.values?.length > 0) ? line.tax_ids?.values[0].id : []"
                  (ngModelChange)="updateTax(line, $event)" [disabled]="invoice.state === 'posted'">
                  <option [ngValue]="null">0%</option>
                  <option *ngFor="let tax of taxes" [ngValue]="tax.id">{{tax.name}}</option>
                </select>
              </td>
              <!-- <td class="text-end text-muted">{{line._tax_amount | currency:'EUR'}}</td> -->
              <td class="text-end text-muted">{{line.price_total | currency:'EUR'}}</td>
              <td class="text-end">
                <!-- Remove Line Button -->
                <button class="btn btn-link p-0" (click)="removeLine(invoice, line)" title="Rimuovi Riga"
                  [disabled]="invoice.state === 'posted'"
                  [ngClass]="{'text-primary': invoice.state !== 'posted', 'text-muted': invoice.state === 'posted'}">
                  <i class="fa-solid fa-trash"></i>
                </button>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>
}

<!-- show only if either contratti or sal length >0 -->
@if (allInvoices && allInvoices.length > 0) {
  <div class="table-responsive mt-3">
    <table class="table table-hover align-middle mb-0">
      <tfoot>
        <!-- Contratti e varianti -->
        <tr>
          <th colspan="3">Contratti e varianti</th>
          <td class="text-end" colspan="3">{{ totalContract | currency: 'EUR' }}</td>
          <td></td>
          <td></td>
        </tr>
  
        <!-- Fatturazione prevista (conclusa) -->
        <tr>
          <th colspan="3">Fatture pianificate (concluse)</th>
          <td class="text-end" colspan="3">{{ totalSAL | currency: 'EUR' }} ({{ totalSALPosted | currency: 'EUR' }})</td>
          <td></td>
          <td></td>
        </tr>
  
        <!-- Da fatturare -->
        <tr>
          <th class="text-danger" colspan="3">Da pianificare (da fatturare)</th>
          <td class="text-end" colspan="3">{{ toAdd | currency: 'EUR' }} ({{ toInvoice | currency: 'EUR' }})</td>
          <td></td>
          <td></td>
        </tr>
      </tfoot>
    </table>
  </div>
  }



</div>
</div>