<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, user-scalable=no" />
  <title>Gali ERP</title>
  <base href="/">
  <script>
    if (global === undefined) {
      var global = window;
    }
  </script>
  <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
  <link rel="icon" type="image/png" href="favicon.png">
  <link rel="manifest" href="manifest.json">
  <meta name="theme-color" content="#3367D6">
  <script src="https://cdn.jsdelivr.net/npm/dynamsoft-javascript-barcode@9.3.0/dist/dbr.js"></script>

  <!-- <script src="./assets/zxing_reader.js" type="module"></script> -->

  <!-- <script src="https://cdn.jsdelivr.net/npm/dynamsoft-javascript-barcode/dist/dbr.js"></script> -->
  <script src="https://code.jquery.com/jquery-3.5.1.min.js"  crossorigin="anonymous"></script>


  <!-- <script src="https://cdn.jsdelivr.net/npm/dynamsoft-camera-enhancer@3.1.0/dist/dce.js"></script> -->
  <script src="https://kit.fontawesome.com/f46673fb47.js" crossorigin="anonymous"></script>
  <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" /> -->
  <script src="https://apis.google.com/js/api.js"></script>
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDYOCgBUxRZpzVbmXdpVLt9nl8uFvoGUTc&libraries=places&language=it"></script>

  <script src="https://api.trello.com/1/client.js?key=873440df9c745bc87cbe275bad15c532"></script>

  
  
  <!-- <script src="https://cdn.jsdelivr.net/npm/@undecaf/zbar-wasm@0.9.11/dist/index.js"></script> -->
<!-- <script src="https://cdn.jsdelivr.net/npm/@undecaf/barcode-detector-polyfill@0.9.13/dist/index.js"></script> -->


<!-- 
  <script  >

    
      // import { BarcodeDetectorPolyfill } from 'https://cdn.jsdelivr.net/npm/@undecaf/zbar-wasm@0.9.11/dist/index.js'

  </script> -->


<script>


</script>


</head>
<body>
  <!-- <canvas id="canvas" width="640" height="480"></canvas> -->

  <app-root></app-root>
  <noscript>Please enable JavaScript to continue using this application.</noscript>
</body>
<!-- 


<script type="module">

const canvas = document.getElementById("canvas");

      
const ctx = canvas.getContext("2d", { willReadFrequently: true });
const video = document.createElement("video");

video.setAttribute("id", "video");
		video.setAttribute("width", canvas.width);
		video.setAttribute("height", canvas.height);
		video.setAttribute("autoplay", "");


    function readBarcodeFromCanvas(canvas, format, mode) {

			var imgWidth = canvas.width;
			var imgHeight = canvas.height;
			var imageData = canvas.getContext('2d').getImageData(0, 0, imgWidth, imgHeight);
			var sourceBuffer = imageData.data;

			if (zxing != null) {
				var buffer = zxing._malloc(sourceBuffer.byteLength);
				zxing.HEAPU8.set(sourceBuffer, buffer);

				var result = zxing.readBarcodeFromPixmap(buffer, imgWidth, imgHeight, mode, format);
				zxing._free(buffer);
        console.log("res", result)
				return result;
			} else {
				return { error: "ZXing not yet initialized" };
			}
		}



      navigator.mediaDevices
				.getUserMedia({ video: { facingMode: true }, audio: false })
				.then(function (stream) {
					video.srcObject = stream;
					video.setAttribute("playsinline", true);
					video.play();
					processFrame();
				})
				.catch(function (error) {
					console.error("Error accessing camera:", error);
				});



        const processFrame = function () {
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            const code = readBarcodeFromCanvas(canvas, 'DataMatrix', true);
            if (code.text) {
              console.log(code.format + ": " + code.text)
            } else {
            }
            setTimeout(x => {

              requestAnimationFrame(processFrame);
            },100)
		    };





    var zxing
      await window.ZXing().then(function (instance) {
        zxing = instance
        // zxing = instance; // this line is supposedly not required but with current emsdk it is :-/
        console.log("---",instance)
        processFrame();
      });
  


  
      
  </script> -->



</html>

