<app-navbar [loading]="loading" backroute="..">
  <div class="d-flex justify-content-between align-items-center w-100">
    <a class="navbar-brand">
      Vendite
    </a>
    <ng-container>
      <div class="ms-auto dropdown sticky-top me-4">
        <a class="btn text-white btn-primary dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
          aria-expanded="false">
          Nuovo
        </a>
        <ul class="dropdown-menu dropdown-menu-end p-3" 
        style="min-width: 300px; z-index: 30000000000000000000; position: absolute;">

      <div class="mb-3" style="min-width: 400px; z-index: 3000000000000000000;">
        <label class="form-label fw-bold">Contatto</label>
            <label class="form-label fw-bold">Contatto</label>
            <app-contact-picker2 [mode]="'embedded'" class="embedded overflow-hidden border-0"
              (onSelect)="onContact($event)"></app-contact-picker2>
          </div>
          <button [disabled]="!newSale.partner_id.value" class="btn btn-primary text-white mt-3"
            (click)="$event.stopPropagation();create()">Crea</button>

        </ul>
      </div>
    </ng-container>
  </div>
</app-navbar>

<!-- Enhanced Filter Section -->
<div class="bg-light px-3 py-3 w-100">

  <!-- FIRST ROW - Status, Type, User, and Quick Actions -->
  <div class="row mb-3 align-items-center ">
    <!-- Status Buttons -->
    <div class="col-md-5 d-flex">
      <div class="d-flex  align-items-center">
        <span class="me-2 fw-semibold">Stato:</span>
        <div class="btn-group  text-dark" role="group">
          <input type="checkbox" class="btn-check" id="btn-draft" [checked]="selectedSalesStates.includes('draft')"
            (change)="toggleSalesState('draft')">
          <label class="btn btn-outline-muted" for="btn-draft">
            <span class="fa fa-circle text-dark me-1"></span> Bozza
          </label>

          <input type="checkbox" class="btn-check" id="btn-pending"
            [checked]="selectedDeliveryStatuses.includes('pending')" (change)="toggleDeliveryStatus('pending')">
          <label class="btn btn-outline-muted" for="btn-pending">
            <span class="fa fa-circle text-primary me-1"></span> Non consegnato
          </label>

          <input type="checkbox" class="btn-check" id="btn-partial"
            [checked]="selectedDeliveryStatuses.includes('partial')" (change)="toggleDeliveryStatus('partial')">
          <label class="btn btn-outline-muted" for="btn-partial">
            <span class="fa fa-circle text-warning me-1"></span> Parziale
          </label>

          <input type="checkbox" class="btn-check" id="btn-full" [checked]="selectedDeliveryStatuses.includes('full')"
            (change)="toggleDeliveryStatus('full')">
          <label class="btn btn-outline-muted" for="btn-full">
            <span class="fa fa-circle text-success me-1"></span> Completo
          </label>

          <input type="checkbox" class="btn-check" id="btn-cancel" [checked]="selectedSalesStates.includes('cancel')"
            (change)="toggleSalesState('cancel')">
          <label class="btn btn-outline-muted" for="btn-cancel">
            <span class="fa fa-circle text-white me-1"></span> Annullato
          </label>
        </div>
      </div>
    </div>

    <!-- Type Buttons -->
    <div class="col-md-3 d-flex align-items-center">
      <div class="d-flex align-items-center">
        <span class="me-2 fw-semibold">Tipo:</span>
        <div class="btn-group" role="group">
          <input type="checkbox" class="btn-check" id="btn-opportunity" [checked]="filterOpportunity"
            (change)="toggleOpportunityFilter(true)">
          <label class="btn btn-outline-muted" for="btn-opportunity">
            <i class="fa fa-shop me-1"></i> Commessa
          </label>

          <input type="checkbox" class="btn-check" id="btn-direct" [checked]="filterNoOpportunity"
            (change)="toggleOpportunityFilter(false)">
          <label class="btn btn-outline-muted" for="btn-direct">
            <i class="fa fa-square-list me-1"></i> Su lista
          </label>
        </div>
      </div>
    </div>


    <!-- Quick Actions -->
    <div class="col-md-4 d-flex justify-content-end">
        <button class="btn" (click)="toggleMySalesCheck()"
          [ngClass]="mySalesCheck ? 'btn-primary' : 'btn-outline-secondary'">
          <i class="fa-solid fa-user-check me-1"></i> Mie aperte
        </button>
        <button class="btn btn-info ms-2" (click)="resetFilters()">
          <i class="fa-solid fa-refresh me-1"></i> Reset
        </button>

    </div>
  </div>

  <!-- SECOND ROW - Search, Date Filter, and Quick Filters -->
  <div class="row g-3">
    <div class="col-md-4 d-flex">
      <div class="input-group ">
        <span class="input-group-text">
          <i class="fa fa-search"></i>
        </span>
        <input class="form-control" [ngModel]="searchInput | async" (ngModelChange)="searchInput.next($event)"
          placeholder="Cliente, numero ordine...">
      </div>
    </div>

    <!-- User Selection -->
    <div class="col-md-3 d-flex">
      <div class="input-group ">
        <span class="input-group-text">Utente</span>
        <select class="form-select" [(ngModel)]="selectedUser" (change)="load()">
          <option [ngValue]="null">Tutti</option>
          <option *ngFor="let partner of followers" [ngValue]="partner">{{partner.name}}</option>
        </select>
        <button *ngIf="currentUserId" class="btn btn-outline-secondary" (click)="filterMyDeals()"
          [ngClass]="isMyDealsActive ? 'active' : ''" title="Solo i miei ordini">
          <i class="fa-solid fa-user"></i>
        </button>
      </div>
    </div>

    <div class="col-md-3 d-flex">
      <div class="input-group ">
        <span class="input-group-text">Data ordine</span>
        <input type="date" class="form-control" [(ngModel)]="orderStartDate" (change)="load()">
        <span class="input-group-text">a</span>
        <input type="date" class="form-control" [(ngModel)]="orderEndDate" (change)="load()">
      </div>
    </div>

    <div class="col-md-2 d-flex">
      <button class="btn btn-outline-secondary  w-100" (click)="toggleJustRecents()"
        [ngClass]="justRecents ? 'active' : ''">
        <i class="fa-solid fa-clock me-1"></i> Solo recenti
      </button>
    </div>
  </div>
</div>


<!-- PrimeNG Table Section -->
<div class="flex-grow-1" [hidden]="loading">
  <p-table [value]="sales" [paginator]="true" [rows]="25" [rowsPerPageOptions]="[10, 25, 50, 100]"
    [showCurrentPageReport]="true" currentPageReportTemplate="Visualizzando {first} - {last} di {totalRecords} vendite"
    styleClass="p-datatable-sm" [tableStyle]="{'min-width': '50rem'}" [loading]="loading">

    <ng-template pTemplate="header" class="bg-transparent">
      <tr>
        <th style="width: 3rem"></th>
        <th pSortableColumn="name">
          Numero
          <p-sortIcon field="name"></p-sortIcon>
        </th>
        <th pSortableColumn="partner_id.name">
          Cliente
          <p-sortIcon field="partner_id.name"></p-sortIcon>
        </th>
        <th pSortableColumn="opportunity_id.name">
          Commessa
          <p-sortIcon field="opportunity_id.name"></p-sortIcon>
        </th>
        <th pSortableColumn="user_id.name">
          Responsabile
          <p-sortIcon field="user_id.name"></p-sortIcon>
        </th>
        <th pSortableColumn="date_order">
          Data ordine
          <p-sortIcon field="date_order"></p-sortIcon>
        </th>
        <th pSortableColumn="amount_untaxed" class="text-end">
          Importo
          <p-sortIcon field="amount_untaxed"></p-sortIcon>
        </th>
        <th style="width: 8rem bg-transparent">Produzione</th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-s>
      <tr>
        <!-- Type Icon -->
        <td class="text-center py-1">
          <i *ngIf="s.opportunity_id.id" class="fa-lg fa fa-shop" title="Parte di commessa"></i>
          <i *ngIf="!s.opportunity_id.id" class="fa-lg fa fa-square-list" title="Vendita diretta"></i>
        </td>

        <!-- Number with Status -->
        <td (click)="onRowClick(s.id)" class="text-nowrap" style="cursor: pointer;">
          <span title="Spedito parzialmente" class="fa fa-circle text-warning"
            *ngIf="s.state == 'sale' && (s.delivery_status == 'partial' || s.delivery_status == 'started')"></span>
          <span title="Confermato - Non spedito" class="fa fa-circle text-primary"
            *ngIf="s.state == 'sale' && (s.delivery_status == 'pending')"></span>
          <span title="Spedito interamente" class="fa fa-circle text-success"
            *ngIf="s.state == 'sale' && s.delivery_status == 'full'"></span>
          <span title="Confermato" class="fa fa-circle text-primary"
            *ngIf="s.state == 'sale' && !s.delivery_status"></span>
          <span title="Bozza" class="fa fa-circle text-dark" *ngIf="s.state == 'draft'"></span>
          <span title="Annullato" class="fa fa-circle text-light" *ngIf="s.state == 'cancel'"></span>
          <span class="ms-3">{{s.name}}</span>
        </td>

        <!-- Customer -->
        <td (click)="onRowClick(s.id)" class="text-muted" style="cursor: pointer;">
          <strong>{{s.partner_id?.name}}</strong>
          <span *ngIf="s.ga_title"> - {{s.ga_title}}</span>
        </td>

        <!-- Opportunity -->
        <td (click)="onRowClick(s.id)" class="text-muted" style="cursor: pointer;">
          <span *ngIf="s.opportunity_id.value?.tracking_code; else directSale">
            {{s.opportunity_id.value.tracking_code}}
            <span *ngIf="s.opportunity_id.value?.name"> - {{s.opportunity_id.value.name}}</span>
          </span>
          <ng-template #directSale>Vendita diretta</ng-template>
        </td>

        <!-- User -->
        <td (click)="onRowClick(s.id)" class="text-muted" style="cursor: pointer;">
          {{s.user_id?.name}}
        </td>

        <!-- Date -->
        <td (click)="onRowClick(s.id)" class="text-muted" style="cursor: pointer;">
          {{s.date_order | Date4Humans}}
        </td>

        <!-- Amount -->
        <td (click)="onRowClick(s.id)" class="text-muted text-end" style="cursor: pointer;">
          {{s.amount_untaxed | currency:'EUR':'symbol':'1.0-2':'it-IT'}}
        </td>

        <!-- Production Actions -->
        <td class="text-end py-0 px-2" *ngIf="actionsLoaded">
          <div class="d-flex text-nowrap text-end align-items-center justify-content-end">

            <ng-container *ngIf="s.state == 'sale' && s.delivery_status">
              <app-activity-scheduler [sale]="s"></app-activity-scheduler>
            </ng-container>

            <!-- Delivery date button -->
            <div class="dropdown me-2">
              <button class="btn btn-sm dropdown-toggle"
                [ngClass]="s.commitment_date ? 'btn-secondary text-white' : 'bg-muted text-white'"
                data-bs-auto-close="outside" type="button" data-bs-toggle="dropdown"
                [disabled]="s.delivery_status == 'full' || s.state == 'cancel'">
                <i class="fa-solid fa-calendar-days"></i>
                <span *ngIf="s.commitment_date; else noDate">
                  Consegna {{s.commitment_date | date:'dd/MM/yyyy'}}
                </span>
                <ng-template #noDate>Consegna</ng-template>
              </button>

              <div class="dropdown-menu px-3 py-3">
                <div class="mb-3">Consegna prevista</div>
                <input class="form-control mb-3" type="date"
                  [ngModel]="s.commitment_date ? (s.commitment_date | date:'yyyy-MM-dd') : ''"
                  (change)="updateCommitmentDate(s, $event.target.value)">
              </div>
            </div>

            <app-connect-to class="d-block-inline text-nowrap text-end" faSize="fa-2x"
              [driveFolders]="s.drive_folder_ids.values" [connectable]="s" type="Produzione"
              [driveSource]="cfg?.produzione_src" [driveTarget]="cfg?.produzione_dst"
              [driveTargetTitle]="s.name + ' ' + (s?.ga_title ? s?.ga_title : '') + '-' + s.partner_id.name"
              (onConnect)="onConnect(s,$event)"
              [trelloTitle]="s.name + ' ' + (s?.ga_title ? s?.ga_title : '') + '-' + s.partner_id?.name"
              [trelloCards]="s.trello_card_ids.values" [trelloListId]="cfg?.produzione_trello"
              [trelloBoardId]="cfg?.produzione_trello_board">
            </app-connect-to>
          </div>
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="8" class="text-center py-4">
          <i class="fa fa-search fa-2x text-muted mb-2"></i>
          <p class="text-muted mb-0">Nessuna vendita trovata</p>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>