import { Component, OnInit, OnDestroy, Input, ChangeDetectorRef, Output, EventEmitter } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { MrpProduction, Mrp<PERSON><PERSON><PERSON><PERSON>, Mrp<PERSON>orkcenter, MrpWorkcenterProductivity, MrpWorkcenterTag } from 'src/app/models/mrp-production';
import { User } from 'src/app/models/user.model';
import { OverlayPanel } from 'primeng/overlaypanel';
import { ODOO_IDS } from 'src/app/models/deal';

@Component({
  selector: 'app-production-workers-tab',
  templateUrl: './production-workers-tab.component.html',
  standalone: false,
})
export class ProductionWorkordersComponent implements OnInit, OnDestroy {
  // Production data
  @Input() production: MrpProduction | null = null;
  @Output() loadingChange = new EventEmitter<boolean>();
  //sectors
  sectors: string[] = ['Pavimenti', 'Tetti', 'Case', 'Facciate e Decking', 'Aziendale'];
  selectedSector: string = 'Pavimenti';

  // Workorders data
  workorders: MrpWorkorder[] = [];
  // loading: boolean = true;
  workers: User[] = [];

  // New workorder form
  newWorkorderName: string = '';
  newWorkorderWorkcenter: MrpWorkcenter | null = null;
  showNewRow: boolean = false;
  newPlannedDate: string = '';

  // Available workcenters for selection
  availableWorkcenters: MrpWorkcenter[] = [];

  // Current user
  currentUser: User | null = null;

  // Duration tracking
  activeTimers: { [workorderId: number]: any } = {};
  addHoursInput: { [workorderId: number]: number } = {};
  addHoursStartTime: { [workorderId: number]: string } = {};
  selectedWorker: User | null = null;
  errorDraft: boolean;


  constructor(
    private odooEM: OdooEntityManager,
    private changeDetectorRef: ChangeDetectorRef
  ) { }

  async ngOnInit() {

      // !!!!!VERIFY PRODUCTION IS NOT IN DRAFT OVERWISE PIALLA TUTTO
    var pro = await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), [['id', '=', this.production.id]]));
    if (pro[0].state === 'draft') {
      this.errorDraft = true;
      return;
    }
    await this.loadProduction();

    await this.loadWorkorders();
    await this.loadAllWorkcentersForSelection();
    await this.loadCurrentUser();

    // Initialize timers for active workorders
    this.updateTimers();

    console.log('[DEBUG] Workorders:', this.workorders);
    console.log('[DEBUG] showNewRow:', this.showNewRow);
  }

  async loadProduction() {
    
  }

  ngOnDestroy() {
    // Clean up all active timers
    for (const workorderId in this.activeTimers) {
      this.stopTimerForWorkorder(parseInt(workorderId));
    }
  }

  // ======= Data Loading Methods =======

  async loadWorkorders() {
      
      
      try {
        this.loadingChange.emit(true);
        await firstValueFrom(this.odooEM.resolve(this.production.workorder_ids));
        const workorders = this.production.workorder_ids.values;
        this.workorders = [...workorders]
      // if (workorders && workorders.length > 0) {
      //   this.workorders = [...workorders];

      //   // Start timers for any workorders in progress
      //   // for (const workorder of this.workorders) {
      //   //   if (workorder.is_user_working  && workorder.id) {
      //   //     this.startTimerForWorkorder(workorder);
      //   //   }
      //   // }
      // }
    } catch (error) {
      console.error('[ERROR] Failed to load workorders:', error);
    } finally {
      this.loadingChange.emit(false);
    }
  }

  async loadAllWorkcentersForSelection() {
    try {
      let workcenters = await firstValueFrom(this.odooEM.search<MrpWorkcenter>(
        new MrpWorkcenter(), [['active', '=', true]]
      ));

      //solve tags
      await firstValueFrom(this.odooEM.resolveArray(new MrpWorkcenterTag(), workcenters, "tag_ids"));

      console.log('[DEBUG] All workcenters:', workcenters);

      //only keep workcenters with a tag name that is = to the selected sector (tolowercase)
      workcenters = workcenters.filter(w => w.tag_ids?.values?.some(t => t.name.toLowerCase() === this.selectedSector.toLowerCase()));

      this.availableWorkcenters = workcenters || [];
      this.availableWorkcenters.sort((a, b) => a.name.localeCompare(b.name));
      console.log('[DEBUG] Available workcenters:', this.availableWorkcenters);
    } catch (error) {
      console.error('[ERROR] Failed to load workcenters:', error);
    }
  }

  async loadCurrentUser() {
    try {
      const result: any = await this.odooEM.odoorpcService.getSessionInfo();
      if (result?.result?.user_id) {
        const userId = result.result.user_id[0];
       // workers are the combination of current user and all active users in group ODOO_IDS.workers_group_id
        this.workers = await firstValueFrom(this.odooEM.search<User>(new User(), [['active', '=', true],['groups_id', 'in', [ODOO_IDS.workers_group_id]]]));
        let thisuser = await firstValueFrom(this.odooEM.search<User>(new User(), [['id', '=', userId]]));

        //if not already there, add the current user
        if (!this.workers.some(u => u.id === userId)) {
          this.workers.push(thisuser[0]);
        }
        const users = this.workers.filter(u => u.id === userId);

        if (users && users.length > 0) {
          this.currentUser = users[0];
        }
      }
   
    } catch (error) {
      console.error('[ERROR] Failed to load current user:', error);
    }
  }


  async addInternalWorkorder(title:string) {
    this.newWorkorderName = title;
    this.newWorkorderWorkcenter = this.availableWorkcenters.find(w => w.id === 14);

    // new work order with 1 time id of 1 hour
    const newWorkorder = {
      name: this.newWorkorderName,
      production_id: this.production.id,
      workcenter_id: 24,
      product_uom_id: this.production.product_uom_id?.id,
      costs_hour: 30,
      time_ids: [[0, 0, {
        loss_id: 7,
        workcenter_id: this.newWorkorderWorkcenter.id,
        date_start: this.formatDateForOdoo(new Date()),
        date_end: this.formatDateForOdoo(new Date(new Date().getTime() + 3600000)),
        description: 'Ore aggiunte manualmente: 1h',
      }]]
    };

    var wo = await firstValueFrom(this.odooEM.create<MrpWorkorder>(
      new MrpWorkorder(), newWorkorder
    ));

    // add to this.production workorder_ids object 
    this.production.workorder_ids.ids.push(wo.id);
    this.production.workorder_ids.values.push(wo);

    
    await this.loadWorkorders()
      // product_id: ,
      // date_planned_start: date_planned_start || false


  }

  // ======= Workorder CRUD Methods =======
  toggleNewRow() {
    this.showNewRow = !this.showNewRow;
  
    if (this.showNewRow) {
      // Reset form when opening
      this.newWorkorderName = '';
      this.newWorkorderWorkcenter = null;
      this.newPlannedDate = '';
      this.selectedWorker = this.currentUser;

    }
  }

  onSectorChange() {
    this.loadAllWorkcentersForSelection();
  }

  async addPredefinedWorkorder(type: 'oliatura' | 'verniciatura' | 'imballaggio') {
    if (!this.production) {
      console.error('[ERROR] No production available');
      return;
    }
  
  
    try {
      let workorderName: string;
      let workcenterId: number;
  
      // Set predefined values based on type
      switch (type) {
        case 'oliatura':
          workorderName = 'Mano Oliatura';
          workcenterId = 14;
          break;
        case 'verniciatura':
          workorderName = 'Mano Verniciatura';
          workcenterId = 15;
          break;
        case 'imballaggio':
          workorderName = 'Imballaggio';
          workcenterId = 22;
          break;
        default:
          console.error('[ERROR] Unknown workorder type:', type);
          return;
      }
  
      // Find the workcenter to ensure it exists
      const workcenter = this.availableWorkcenters.find(w => w.id === workcenterId);
      if (!workcenter) {
        alert(`Errore: Centro di lavoro non trovato (ID: ${workcenterId}). Verifica la configurazione.`);
        return;
      }
  
      // Set default planned date
      let date_planned_start = '';
      if (this.production.date_planned_start) {
        date_planned_start = this.production.date_planned_start;
      }
  
      // Create the predefined workorder
      const newWorkorderData = {
        name: workorderName,
        production_id: this.production.id,
        workcenter_id: workcenterId,
        product_uom_id: this.production.product_uom_id?.id,
        product_id: this.production.product_id?.id,
        date_planned_start: date_planned_start || false
      };
  
      console.log(`[DEBUG] Creating predefined workorder (${type}):`, newWorkorderData);
  
      const createdWorkorder = await firstValueFrom(this.odooEM.create<MrpWorkorder>(
        new MrpWorkorder(), newWorkorderData
      ));
  
      // Fetch the created workorder to ensure it has all required data
      const fetchedWorkorders = await firstValueFrom(this.odooEM.search<MrpWorkorder>(
        new MrpWorkorder(), [['id', '=', createdWorkorder.id]]
      ));
  
      if (fetchedWorkorders && fetchedWorkorders.length > 0) {
        this.workorders.push(fetchedWorkorders[0]);
        console.log(`[SUCCESS] Created ${type} workorder:`, fetchedWorkorders[0]);
      }
  
    } catch (error) {
      console.error(`[ERROR] Failed to create ${type} workorder:`, error);
      alert(`Errore durante la creazione dell'operazione ${type}. Riprova più tardi.`);
    } finally {
    }
  }

  async createWorkorder() {
    if (!this.newWorkorderName.trim() || !this.production || !this.newWorkorderWorkcenter) {
      return;
    }
  
  
    try {
      let date_planned_start = '';
  
      if (this.newPlannedDate) {
        const date = new Date(this.newPlannedDate);
        // Format as YYYY-MM-DD HH:MM:SS
        date_planned_start = date.getFullYear() + '-' +
          String(date.getMonth() + 1).padStart(2, '0') + '-' +
          String(date.getDate()).padStart(2, '0') + ' ' +
          String(date.getHours()).padStart(2, '0') + ':' +
          String(date.getMinutes()).padStart(2, '0') + ':' +
          String(date.getSeconds()).padStart(2, '0');
      } else if (this.production.date_planned_start) {
        date_planned_start = this.production.date_planned_start;
      }
  
      // Create a new workorder (manual entry)
      const newWorkorder = {
        name: this.newWorkorderName,
        production_id: this.production.id,
        workcenter_id: this.newWorkorderWorkcenter.id,
        product_uom_id: this.production.product_uom_id?.id,
        product_id: this.production.product_id?.id,
        date_planned_start: date_planned_start || false
      };
  
      const createdWorkorder = await firstValueFrom(this.odooEM.create<MrpWorkorder>(
        new MrpWorkorder(), newWorkorder
      ));
  
      // Fetch the created workorder to ensure it has all required data
      let works = await firstValueFrom(this.odooEM.search<MrpWorkorder>(
        new MrpWorkorder(), [['id', '=', createdWorkorder.id]]
      ));
  
      if (works && works.length > 0) {
        this.workorders.push(works[0]);
      }
  
      // Reset form
      this.resetNewWorkorderForm();
  
    } catch (error) {
      console.error('[ERROR] Failed to create manual workorder:', error);
      alert('Errore durante la creazione dell\'operazione. Riprova più tardi.');
    } finally {
    }
  }
  private resetNewWorkorderForm() {
    this.newWorkorderName = '';
    this.newWorkorderWorkcenter = null;
    this.newPlannedDate = '';
    this.showNewRow = false;
  }

  async updateWorkorder(workorder: MrpWorkorder, field: string, value: any) {
    if (!workorder || !workorder.id) return;

    try {
      const updateData: any = {};
      updateData[field] = value;

      // Format dates properly for Odoo
      if (field === 'date_planned_start' || field === 'date_planned_finished') {
        const date = new Date(value);
        const formattedDate = date.getFullYear() + '-' +
          String(date.getMonth() + 1).padStart(2, '0') + '-' +
          String(date.getDate()).padStart(2, '0') + ' ' +
          String(date.getHours()).padStart(2, '0') + ':' +
          String(date.getMinutes()).padStart(2, '0') + ':' +
          String(date.getSeconds()).padStart(2, '0');
        updateData[field] = formattedDate;
      }

      await firstValueFrom(this.odooEM.update<MrpWorkorder>(
        workorder, updateData
      ));
    } catch (error) {
      console.error('[ERROR] Failed to update workorder:', error);
      await this.loadWorkorders();
    }
  }

  async deleteWorkorder(workorder: MrpWorkorder) {
    if (!workorder || !workorder.id || !confirm(`Sei sicuro di voler eliminare "${workorder.name}"?`)) {
      return;
    }

    try {
      this.loadingChange.emit(true)
      await firstValueFrom(this.odooEM.delete<MrpWorkorder>(new MrpWorkorder(), [workorder.id]));
      this.workorders = this.workorders.filter(w => w.id !== workorder.id);
    } catch (error) {
      console.error('[ERROR] Failed to delete workorder:', error);
    } finally {
      this.loadingChange.emit(false)
    }
  }


  // add hours button
  showAddHoursOverlay(event: Event, overlay: OverlayPanel, workorder: MrpWorkorder) {
    this.initAddHoursInput(workorder); // Your existing logic
    overlay.toggle(event);
  }
  
  initAddHoursInput(workorder: MrpWorkorder) {
    if (!this.addHoursInput[workorder.id]) {
      this.addHoursInput[workorder.id] = 0;
    }
    if (!this.addHoursStartTime[workorder.id]) {
      // Set default to current time
      const now = new Date();
      const localDateTime = new Date(now.getTime() - (now.getTimezoneOffset() * 60000))
        .toISOString()
        .slice(0, 16);
      this.addHoursStartTime[workorder.id] = localDateTime;
    }

    this.selectedWorker = this.currentUser;
  }

   // Format dates for Odoo (YYYY-MM-DD HH:MM:SS)
  formatDateForOdoo = (date: Date): string => {
    return date.getFullYear() + '-' +
      String(date.getMonth() + 1).padStart(2, '0') + '-' +
      String(date.getDate()).padStart(2, '0') + ' ' +
      String(date.getHours()).padStart(2, '0') + ':' +
      String(date.getMinutes()).padStart(2, '0') + ':' +
      String(date.getSeconds()).padStart(2, '0');
  };

  async confirmAddHours(workorder: MrpWorkorder) {
    if (!workorder || !workorder.id || !this.addHoursInput[workorder.id] || this.addHoursInput[workorder.id] <= 0) {
      return;
    }

    try {
      const hoursToAdd = this.addHoursInput[workorder.id];
      const durationInMinutes = hoursToAdd * 60; // Convert hours to minutes
      const workerId = this.selectedWorker?.id || false;

      // Prepare start and end times
      let startTime: Date;
      let endTime: Date;

      // if (this.addHoursStartTime[workorder.id]) {
      //   startTime = new Date(this.addHoursStartTime[workorder.id]);
      //   endTime = new Date(startTime.getTime() + (durationInMinutes * 60000)); // Add duration in milliseconds
      // } else {
        // Default to current time minus the duration for start, current time for end
        endTime = new Date();
        startTime = new Date(endTime.getTime() - (durationInMinutes * 60000));
      // }

     

      // Create work time entry (MrpWorkcenterProductivity)
      const timeEntryData = {
        workorder_id: workorder.id,
        loss_id: 7,
        workcenter_id: workorder.workcenter_id.id,  // ← QUESTO ERA MANCANTE!
        date_start: this.formatDateForOdoo(startTime),
        date_end: this.formatDateForOdoo(endTime),
        user_id: workerId ? workerId : this.currentUser?.id,
        description: `Ore aggiunte manualmente: ${hoursToAdd}h`,
      };

      console.log('[DEBUG] Creating time entry:', timeEntryData);

      // Create the time entry
      const createdTimeEntry = await firstValueFrom(this.odooEM.create<MrpWorkcenterProductivity>(
        new MrpWorkcenterProductivity(), timeEntryData
      ));

      console.log('[DEBUG] Created time entry:', createdTimeEntry);

      // Update workorder duration locally
      if (workorder.duration) {
        workorder.duration += durationInMinutes;
      } else {
        workorder.duration = durationInMinutes;
      }

      // Clear the input values
      this.cancelAddHours(workorder);    
      console.log(`[SUCCESS] Added ${hoursToAdd} hours to workorder ${workorder.name}`);

      //we should refetch the time_ids
      await this.reloadTimeIds(workorder);

    } catch (error) {
      console.error('[ERROR] Failed to add hours to workorder:', error);
      
      // Show user-friendly error message
      alert('Errore durante l\'aggiunta delle ore. Riprova più tardi.');
      
    } finally {
    }
  }

  cancelAddHours(workorder: MrpWorkorder) {
    delete this.addHoursInput[workorder.id];
    delete this.addHoursStartTime[workorder.id];
  }

  addHours(workorder: MrpWorkorder) {
    this.initAddHoursInput(workorder);
    // The dropdown will handle the UI interaction
  }

  async deleteTimeRecord(timeRecord: MrpWorkcenterProductivity, workorder: MrpWorkorder) {
    if (!timeRecord || !timeRecord.id || !workorder) {
      console.error('[ERROR] Invalid time record or workorder');
      return;
    }
  
    // Confirm deletion
    if (!confirm('Sei sicuro di voler eliminare questo registro di tempo?')) {
      return;
    }
  
  
    try {
      console.log('[DEBUG] Deleting time record:', timeRecord.id);
  
      // Delete from Odoo
      await firstValueFrom(this.odooEM.delete<MrpWorkcenterProductivity>(
        new MrpWorkcenterProductivity(), 
        [timeRecord.id]
      ));
  
      console.log('[SUCCESS] Deleted time record:', timeRecord.id);
  
      // Remove from local workorder time_ids array
      if (workorder.time_ids && workorder.time_ids.values) {
        workorder.time_ids.values = workorder.time_ids.values.filter(
          record => record.id !== timeRecord.id
        );
        
        // Also update the IDs array if it exists
        if (workorder.time_ids.ids) {
          workorder.time_ids.ids = workorder.time_ids.ids.filter(
            id => id !== timeRecord.id
          );
        }
      }
  
      // Recalculate workorder duration by subtracting the deleted record's duration
      if (workorder.duration && timeRecord.duration) {
        workorder.duration -= timeRecord.duration;
        
        // Ensure duration doesn't go below 0
        if (workorder.duration < 0) {
          workorder.duration = 0;
        }
      }
  
      console.log('[SUCCESS] Updated local workorder duration:', workorder.duration);
  
    } catch (error) {
      console.error('[ERROR] Failed to delete time record:', error);
      alert('Errore durante l\'eliminazione del registro di tempo. Riprova più tardi.');
    } finally {
    }
  }

  showDetailsOverlay(event: Event, overlay: OverlayPanel, workorder: MrpWorkorder) {
    this.reloadTimeIds(workorder);
    overlay.toggle(event);
  }

  // ======= UI Helper Methods =======

  onWorkcenterSelected(workorder: MrpWorkorder, workcenterId: number) {
    if (!workorder || !workcenterId) return;

    // Find the workcenter object
    const workcenter = this.availableWorkcenters.find(w => w.id === workcenterId);
    if (!workcenter) return;

    // Update the workcenter relationship
    workorder.workcenter_id.id = workcenter.id;
    workorder.workcenter_id.value = workcenter;

    // Update in Odoo
    this.updateWorkorder(workorder, 'workcenter_id', workcenter.id);
  }

  onOperationNameBlur(workorder: MrpWorkorder) {
    if (!workorder) return;
    this.updateWorkorder(workorder, 'name', workorder.name);
  }

  onDatePlannedStartBlur(workorder: MrpWorkorder) {
    if (!workorder) return;
    this.updateWorkorder(workorder, 'date_planned_start', workorder.date_planned_start);
  }

  isProductionDraft(): boolean {
    return this.production && this.production.state === 'draft';
  }

  getWorkorderStatusText(w: MrpWorkorder): string {
    let result = 'Unknown';
    if (w.is_user_working) {
      result = 'In Corso';
    } else {
      switch (w.state) {
        case 'pending': result = 'In Attesa'; break;
        case 'ready': result = 'Pronto'; break;
        case 'waiting': result = 'In Attesa'; break;
        case 'progress': result = 'Iniziato'; break;
        case 'done': result = 'Completato'; break;
        case 'cancel': result = 'Annullato'; break;
        default: result = 'Unknown';             
    }
  }
    return result;
  }

  getWorkorderStatusClass(state: string): string {
    switch (state) {
      case 'pending': return 'text-warning';
      case 'ready': return 'text-primary';
      case 'waiting': return 'text-warning';
      case 'progress': return 'text-primary';
      case 'done': return 'text-success';
      case 'cancel': return 'text-muted';
      default: return 'text-secondary';
    }
  }

  // Helper methods for enhanced UI
  getWorkorderStatusIcon(w: MrpWorkorder): string {
    let result = 'fa-question-circle';
    if (w.is_user_working) {
      result = 'fa-spinner fa-spin';
    } else {
      switch (w.state) {
        case 'pending':  result = 'fa-clock';  break;
        case 'ready':  result = 'fa-thumbs-up';  break;
        case 'waiting': result = 'fa-hourglass-half';      break;
        case 'progress':   result = 'fa-pause';     break;
        case 'done':   result = 'fa-check-circle';   break;
        case 'cancel':   result = 'fa-times-circle';  break;
        default:  result = 'fa-question-circle';
      }
    }
    return result;
  }
  
  hasActiveWorkorders(): boolean {
    return this.workorders.some(w => w.is_user_working);
  }
  
  getActiveWorkordersCount(): number {
    return this.workorders.filter(w => w.is_user_working).length;
  }
  
  getTotalCompletedWorkordersCount(): number {
    return this.workorders.filter(w => w.state === 'done').length;
  }
  
  getTotalWorkordersTime(): string {
    const totalDuration = this.workorders.reduce((total, workorder) => {
      return total + (workorder.duration || 0);
    }, 0);
    
    return this.formatDuration(totalDuration);
  }


  async reloadTimeIds(workorder: MrpWorkorder) {
    // here we empty the timeids for the workorder and resolve them again
  if (!workorder || !workorder.id) {
    return;
  }
  if (workorder.time_ids && workorder.time_ids.ids.length > 0) {
    workorder.time_ids.values = [];
  }

  let timeIds = await firstValueFrom(this.odooEM.search <MrpWorkcenterProductivity>(
    new MrpWorkcenterProductivity(), [['workorder_id', '=', workorder.id]]
  ));

  //rewrite ids and values of the workcenter 
  workorder.time_ids.ids = timeIds.map(t => t.id);
  workorder.time_ids.values = timeIds.map(t => t);


}


  // ======= Workorder Action Methods =======

  async startWorkorder(workorder: MrpWorkorder) {
    if (!workorder || !workorder.id) return;


    try {
      // Call the button_start method
      await this.odooEM.call2(
        'mrp.workorder',
        'button_start',
        [[workorder.id]]
      );

      // Update local state
      workorder.is_user_working = true;
      

      // Start fetching duration for this workorder
      this.startTimerForWorkorder(workorder);
    } catch (error) {
      console.error('[ERROR] Failed to start workorder:', error);
      await this.loadWorkorders();
    } finally {
    }
  }

  async pauseWorkorder(workorder: MrpWorkorder) {
    if (!workorder || !workorder.id) return;

    try {
      // First, get the final duration before pausing
      try {
        const response = await this.odooEM.call2(
          'mrp.workorder',
          'get_working_duration',
          [[workorder.id]]
        );

        // Save the final duration value
        if (response !== null && response !== undefined) {
          const duration = Number(response);
          if (!isNaN(duration)) {
            workorder.duration = duration;
          }
        }
      } catch (e) {
        console.error('[ERROR] Error fetching final duration before pause');
      }

      // Stop the duration refresh
      this.stopTimerForWorkorder(workorder.id);

      // Call the button_pending method
      await this.odooEM.call2(
        'mrp.workorder',
        'button_pending',
        [[workorder.id]]
      );

      // Update local state
      workorder.is_user_working = false;
    } catch (error) {
      console.error('[ERROR] Failed to pause workorder:', error);
      await this.loadWorkorders();
    } finally {
    }
  }

  async completeWorkorder(workorder: MrpWorkorder) {
    if (!workorder || !workorder.id) return;


    try {
      // First, get the final duration before completing
      try {
        if (!confirm("Sei sicuro di voler completare l'operazione?")) {
          return;
          }
        const response = await this.odooEM.call2(
          'mrp.workorder',
          'get_working_duration',
          [[workorder.id]]
        );

        // Save the final duration value
        if (response !== null && response !== undefined) {
          const duration = Number(response);
          if (!isNaN(duration)) {
            workorder.duration = duration;
          }
        }
        
      } catch (e) {
        console.error('[ERROR] Error fetching final duration before completion');
      }

      // Stop the duration refresh
      this.stopTimerForWorkorder(workorder.id);

      // Call the button_finish method
      await this.odooEM.call2(
        'mrp.workorder',
        'button_finish',
        [[workorder.id]]
      );

      // Update local state
      workorder.state = 'done';
      workorder.is_user_working = false;
    } catch (error) {
      console.error('[ERROR] Failed to complete workorder:', error);
      await this.loadWorkorders();
    } finally {
    }
  }

    // ======= Duration Management Methods =======

    async getWorkingDuration(workorderId: number): Promise<number> {
      if (!workorderId) return 0;
  
      try {
        const response = await this.odooEM.call2(
          'mrp.workorder',
          'get_working_duration',
          [[workorderId]]
        );
  
        return typeof response === 'number' ? response : 0;
      } catch (error) {
        console.error(`[ERROR] Failed to get duration for workorder ${workorderId}:`, error);
        return 0;
      }
    }
  
    formatDuration(duration: number | null | undefined): string {
      if (duration === null || duration === undefined || isNaN(duration)) {
        return '00:00:00';
      }
    
      try {
        const durationNum = Number(duration);
        if (isNaN(durationNum)) {
          return '00:00:00';
        }
        
        // Convert minutes to seconds
        const totalSeconds = durationNum * 60;
        
        // Calculate hours
        const hours = Math.floor(totalSeconds / 3600);
        
        // Calculate minutes
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        
        // Calculate seconds
        const seconds = Math.floor(totalSeconds % 60);
        
        // Format with padding to ensure HH:MM:SS format
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      } catch (error) {
        console.error('[ERROR] Error formatting duration:', error);
        return '00:00:00';
      }
    }
  
    startTimerForWorkorder(workorder: MrpWorkorder) {
      // Clear any existing timer for this workorder
      let oldDuration = workorder.duration || 0;
      if (this.activeTimers[workorder.id]) {
        clearInterval(this.activeTimers[workorder.id]);
      }
  
  
      // Set up interval to fetch the duration every 2 seconds
      this.activeTimers[workorder.id] = setInterval(async () => {
  
        try {
          const response = await this.odooEM.call2(
            'mrp.workorder',
            'get_working_duration',
            [[workorder.id]]
          );
  
          console.log(`[DEBUG] Duration for workorder :`, workorder, response);
  
          if (response.result !== null && response.result !== undefined) {
            //response.result is the number of minutes elapsed since start. ex 2.5 is 2 minutes and 30 seconds to add to the duration of the workorder
            const duration = Number(response.result);
            console.log(`[DEBUG] Duration for workorder ${workorder.id}:`, duration);
            //update ui
            if (!isNaN(duration)) {
              workorder.duration = duration + oldDuration;
              this.changeDetectorRef.detectChanges();
            }
          }
        } catch (error) {
          console.error(`[ERROR] Failed to get duration for workorder ${workorder.id}:`, error);
        }
      }, 2000);
    }
  
    stopTimerForWorkorder(workorderId: number) {
      if (this.activeTimers[workorderId]) {
        clearInterval(this.activeTimers[workorderId]);
        delete this.activeTimers[workorderId];
      }
    }
  
    updateTimers() {
      // Stop timers for workorders that are not in progress
      for (const workorderId in this.activeTimers) {
        const id = parseInt(workorderId);
        const workorder = this.workorders.find(w => w.id === id);
  
        if (!workorder || !workorder.is_user_working ) {
          this.stopTimerForWorkorder(id);
        }
      }
  
      // Start timers for workorders in progress without active timers
      for (const workorder of this.workorders) {
        if (workorder.is_user_working && workorder.id && !this.activeTimers[workorder.id]) {
          this.startTimerForWorkorder(workorder);
        }
      }
    }
}