import { Component, TemplateRef } from '@angular/core';
import { ToastService } from '../../services/toast.service';

@Component({
    selector: 'app-toasts',
    template: `
    <ngb-toast
      *ngFor="let toast of toastService.toasts"
      class="text-white"
      [ngClass]="toast.classname ? ( toast && toast.classname) : 'bg-success'"
      [autohide]="true"
      [delay]="toast.delay || 500"
      (hidden)="toastService.remove(toast)"
    >
      <ng-template [ngIf]="isTemplate(toast)" [ngIfElse]="text">
        <ng-template [ngTemplateOutlet]="toast.textOrTpl"></ng-template>
      </ng-template>

      <ng-template #text>{{ toast.textOrTpl }}
        <button class="btn text-white" (click)="toastService.remove(toast)">X</button>
      </ng-template>
    </ngb-toast>
  `,
    host: { '[class.ngb-toasts]': 'true' },
    standalone: false
})
// tslint:disable-next-line:component-class-suffix
export class ToastsContainerComponent {
  constructor(public toastService: ToastService) {
  }

  isTemplate(toast) {
    return toast.textOrTpl instanceof TemplateRef;
  }
}
