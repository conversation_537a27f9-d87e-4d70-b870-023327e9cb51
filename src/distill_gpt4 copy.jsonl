    {"prompt": "What are the names of all active products in our system?","completion": "SELECT name FROM product_template WHERE active = true;"}
    {"prompt": "Can you tell me the total sales for a particular product?","completion": "SELECT SUM(product_uom_qty) FROM sale_order_line WHERE product_id = <product_id>;"}
    {"prompt": "Who are the customers from a particular country?","completion": "SELECT name FROM res_partner WHERE country_id = <country_id> AND customer_rank > 0;"}
    {"prompt": "What is the current inventory level of a specific product?","completion": "SELECT quantity FROM stock_quant WHERE product_id = <product_id>;"}
    {"prompt": "What is the cost of a specific product?","completion": "SELECT standard_price FROM product_template WHERE id = <product_id>;"}
    {"prompt": "What are the variants of a particular product?","completion": "SELECT name FROM product_product WHERE product_tmpl_id = <product_id>;"}
    {"prompt": "What is the sale price of a specific product?","completion": "SELECT list_price FROM product_template WHERE id = <product_id>;"}
    {"prompt": "What are the products in a specific category?","completion": "SELECT name FROM product_template WHERE categ_id = <category_id>;"}
    {"prompt": "What are the products supplied by a specific vendor?","completion": "SELECT pt.name FROM product_supplierinfo ps INNER JOIN product_template pt ON ps.product_tmpl_id = pt.id WHERE ps.name = <supplier_id>;"}
    {"prompt": "What is the weight of a specific product?","completion": "SELECT weight FROM product_template WHERE id = <product_id>;"}
    {"prompt": "What is the volume of a specific product?","completion": "SELECT volume FROM product_template WHERE id = <product_id>;"}
    {"prompt": "Is a specific product a consumable?","completion": "SELECT type FROM product_template WHERE id = <product_id> AND type = 'consu';"}
    {"prompt": "Is a specific product a service?","completion": "SELECT type FROM product_template WHERE id = <product_id> AND type = 'service';"}
    {"prompt": "Is a specific product a storable product?","completion": "SELECT type FROM product_template WHERE id = <product_id> AND type = 'product';"}
    {"prompt": "What are the products that can be purchased?","completion": "SELECT name FROM product_template WHERE purchase_ok = true;"}
    {"prompt": "What are the products that can be sold?","completion": "SELECT name FROM product_template WHERE sale_ok = true;"}
    {"prompt": "What are the products with warranty?","completion": "SELECT name FROM product_template WHERE warranty > 0;"}
    {"prompt": "Which products have been archived?","completion": "SELECT name FROM product_template WHERE active = false;"}
    {"prompt": "What products have a certain responsible person?","completion": "SELECT name FROM product_template WHERE responsible_id = <responsible_id>;"}
    {"prompt": "What are the products with a specific internal reference?","completion": "SELECT name FROM product_template WHERE default_code = '<default_code>';"}
    {"prompt": "What products have a specific barcode?","completion": "SELECT name FROM product_product WHERE barcode = '<barcode>';"}
    {"prompt": "What are the products with a specific Public Category?","completion": "SELECT pt.name FROM product_public_category_product_template_rel pcptr INNER JOIN product_template pt ON pcptr.product_template_id = pt.id WHERE pcptr.product_public_category_id = <public_category_id>;"}
    {"prompt": "What are the products with a specific EAN13 barcode?","completion": "SELECT name FROM product_product WHERE ean13 = '<ean13>';"}
    {"prompt": "What is the supplier information for a specific product?","completion": "SELECT name, delay, min_qty, price FROM product_supplierinfo WHERE product_tmpl_id = <product_id>;"}
    {"prompt": "What are the products with a certain Unit of Measure?","completion": "SELECT name FROM product_template WHERE uom_id = <uom_id>;"}
    {"prompt": "What are the products with a certain Purchase Unit of Measure?","completion": "SELECT name FROM product_template WHERE uom_po_id = <uom_po_id>;"}
    {"prompt": "What are the products with a specific route (like Buy, Manufacture, or Make to Order)?","completion": "SELECT pt.name FROM stock_location_route_product_template_rel slrptr INNER JOIN product_template pt ON slrptr.product_template_id = pt.id WHERE slrptr.stock_location_route_id = <route_id>;"}
    {"prompt": "What products have a specific company as their company?","completion": "SELECT name FROM product_template WHERE company_id = <company_id>;"}
    {"prompt": "What products have a specific taxes assigned?","completion": "SELECT pt.name FROM account_tax_product_template_rel atr INNER JOIN product_template pt ON atr.product_template_id = pt.id WHERE atr.account_tax_id = <tax_id>;"}
    {"prompt": "What products have a specific vendor taxes assigned?","completion": "SELECT pt.name FROM account_tax_product_template_rel atr INNER JOIN product_template pt ON atr.product_template_id = pt.id WHERE atr.account_tax_id = <vendor_tax_id>;"}
    {"prompt": "What products have a specific attribute?","completion": "SELECT pt.name FROM product_template_attribute_line ptal INNER JOIN product_template pt ON ptal.product_tmpl_id = pt.id WHERE ptal.attribute_id = <attribute_id>;"}
    {"prompt": "What products have a specific attribute value?","completion": "SELECT pt.name FROM product_template_attribute_value ptav INNER JOIN product_template pt ON ptav.product_tmpl_id = pt.id WHERE ptav.product_attribute_value_id = <attribute_value_id>;"}
    {"prompt": "What are the products with specific seller?","completion": "SELECT pt.name FROM product_supplierinfo ps INNER JOIN product_template pt ON ps.product_tmpl_id = pt.id WHERE ps.name = <seller_id>;"}
    {"prompt": "What are the products with a specific route (like Buy, Manufacture, or Make to Order)?","completion": "SELECT pt.name FROM stock_location_route_product_template_rel slrptr INNER JOIN product_template pt ON slrptr.product_template_id = pt.id WHERE slrptr.stock_location_route_id = <route_id>;"}
    {"prompt": "What are the products with a specific sales team?","completion": "SELECT pt.name FROM product_template_sale_team_rel ptsr INNER JOIN product_template pt ON ptsr.product_template_id = pt.id WHERE ptsr.crm_team_id = <sales_team_id>;"}
    {"prompt": "What products are part of a specific pricelist?","completion": "SELECT pt.name FROM product_pricelist_item ppi INNER JOIN product_template pt ON ppi.product_tmpl_id = pt.id WHERE ppi.pricelist_id = <pricelist_id>;"}
    {"prompt": "What are the products with a certain route (like Buy, Manufacture, or Make to Order)?","completion": "SELECT pt.name FROM stock_location_route_product_template_rel slrptr INNER JOIN product_template pt ON slrptr.product_template_id = pt.id WHERE slrptr.stock_location_route_id = <route_id>;"}
    {"prompt": "What are the products with a specific sales team?","completion": "SELECT pt.name FROM product_template_sale_team_rel ptsr INNER JOIN product_template pt ON ptsr.product_template_id = pt.id WHERE ptsr.crm_team_id = <sales_team_id>;"}
    {"prompt": "What products have a specific fiscal position?","completion": "SELECT pt.name FROM product_template pt INNER JOIN account_fiscal_position_product_template_rel afp ON pt.id = afp.product_template_id WHERE afp.account_fiscal_position_id = <fiscal_position_id>;"}
    {"prompt": "What products have a specific product category?","completion": "SELECT name FROM product_template WHERE categ_id = <category_id>;"}
    {"prompt": "What products have a specific internal category?","completion": "SELECT name FROM product_template WHERE categ_id = <internal_category_id>;"}
    {"prompt": "What products have a specific internal reference (default_code)?","completion": "SELECT name FROM product_template WHERE default_code = '<default_code>';"}
    {"prompt": "What products have a specific barcode?","completion": "SELECT name FROM product_product WHERE barcode = '<barcode>';"}
    {"prompt": "What products have a specific EAN13 barcode?","completion": "SELECT name FROM product_product WHERE ean13 = '<ean13>';"}
    {"prompt": "What products have a specific public category?","completion": "SELECT pt.name FROM product_public_category_product_template_rel pcptr INNER JOIN product_template pt ON pcptr.product_template_id = pt.id WHERE pcptr.product_public_category_id = <public_category_id>;"}
    {"prompt": "What products have a specific weight?","completion": "SELECT name FROM product_template WHERE weight = <weight>;"}
    {"prompt": "What products have a specific volume?","completion": "SELECT name FROM product_template WHERE volume = <volume>;"}
    {"prompt": "What products are labeled as 'Can be Expensed'?","completion": "SELECT name FROM product_template WHERE can_be_expensed = true;"}
    {"prompt": "What products have a specific sales unit of measure?","completion": "SELECT name FROM product_template WHERE uom_id = <uom_id>;"}
    {"prompt": "What products are part of a specific product pack?","completion": "SELECT pt.name FROM product_pack_line ppl INNER JOIN product_template pt ON ppl.product_id = pt.id WHERE ppl.parent_product_id = <parent_product_id>;"}
    {"prompt": "What products have a specific route (like Buy, Manufacture, Replenish on Order)?","completion": "SELECT pt.name FROM stock_location_route_product_template_rel slrptr INNER JOIN product_template pt ON slrptr.product_template_id = pt.id WHERE slrptr.stock_location_route_id = <route_id>;"}
    {"prompt": "What products have a specific seller?","completion": "SELECT pt.name FROM product_supplierinfo ps INNER JOIN product_template pt ON ps.product_tmpl_id = pt.id WHERE ps.name = <seller_id>;"}
    {"prompt": "What products have a specific attribute?","completion": "SELECT pt.name FROM product_template_attribute_line ptal INNER JOIN product_template pt ON ptal.product_tmpl_id = pt.id WHERE ptal.attribute_id = <attribute_id>;"}
    {"prompt": "What products have a specific attribute value?","completion": "SELECT pt.name FROM product_template_attribute_value ptav INNER JOIN product_template pt ON ptav.product_tmpl_id = pt.id WHERE ptav.product_attribute_value_id = <attribute_value_id>;"}
    {"prompt": "What products are labeled as 'Can be Purchased'?","completion": "SELECT name FROM product_template WHERE purchase_ok = true;"}
    {"prompt": "What products are labeled as 'Can be Sold'?","completion": "SELECT name FROM product_template WHERE sale_ok = true;"}
    {"prompt": "What products are labeled as 'Can be Expensed'?","completion": "SELECT name FROM product_template WHERE can_be_expensed = true;"}
    {"prompt": "What products are labeled as 'Can be Rented'?","completion": "SELECT name FROM product_template WHERE rental = true;"}
    {"prompt": "What products have a specific cost?","completion": "SELECT name FROM product_template WHERE standard_price = <standard_price>;"}
    {"prompt": "What products have a specific sale price?","completion": "SELECT name FROM product_template WHERE list_price = <list_price>;"}
    {"prompt": "What products have a specific tax?","completion": "SELECT pt.name FROM account_tax_product_template_rel atr INNER JOIN product_template pt ON atr.product_template_id = pt.id WHERE atr.account_tax_id = <tax_id>;"}
    {"prompt": "What products have a specific internal category?","completion": "SELECT name FROM product_template WHERE categ_id = <internal_category_id>;"}
    {"prompt": "What products have a specific external category?","completion": "SELECT pt.name FROM product_public_category_product_template_rel pcptr INNER JOIN product_template pt ON pcptr.product_template_id = pt.id WHERE pcptr.product_public_category_id = <external_category_id>;"}    
    {"prompt": "What products have a specific responsible?","completion": "SELECT name FROM product_template WHERE responsible_id = <responsible_id>;"}
    {"prompt": "What products have a specific sales margin?","completion": "SELECT name FROM product_template WHERE margin = <margin>;"}
    {"prompt": "What products have a specific image?","completion": "SELECT name FROM product_template WHERE image_1920 = '<image_1920>';"}
    {"prompt": "What products have a specific product manager?","completion": "SELECT name FROM product_template WHERE product_manager = <product_manager>;"}
    {"prompt": "What products have a specific supplier?","completion": "SELECT pt.name FROM product_supplierinfo ps INNER JOIN product_template pt ON ps.product_tmpl_id = pt.id WHERE ps.name = <supplier_id>;"}
    {"prompt": "What products have a specific product category?","completion": "SELECT name FROM product_template WHERE categ_id = <category_id>;"}
    {"prompt": "What products have a specific internal reference (default_code)?","completion": "SELECT name FROM product_template WHERE default_code = '<default_code>';"}
    {"prompt": "What products have a specific barcode?","completion": "SELECT name FROM product_product WHERE barcode = '<barcode>';"}
    {"prompt": "What products have a specific EAN13 barcode?","completion": "SELECT name FROM product_product WHERE ean13 = '<ean13>';"}
    {"prompt": "What products have a specific public category?","completion": "SELECT pt.name FROM product_public_category_product_template_rel pcptr INNER JOIN product_template pt ON pcptr.product_template_id = pt.id WHERE pcptr.product_public_category_id = <public_category_id>;"}
    {"prompt": "What products have a specific weight?","completion": "SELECT name FROM product_template WHERE weight = <weight>;"}
    {"prompt": "What products have a specific volume?","completion": "SELECT name FROM product_template WHERE volume = <volume>;"}
    {"prompt": "What products are labeled as 'Can be Expensed'?","completion": "SELECT name FROM product_template WHERE can_be_expensed = true;"}
    {"prompt": "What products have a specific sales unit of measure?","completion": "SELECT name FROM product_template WHERE uom_id = <uom_id>;"}
    {"prompt": "What products have a specific purchase unit of measure?","completion": "SELECT name FROM product_template WHERE uom_po_id = <uom_po_id>;"}
    {"prompt": "What are the variants of a specific product?","completion": "SELECT name_template FROM product_product WHERE product_tmpl_id = <product_template_id>;"}
    {"prompt": "What are the attribute values of a specific product variant?","completion": "SELECT pav.name FROM product_attribute_value_product_product_rel pavppr INNER JOIN product_attribute_value pav ON pavppr.product_attribute_value_id = pav.id WHERE pavppr.product_product_id = <product_variant_id>;"}
    {"prompt": "What are the products associated with a specific attribute?","completion": "SELECT pt.name FROM product_template_attribute_line ptal INNER JOIN product_template pt ON ptal.product_tmpl_id = pt.id WHERE ptal.attribute_id = <attribute_id>;"}
    {"prompt": "What are the product variants associated with a specific attribute value?","completion": "SELECT pp.name_template FROM product_attribute_value_product_product_rel pavppr INNER JOIN product_product pp ON pavppr.product_product_id = pp.id WHERE pavppr.product_attribute_value_id = <attribute_value_id>;"}
    {"prompt": "What are the attributes associated with a specific product template?","completion": "SELECT pa.name FROM product_template_attribute_line ptal INNER JOIN product_attribute pa ON ptal.attribute_id = pa.id WHERE ptal.product_tmpl_id = <product_template_id>;"}
    {"prompt": "What are the attributes associated with a specific product variant?","completion": "SELECT pa.name FROM product_attribute_value_product_product_rel pavppr INNER JOIN product_attribute_value pav ON pavppr.product_attribute_value_id = pav.id INNER JOIN product_attribute pa ON pav.attribute_id = pa.id WHERE pavppr.product_product_id = <product_variant_id>;"}
    {"prompt": "What are the attribute values of a specific attribute?","completion": "SELECT name FROM product_attribute_value WHERE attribute_id = <attribute_id>;"}
    {"prompt": "What is the product variant with a specific internal reference (default_code)?","completion": "SELECT name_template FROM product_product WHERE default_code = '<default_code>';"}
    {"prompt": "What is the product variant with a specific barcode?","completion": "SELECT name_template FROM product_product WHERE barcode = '<barcode>';"}
    {"prompt": "What are the product variants of a specific product category?","completion": "SELECT pp.name_template FROM product_product pp INNER JOIN product_template pt ON pp.product_tmpl_id = pt.id WHERE pt.categ_id = <category_id>;"}
    {"prompt": "What are the product variants with a specific weight?","completion": "SELECT name_template FROM product_product WHERE weight = <weight>;"}
    {"prompt": "What are the product variants with a specific volume?","completion": "SELECT name_template FROM product_product WHERE volume = <volume>;"}
    {"prompt": "What are the product variants with a specific purchase unit of measure?","completion": "SELECT name_template FROM product_product WHERE uom_id = <uom_id>;"}
    {"prompt": "What are the product variants with a specific sales unit of measure?","completion": "SELECT name_template FROM product_product WHERE uom_po_id = <uom_po_id>;"}
    {"prompt": "What are the sales orders for a specific product?",
        "query": "SELECT so.name FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id WHERE sol.product_id = <product_id>;"
    },
    {"prompt": "What are the products in a specific sales order?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN product_product pp ON sol.product_id = pp.id WHERE sol.order_id = <order_id>;"
    },
    {"prompt": "What are the sales orders by a specific customer?",
        "query": "SELECT name FROM sale_order WHERE partner_id = <partner_id>;"
    },
    {"prompt": "What are the sales orders with a specific total amount?",
        "query": "SELECT name FROM sale_order WHERE amount_total = <total_amount>;"
    },
    {"prompt": "What are the sales orders in a specific state (like 'Quotation', 'Sales Order', 'Locked')?",
        "query": "SELECT name FROM sale_order WHERE state = '<state>';"
    },
    {"prompt": "What are the sales orders confirmed on a specific date?",
        "query": "SELECT name FROM sale_order WHERE confirmation_date::date = '<date>';"
    },
    {"prompt": "What are the products in the sales orders confirmed on a specific date?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id INNER JOIN product_product pp ON sol.product_id = pp.id WHERE so.confirmation_date::date = '<date>';"
    },
    {"prompt": "What are the sales orders with a specific salesperson?",
        "query": "SELECT name FROM sale_order WHERE user_id = <user_id>;"
    },
    {"prompt": "What are the sales orders with a specific sales team?",
        "query": "SELECT name FROM sale_order WHERE team_id = <team_id>;"
    },
    {"prompt": "What are the products in the sales orders with a specific salesperson?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id INNER JOIN product_product pp ON sol.product_id = pp.id WHERE so.user_id = <user_id>;"
    },
    {"prompt": "What are the products in the sales orders with a specific sales team?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id INNER JOIN product_product pp ON sol.product_id = pp.id WHERE so.team_id = <team_id>;"
    },
    {"prompt": "What are the sales orders with a specific delivery address?",
        "query": "SELECT name FROM sale_order WHERE partner_shipping_id = <partner_shipping_id>;"
    },
    {"prompt": "What are the products in the sales orders with a specific delivery address?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id INNER JOIN product_product pp ON sol.product_id = pp.id WHERE so.partner_shipping_id = <partner_shipping_id>;"
    },
    {"prompt": "What are the sales orders with a specific invoice address?",
        "query": "SELECT name FROM sale_order WHERE partner_invoice_id = <partner_invoice_id>;"
    },
    questions_and_queries = [
    {"prompt": "Which sales orders contain a particular product?",
        "query": "SELECT so.name FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id WHERE sol.product_id = <product_id>;"
    },
    {"prompt": "Which products are included in a specific sales order?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN product_product pp ON sol.product_id = pp.id WHERE sol.order_id = <order_id>;"
    },
    {"prompt": "Which sales orders are associated with a specific customer?",
        "query": "SELECT name FROM sale_order WHERE partner_id = <partner_id>;"
    },
    {"prompt": "Which sales orders have a certain total amount?",
        "query": "SELECT name FROM sale_order WHERE amount_total = <total_amount>;"
    },
    {"prompt": "Which sales orders have a particular status (e.g., 'Quotation', 'Sales Order', 'Locked')?",
        "query": "SELECT name FROM sale_order WHERE state = '<state>';"
    },
    {"prompt": "Which sales orders were confirmed on a specific date?",
        "query": "SELECT name FROM sale_order WHERE confirmation_date::date = '<date>';"
    },
    {"prompt": "Which products are in the sales orders that were confirmed on a certain date?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id INNER JOIN product_product pp ON sol.product_id = pp.id WHERE so.confirmation_date::date = '<date>';"
    },
    {"prompt": "Which sales orders are assigned to a specific salesperson?",
        "query": "SELECT name FROM sale_order WHERE user_id = <user_id>;"
    },
    {"prompt": "Which sales orders are associated with a specific sales team?",
        "query": "SELECT name FROM sale_order WHERE team_id = <team_id>;"
    },
    {"prompt": "Which products are in the sales orders assigned to a certain salesperson?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id INNER JOIN product_product pp ON sol.product_id = pp.id WHERE so.user_id = <user_id>;"
    },
    {"prompt": "Which products are in the sales orders associated with a specific sales team?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id INNER JOIN product_product pp ON sol.product_id = pp.id WHERE so.team_id = <team_id>;"
    },
    {"prompt": "Which sales orders have a certain delivery address?",
        "query": "SELECT name FROM sale_order WHERE partner_shipping_id = <partner_shipping_id>;"
    },
    {"prompt": "Which products are in the sales orders with a particular delivery address?",
        "query": "SELECT pp.name_template FROM sale_order_line sol INNER JOIN sale_order so ON sol.order_id = so.id INNER JOIN product_product pp ON sol.product_id = pp.id WHERE so.partner_shipping_id = <partner_shipping_id>;"
    },
    {"prompt": "Which sales orders have a specific invoice address?",
        "query": "SELECT name FROM sale_order WHERE partner_invoice_id = <partner_invoice_id>;"
    }