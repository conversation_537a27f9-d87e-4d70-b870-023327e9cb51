import { Component, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { SaleOrder } from 'src/app/models/sale-order.model';
import { SaleOrderLine } from 'src/app/models/sale-order-line.model';
import { StockMove } from 'src/app/models/stock-move';
import { ODOO_IDS } from 'src/app/models/deal';
import { PurchaseOrderLine } from '../models/purchase-order-line.model';
import { MrpProduction } from '../models/mrp-production';
import { ActivatedRoute, Router } from '@angular/router';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { firstValueFrom } from 'rxjs';

@Component({
    selector: 'app-transfers-table',
    templateUrl: './transfers-table.component.html',
    standalone: false
})
export class TransfersTableComponent {
  @Input() saleOrder: SaleOrder;
  @Input() orderLine: SaleOrderLine;
  @Input() productions?: MrpProduction[];
  @Input() purchases?: PurchaseOrderLine[];
  @Output() loading = new EventEmitter<boolean>();
  preProductions: MrpProduction[] = [];

  // Location priority mapping
  private readonly LOCATION_PRIORITY = {
    4: 1,   // vendor
    8: 2,   // giacenza
    43: 3,  // H3
    48: 4,  // cap case
    41: 5,  // spedizioni
    301: 6, // carico trasporto
    5: 7    // customer
  };

  // State priority mapping
  private readonly STATE_PRIORITY = {
    'done': 1,
    'assigned': 2,
    'confirmed': 3,
    'partially_available': 4,
    'waiting': 5
  };

    // Constructor to inject services
    constructor(
      private route: ActivatedRoute,
      private router: Router,
      private odooEm: OdooEntityManager,
      private changeDetectorRef: ChangeDetectorRef
    ) {}
  // Get location priority (lower number = higher priority)
  private getLocationPriority(locationId: number): number {
    return this.LOCATION_PRIORITY[locationId] || 999; // Unknown locations go last
  }

  // Get state priority (lower number = higher priority)
  private getStatePriority(state: string): number {
    return this.STATE_PRIORITY[state] || 999; // Unknown states go last
  }

  async ngOnInit() {

    console.log ("ULLALLERO ULLALLA getting transfers data from sale order", this.saleOrder);
    console.log ("ULLALLERO ULLALLA getting transfers data from order line", this.orderLine);
    console.log ("ULLALLERO ULLALLA getting transfers data from productions", this.productions);
    console.log ("ULLALLERO ULLALLA getting transfers data from purchases", this.purchases);

    //fetch pre-prductions that are productions with origin = name of productions
    if (this.productions) {

      this.preProductions = await firstValueFrom(this.odooEm.search<MrpProduction>(new MrpProduction(), [["origin", "in", this.productions.map(p => p.name)]]));
      console.log("ULLALLERO ULLALLA getting transfers data from pre-productions", this.preProductions);
    }

  }

  // Get filtered moves for the current line
  getMoves(): StockMove[] {
    if (!this.saleOrder.procurement_group_id?.value?.stock_move_ids?.values) {
      return [];
    }

    //filter out incoming moves from vendors cause we show them in the purchases section
    this.saleOrder.procurement_group_id.value.stock_move_ids.values = this.saleOrder.procurement_group_id.value.stock_move_ids.values.filter(move => 
      move.picking_code !== 'incoming' ||
      move.location_id.id !== ODOO_IDS.stock_location_vendor
    );
  

    const filteredMoves:StockMove[] = this.saleOrder.procurement_group_id.value.stock_move_ids.values.filter(move => 
      move.product_id.id === this.orderLine.product_id.id &&
      move.state !== 'cancel' &&
      move.state !== 'draft' &&
      (move.product_uom_qty > 0 || move.quantity_done > 0)
    );
  
     // Sort by location priority first, then by state priority
     var y = filteredMoves.sort((a, b) => {
      // Compare location priorities
      const locationComparison = this.getLocationPriority(a.location_id.id) - 
                                this.getLocationPriority(b.location_id.id);
      
      // If locations are different, return location comparison
      if (locationComparison !== 0) {
        return locationComparison;
      }
      
      // If locations are the same, sort by state priority
      return this.getStatePriority(a.state) - this.getStatePriority(b.state);
    });
    //merge moves with same picking id
    y = y.reduce((acc, move) => {
      const existingMove = acc.find(m => m.picking_id.id === move.picking_id.id);
      if (existingMove) {
        console.log("xx",existingMove, move);
        // existingMove.product_uom_qty += move.product_uom_qty;
        existingMove.quantity_done += move.quantity_done;
      } else {
        acc.push(move);
      }
      return acc;
    }, []);
   
    return y;
  }


  

  getArrivedDate(purchase: PurchaseOrderLine) {
    if (purchase) return purchase.date_planned
  }


  // Helper to get state display text
  getStateDisplay(state: string): string {
    const states = {
      'waiting': 'In attesa',
      'partially_available': 'Da fare',
      'confirmed': 'Da fare',
      'assigned': 'Da fare',
      'done': 'Completato',
      'pending': 'In Arrivo',
      'full': 'Completato',
      'partial': 'Parziale',
      'false': '-',
      'draft': 'Bozza'
    };
    return states[state] || state;
  }

  // Helper to get state text color class
  getStateClass(state: string): string {
    const classes = {
      'waiting': 'text-warning',
      'partially_available': 'text-primary',
      'confirmed': 'text-primary',
      'assigned': 'text-primary',
      'done': 'text-success',
      'pending': 'text-warning',
      'full': 'text-success',
      'partial': 'text-primary',
      'false': 'text-muted',
      'draft': 'text-muted'
    };
    return classes[state] || '';
  }


  
  // Format location name by removing 'LOM/' prefix
  formatLocation(location: string): string {
    return location?.replace('LOM/', '') || '';
  }

  // Handle button click to prevent dropdown close
  onButtonClick(event: Event): void {
    event.stopPropagation();
  }
  

  onMoveClick(move: StockMove) {
    console.log('move clicked', move);
    let id = move.picking_id.id;
    window.open(`/pickings/internal/${id}`, '_blank');

  }

  getQuantity(move: StockMove): number {
    if (move.state === 'done') {
      return move.quantity_done;
    }
    else {
      return move.product_uom_qty;
    }
  }


  async onClickPurchase(purchase) {
    
    this.router.navigate(["purchase", purchase.order_id.id], {
      relativeTo: this.route,
    });
    //}
  }

  async onClickPreProduction(p: MrpProduction){
    console.log('pre production clicked', );
    let id = p.id
    window.open(`/productions-ext/${id}`, '_blank');
  }

  async onClickProduction(p: MrpProduction){
    console.log('poost production clicked', );
    let id = p.id
    window.open(`/pickings/production/${id}`, '_blank');
  }

  async onViewPickings(s: SaleOrder) {
    this.router.navigate(["/pickings"], {
      relativeTo: this.route,
      queryParams: { search: s.name },
    });
  }
}