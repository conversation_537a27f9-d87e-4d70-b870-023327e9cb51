import { UomUom } from './uom-uom.model';
import { Partner } from './partner';
import { AccountMove } from './account-move.model';
import { AccountMoveLine } from './account-move-line.model';
import { OdooMultiRelationship } from './odoo-multi-relationship.model';
import { OdooRelationship } from './odoo-relationship.model';
import { ProductCategory } from './product.category';
import { OdooSerializableInterface } from '../shared/interfaces/odoo-serializable-interface';
import { OdooModel } from './odoo-model.model';
import { Product } from './product.model';
import { ProductTemplate } from './product.template.model';
import { StockMove } from './stock-move';
import { User } from './user.model';
import { Currency } from './mail.message';

export class StockValuationLayer extends OdooModel implements OdooSerializableInterface<StockValuationLayer> {
  public readonly ODOO_MODEL = 'stock.valuation.layer';

  // Basic fields
  __last_update: string = "";
  create_date: string = "";
  write_date: string = "";
  display_name: string = "";
  description: string = "";
  reference: string = "";
  
  // Numeric fields
  quantity: number = 0;
  remaining_qty: number = 0;
  remaining_value: number = 0;
  unit_cost: number = 0;
  value: number = 0;
  price_diff_value: number = 0;

  // Relationships - Many to One
  account_move_id: OdooRelationship<AccountMove> = new OdooRelationship<AccountMove>();
  account_move_line_id: OdooRelationship<AccountMoveLine> = new OdooRelationship<AccountMoveLine>();
  categ_id: OdooRelationship<ProductCategory> = new OdooRelationship<ProductCategory>();
  create_uid: OdooRelationship<User> = new OdooRelationship<User>();
  currency_id: OdooRelationship<Currency> = new OdooRelationship<Currency>();
  product_id: OdooRelationship<Product> = new OdooRelationship<Product>();
  product_tmpl_id: OdooRelationship<ProductTemplate> = new OdooRelationship<ProductTemplate>();
//   stock_landed_cost_id: OdooRelationship<landedCost> = new OdooRelationship<StockLandedCost>();
  stock_move_id: OdooRelationship<StockMove> = new OdooRelationship<StockMove>();
  stock_valuation_layer_id: OdooRelationship<StockValuationLayer> = new OdooRelationship<StockValuationLayer>();
  uom_id: OdooRelationship<UomUom> = new OdooRelationship<UomUom>();
  write_uid: OdooRelationship<User> = new OdooRelationship<User>();

  // One to Many relationships
  stock_valuation_layer_ids: OdooMultiRelationship<StockValuationLayer> = new OdooMultiRelationship<StockValuationLayer>(StockValuationLayer);

  // Internal tracking
  _checked: any = false;

  constructor(id?: number) {
    super(id);
    this.id = id;

  }

  create(): StockValuationLayer {
    return new StockValuationLayer();
  }
}