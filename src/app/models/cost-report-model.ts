
import { OdooSerializableInterface } from '../shared/interfaces/odoo-serializable-interface';
import { CostReportLine } from './cost-report-line-model';
import { Lead } from './crm.lead.model';
import { CrmLeadPart } from './crm.lead.part.model';
import { OdooModel } from './odoo-model.model';
import { OdooMultiRelationship } from './odoo-multi-relationship.model';
import { OdooRelationship } from './odoo-relationship.model';

export class CostReport extends OdooModel implements OdooSerializableInterface<CostReport> {


  public ODOO_MODEL = 'ga.cost.report';

  cost_line_ids: OdooMultiRelationship<CostReportLine> = new OdooMultiRelationship<CostReportLine>(CostReportLine)  // le linee di costo
  total_cost_line_id: OdooRelationship<CostReportLine> = new OdooRelationship<CostReportLine>()   // il totale
  lastUpdate: Date; // data dell'ultimo aggiornamento+
  lead_id: OdooRelationship<Lead> = new OdooRelationship<Lead>() // il lead a cui è associato il report


  create() {
    return new CostReport();
  }
}

