$topnav-background-color: #FF8200;
;
:host {
    display: block;
}
.login-page {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
    background: $topnav-background-color;
    text-align: center;
    color: #fff;
    padding: 3em;
    .col-lg-4 {
        padding: 0;
    }
    .input-lg {
        height: 46px;
        padding: 10px 16px;
        font-size: 18px;
        line-height: 1.3333333;
        border-radius: 0;
    }
    .input-underline {
        background: 0 0;
        border: none;
        box-shadow: none;
        border-bottom: 2px solid rgba(255, 255, 255, 0.986); //bordo sotto input
        color: #000;
        border-radius: 0;
    }
    .input-underline:focus {
        border-bottom: 2px solid #fff;
        box-shadow: none;
    }
    .rounded-btn {
        -webkit-border-radius: 50px;
        border-radius: 50px;
        color: rgb(255, 255, 255);
        background: $topnav-background-color;
        border: 2px solid rgb(255, 255, 255);
        font-size: 18px;
        line-height: 40px;
        padding: 0 25px;
    }
    .rounded-btn:hover,
    .rounded-btn:focus,
    .rounded-btn:active,
    .rounded-btn:visited {
        color: rgba(255, 255, 255, 1);
        border: 2px solid rgba(255, 255, 255, 1);
        outline: none;
    }

    h1 {
        font-weight: 300;
        margin-top: 20px;
        margin-bottom: 10px;
        font-size: 36px;
        small {
            color: rgba(255, 255, 255, 0.7);
        }
    }

    .form-group {
        padding: 8px 0;
        input::-webkit-input-placeholder {
            color: rgba(255, 255, 255, 0.6) !important;
        }

        input:-moz-placeholder {
            /* Firefox 18- */
            color: rgba(255, 255, 255, 0.6) !important;
        }

        input::-moz-placeholder {
            /* Firefox 19+ */
            color: rgba(255, 255, 255, 0.6) !important;
        }

        input:-ms-input-placeholder {
            color: rgba(255, 255, 255, 0.6) !important;
        }
    }
    .form-content {
        padding: 40px 0;
    }
    .user-avatar {
        -webkit-border-radius: 50%;
        border-radius: 50%;
        border: 2px solid #fff;
    }
}