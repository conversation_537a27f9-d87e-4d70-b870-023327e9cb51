<div class="modal fade show" tabindex="-1" style="display: block; background: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h6 class="modal-title">Creare un promemoria per la commessa {{lead.tracking_code}} - {{lead.name}}?</h6>
        </div>
        <div class="modal-body">
          <!-- days -->
          <div class="mb-4">
            <label class="form-label">Ricordami tra</label>
            <div class="d-flex align-items-center">
              <input type="number" class="form-control me-2" 
                     style="max-width: 100px;"
                     [(ngModel)]="daysFromToday" 
                     min="1" 
                     max="365">
              <span>Giorni</span>
            </div>
          </div>
  
          <!-- activity type -->  
          <div class="mb-4">
            <label class="form-label">Tipo di Attività</label>
            <select class="form-select" 
                    [(ngModel)]="selectedActivityType">
              <option [ngValue]="null">Seleziona tipo attività</option>
              <option *ngFor="let type of activityTypes" [ngValue]="type">
                {{ type.name }}
              </option>
            </select>
          </div>

         <!-- assigned to (user_id) -->
<div class="mb-4">
  <label class="form-label mb-2">Assegnato a</label>
  <div class="d-flex align-items-center">
    <select class="form-select me-2" [(ngModel)]="assignedUser">
      <option [ngValue]="null">Seleziona utente</option>
      <option *ngFor="let user of users" [ngValue]="user">
        {{ user.name }}
      </option>
    </select>
  </div>
</div>

          
          <!-- summary -->
          <div class="mb-4">
            <label class="form-label">Riepilogo</label>
            <textarea class="form-control" 
                      [(ngModel)]="summary"
                      rows="3"></textarea>                                    
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" (click)="cancelReminder()">
            Non Creare
          </button>
          <button type="button" class="btn btn-primary" 
                  (click)="saveReminder()"
                  [disabled]="!selectedActivityType">
            Salva
          </button>
        </div>
      </div>
    </div>
  </div>