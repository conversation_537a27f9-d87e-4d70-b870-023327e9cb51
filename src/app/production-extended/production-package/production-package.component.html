<div *ngIf="production && production.move_finished_ids.values[0].product_id.name != 'EMPTY'"
  class="d-flex flex-column h-100 p-3" style="height: 100vh;">


  <!-- Packages for Sale Section -->
  <div class="card mb-3">
    <!-- <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">{{production?.move_finished_ids?.values[0]?.product_id.name}}</h5>
      <span class="badge bg-primary fs-6">
        {{production?.move_finished_ids?.values[0]?.product_uom_qty || 0}} 
        {{production?.move_finished_ids?.values[0]?.product_uom?.name || 'pz'}}
      </span>
    </div> -->

  <div class="card-header d-flex justify-content-between align-items-center py-3">
    <h5 class="mb-0">
      <i class="fa fa-box-check text-success me-2"></i>
      Colli per la vendita
    </h5>
     <button class="btn btn-link text-primary" (click)="printPackages()">
      <i class="fa fa-print fa-xl"></i>
    </button>
    <!-- <span class="badge bg-success">{{getSelectedPackages().length}}</span> -->
  </div>
  
  <div class="card-body p-0">
    <div *ngIf="getSelectedPackages().length === 0" class="p-3 text-center text-muted lead my-3">
      <!-- alert icon  with fa icon -->
      <i class="fa fa-triangle-exclamation fa-xl text-warning me-2"></i>
      Nessun collo selezionato per la vendita
    </div>

    <p-table *ngIf="getSelectedPackages().length > 0" [value]="getSelectedPackages()" dataKey="id" [scrollable]="true"
      [showGridlines]="true" styleClass="p-datatable-sm">

      <ng-template pTemplate="header">
        <tr>
          <th>Collo</th>
          <th>Contenuto</th>
          <th>Qtà</th>
          <th style="width: 60px"></th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-package>
        <tr>
          <td><strong>{{package.name}}</strong></td>
          <td>
            <div *ngIf="package.quant_ids && package.quant_ids.values.length > 0">
              <div *ngFor="let quant of package.quant_ids.values.slice(0, 2)">
                {{quant.product_id?.name || 'Prodotto'}} 
              </div>
              <div *ngIf="package.quant_ids.values.length > 2" class="text-muted">
                +{{package.quant_ids.values.length - 2}} altri
              </div>
            </div>
          </td>
          <td>{{getTotalQuantity(package)}} </td>
          <td>
            <p-button (click)="onPackageClick(package)" icon="fa fa-arrow-down" severity="primary" size="small"
              [text]="true">
            </p-button>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  </div>


<!-- Packages for Inventory Section -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center py-3">
    <h5 class="mb-0">
      <i class="fa fa-warehouse text-secondary me-2"></i>
      Colli a magazzino
    </h5>
    <!-- <span class="badge bg-secondary fs-">{{getInventoryPackages().length}}</span> -->
    <!-- print button -->
    
  </div>

  <div class="card-body p-0">
    <div *ngIf="getInventoryPackages().length === 0" class="p-3 text-center text-muted">
      Tutti i colli sono stati selezionati per la vendita
    </div>

    <p-table *ngIf="getInventoryPackages().length > 0" [value]="getInventoryPackages()" dataKey="id" [scrollable]="true"
      [showGridlines]="true" styleClass="p-datatable-sm">

      <ng-template pTemplate="header">
        <tr>
          <th>Collo</th>
          <th>Contenuto</th>
          <th>Qtà</th>
          <th style="width: 60px"></th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-package>
        <tr>
          <td><strong>{{package.name}}</strong></td>
          <td>
            <div *ngIf="package.quant_ids && package.quant_ids.values.length > 0">
              <div *ngFor="let quant of package.quant_ids.values.slice(0, 2)">
                {{quant.product_id?.name || 'Prodotto'}} 
              </div>
              <div *ngIf="package.quant_ids.values.length > 2" class="text-muted">
                +{{package.quant_ids.values.length - 2}} altri
              </div>
            </div>
          </td>
          <td>{{getTotalQuantity(package)}}</td>
          <td>
            <p-button (click)="onPackageClick(package)" icon="fa fa-arrow-up" severity="secondary" size="small"
              [text]="true">
            </p-button>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
</div>