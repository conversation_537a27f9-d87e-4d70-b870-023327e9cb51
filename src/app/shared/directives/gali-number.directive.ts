import { Directive, DoCheck, ElementRef } from '@angular/core';


@Directive({
    // tslint:disable-next-line:directive-selector
    selector: '[galiNumber]',
    standalone: false
})
export class GaliNumberDirective implements DoCheck {
  private el: HTMLInputElement;

  constructor(
    private elementRef: ElementRef,
  ) {
    this.el = this.elementRef.nativeElement;
  }

  public ngDoCheck() {
    if (this.el.value) {
      this.el.value = this.el.value.replace('.', ',');
    }
  }
}
