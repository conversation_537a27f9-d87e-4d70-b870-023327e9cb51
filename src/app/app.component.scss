* {
    -webkit-transition: margin-left 0.2s ease-in-out;
    -moz-transition: margin-left 0.2s ease-in-out;
    -ms-transition: margin-left 0.2s ease-in-out;
    -o-transition: margin-left 0.2s ease-in-out;
    transition: margin-left 0.2s ease-in-out;
}
.main-container {
    // margin-top: 48px;
    margin-bottom: 56px;
    // margin-left: 235px;
    // padding: 15px;
    padding-top:0px;
    -ms-overflow-x: hidden;
    overflow-x: hidden;
    overflow-y: scroll;
    position: relative;
    overflow: hidden;
}
.collapsed {
    margin-left: 100px;
}
@media screen and (max-width: 992px) {
    .main-container {
        margin-left: 0px !important;
    }
}
@media print {
    .main-container {
        margin-top: 0px !important;
        margin-left: 0px !important;
    }
}

