<!-- <div class="d-flex w-100 h-100">


  <app-flow-sidebar [flows]="flows" [selectedFlow]="selectedFlow" class="w-25"
    (choosedFlow)="handleFlowSelected($event)">
    <div class="mt-5 d-flex flex-column w-100">
      <span class="text-nowrap">GPT API KEY</span>
      <input class="form-control w-50 " type="password" (change)="persistkey($event)">
    </div>
  </app-flow-sidebar>


  <div class="h-100 d-flex flex-column w-75 overflow-y-scroll" data-bs-theme="dark" *ngIf="selectedFlow"> 

    <div class="dropdown-center ms-auto me-auto mt-4 ">
      <button class="btn btn-lg btn-success text-white " type="button" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fa fa-lg fa-plus"></i>
      </button>
      <ul class="dropdown-menu shadow" style="position: relative; left:-20px;">
        <li>
          <a class="dropdown-item" (click)="newActionSearch()">
            <h2 class="mb-0 text-white">Cerca</h2>
            <p>Cerca e trova quello che ti serve combinando criteri, raggupamenti</p>
          </a>
        </li>

        <li>
          <a class="dropdown-item" href="#">
            <h2 class="mb-0 text-white">Controlla</h2>
            <p>Verifica che tutto sia come ti aspetti </p>
          </a>
        </li>

        <li>
          <a class="dropdown-item " href="#">
            <h2 class="mb-0 text-white">Modifica</h2>
            <p>Modifica dei dati in blocco</p>
          </a>
        </li>

        <li>
          <a class="dropdown-item " href="#">
            <h2 class="mb-0 text-white">Plotta</h2>
            <p>Genera grafici da allegare ai tuoi report</p>
          </a>
        </li>

        <li>
          <a class="dropdown-item " href="#">
            <h2 class="mb-0 text-white">Invia report</h2>
            <p>Invia a chi vuoi e quando vuoi i tuoi risultati</p>
          </a>
        </li>

      </ul>
    </div>

   

    <div class="d-flex flex-column p-5">
      <ng-container *ngFor="let a of actions">
        <app-action-plot *ngIf="a.type == 'plot'" class="mb-5 shadow-lg" (onDelete)="delete(a)"></app-action-plot>
        <app-action-search *ngIf="a.type == 'search'" class="mb-5 shadow-lg" (onDelete)="delete(a)"
          [action]="a"></app-action-search>
      </ng-container>

    </div>




    <button  class="btn btn-dark btn-lg text-white ms-auto me-auto" (click)="persist()">

      <i class="fa fa-save"></i></button>


  </div>


</div> -->