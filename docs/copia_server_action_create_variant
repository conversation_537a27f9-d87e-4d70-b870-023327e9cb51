debu = []

def create_variant_if_not_exists(product_template, attributes_values, log):
    """
    Crea (o restituisce) la variante corrispondente a un product template,
    dato un dizionario di coppie attributo: valore.
    
    :param product_template: record di 'product.template'
    :param attributes_values: dict dei valori degli attributi, ad esempio
                              {'Lunghezza': '100', 'Colore': 'Rosso'}
    :return: record di 'product.product' (variante) se esiste o viene creata, altrimenti None
    """
    env = product_template.env
    log("Iniziando creazione/ricerca variante")
    
    # Lista per raccogliere i product.template.attribute.value IDs
    ptav_ids = []
    
    try:
        # Per ogni attributo indicato, cercare la linea corrispondente nel template
        for attr_name, value in attributes_values.items():
            log(f"Processando attributo: {attr_name} = {value}")
            
            # Filtra le linee attributo dove il nome dell'attributo inizia con il valore fornito
            attr_line = None
            for line in product_template.attribute_line_ids:
                if line.attribute_id.name.startswith(attr_name):
                    attr_line = line
                    break
            
            if not attr_line:
                error_msg = f"Attributo '{attr_name}' non trovato nel template ID {product_template.id}"
                debu.append(f"ERRORE: {error_msg}")
                log(error_msg, level='error')
                raise UserError(error_msg)
            
            debu.append(f"Attributo trovato: {attr_line.attribute_id.name}")
            
            # Cerca il valore dell'attributo esistente
            attr_value = env['product.attribute.value'].search([
                ('name', '=', str(value)),
                ('attribute_id', '=', attr_line.attribute_id.id)
            ], limit=1)
            
            
            
            
            if not attr_value:
                # Crea il valore dell'attributo al volo
                try:
                    attr_value = env['product.attribute.value'].create({
                        'name': str(value),
                        'attribute_id': attr_line.attribute_id.id
                    })
                    debu.append(f"Valore attributo creato: {value} per attributo {attr_line.attribute_id.name}")
                    #log(f"Creato nuovo valore attributo: {value}")
                    
                    # Aggiorna la linea attributo per aggiungere il nuovo valore
                    attr_line.write({'value_ids': [(4, attr_value.id)]})
                    debu.append(f"Valore aggiunto alla linea attributo")
                    
                except Exception as e:
                    error_msg = f"Errore nella creazione del valore attributo '{value}' per '{attr_name}': {str(e)}"
                    debu.append(f"ERRORE: {error_msg}")
                    log(error_msg, level='error')
                    raise UserError(error_msg)
            else:
                debu.append(f"Valore attributo esistente trovato: {value}")
            
            # Verifica se esiste il record 'product.template.attribute.value'
            ptav = env['product.template.attribute.value'].search([
                ('product_attribute_value_id', '=', attr_value.id),
                ('attribute_line_id', '=', attr_line.id)
            ], limit=1)
            
            
            if not ptav:
              # Crea il record 'product.template.attribute.value'
              ptav = env['product.template.attribute.value'].create({
                  'product_attribute_value_id': attr_value.id,
                  'product_tmpl_id': product_template.id,
                  'attribute_line_id': attr_line.id,
                  'ptav_active': True
              })
              debu.append(f"Record product.template.attribute.value creato: {ptav.id}")
            
            
            ptav_ids.append(ptav.id)

        domain = [
            ('product_tmpl_id', '=', product_template.id),
        ] + [
            ('product_template_attribute_value_ids', '=', ptav_id)
            for ptav_id in ptav_ids
        ]
        
        variant = env['product.product'].search(domain)
        
        debu.append(f"CREA LA VARIANTE: {variant.id}")

        
        if not variant:
            variant = env['product.product'].create({
                'product_tmpl_id': product_template.id,
                'product_template_attribute_value_ids': [(6, 0, ptav_ids)]
            })
   
    except UserError:
        # Rilancia gli errori UserError così come sono
        raise
    except Exception as e:
        # Gestisci altri errori imprevisti
        error_msg = f"Errore imprevisto nella gestione degli attributi: {str(e)}"
        debu.append(f"ERRORE CRITICO: {error_msg}")
        log(error_msg, level='error')
        return None

# Esecuzione principale con gestione errori migliorata
try:
    product_template_id = env.context.get('product_tmpl_id')
    attributes_values = env.context.get('attributes')
    
    if not product_template_id:
        debu.append("ERRORE: product_tmpl_id non fornito nel context")
        raise UserError("ID del template prodotto non specificato")
    
    if not attributes_values:
        debu.append("ERRORE: attributes non forniti nel context")
        raise UserError("Valori degli attributi non specificati")
    
    product_template = env['product.template'].search([
        ('id', '=', product_template_id)
    ], limit=1)
    
    if not product_template:
        error_msg = f"Template prodotto con ID {product_template_id} non trovato"
        debu.append(f"ERRORE: {error_msg}")
        raise UserError(error_msg)
    
    debu.append(f"Template trovato: {product_template.display_name}")
    debu.append(f"Attributi da processare: {attributes_values}")
    
    result_variant = create_variant_if_not_exists(product_template, attributes_values, log)
    
    #if result_variant:
    #    debu.append(f"SUCCESSO: Variante ottenuta/creata: {result_variant.display_name}")
    #else:
    #    debu.append("ERRORE: Impossibile ottenere/creare la variante")

except UserError as e:
    debu.append(f"ERRORE UTENTE: {str(e)}")
    # Rilancia l'errore per mostrarlo all'utente
    raise
except Exception as e:
    debu.append(f"ERRORE CRITICO: {str(e)}")
    raise UserError(f"Errore imprevisto: {str(e)}")

# Return an action with formatted table data
action = {
    "type": "ir.actions.client",
    "tag": "display_table",
    "params": {
        "debug": debu,
        "title": "Gestione Varianti Prodotto",
    }
}
action