/**
 * Esempi di utilizzo del L33tService per l'integrazione con OpenAI
 * 
 * Questo file contiene esempi pratici di come utilizzare il servizio
 * L33tService per integrare le funzionalità OpenAI nella tua applicazione.
 */

import { Component, Injectable } from '@angular/core';
import { L33tService, OpenAIMessage } from '../l33t.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ExampleAIService {
  
  constructor(private l33tService: L33tService) {
    // Configura la API Key (in un'app reale, questa dovrebbe venire da un config sicuro)
    // this.l33tService.setApiKey('sk-your-api-key-here');
  }

  /**
   * Esempio 1: Analisi di testo semplice
   */
  analyzeText(text: string): Observable<string> {
    const prompt = `Analizza il seguente testo e fornisci un riassunto delle informazioni principali:

Testo: "${text}"

Fornisci:
1. Riassunto principale
2. Tono del testo
3. Argomenti chiave
4. Sentiment generale`;

    return this.l33tService.sendMessage(prompt, 'gpt-3.5-turbo');
  }

  /**
   * Esempio 2: Generazione di email professionali
   */
  generateEmail(recipient: string, subject: string, context: string): Observable<string> {
    const prompt = `Genera una email professionale con i seguenti dettagli:

Destinatario: ${recipient}
Oggetto: ${subject}
Contesto: ${context}

L'email deve essere:
- Professionale e cortese
- Chiara e concisa
- In italiano
- Con saluti appropriati`;

    return this.l33tService.sendMessage(prompt, 'gpt-3.5-turbo');
  }

  /**
   * Esempio 3: Assistente per documentazione tecnica
   */
  generateDocumentation(codeSnippet: string, language: string): Observable<string> {
    const systemPrompt = 'Sei un esperto sviluppatore che scrive documentazione tecnica chiara e dettagliata.';
    
    const userMessage = `Genera la documentazione per questo codice ${language}:

\`\`\`${language}
${codeSnippet}
\`\`\`

Includi:
1. Descrizione della funzione
2. Parametri e tipi
3. Valore di ritorno
4. Esempio di utilizzo
5. Note importanti`;

    return this.l33tService.sendMessage(userMessage, 'gpt-4', systemPrompt);
  }

  /**
   * Esempio 4: Traduzione intelligente
   */
  smartTranslate(text: string, fromLang: string, toLang: string, context?: string): Observable<string> {
    const contextInfo = context ? `\nContesto: ${context}` : '';
    
    const prompt = `Traduci il seguente testo da ${fromLang} a ${toLang}.
Mantieni il tono e lo stile originale.${contextInfo}

Testo da tradurre: "${text}"

Fornisci solo la traduzione, senza spiegazioni aggiuntive.`;

    return this.l33tService.sendMessage(prompt, 'gpt-3.5-turbo');
  }

  /**
   * Esempio 5: Analisi di sentiment per feedback clienti
   */
  analyzeFeedback(feedback: string): Observable<string> {
    const systemPrompt = 'Sei un analista esperto di customer experience che analizza feedback dei clienti.';
    
    const prompt = `Analizza questo feedback del cliente:

"${feedback}"

Fornisci:
1. Sentiment (Positivo/Neutro/Negativo) con score 1-10
2. Emozioni principali rilevate
3. Problemi specifici menzionati
4. Suggerimenti per migliorare l'esperienza
5. Priorità di intervento (Alta/Media/Bassa)

Formato la risposta in modo strutturato.`;

    return this.l33tService.sendMessage(prompt, 'gpt-4', systemPrompt);
  }

  /**
   * Esempio 6: Generazione di embeddings per ricerca semantica
   */
  createSearchEmbeddings(documents: string[]): Observable<number[][]> {
    // Crea embeddings per più documenti
    const embeddingPromises = documents.map(doc => 
      this.l33tService.getTextEmbedding(doc, 'text-embedding-ada-002')
    );

    // Combina tutti gli embeddings
    return new Observable(observer => {
      Promise.all(embeddingPromises.map(obs => obs.toPromise()))
        .then(embeddings => {
          observer.next(embeddings as number[][]);
          observer.complete();
        })
        .catch(error => observer.error(error));
    });
  }

  /**
   * Esempio 7: Chat conversazionale con memoria
   */
  continueConversation(messages: OpenAIMessage[], newMessage: string): Observable<string> {
    const conversationMessages: OpenAIMessage[] = [
      ...messages,
      { role: 'user', content: newMessage }
    ];

    return this.l33tService.createConversation(
      conversationMessages,
      'gpt-3.5-turbo',
      { temperature: 0.7, max_tokens: 500 }
    );
  }
}

/**
 * Esempio di componente che utilizza il servizio AI
 */
@Component({
  selector: 'app-ai-example',
  template: `
    <div class="ai-example">
      <h3>Esempio AI Integration</h3>
      
      <div class="example-section">
        <h4>Analisi Testo</h4>
        <textarea [(ngModel)]="textToAnalyze" placeholder="Inserisci testo da analizzare..."></textarea>
        <button (click)="analyzeText()" [disabled]="isLoading">Analizza</button>
      </div>

      <div class="example-section">
        <h4>Generazione Email</h4>
        <input [(ngModel)]="emailRecipient" placeholder="Destinatario">
        <input [(ngModel)]="emailSubject" placeholder="Oggetto">
        <textarea [(ngModel)]="emailContext" placeholder="Contesto..."></textarea>
        <button (click)="generateEmail()" [disabled]="isLoading">Genera Email</button>
      </div>

      <div class="result" *ngIf="result">
        <h4>Risultato:</h4>
        <pre>{{ result }}</pre>
      </div>

      <div class="loading" *ngIf="isLoading">
        Elaborazione in corso...
      </div>
    </div>
  `
})
export class AIExampleComponent {
  textToAnalyze = '';
  emailRecipient = '';
  emailSubject = '';
  emailContext = '';
  result = '';
  isLoading = false;

  constructor(private aiService: ExampleAIService) {}

  analyzeText() {
    if (!this.textToAnalyze.trim()) return;
    
    this.isLoading = true;
    this.aiService.analyzeText(this.textToAnalyze).subscribe({
      next: (result) => {
        this.result = result;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Errore:', error);
        this.result = `Errore: ${error.message}`;
        this.isLoading = false;
      }
    });
  }

  generateEmail() {
    if (!this.emailRecipient || !this.emailSubject || !this.emailContext) return;
    
    this.isLoading = true;
    this.aiService.generateEmail(this.emailRecipient, this.emailSubject, this.emailContext).subscribe({
      next: (result) => {
        this.result = result;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Errore:', error);
        this.result = `Errore: ${error.message}`;
        this.isLoading = false;
      }
    });
  }
}
