import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { Subject, firstValueFrom } from 'rxjs';
import _ from 'lodash';
import { ODOO_IDS } from '../models/deal';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { TreeNode } from 'primeng/api';
import { StockQuantPackage } from '../models/stock-quant-package';
import { Product } from '../models/product.model';
import { StockPicking } from '../models/stock-picking';
import { StockMoveLine } from '../models/stock-move-line';
import { StockMove } from '../models/stock-move';
import { StockQuant } from '../models/stock-quant';
import { ProductAttribute } from '../models/product.attribute.model';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';
import { TableColResizeEvent, TableColumnReorderEvent } from 'primeng/table';
import { StockLocation } from '../models/stock-location';
import { StockPackageLevel } from '../models/stock-package-level';

export interface ColumnType {
  id?: number;
  field: string; 
  fieldType: 'field' | 'function' | 'attribute' ;
  label: string;
  type: 'text' | 'number' | 'date' | 'currency' | 'dimension';
  sort?: 'asc' | 'desc' | 'none';
  style?: 'Collo' | 'Dettaglio' | 'Categoria' | 'Prodotto';
  selected: boolean;
  searchInput?: string;
  sortable?: boolean;
  editable?: boolean; // New property for edit mode columns
}

export interface QuantFromServerAction {
  id: number;
  // 'product_tmpl_id.id': number;
  product_id: number;
  selectedPieces?: number; // New field for edit mode
  destPackage?: string; // New field for edit mode
  odooPackage?: StockQuantPackage;
  [key: string]: any; // Allow any other properties
}



@Component({
  selector: 'app-package-list',
  templateUrl: './package-list.component.html',
  styleUrls: ['./package-list.component.scss'],
  standalone: false
})
export class PackageListComponent implements OnInit {
expandedRows: { [key: string]: boolean } = {};
  quants: QuantFromServerAction[] = [];
  loading: boolean = false;
  quantsComplete: boolean = false;
  showPhotos: boolean = false;
  quantsForPhotos: StockQuant[] = [];
  showingQuantPhotos: StockQuant = null;
  loadedPhotos: boolean = false;
  selectionKeys: { [key: string]: boolean } = {};
  attributes: ProductAttribute[] = [];

  @ViewChild('photosPanel') photosPanel: OverlayPanel;
  @ViewChild('sellFromListPanel') sellFromListPanel: OverlayPanel;
  showSellFromListComponent = false;

  
  // Edit mode properties
  editMode: boolean = false;
  hasOperations: boolean = false; // Track if any operations are defined
  canMoveInBlockTo: StockLocation[] = null;
  fromLocation: StockLocation = null;
  locations: StockLocation[];
  selectedDestinationLocation: StockLocation | null = null;


  // Product template selection
  productTemplateOptions = ODOO_IDS.CAN_BE_PRODUCED_OPTIONS;
  selectedProductTemplate = this.productTemplateOptions[0];

  // Selection model for PrimeNG Table
  selectedQuantIds: number[] = [];
  selectedPackages: StockQuantPackage[] = [];

  // Gestione delle colonne
  allColumns: ColumnType[] = [];
  selectedColumns: ColumnType[] = [];

  //totals
  totalQuantities: { [field: string]: number } = {};
  selectedQuantities: { [field: string]: number } = {};

  @Input() embedded?: boolean = false;
  @Output() emitQuants: EventEmitter<number[]> = new EventEmitter<number[]>();

  treenodes: TreeNode[] = [];
  showAlertAtLeastOneColumn: boolean = false;

  private searchSubject = new Subject<{field: string, value: string}>();
  count = 0;

  constructor(private odooEm: OdooEntityManager) {
    this.showAlertAtLeastOneColumn = false;
  }

  async ngOnInit() {
    console.log('PackageListComponent initialized');
    await this.loadAttributesSequence();
    // await this.refreshQuants(); // Enable this to load data on init
    this.selectedQuantIds = [];

    // Load saved product template
    const savedTemplate = localStorage.getItem('packageListSelectedTemplate');
    if (savedTemplate) {
      try {
        this.selectedProductTemplate = JSON.parse(savedTemplate);
        this.onProductTemplateChange();
      } catch (e) {
        console.error('Error loading saved template', e);
      }
    }
    //fetch locations with id 8 and 316
    this.locations = await firstValueFrom(this.odooEm.search<StockLocation>(new StockLocation(), [['id', 'in', [8, 316]]]));
    console.log("Locations", this.locations); 
  }

  async loadAttributesSequence() {
    let res = await firstValueFrom(this.odooEm.search<ProductAttribute>(new ProductAttribute(), [], 0));
    this.attributes = res;
    console.log("ATTRIBUTES!!!", res);
  }

  async reorderColumns() {
    //we basically have to change order of attriubtes so that they follow the sequence of this.attributes.sequence
    this.allColumns.sort((a, b) => {
      if (a.fieldType === 'attribute' && b.fieldType === 'attribute') {
        const aSeq = this.attributes.find(attr => attr.id === a.id)?.sequence || 0;
        const bSeq = this.attributes.find(attr => attr.id === b.id)?.sequence || 0;
        return aSeq - bSeq;
      }
      return 0;
    });
  }
   

  onProductTemplateChange() {
    this.clearAllSelections()
    console.log('Product template changed to:', this.selectedProductTemplate.name);
    this.refreshQuants();
  }

  onColReorder(event:TableColumnReorderEvent) {
    console.log('Columns reordered:', event);
    this.persistColumnOrderForTemplate(this.selectedProductTemplate,event);
  }
  persistColumnOrderForTemplate(selectedProductTemplate, event: TableColumnReorderEvent) {
    // use local storage use selectedProductTemplate as key

    localStorage.setItem('packageListColumnOrder_'+selectedProductTemplate.id, JSON.stringify(event));
    console.log("get item ", localStorage.getItem('packageListColumnOrder_'+selectedProductTemplate.id));
  }

  // Toggle edit mode
  toggleEditMode() {
    this.editMode = !this.editMode;
    console.log('Edit mode toggled to:', this.editMode);
    
    if (this.editMode) {
      this.addEditModeColumns();
    } else {
      this.removeEditModeColumns();
      this.clearOperations();
    }
    this.updateSelectedColumns();
  }

  // Add edit mode columns
  private addEditModeColumns() {
    console.log('Adding edit mode columns');
    
    // Add selectedPieces column if not exists
    if (!this.allColumns.find(col => col.field === 'selectedPieces')) {
      const selectedPiecesColumn: ColumnType = {
        field: 'selectedPieces',
        fieldType: 'field',
        label: 'Pz Sel',
        type: 'number',
        selected: true,
        sortable: false,
        editable: true,
        searchInput: ''
      };
      //add before everything else
      this.allColumns.unshift(selectedPiecesColumn);
      this.selectedColumns.unshift(selectedPiecesColumn);
    }

    // Add destPackage column if not exists
    if (!this.allColumns.find(col => col.field === 'destPackage')) {
      const destPackageColumn: ColumnType = {
        field: 'destPackage',
        fieldType: 'field',
        label: 'Collo Dest',
        type: 'text',
        selected: true,
        sortable: false,
        editable: true,
        searchInput: ''
      };
      this.allColumns.unshift(destPackageColumn);
      this.selectedColumns.unshift(destPackageColumn);
    }
  }

  // Remove edit mode columns
  private removeEditModeColumns() {
    console.log('Removing edit mode columns');
    this.allColumns = this.allColumns.filter(col => 
      col.field !== 'selectedPieces' && col.field !== 'destPackage'
    );
  }

  // Update selected columns based on allColumns
  private updateSelectedColumns() {
    this.selectedColumns = this.allColumns.filter(col => col.selected);

    // randomize selected columns
    this.selectedColumns = this.selectedColumns.sort(() => Math.random() - 0.5);
    console.log('Updated selected columns:', this.selectedColumns.map(c => c.field));
  }

  // Clear all operations
  private clearOperations() {
    console.log('Clearing all operations');
    this.quants.forEach(quant => {
      quant.selectedPieces = undefined;
      quant.destPackage = undefined;
    });
    this.hasOperations = false;
    this.updateTreeNodes();
  }

  openSellFromListPanel(event: any) {
    if (this.selectedQuantIds.length > 0) {
      // First hide and reset the component
      this.showSellFromListComponent = false;
      
      // Use setTimeout to ensure the component is destroyed before recreating
      setTimeout(() => {
        this.showSellFromListComponent = true;
        this.sellFromListPanel.show(event);
      }, 0);
    }
  }

  onSaleComplete(event: any) {
    console.log('Sale completed:', event);
    this.sellFromListPanel.hide();
    this.refreshQuants();
  }

  // Update tree nodes with current quant data
  private async updateTreeNodes() {
    this.treenodes =  this.createTreeNodes(this.quants);
    this.aggregateQuantitaValore(this.treenodes, ['Quantità','Costo','Pz']);
    this.aggregateAttributeMinMax(this.treenodes);
    this.calculatePackageUnitCost(this.treenodes);

  }


  makeSum(quantIds: number[], col: ColumnType): number {
    return quantIds.reduce((sum, quantId) => {
      const quant = this.quants.find(q => q.id === quantId);
      return sum + (quant ? this.getQuantValueForCalculation(quant, col) : 0);
    }, 0);
  }

  // Handle pieces input change
  onPiecesChange(quantId: number, value: number) {
    console.log('Pieces changed for quant', quantId, 'to:', value);
    const quant = this.quants.find(q => q.id === quantId);
    if (quant) {
      quant.selectedPieces = value > 0 ? value : undefined;
      this.checkHasOperations();
      this.updateTreeNodes();
    }
  }

  // Handle destination package change
  onDestPackageChange(quantId: number, value: string) {
    console.log('Destination package changed for quant', quantId, 'to:', value);
    const quant = this.quants.find(q => q.id === quantId);
    if (quant) {
      quant.destPackage = value.trim() || undefined;
      this.checkHasOperations();
      this.updateTreeNodes();
    }
  }

  // Check if there are any operations defined
  private checkHasOperations() {
    this.hasOperations = this.quants.some(quant => 
      (quant.selectedPieces && quant.selectedPieces > 0) || 
      (quant.destPackage && quant.destPackage.trim().length > 0)
    );
    console.log('Has operations:', this.hasOperations);
  }

 // Confirm operations
 async confirmOperations() {
  console.log('Confirm operations clicked - to be implemented');

  // create an array of nodes to process. the ones with an operation
  let nodesWithOperations = this.quants.filter(quant =>
    (quant.selectedPieces && quant.selectedPieces > 0) || 
    (quant.destPackage && quant.destPackage.trim().length > 0)
  );
  console.log('Nodes with operations:', nodesWithOperations);

  //create an array of unique destination packages names 
  let destPackages = [...new Set(nodesWithOperations.map(quant => quant.destPackage).filter(Boolean))];
  console.log('Destination packages:', destPackages);

  //search if similar packages already exist:
  let packagesToCreate: string[] = [];
  let packagesFound: string[] = [];

  // Use for...of loop instead of forEach to properly handle async/await
  for (const packageName of destPackages) {
    console.log('Searching for package:', packageName);

    try {
      let packageFound = await firstValueFrom(
        this.odooEm.search<StockQuantPackage>(new StockQuantPackage(), [
          ["name", "=like", packageName + "%"],
        ])
      );

      if (packageFound && packageFound.length > 0) {
        console.log('Package found:', packageFound[0]);
        packagesFound.push(packageFound[0].name);
        //assign it to the quants that have this package
        nodesWithOperations.forEach((quant) => {
          if (quant.destPackage === packageName) {
            quant.odooPackage = packageFound[0];
          }
        });   
      } else {
        console.log('Package not found:', packageName);
        packagesToCreate.push(packageName);
      }
    } catch (error) {
      console.error('Error searching for package:', packageName, error);
      packagesToCreate.push(packageName); // Treat as not found if error occurs
    }
  }

  //signal to the user that the packages found and to be created
  console.log('Packages to create:', packagesToCreate);
  console.log('Packages found:', packagesFound);

  const confirmMessage = 
    "Hai selezionato i seguenti pacchi di destinazione:\n" +
    (packagesFound.length > 0 ? "Pacchi già esistenti:\n" + packagesFound.join("\n") + "\n" : "") +
    (packagesToCreate.length > 0 ? "Pacchi da creare:\n" + packagesToCreate.join("\n") + "\n" : "") +
    "Vuoi continuare?";

  if (confirm(confirmMessage)) {
    this.loading = true;

    try {
      //create the packages sequentially
      for (const packageName of packagesToCreate) {
        try {
          let p = await firstValueFrom(
            this.odooEm.create<StockQuantPackage>(new StockQuantPackage(), {
              name: packageName,
              location_id: ODOO_IDS.stock_location_stock
            })
          );
          console.log('Created package:', p);
          //assign it to the quants that have this package
          nodesWithOperations.forEach((quant) => {
            if (quant.destPackage === packageName) {
              console.log('Assigned package to quant:', quant.id);

              quant.odooPackage = p;
            }
          });
        } catch (error) {
          console.error('Error creating package:', packageName, error);
        }
      }

      console.log('All packages processed');

      //first we solve quant products by searching in Odoo
      let productIds = nodesWithOperations.map((quant) => quant.product_id);
      let products = await firstValueFrom(
        this.odooEm.search<Product>(new Product(), [["id", "in", productIds]])
      );


      console.log('Products found:', products);

      //then we solve package origins by searching in Odoo. this is needed for the move creation
      let packageNames = nodesWithOperations.map((quant) => quant["Collo"]);
      let packages = await firstValueFrom(
        this.odooEm.search<StockQuantPackage>(new StockQuantPackage(), [["name", "in", packageNames ]])
      );
      console.log('Packages origins found:', packages);


      // Filter only quants with selected pieces for transfer
      const quantsToTransfer = nodesWithOperations.filter(quant => 
        quant.selectedPieces && quant.selectedPieces > 0 && quant.odooPackage
      );

      if (quantsToTransfer.length === 0) {
        alert('Nessun articolo da trasferire');
        return;
      }

      console.log('Creating single picking for', quantsToTransfer.length, 'quants');

      // Create ONE single picking for all operations
      
      const pickingData = {
        picking_type_id: ODOO_IDS.picking_type_generic,
        origin: "Operazione sui pacchi - Da Packlist",
        location_id: ODOO_IDS.stock_location_stock,
        location_dest_id: ODOO_IDS.stock_location_stock,
      };

      const newPick = await firstValueFrom(
        this.odooEm.create<StockPicking>(new StockPicking(), pickingData)
      );

      console.log('Created single picking:', newPick.id);

      // Create moves and move lines for each quant
      for (const quant of quantsToTransfer) {
        const product = products.find(p => p.id === quant["product_id.id"]);
        const transferQty = quant.selectedPieces * (quant["Quantità"] / quant["Pz"]);
        const fromPackage = packages.find(p => p.name === quant["Collo"]);
        const toPackage = quant.odooPackage;

        console.log('Processing quant:', {
          quant_id: quant.id,
          product_name: product?.name,
          selectedPieces: quant.selectedPieces,
          transferQty: transferQty,
          fromPackage: fromPackage?.name,
          toPackage: toPackage?.name
        });

        // Create move for this quant
        const moveData = {
          name: product?.name || `Product ${quant["product_id.id"]}`,
          location_id: fromPackage?.location_id.id || ODOO_IDS.stock_location_stock,
          location_dest_id: toPackage?.location_id.id || ODOO_IDS.stock_location_stock,
          product_id: quant["product_id.id"],
          product_uom_qty: transferQty,
          picking_id: newPick.id,
          product_uom: quant["product_id.uom_id.id"] || 1, // fallback to unit UoM
        };

        const createdMove = await firstValueFrom(
          this.odooEm.create<StockMove>(new StockMove(), moveData)
        );

        console.log('Created move:', createdMove.id, 'for quant:', quant.id);

        // Create move line for this quant
        await firstValueFrom(
          this.odooEm.create<StockMoveLine>(new StockMoveLine(), {
            product_id: quant["product_id.id"],
            picking_id: newPick.id,
            qty_done: transferQty,
            package_id: fromPackage?.id,
            result_package_id: toPackage?.id,
            location_id: fromPackage?.location_id.id || ODOO_IDS.stock_location_stock,
            location_dest_id: toPackage?.location_id.id || ODOO_IDS.stock_location_stock,
            move_id: createdMove.id,
          })
        );

        console.log('Created move line for quant:', quant.id);
      }

      // Validate the single picking  
      console.log('Validating picking:', newPick.id);
      await this.odooEm.call2(
        new StockPicking().ODOO_MODEL,
        "button_validate",
        [[newPick.id]]
      );

      console.log('Picking validated:', newPick.id);

      // Clear edit mode and refresh data
      this.clearOperations();
      this.editMode = false;
      this.removeEditModeColumns();
      this.updateSelectedColumns();
      
      // Refresh the quants to show updated data
      await this.refreshQuants();

      alert(`Trasferimento completato! Creato picking ${newPick.id} con ${quantsToTransfer.length} operazioni.`);

    } catch (error) {
      console.error('Error during transfer operations:', error);
      alert('Errore durante le operazioni di trasferimento: ' + error);
    } finally {
      this.loading = false;
    }

  } else {
    console.log('Operation cancelled by user');
  }
}
async confirmLocationMove(): Promise<void> {
  if (!this.selectedDestinationLocation || !this.fromLocation) {
    console.error('Destination or source location not selected');
    return;
  }

  const selectedPackages = this.getSelectedPackages();
  if (selectedPackages.length === 0) {
    console.error('No packages selected');
    return;
  }

  const confirmMessage = `Confermi di voler spostare ${selectedPackages.length} pacchi da "${this.fromLocation.name}" a "${this.selectedDestinationLocation.name}"?`;
  
  if (!confirm(confirmMessage)) {
    return;
  }

  try {
    this.loading = true;
    console.log('Starting bulk location move...', {
      packages: selectedPackages.map(p => p.Collo),
      from: this.fromLocation.name,
      to: this.selectedDestinationLocation.name,
      fromId: this.fromLocation.id,
      toId: this.selectedDestinationLocation.id
    });

    // 1. Get the package IDs from Odoo (we need the actual package records)
    const packageNames = selectedPackages.map(p => p.Collo);
    console.log('Searching for packages:', packageNames);
    
    const odooPackages = await firstValueFrom(
      this.odooEm.search<StockQuantPackage>(new StockQuantPackage(), [
        ['name', 'in', packageNames]
      ])
    );

    if (odooPackages.length === 0) {
      throw new Error('Nessun pacco trovato in Odoo');
    }
    
    if (odooPackages.length !== packageNames.length) {
      const foundNames = odooPackages.map(p => p.name);
      const missingNames = packageNames.filter(name => !foundNames.includes(name));
      console.warn('Some packages not found:', missingNames);
      
      if (!confirm(`Attenzione: ${missingNames.length} pacchi non trovati in Odoo (${missingNames.join(', ')}). Continuare con ${odooPackages.length} pacchi?`)) {
        return;
      }
    }

    console.log('Found packages in Odoo:', odooPackages.map(p => `${p.name} (ID: ${p.id})`));

    // 2. Create the picking
    const pickingData = {
      picking_type_id:  60, // pack block move
      location_id: this.fromLocation.id,
      location_dest_id: this.selectedDestinationLocation.id,
      origin: `Transfer of ${odooPackages.length} packages - From PackageList Component`,
    };

    console.log('Creating picking with data:', pickingData);
    const newPicking = await firstValueFrom(
      this.odooEm.create<StockPicking>(new StockPicking(), pickingData)
    );

    console.log('Created picking:', newPicking.id);

    // 3. Create package levels for each selected package
    console.log('Creating package levels...');
    for (const [index, odooPackage] of odooPackages.entries()) {
      const packageLevelData = {
        picking_id: newPicking.id,
        package_id: odooPackage.id,
        location_id: this.fromLocation.id,
        location_dest_id: this.selectedDestinationLocation.id,
      };

      console.log(`Creating package level ${index + 1}/${odooPackages.length} for package:`, odooPackage.name);
      
      // Se hai la classe StockPackageLevel, usa quella, altrimenti usa un metodo generico
      await firstValueFrom(
        this.odooEm.create<StockPackageLevel>( new StockPackageLevel(), packageLevelData)
      );

      console.log(`Created package level for package: ${odooPackage.name}`);
    }

    // 4. Confirm the picking
    console.log('Confirming picking:', newPicking.id);
    await this.odooEm.call2(
      new StockPicking().ODOO_MODEL,
      'action_confirm',
      [[newPicking.id]]
    );

    console.log('Picking confirmed successfully:', newPicking.id);

    // 5. Show success message
    const successMessage = `Spostamento completato con successo!\n\n` +
                          `• Picking creato: ${newPicking.id}\n` +
                          `• Pacchi trasferiti: ${odooPackages.length}\n` +
                          `• Da: ${this.fromLocation.name}\n` +
                          `• A: ${this.selectedDestinationLocation.name}`;
    
    alert(successMessage);
    
    // 6. Reset selezioni e refresh
    this.clearAllSelections();
    this.selectedDestinationLocation = null;
    
    // Refresh dei dati per mostrare i nuovi stati
    await this.refreshQuants();
    
  } catch (error) {
    console.error('Error during bulk move:', error);
    
    let errorMessage = 'Errore durante lo spostamento: ';
    if (error instanceof Error) {
      errorMessage += error.message;
    } else {
      errorMessage += String(error);
    }
    
    alert(errorMessage);
  } finally {
    this.loading = false;
  }
  }
 
// Node selection event handler - FIXED VERSION
nodeSelect(node: TreeNode) {
  console.log("node clicked", node);

  // Toggle the selected state of the node
  node['selected'] = !node['selected'];
  let sel = node['selected'];

  // If this node has children (it's a package)
  if (node.children) {
    console.log("setting all the children to", sel);
    
    // Set all children to the same state as parent
    node.children.forEach((child: any) => {
      if (child.selected === sel) return; // Skip if already in correct state
      child.selected = sel;
      
      // Update selectedQuantIds for quant children
      if (child.data && child.data.nodeType === 'quant' && child.data.id) {
        if (sel) {
          // Add to selection if not already there
          if (!this.selectedQuantIds.includes(child.data.id)) {
            this.selectedQuantIds.push(child.data.id);
          }
        } else {
          // Remove from selection
          this.selectedQuantIds = this.selectedQuantIds.filter(id => id !== child.data.id);
        }
      }
    });
    
    // Expand the node to show children
    node.expanded = true;
    this.expandedRows[node.key] = true;
    
  } else {
    // This is a child node (quant)
    this.toggleQuant(node);
    
    // Check if parent should be updated
    const parent = this.treenodes.find(n => 
      n.children && n.children.some(c => c.key === node.key)
    );
    
    if (parent) {
      console.log("updating parent", parent);
      
      // Check if all children are now selected
      const allChildrenSelected = parent.children.every(c => c['selected']);
      
      // Update parent state based on children
      if (allChildrenSelected) {
        parent['selected'] = true;
      } else {
        parent['selected'] = false;
      }
      // If some but not all children are selected, you might want to use an indeterminate state
      // For now, we'll leave the parent as is in this case
      
      console.log("parent selected", parent['selected']);
    }
  }
  this.checkIfCanMoveInBlock();
}

  async checkIfCanMoveInBlock() {
    //update selected packages array
    let selection = this.treenodes.filter(n => n['selected'] && n.data.nodeType === 'package').map(n => n.data);
    console.log("selected packages", selection);
    //check if we have the same location for all selected packages
    if (selection.length === 0) {
      this.canMoveInBlockTo = null;
      this.fromLocation = null;
      return;
    }
    //take the "Locazione" field from the packages
    const uniqueLocations = [...new Set(selection.map(p => p.Locazione))];
    if (uniqueLocations.length === 1) {
      this.fromLocation = this.locations.find(l => l.name === uniqueLocations[0]);
      this.canMoveInBlockTo = this.locations.filter(l => l.name !== uniqueLocations[0]);
    } else {
      this.canMoveInBlockTo = null;
      this.fromLocation = null;
    }
    console.log("can move in block", this.canMoveInBlockTo);
  }

  async checkIfAllToggled() {
    //check if all packages are selected
    const allPackages = this.treenodes.filter(n => n.data.nodeType === 'package');
    const allSelected = allPackages.every(n => n['selected']);
    return allSelected;
  }

  getSelectedPackages(): any[] {
    return this.treenodes
      .filter(n => n['selected'] && n.data.nodeType === 'package')
      .map(n => n.data);
  }


  customSort() {
    
  }

  toggleQuant(node:TreeNode) {
    // check if already selected
    if (this.selectedQuantIds.includes(node.data.id)) {
      this.selectedQuantIds = this.selectedQuantIds.filter(id => id !== node.data.id);
      return;
    }
    this.selectedQuantIds.push(node.data.id);
    console.log("select quant", node);
  }

  async toggleAll() {

    //if all toggled, run deselect all
    if (await this.checkIfAllToggled()) {
      console.log("deselect all");
      this.treenodes.forEach(n => {
        if (n.data.nodeType === 'package') {
          this.nodeSelect(n);
        }
      });
      this.checkIfCanMoveInBlock();
      return;
    }
    else {
      //run selection thoruog all non selected packages
      this.treenodes.forEach(n => {
        if (n.data.nodeType === 'package' && !n['selected']) {
          this.nodeSelect(n);
        }
      });
    }

    this.checkIfCanMoveInBlock();
  }

  // Node unselection event handler
  // nodeUnselect(event: any) {
  //   console.log('Node unselected:', event.node);

  //   //adjust the selectednodes array removing the node and all his children
  //   this.selectedNodes = this.selectedNodes.filter(node => node !== event.node);
  //   console.log("selectedNodes", this.selectedNodes);
  //   //remove from the selected ids all the quants inside this node
  //   let ids = []
  //   if (event.node.children) {
  //     event.node.children.forEach((child: any) => {
  //       if (child.data && child.data.nodeType === 'quant' && child.data.id) {
  //         ids.push(child.data.id);
  //       }
  //     });
  //   }
  //   if (event.node.data && event.node.data.nodeType === 'quant' && event.node.data.id) {
  //     ids.push(event.node.data.id);
  //   }
  //   console.log("ids to remove", ids);
  //   this.selectedQuantIds = this.selectedQuantIds.filter(id => !ids.includes(id));
  //   console.log("selectedQuantIds", this.selectedQuantIds);
  // }

  async refreshQuants() {
    this.loading = true;
    this.showPhotos = false;
    console.log('Refreshing quants...');
    try {
      // 1) Salvo una copia delle colonne correnti
      const oldColumns = this.allColumns;
  
      // 2) Get data from server
      const quants = await this.getFilteredQuants();
      this.quants = quants;
      console.log('Fetched quants:', quants.length);
  
      if (quants.length) {
        // 3) Dynamically build columns from response, copiando searchInput da oldColumns

        const sample = quants[0];
        this.allColumns = Object.keys(sample).map(field => {
          const cell = sample[field];
          const isAttr = cell && typeof cell === 'object' && cell.attribute_id != null && cell.value != null;
          const shouldBeSelected = !field.endsWith('id'); // no need to show ids
          const label = field; //if field is a dimenison (starts with lunghezza, altezza, larghezza, trim to 3 chars)
          const style = this.getColumnStyle(field, isAttr);

          // Trovo la colonna precedente (se esiste)
          const old = oldColumns.find(c => c.field === field);
  
          return {
            id: isAttr ? cell.attribute_id : undefined,
            field, 
            fieldType: isAttr ? 'attribute' : 'field',
            callString: isAttr ? `${field}.value` : undefined,
            label: label,
            type: this.inferType(quants, field),
            sortable: true,
            style: style,
            selected: old?.selected ?? shouldBeSelected,      // preserva selezione, se non c'è selezione, includi solo non extra
            sort: old?.sort,                         // preserva ordinamento
            searchInput: old?.searchInput || '',     // preserva filtro di ricerca
            editable: false                          // default not editable
          } as ColumnType;
        });

        // filtro colonne "Extra" e limitazione
        this.allColumns = this.allColumns.filter(col =>
          !col.field.toLocaleLowerCase().includes('extra') && !col.label.includes('Extra')
        );

        // await this.reorderColumns();
        
        if (this.allColumns.length > 30) {
          console.warn(`Too many columns (${this.allColumns.length}). Limiting to 30.`);
          this.allColumns = this.allColumns.slice(0, 30);
          this.showAlertAtLeastOneColumn = true;
        }


        // Re-add edit mode columns if edit mode is active
        if (this.editMode) {
          this.addEditModeColumns();
        }

        this.selectedColumns = this.allColumns.filter(col => col.selected);
        // check if we have persisted col 
        // persisted is like this
        // get item  {"dragIndex":0,"dropIndex":9,"columns":[{"field":"Collo","fieldType":"field","label":"Collo","type":"text","sortable":true,"style":"Collo","selected":true,"searchInput":"","editable":false},{"field":"Data Pacco","fieldType":"field","label":"Data Pacco","type":"text","sortable":true,"style":"Collo","selected":true,"searchInput":"","editable":false},{"field":"Tag","fieldType":"field","label":"Tag","type":"text","sortable":true,"style":"Collo","selected":true,"searchInput":"","editable":false},{"field":"Note Pacco","fieldType":"field","label":"Note Pacco","type":"text","sortable":true,"style":"Collo","selected":true,"searchInput":"","editable":false},{"field":"Quantità","fieldType":"field","label":"Quantità","type":"number","sortable":true,"style":"Collo","selected":true,"searchInput":"","editable":false},{"field":"Costo um","fieldType":"field","label":"Costo um","type":"number","sortable":true,"style":"Collo","selected":true,"searchInput":"","editable":false},{"field":"Costo","fie
        const savedOrder = localStorage.getItem('packageListColumnOrder_' + this.selectedProductTemplate.id);
        // console.log("save order ", savedOrder);


        if (savedOrder) {
          const parsed = JSON.parse(savedOrder);
          this.selectedColumns = this.selectedColumns.sort((a, b) => {
            const aIndex = parsed.columns.findIndex((col: any) => col.field === a.field);
            const bIndex = parsed.columns.findIndex((col: any) => col.field === b.field);
            return aIndex - bIndex;
          });
        }

      }
  
      // 4) resto del tuo flusso (creazione tree, aggregazioni, ecc.)

      this.updateTreeNodes();
      this.calculateTotals(); 
  
    } finally {
      this.loading = false;
      this.quantsComplete = true;
      console.log('Quants refresh completed');
    }
  }


 
  getColumnStyle (field: string, isAttr: boolean): 'Collo' | 'Dettaglio' | 'Categoria' | 'Prodotto' {
    //if it's a dimension we call it Dettaglio
    if (field === 'Lunghezza' || field === 'Larghezza' || field === 'Altezza' || field === 'Spessore' || field === 'Pz') {
      return 'Dettaglio';
    }
    //if it's ana ttribute we call it Prodotto
    else if (isAttr) {
      return 'Prodotto';
    }
    //in all other cases for now we assume it's a Collo filed
    else {
      return 'Collo';
    }
  }

  getStyleClass(col: ColumnType) {
 //based off of the style og f the column we bg the cell a certain way
    if (col.style === 'Collo') {
      return 'text-secondary ';
    }
    else if (col.style === 'Dettaglio') {
      return 'text-danger';
    }
    else if (col.style === 'Categoria') {
      return 'bg-light';
    }
    else if (col.style === 'Prodotto') {
      return 'text-success ';
    }
    else {
      return '';
    }
  }

  


  
  /** Simple type inference: if first value is number, use number type, otherwise text */
  private inferType(data: any[], field: string): 'text' | 'number' | 'date' | 'currency' | 'dimension' {
    let val = data[0][field];
    
    // If it's an attribute, take the .value
    if (val && typeof val === 'object' && 'value' in val) {
      val = val.value;
    }
    
    // Handle null/undefined values
    if (val === null || val === undefined) return 'text';
    
     // Check if it's a dimension
     if (field === 'Lunghezza' || field === 'Larghezza' || field === 'Altezza') {
      return 'dimension';
    }

    //check if it's cost  
    if (field === 'Costo' || field === 'Costo um' ) {
      return 'currency';
    }

    if (typeof val === 'number') return 'number';
    
    return 'text';
  }

   // Create tree nodes with proper structure for PrimeNG TreeTable
   createTreeNodes(quants: QuantFromServerAction[]): TreeNode[] {
    const packageMap = new Map<string, TreeNode>();

    quants.forEach(quant => {
      const packageKey = quant.Collo || '';

      // Package node
      let pkg = packageMap.get(packageKey);
      if (!pkg) {
        const pkgData: any = { 
          nodeType: 'package', 
          Collo: packageKey,
          'Data Pacco': quant['Data Pacco'],
          'Note Pacco': quant['Note Pacco'],
          'Tag': quant['Tag'],
          Locazione: quant.Locazione,
          'product_id.product_tmpl_id.name': quant['product_id.product_tmpl_id.name'],
          'UdM': quant['UdM']
        };
        
        pkg = {
          key: packageKey,
          data: pkgData,
          expanded: true,
          children: [],
          type: 'package',
          selectable: false,
         
        };
        pkg['selected'] = false;
        packageMap.set(packageKey, pkg);
      }

      // Quant node
      const quantNode: TreeNode = {
        key: String(quant.id),
        data: { nodeType: 'quant', ...quant },
        type: 'quant',
        selectable: true,
      };
      quantNode['selected'] = false;
      pkg.children!.push(quantNode);
    });

    return Array.from(packageMap.values());
  }

  async getFilteredQuants(): Promise<QuantFromServerAction[]> {
    const sortable = this.allColumns.filter(col => col.sortable && col.sort);
    const sort = sortable.map(col => [col.field, col.sort!] as [string, 'asc'|'desc']);

    const criteria: any[] = [];
    const domain: any[] = [];

    this.allColumns.forEach(col => {
      var input = col.searchInput?.trim();
      // convert dot to comma
      input = input.replace(",",".")

      if (!input) return;

      if (col.fieldType === 'attribute') {
        // 1) operatori espliciti =, >, <
        let op: '='|'>'|'<'|'ilike';
        let term = input;
        if (['=', '>', '<'].includes(input[0])) {
          op = input[0] as any;
          term = input.slice(1).trim();
        } else if (input.includes('-') && input != '-') {
          // 2) gestione range a-b
          const [minStr, maxStr] = input.split('-');
          const min = parseFloat(minStr.trim());
          const max = parseFloat(maxStr.trim());
          criteria.push([col.label, '>', min], [col.id!, '<', max]);
          return;
        } else {
          // 3) nessun prefisso: decide da valore
          const num = Number(term);
          if (!isNaN(num)) op = '=';
          else op = 'ilike';
        }
        if (op === 'ilike') {
          criteria.push([col.field, 'ilike', `${term}`]);
        } else {
          const num = Number(term);
          criteria.push([col.label, op, num]);
        }
      } else {
        // searching criterias
        // if (col.field === 'Pz') {
        //   domain.push(["package_id.ga_data_pacco", 'ilike', `%${input}%`]);
        // }
        if (col.field === 'Note Pacco') {
          domain.push(["package_id.ga_note_pacco", 'ilike', `%${input}%`]);
        }
        if (col.field === 'Collo') {
          domain.push(["package_id", 'ilike', `%${input}%`]);
        }
        if (col.field === 'Quantità') {
          domain.push(["qty_available", '=', `%${input}%`]);
        } 
        if (col.field === 'Locazione') {
          domain.push(["location_id.name", 'ilike', `%${input}%`]);
        }
      }
    });


    // filtri fissi
    domain.push(['location_id.usage', '=', 'internal']);
    domain.push(['product_id.active', '=', true]);
    domain.push(['product_id.product_tmpl_id.name', 'ilike', this.selectedProductTemplate.searchPattern]);

    const response = await this.odooEm.searchByAttrs(domain, criteria, sort);
    return response.result.params.data;
  }

  getQuantValue(rowNode: any, col: ColumnType): any {
    if (col.fieldType === 'attribute') {
      // console.log('rowNode.node.data[col.field]', rowNode.node);
      return rowNode.node.data[col.field]?.value ?? '';
    }

    return rowNode.node.data[col.field] ?? '';
  }

  getQuantValue4Table(line: any, col: ColumnType): any {
    // return line.data[col.field];
    if (col.fieldType === 'attribute') {
      // console.log('rowNode.node.data[col.field]', rowNode.node);
      return line.data[col.field]?.value ?? '';
    }
    return line.data[col.field] ?? '';
  }

  updateColumns() {
    this.allColumns.forEach(col => {
      col.selected = this.selectedColumns.some(selected => selected.field === col.field);
    });
  }


  async getPackageUrl(packageNode: TreeNode) {
    //fetch the id of the treenode
    console.log("fetching id of treenode", packageNode);
    let packName = packageNode.key
    //search for package
    let packs = await this.odooEm.search<StockQuantPackage>(new StockQuantPackage(), [["name", "=", packName]]).toPromise()
    let packId = packs[0].id
    //open link in another window
    window.open(`https://o3.galimberti.eu/web#id=${packId}&cids=1&menu_id=223&action=370&model=stock.quant.package&view_type=form`, '_blank');  
  }
 

  exportSelectedQuants() {
    // Get all selected quant IDs
    if (this.selectedQuantIds.length > 0) {
      this.emitQuants.emit(this.selectedQuantIds);
    }
  }
 
/**
 * Aggregates bottom-up the specified fields for each node in the tree.
 * @param tree l'array di TreeNode da processare
 * @param aggFields i nomi dei campi da sommare (default: ['Quantità','Valore'])
 */
private aggregateQuantitaValore(tree: TreeNode[], aggFields: string[] ): void {
  const recurse = (node: TreeNode): Record<string, number> => {
    const data = node.data as any;
    // inizializza a 0 tutti i totali
    const totals: Record<string, number> = {};
    aggFields.forEach(f => totals[f] = 0);

    if (node.children && node.children.length > 0) {
      // nodo intermedio: somma dai figli
      for (const child of node.children) {
        const childTotals = recurse(child);
        aggFields.forEach(f => totals[f] += childTotals[f] || 0);
      }
      // assegna i totali calcolati
      aggFields.forEach(f => data[f] = totals[f]);
      return totals;
    } else if (data.nodeType === 'quant') {
      // foglia: prendi i valori già presenti
      aggFields.forEach(f => {
        totals[f] = Number(data[f] || 0);
      });
      return totals;
    } else {
      // nodo non-quant senza figli
      return totals;
    }
  };

  tree.forEach(root => recurse(root));
}

/**
 * Aggrega dal basso verso l'alto il valore minimo e massimo
 * per tutte le colonne di tipo 'attribute'.
 */
/**
 * Aggrega dal basso verso l'alto il valore minimo e massimo
 * per gli attributi numerici e concatena le stringhe per gli attributi testuali.
 */
private aggregateAttributeMinMax(tree: TreeNode[]): void {
  // 1) Prendo TUTTE le colonne attribute
  const attrCols = this.allColumns.filter(col => col.fieldType === 'attribute');
  const attrFields = attrCols.map(col => col.field);

  // 2) Mappo field → type per decidere stringa vs numero
  const attrTypeMap = new Map<string, ColumnType['type']>(
    attrCols.map(col => [col.field, col.type] as [string, ColumnType['type']])
  );

  // 3) Funzione ricorsiva
  const recurse = (node: TreeNode): Record<string, any> => {
    const data: any = node.data;
    // Inizializzo uno stato diverso a seconda del tipo
    const stats: Record<string, any> = {};
    attrFields.forEach(f => {
      if (attrTypeMap.get(f) === 'text') {
        stats[f] = new Set<string>();
      } else {
        stats[f] = { min: Infinity, max: -Infinity };
      }
    });

    if (node.children && node.children.length) {
      // Nodo intermedio: aggrego dai figli
      for (const child of node.children) {
        const childStats = recurse(child);
        attrFields.forEach(f => {
          if (attrTypeMap.get(f) === 'text') {
            // unisco i Set di stringhe
            childStats[f].forEach((v: string) => stats[f].add(v));
          } else {
            // min/max numerico. 
            stats[f].min = Math.min(stats[f].min, childStats[f].min);
            stats[f].max = Math.max(stats[f].max, childStats[f].max);
          }
        });
      }
      // Assegno al node.data il valore aggregato
      attrFields.forEach(f => {
        if (attrTypeMap.get(f) === 'text') {
          // concatena valori unici con virgola 
          data[f] = {
            attribute_id: (data[f] as any)?.attribute_id,
            value: Array.from(stats[f]).join(', ')
          };
        } else {
          const { min, max } = stats[f]; //format "100.000 - 200.000 with "." to divide  thousands"
            let minFormatted = min.toLocaleString('it-IT', { minimumFractionDigits: 0, maximumFractionDigits: 2 });
            let maxFormatted = max.toLocaleString('it-IT', { minimumFractionDigits: 0, maximumFractionDigits: 2 });
          data[f] = { 
            attribute_id: (data[f] as any)?.attribute_id,
            value: min === max ? minFormatted : `${minFormatted} - ${maxFormatted}`
          };
        }
      });

    } else if ((data.nodeType as string) === 'quant') {
      // Foglia: prendo direttamente i valori
      attrFields.forEach(f => {
        const raw = data[f];
        const valObj = raw && typeof raw === 'object' && 'value' in raw ? raw.value : raw;
        if (attrTypeMap.get(f) === 'text') {
          // se è stringa, la metto nel Set
          stats[f].add(String(valObj ?? '').trim());
        } else {
          // numero: parsifico
          const num = Number(valObj) || 0;
          stats[f].min = stats[f].max = num;
        }
      });
    }

    return stats;
  };

  // 4) Avvio la ricorsione su ogni radice
  tree.forEach(root => recurse(root));
}
calculatePackageUnitCost(tree: TreeNode[]): void {
  const recurse = (node: TreeNode): void => {
    // Process children first (bottom-up approach)
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => recurse(child));
      
      // If this is a package node, calculate unit cost from children
      if (node.data.nodeType === 'package') {
        let totalCost = 0;
        let totalQuantity = 0;
        
        // Sum up costs and quantities from all quant children
        node.children.forEach(child => {
          if (child.data.nodeType === 'quant') {
            const cost = Number(child.data['Costo']) || 0;
            const quantity = Number(child.data['Quantità']) || 0;
            
            totalCost += cost;
            totalQuantity += quantity;
          }
        });
        
        // Calculate and assign unit cost to package
        if (totalQuantity > 0) {
          const unitCost = totalCost / totalQuantity;
          node.data['Costo um'] = unitCost;
          
          console.log(`Package ${node.data.Collo}: Total Cost=${totalCost}, Total Qty=${totalQuantity}, Unit Cost=${unitCost.toFixed(2)}`);
        } else {
          node.data['Costo um'] = 0;
          console.warn(`Package ${node.data.Collo} has zero quantity - setting unit cost to 0`);
        }
      }
    }
  };
  
  // Process all root nodes
  tree.forEach(root => recurse(root));
}

  trackByColumn(index: number, column: ColumnType): string {
    return column.field;
  }

  compareById(option1: any, option2: any): boolean {
    return option1 && option2 ? option1.id === option2.id : option1 === option2;
  }

  async togglePhotos() {
    this.showPhotos = !this.showPhotos;
    this.loadedPhotos = false;
    if (this.showPhotos){

      //select all package nodes
      let packageNodes = this.treenodes.filter(node => node.data.nodeType === 'package');
      //for each pachage node, store the id of its first child in a set
      let quantIds = new Set<number>();
      packageNodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          quantIds.add(Number(node.children[0].key));
        }
      });
      //fetch all quants from odoo to load data for photos. take ids form nodes. field "id" only for quant nodes
  
      console.log("IDs to fetch: ",   Array.from(quantIds));
      this.quantsForPhotos = await firstValueFrom(this.odooEm.search<StockQuant>(new StockQuant(), [["id", "in", Array.from(quantIds)]]));
      //resolve package ids
      await firstValueFrom(this.odooEm.resolveArrayOfSingle(new StockQuantPackage(), this.quantsForPhotos, "package_id"));
      console.log("Quants for photos: ", this.quantsForPhotos);
      this.loadedPhotos = true;
    }
  }

  showPackagePhotos(event: MouseEvent, pack: TreeNode) {
    this.showingQuantPhotos = null;
    this.photosPanel.hide();
    const packageQuantId = Number(pack.children[0].key);
    const found = this.quantsForPhotos.find(q => q.id === packageQuantId);
  
    if (found) {
      this.showingQuantPhotos = found;
        // Open with a small delay to let the DOM/render update
  setTimeout(() => {
    if (this.showingQuantPhotos) {
      this.photosPanel.show(event);
    }
  }, 10);
    } else {
      console.error('Quant not found for package:', pack.key);
    }
  }

  getPackageFromQuants(packageNode: TreeNode): StockQuant | null {
    if (!packageNode.children || packageNode.children.length === 0) {
      return null;
    }
    
    const quantId = Number(packageNode.children[0].key);
    return this.quantsForPhotos.find(q => q.id === quantId) || null;
  }
  

  getQuantFromPackage(pack: TreeNode): StockQuant | null {
    if (!pack.children || pack.children.length === 0) return null;
    //find in this.quantsForPhotos the quant with id = pack.children[0].data.id
    let idToFind = Number(pack.children[0].key);
    let found = this.quantsForPhotos.find(q => q.id === idToFind);
    this.showingQuantPhotos = found;
    
    return found; // Add this return statement
  }


  persistSelectedTemplate(): void {
    localStorage.setItem('packageListSelectedTemplate', JSON.stringify(this.selectedProductTemplate));
  }

private calculateTotals(): void {
  // Reset totals
  this.totalQuantities = {};
  this.selectedQuantities = {};

  // Initialize totals for numeric columns
  this.selectedColumns.forEach(col => {
    if (col.type === 'number') {
      this.totalQuantities[col.field] = 0;
      this.selectedQuantities[col.field] = 0;
    }
    
  });

  // Calculate totals from all quants
  this.quants.forEach(quant => {
    this.selectedColumns.forEach(col => {
      if (col.type === 'number') {
        const value = this.getQuantValueForCalculation(quant, col);
        if (typeof value === 'number' && !isNaN(value)) {
          this.totalQuantities[col.field] += value;
          
          // Add to selected totals if this quant is selected
          if (this.selectedQuantIds.includes(quant.id)) {
            this.selectedQuantities[col.field] += value;
          }
        }
      }
    });
  });
  //recalculate costo UM 
  this.totalQuantities['Costo um'] = this.totalQuantities['Costo'] / this.totalQuantities['Quantità'];
  this.selectedQuantities['Costo um'] = this.selectedQuantities['Costo'] / this.selectedQuantities['Quantità'];
}


hasSomeFilter():number {
  return this.allColumns.filter(col => col.searchInput?.trim().length > 0).length;
}

private getQuantValueForCalculation(quant: any, col: ColumnType): number {
  let value: any;
  
  if (col.fieldType === 'attribute') {
    value = quant[col.field]?.value;
  } else {
    value = quant[col.field];
  }
  
  // Convert to number, return 0 if not a valid number
  const numValue = Number(value);
  return isNaN(numValue) ? 0 : numValue;
}

formatTotal(value: number, col: ColumnType): string {
  if (col.type === 'number') {
    return value.toLocaleString('it-IT', { 
      minimumFractionDigits: 0, 
      maximumFractionDigits: 2 
    });
  }
  return '';
}

// 6. Method to check if column should show totals:

shouldShowTotal(col: ColumnType): boolean {
  return col.type === 'number' && 
         !col.field.includes('id') && 
         col.field !== 'selectedPieces'; // Exclude edit mode columns
}

private clearAllSelections(): void {
  // Reset delle selezioni nei nodi
  this.treenodes.forEach(node => {
    node['selected'] = false;
    if (node.children) {
      node.children.forEach(child => {
        child['selected'] = false;
      });
    }
  });
  
  // Reset degli array di selezione
  this.selectedQuantIds = [];
  
  // Reset delle variabili di spostamento
  this.canMoveInBlockTo = null;
  this.fromLocation = null;
}
}
