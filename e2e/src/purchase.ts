import { browser, by, element, protractor } from 'protractor';
const EC = protractor.ExpectedConditions;

export class PurchaseTest {

    async checProductLoad() {
        await browser.wait(EC.visibilityOf(element(by.css('[data-test-id=orderLinesList] .badge-success'))), 15000);
    }

    async confirmkLoadPicking(barcodeId) {

        element.all(by.css('[data-test-id=orders] a')).first().click()

        await browser.wait(EC.presenceOf( element(by.css('[data-test-id=keyboardButton]'))), 15000);
        var el = await element(by.css('[data-test-id=keyboardButton]'))
        await browser.driver.executeScript("arguments[0].click();", el.getWebElement());

        var input = await element(by.css('[data-test-id=keyboardInput]'))
        await browser.driver.executeScript("arguments[0].click();", input.getWebElement());
        await input.sendKeys(barcodeId)

        var el = await element(by.css('[data-test-id=keyboardConfirm]'))
        await browser.driver.executeScript("arguments[0].click();", el.getWebElement());

        element(by.css('[data-test-id=confermaButton]')).click()
    }

    async checkBarCode(barcode: string) {
        var el = await element(by.cssContainingText('[data-test-id=listProductToLoad] [data-test-id=barcode]', barcode))
        await browser.driver.executeScript("arguments[0].click();", el.getWebElement());

        await browser.wait(EC.visibilityOf(element(by.css('[data-test-id=listPickingLinesDoing] [data-test-id=barcode]'))), 15000);
        var el = await element(by.cssContainingText('[data-test-id=listPickingLinesDoing] [data-test-id=barcode]', barcode))
        await browser.driver.executeScript("arguments[0].click();", el.getWebElement());
    }

    async addBarCode(barCodeName: String) {

        await browser.wait(EC.visibilityOf(element(by.css('[data-test-id=addBarcodeButton]'))), 15000)

        var el = await element(by.css('[data-test-id=addBarcodeButton]'))
        await browser.driver.executeScript("arguments[0].click();", el.getWebElement());

        var el = await element(by.css('[data-test-id=keyboardButton]'))
        await browser.driver.executeScript("arguments[0].click();", el.getWebElement());

        var input = await element(by.css('[data-test-id=keyboardInput]'))
        await browser.driver.executeScript("arguments[0].click();", input.getWebElement());
        await input.sendKeys(barCodeName)

        var el = await element(by.css('[data-test-id=keyboardConfirm]'))
        await browser.driver.executeScript("arguments[0].click();", el.getWebElement());
    }

    async navigateToReceipt(id: string) {
        await browser.get('/receipt')
        var el = await element(by.cssContainingText('[data-test-id=orderTitle]', "PO00" + id))
        await browser.driver.executeScript("arguments[0].click();", el.getWebElement());
    }

    async navigateToload(id: string) {
        await browser.get('/load')
        var el = await element(by.cssContainingText('[data-test-id=orderTitle]', id))
        await browser.driver.executeScript("arguments[0].click();", el.getWebElement());
    }

    async confirmPurchasesOrder() {
        await browser.driver.executeScript("arguments[0].click();", element(by.css('[data-test-id=confirmButton]')).getWebElement());
    }

    async newPurchasesOrder() {

        await browser.wait(EC.presenceOf( element(by.css('[data-test-id=purchasesButton]'))), 15000);

        var purchasesBtn = element.all(by.css('[data-test-id=purchasesButton]')).get(0)
        await browser.driver.executeScript("arguments[0].click();", purchasesBtn.getWebElement());

        var createPurchaserderBtn = element.all(by.css('[data-test-id=createPurchasesOrderButton]')).get(0)
        await browser.driver.executeScript("arguments[0].click();", createPurchaserderBtn.getWebElement());
    }

    async addProduct(id) {

        await browser.driver.executeScript("arguments[0].click();", element.all(by.css('[data-test-id=inventoryButton]')).get(0).getWebElement());
        var input = element(by.css('[data-test-id=search-input]'))
        input.clear()
        input.sendKeys(id)
        element.all(by.css('[data-test-id=tableInventory]')).first().click()
        var close = element(by.css('[data-test-id=inventoryCloseIcon]'))
        browser.driver.executeScript("arguments[0].click();", close.getWebElement());
    }
}