import { Component, Input, Output, EventEmitter } from '@angular/core';
import { MailActivity } from 'src/app/models/mail.message';
import { Lead } from 'src/app/models/crm.lead.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { User } from '../models/user.model';
import { firstValueFrom } from 'rxjs';

interface ReminderType {
  id: number;
  name: string;
}

@Component({
    selector: 'app-reminder-popup',
    templateUrl: './reminder-popup.component.html',
    styleUrls: ['./reminder-popup.component.scss'],
    standalone: false
})
export class ReminderPopupComponent {
  @Input() lead: Lead;
  @Output() close = new EventEmitter<boolean>();

  daysFromToday: number = 7;
  selectedActivityType: ReminderType | null = null;
  summary: string = '';
  user: User | null = null;
  assignedUser: User | null = null;
  users: User[] = [];

  activityTypes: ReminderType[] = [
    { id: 2, name: '<PERSON><PERSON><PERSON>' },
    { id: 1, name: 'Email' },
    { id: 23, name: 'Appuntamento' },
    { id: 21, name: 'Attività libera' }
  ];

  constructor(private odooEm: OdooEntityManager) {}

  async ngOnInit() {
    console.log('activity reminder for this lead ', this.lead);
      const result: any = await this.odooEm.odoorpcService.getSessionInfo();
      const userId = result.result.user_id[0];
      const users = await firstValueFrom(this.odooEm.search<User>(
        new User()))

      this.users = users;
      this.user = this.users.find(u => u.id === userId);
      this.assignedUser = this.user;
    }

  async saveReminder() {
    if (!this.selectedActivityType) {
      alert('Seleziona un tipo di attività');
      return;
    }

    const reminderDate = new Date();
    reminderDate.setDate(reminderDate.getDate() + this.daysFromToday);

    try {
      const reminderActivity = {
        activity_type_id: this.selectedActivityType.id,
        res_model: 'crm.lead',
        res_id: this.lead.id,
        res_model_id: 620,
        summary: this.summary,
        date_deadline: reminderDate.toISOString().split('T')[0],
        user_id: this.assignedUser.id,
        create_uid: this.user.id,
        note: 'Reminder creato all\'invio del preventivo'
      };

      await this.odooEm.create(new MailActivity(), reminderActivity).toPromise();
      this.close.emit(true);
    } catch (error) {
      console.error('Errore nella creazione del reminder', error);
      alert('Errore nella creazione del reminder');
    }
  }

  cancelReminder() {
    this.close.emit(false);
  }
}