const cookie = require('cookie')

exports.handler = async (event, context) => {

  // var res = JSON.parse(event.body)

  var sid = event.queryStringParameters.sid
  
  const hour = 3600000
  const twoWeeks = 31 * 24 * hour
  const myCookie = cookie.serialize('session_id', sid, {
    secure: true,
    httpOnly: true,
    path: '/',
    maxAge: twoWeeks,
  })
  const redirectUrl = 'https://m3.galimberti.eu/home'
  // Do redirects via html
  const html = `
  <html lang="en">
    <head>
      <meta charset="utf-8">
    </head>
    <body>
      Loggin in ...
      <noscript>
        <meta http-equiv="refresh" content="0; url=${redirectUrl}" />
      </noscript>
    </body>
    <script>
      setTimeout(function() {
        window.location.href = ${JSON.stringify(redirectUrl)}
      }, 500)
    </script>
  </html>`

  return {
    'statusCode': 200,
    'headers': {
      'Set-Cookie': myCookie,
      'Cache-Control': 'no-cache',
      'Content-Type': 'text/html',
    },
    'body': html
  }
}