# GaliErp3
 npx ng serve --host 0.0.0.0 --port 4201 --proxy-config proxy.conf.json           



This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 7.3.0.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Dev end-to-end tests 
Run `npx kill-port 4200  && npx kill-port 8888 && netlify dev -c 'yarn startTest'` to start angular with enviroment test 
Run `ng e2e` to execute all end-to-end tests via [Protractor](http://www.protractortest.org/)
Run `./node_modules/protractor/bin/protractor ./e2e/protractor.dev.conf.js`  to execute signle end-to-end test describe in conf

## Running end-to-end tests
Run `yarn e2e` to execute the end-to-end tests via [Protractor](http://www.protractortest.org/).

## Further help
To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI README](https://github.com/angular/angular-cli/blob/master/README.md).





TRELLO WEBHOOK

TRELLO_API_KEY=<your api key>
TRELLO_TOKEN=<your oauth token>
CardID=<your card id>
CustomFieldID=<your custom field id>

curl -X PUT -H "Content-Type: application/json" \
  "https://api.trello.com/1/card/6013f403f8db4902899f3243/customField/5d25c8c9f06bea31cb01e350/item?key=e6ad0b34f266117a08b2e8704fdc6f29&token=24c7501940680c5c1d974e7707808d4d56065eebb6e08dd5d47550c3ec5df5a5" \
-d '{"value": { "text": "Hello, world!" }}'


curl -X POST -H "Content-Type: application/json" \
https://api.trello.com/1/card/6013f403f8db4902899f3243/webhooks/ \
-d '{
  "key": "e6ad0b34f266117a08b2e8704fdc6f29",
  "idModel":"5c910c36323dbb560103a1ff",
  "description": "testing"
}'


curl -X POST -H "Content-Type: application/json" \
https://api.trello.com/1/tokens/24c7501940680c5c1d974e7707808d4d56065eebb6e08dd5d47550c3ec5df5a5/webhooks/ \
-d '{
  "key": "e6ad0b34f266117a08b2e8704fdc6f29",
  "callbackURL": "http://0.tcp.ngrok.io:15002/.netlify/functions/webhook-trello",
  "idModel":"5c910c36323dbb560103a1ff",
  "description": "testing"
}'

body

 body: '{"model":{"id":"5c910c36323dbb560103a1ff","name":"412 - Produzione tetti","desc":"","descData":null,"closed":false,"idOrganization":"50f5462a8cef062824006f39","idEnterprise":null,"pinned":false,"url":"https://trello.com/b/5DMOaWXQ/412-produzione-tetti","shortUrl":"https://trello.com/b/5DMOaWXQ","prefs":{"permissionLevel":"org","hideVotes":false,"voting":"disabled","comments":"members","invitations":"members","selfJoin":true,"cardCovers":true,"isTemplate":false,"cardAging":"regular","calendarFeedEnabled":true,"background":"red","backgroundImage":null,"backgroundImageScaled":null,"backgroundTile":false,"backgroundBrightness":"dark","backgroundColor":"#B04632","backgroundBottomColor":"#B04632","backgroundTopColor":"#B04632","canBePublic":true,"canBeEnterprise":true,"canBeOrg":true,"canBePrivate":true,"canInvite":true},"labelNames":{"green":"A VISTA","yellow":"PARETI","orange":"HUNDEGGER","red":"CONSEGNATO","purple":"NON A VISTA","blue":"CASSONI","sky":"","lime":"","pink":"BVX PRONTO","black":"Su lista"}},"action":{"id":"600eb9e8911ffb382b45d40d","idMemberCreator":"5fe1d53a00b9e36232a21a91","data":{"card":{"id":"600eb9e8911ffb382b45d40c","name":"sad","idShort":578,"shortLink":"zuIcs8fI"},"list":{"id":"600819932c0f41014f9ba175","name":"Da ERP"},"board":{"id":"5c910c36323dbb560103a1ff","name":"412 - Produzione tetti","shortLink":"5DMOaWXQ"}},"type":"createCard","date":"2021-01-25T12:30:32.271Z","appCreator":null,"limits":{},"display":{"translationKey":"action_create_card","entities":{"card":{"type":"card","id":"600eb9e8911ffb382b45d40c","shortLink":"zuIcs8fI","text":"sad"},"list":{"type":"list","id":"600819932c0f41014f9ba175","text":"Da ERP"},"memberCreator":{"type":"member","id":"5fe1d53a00b9e36232a21a91","username":"assistenzatecnica17","text":"Assistenza Tecnica"}}},"memberCreator":{"id":"5fe1d53a00b9e36232a21a91","username":"assistenzatecnica17","activityBlocked":false,"avatarHash":"39dac6990d9562cf72b2b0d62b2aff23","avatarUrl":"https://trello-members.s3.amazonaws.com/5fe1d53a00b9e36232a21a91/39dac6990d9562cf72b2b0d62b2aff23","fullName":"Assistenza Tecnica","idMemberReferrer":null,"initials":"AT","nonPublic":{},"nonPublicAvailable":true}}}',
  isBase64Encoded:


google api key
  AIzaSyAkr30ZMoby9PmMDGa0K3l_HUc_zgj51MU


  Building an erp in Angular that fetches data from Odoo v16 and through a unique-project css file that covers all components and bootstrap v5.3.0-alpha3 and FontAwesome icons builds an ui to present data to the user.
  components do not have a specific-css.
  always add some log messages to lightly debug code.
  All tables should be build through PrimeNG tables: https://primeng.org/table






  ssh thor2@192.168.10.24
  Evonet2023
  sudo ./rebuild.sh
  exit