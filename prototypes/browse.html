<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title><PERSON><PERSON><PERSON><PERSON></title>
  <link rel="icon" href="galimberti_favicon.png">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" type="text/css">
  <link rel="stylesheet" href="theme.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/1.10.192/css/jquery.dataTables.min.css">
</head>

<body class="d-flex flex-column h-100">
  <nav class="navbar navbar-expand-md border-bottom bg-dark navbar-dark">
    <div class="container-fluid">
      <a class="navbar-brand" href="#">
        <img src="logo.png" width="30" height="30" class="d-inline-block align-top" alt="">
        <b>&nbsp;Magazzino&nbsp;</b>
      </a>
      <button class="navbar-toggler navbar-toggler-right border-0" type="button" data-toggle="collapse" data-target="#navbar4" style="">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbar4">
        <form class="form-inline my-2 my-lg-0 ml-auto"> <input class="form-control mr-sm-2" type="text" placeholder="Cerca" style="	min-width: 400px;"> </form>
        <ul class="navbar-nav">
          <li class="nav-item active"> <a class="nav-link" href="#">Consultazione</a> </li>
          <li class="nav-item"> <a class="nav-link" href="#">Disponibilità</a> </li>
        </ul>
      </div>
    </div>
  </nav>
  <div class="flex-grow-1 d-flex h-100">
    <div class="container-fluid h-100">
      <div class="row h-100">
        <div class="p-4 col-2 bg-light border-right  panel panel-left">
          <ul class="list-unstyled">
            <li>Ferramenta <ul class="">
                <li>Connesione</li>
                <li>Infissione</li>
                <li>Tasselli</li>
                <li><b>Vite</b></li>
              </ul>
            </li>
            <li>Isolante</li>
            <li>Lamiera di copertura</li>
            <li>Lattoneria</li>
          </ul>
        </div>
        <div class="p-0 col d-flex w-100  panel" style="">
          <div class="table-responsive ">
            <table class="table table-bordered noselect " style="overflow-y: scroll; height:400px" id="order">
              <thead class="">
                <tr>
                  <th colspan="4" scope="colgroup" class="table-light">Prodotto</th>
                  <th colspan="4" scope="colgroup" class="table-info">Dimensioni</th>
                  <th colspan="1" scope="colgroup" class="table-light"></th>
                  <th colspan="2" scope="colgroup" class="table-info">Prezzo</th>
                  <th colspan="4" scope="colgroup" class="table-light">Quantità</th>
                  <th colspan="1" scope="colgroup" class="table-info">Altro</th>
                </tr>
                <tr>
                  <th class="table-light"></th>
                  <th class="table-light">Codice</th>
                  <th class="table-light">Nome</th>
                  <th class="table-light">Caratteristiche</th>
                  <th class="table-info">X</th>
                  <th class="table-info">Y</th>
                  <th class="table-info">Z</th>
                  <th class="table-info">R</th>
                  <th class="table-light">UM</th>
                  <th class="table-info">Privati</th>
                  <th class="table-info">Aziende</th>
                  <th class="table-light">Disp</th>
                  <th class="table-light">Imp</th>
                  <th class="table-light">Ord</th>
                  <th class="table-light"><i class="fa fa-clock-o"></i></th>
                  <th class="table-info">Fornitore</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <th class=""><i class="fa fa-copy"></i></th>
                  <th class="">6085</th>
                  <td class="">Perline abete</td>
                  <td>A/B</td>
                  <td>400</td>
                  <td>15,5</td>
                  <td>20</td>
                  <td></td>
                  <td class="">1.000</td>
                  <td class="">1.200</td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td>Galimberti SRL</td>
                </tr>
                <tr>
                  <th class=""><i class="fa fa-copy"></i></th>
                  <th class="">212</th>
                  <td>Perline abete</td>
                  <td>Qualità a vista</td>
                  <td>1200</td>
                  <td>12</td>
                  <td>12</td>
                  <td></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td></td>
                  <td></td>
                </tr>
                <tr>
                  <th class=""><i class="fa fa-copy"></i></th>
                  <th class="">212</th>
                  <td class="">Travi abete lamellare</td>
                  <td>Qualità a vista</td>
                  <td>1200<br></td>
                  <td>14</td>
                  <td>12</td>
                  <td></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td></td>
                  <td></td>
                </tr>
                <tr>
                  <th class=""><i class="fa fa-copy"></i></th>
                  <th class="">212</th>
                  <td class="">Travi abete lamellare&nbsp;</td>
                  <td>Qualità a vista</td>
                  <td>1200</td>
                  <td>16</td>
                  <td>12</td>
                  <td></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td></td>
                  <td></td>
                </tr>
                <tr>
                  <th class=""><i class="fa fa-copy"></i></th>
                  <th class="">212</th>
                  <td class="">Bocchetta svizzera</td>
                  <td>Acciaio preverniciato - Testa di moro RAL 8019</td>
                  <td>1400</td>
                  <td>12</td>
                  <td>12</td>
                  <td>10</td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td></td>
                  <td></td>
                </tr>
                <tr>
                  <th class=""><i class="fa fa-copy"></i></th>
                  <th class="">212</th>
                  <td class="">Bocchetta svizzera</td>
                  <td>Acciaio preverniciato - Testa di moro RAL 8019</td>
                  <td>1400</td>
                  <td>12</td>
                  <td>12</td>
                  <td>10</td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td></td>
                  <td></td>
                </tr>
                <tr>
                  <th class=""><i class="fa fa-copy"></i></th>
                  <th class="">212</th>
                  <td class="">Bocchetta svizzera</td>
                  <td>Acciaio preverniciato - Testa di moro RAL 8019</td>
                  <td>1400</td>
                  <td>12</td>
                  <td>12</td>
                  <td>10</td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td></td>
                  <td></td>
                </tr>
                <tr>
                  <th class=""><i class="fa fa-copy"></i></th>
                  <th class="">212</th>
                  <td class="">Bocchetta svizzera</td>
                  <td>Acciaio preverniciato - Testa di moro RAL 8019</td>
                  <td>1400</td>
                  <td>12</td>
                  <td>12</td>
                  <td>10</td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td class=""></td>
                  <td></td>
                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="card w-50 rounded-0" style="">
            <div class="card-body">
              <h4 class="d-flex"><span>Perline abete</span><i class="fa fa-times fa-fw ml-auto" id="close"></i></h4>
              <div class="table-responsive mb-3">
                <table class="table table-striped table-borderless">
                  <tbody>
                    <tr>
                      <th scope="row">Lunghezza</th>
                      <td>Mark</td>
                    </tr>
                    <tr>
                      <th scope="row">Larghezza</th>
                      <td>Jacob</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                    <tr>
                      <th scope="row">Diametro</th>
                      <td>Larry</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <img class="img-fluid d-block" src="https://static.pingendo.com/img-placeholder-1.svg">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js" integrity="sha384-wHAiFfRlMFy6i5SRaxvfOCifBUQy1xHdJ/yoi7FRNXMRBu5WHdZYu1hA6ZOblgut" crossorigin="anonymous"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
  <script src="https://cdn.datatables.net/1.10.18/js/jquery.dataTables.min.js"></script>
  <script style="">
    $(document).ready(function() {
      var table = $('#order').DataTable({
        searching: false,
        paging: false,
        info: false
      });
      $('#order tbody').on('mouseenter', 'td', function() {
        var colIdx = table.cell(this).index().column;
        $(table.cells().nodes()).removeClass('h');
        $(table.column(colIdx).nodes()).addClass('h');
      });
      // hide card at start
      $(".card").addClass("d-none")
      // close card
      $("#close").on("click", function() {
        //$("table tbody tr.h").removeClass("h")
        $(".card").addClass("d-none").removeClass("d-flex")
      })
      $('#order tbody tr').on("click", function() {
        if ($(this).hasClass("h")) {
          //$(".card").addClass("d-none").removeClass("d-flex")
          //$("table tbody tr.h").removeClass("h")
        } else {
          $("#order tbody tr.h").removeClass("h")
          //$(".card").addClass("d-flex").removeClass("d-none")
          $(".card h4 span").html($(this).find("td:nth(0)").html() + " " + $(this).find("td:nth(1)").html())
          $(this).addClass("h")
        }
      })
      // handle double click
      $('#order tbody tr').on("dblclick", function() {
        if ($(".card").hasClass("d-flex")) {
          $(".card").addClass("d-none").removeClass("d-flex")
        } else {
          $(".card").addClass("d-flex").removeClass("d-none")
          $(".card h4 span").html($(this).find("td:nth(0)").html() + " " + $(this).find("td:nth(1)").html())
        }
      })
    });
  </script>
</body>

</html>