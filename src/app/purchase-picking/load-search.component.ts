import { Component, ElementRef } from '@angular/core';
import { OrderComponent, ConfigOptions } from '../order/order-search.component';
import { RestapiService } from '../shared/services/rest-api.service';
import { Router } from '@angular/router';

@Component({
    selector: 'app-load',
    templateUrl: '../order/order-search.component.html',
    standalone: false
})

export class LoadSearchComponent extends OrderComponent  {
  constructor(
    public restapi : RestapiService,
    public router: Router,
    public elementRef : ElementRef,
  ) { 
    super(restapi, router, elementRef)
    this.config = this.getConfig()
  }

  postProcess(results:any[]):any[] {
    console.log("!!!",results)
    results.forEach((r) => {
      if ((r.barcode_ids && r.barcode_ids.length > 0 ) || r.picking_state == "in_arrivo")
        r.icon = "pallet-alt"
        console.log("RRR", r)
    })

    // keep first order with pending packages
    results = results.sort((r, b) => {
      if (r.icon == "pallet-alt" && r.icon == b.icon)
        return (b.id - r.id) 
      else if(r.icon)
        return -1         
      return 1
    })

    return results
  }
  
  getConfig(): ConfigOptions {
    return {
      table: "purchase.order",
      order: "date_order desc",
      titleNavbar: "Carico Magazzino",
      pathChild: "load/",
      fields : ["id","name","partner_id","state", "picking_state", "picking_ids", "barcode_ids"],
      criteria: [{
        column : "state",
        operator :"!=",
        value : "draft"
      }],
      stateMap : new Map([
        ["non_ricevuto", ["In arrivo", "bg-warning"]],
        ["in_arrivo", ["In arrivo", "bg-warning"]],
        ["ricevuto", ["Arrivato", "bg-success"]]
      ]),
      icon : "icon",
      // order: "barcode_ids asc",
      disableNoFilter : true,
      filters: [
      {
        label : "Aperti",
          criteria : [{
            column: "closed_picking_flag",
            operator: "!=",
            value: "true"
          }]
      },
      {
        label : "Chiusi",
        criteria : [{
          column: "closed_picking_flag",
          operator: "=",
          value: "true"
        }]
      }

    ]
    }
  }

  
}
