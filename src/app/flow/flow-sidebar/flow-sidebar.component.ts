import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { OdooEntityManager } from '../../shared/services/odoo-entity-manager.service';
import { Flow } from '../../models/flow-flow.model';
import { FlowService } from '../services/flow.service';

@Component({
    selector: 'app-flow-sidebar',
    templateUrl: './flow-sidebar.component.html',
    standalone: false
})
export class FlowSidebarComponent implements OnInit {

  @Input() flows:Flow[]
  @Input() selectedFlow:Flow
  @Output() choosedFlow = new EventEmitter<Flow>();

  constructor(
    private odooEM: OdooEntityManager, private flowService : FlowService
  ) { }

  async ngOnInit(): Promise<void> {
  }

  async choose (f){

    this.selectedFlow = f
    this.choosedFlow.emit(f);
    localStorage.setItem('lastFlow', JSON.stringify(f.id))
  }

  async add(){

    let name = prompt("Insert a name:");
    if(!name) return

    var f = {
      "name" : name,
      "date" : Math.floor(Date.now() / 1000) , 
      "actions" : ""
      // "actions_ids": [
      //   [0, 0, {
      //       parsed: 'parsed_data',
      //       debug: 'debug_data',
      //       action_type: 'type_data',
      //       busy: false,
      //       error: 'error_data',
      //       results: 'results_data',
      //       params_ids: [
      //           [0, 0, {
      //               key: 'key_data',
      //               value: 'value_data',
      //           }],
      //           // Add more arrays for more params
      //       ]
      //   }]
      // ]
    } 

    console.log("ffff", f)

    var s = await this.odooEM.create<Flow>(new Flow(), f).toPromise()
    if(s.id && this.flows)
    this.flows.push(s)
    this.choose(s)
  }

  async delete(f : Flow){
    this.flows = this.flows.filter(obj => obj.id !== f.id);
    await this.flowService.remove(f.id)

    this.choosedFlow.emit();
  }

}


