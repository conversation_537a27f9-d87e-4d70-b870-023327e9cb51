<app-navbar [loading]="loading" backroute="../..">
    <div class="d-flex align-items-center justify-content-between w-100">
        <!-- Title section -->
        <div class="navbar-brand">
            <span>Produzione {{production?.name}}</span>
        </div>
    </div>
</app-navbar>

<!-- Main content -->
<div *ngIf="!loading" class="container-fluid">
    <!-- Tabs navigation -->
    <ul class="nav nav-tabs mb-3">
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'products'" (click)="setActiveTab('products')"
                href="javascript:void(0)">
                <i class="fa fa-boxes me-2"></i>
                Prodotti
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'workorders'" (click)="setActiveTab('workorders')"
                href="javascript:void(0)">
                <i class="fa fa-tasks me-2"></i>
                Ordini di Lavoro
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'components'" (click)="setActiveTab('components')"
                href="javascript:void(0)">
                <i class="fa fa-puzzle-piece me-2"></i>
                Componenti
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link" [class.active]="activeTab === 'packaging'" (click)="setActiveTab('packaging')"
                href="javascript:void(0)">
                <i class="fa fa-puzzle-piece me-2"></i>
                Imballaggio
            </a>
        </li>

    </ul>

    <!-- Tab content -->
    <div class="tab-content">
       <!-- Products tab content -->
<div *ngIf="activeTab === 'products'" class="tab-pane fade show active">
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>Descrizione</th>
            <th>Dimensioni</th>
            <th class="text-end"></th> 
            <th class="text-end"></th>
            <th class="text-end">UdM</th>
            <th class="text-end">Quantità</th>
            <th class="text-end">Costo Totale</th>
            <th class="text-end">% Costo</th>
            <th class="text-end">Costo Unitario</th>
          </tr>
        </thead>
        <tbody>
          <!-- Iterate through groups -->
          <ng-container *ngFor="let group of groupedMoves">
            <!-- Template row -->
            <tr [class.table-light]="!group.is_expanded">
              <td>
                <div class="d-flex align-items-center">
                  <button class="btn btn-link btn-sm p-0 me-2" (click)="toggleExpand(group)">
                    <i class="fa" [class.fa-caret-down]="group.is_expanded" 
                       [class.fa-caret-right]="!group.is_expanded"></i>
                  </button>
                  <span class="fw-bold">{{group.product_tmpl_id.value?.name}}</span>
                  <span class="badge ms-2" 
                        [class.bg-primary]="group.is_main_product"
                        [class.bg-secondary]="!group.is_main_product">
                    {{group.is_main_product ? 'P' : 'S'}}
                  </span>
                </div>
              </td>
              <td>              </td>
                <td class="text-end"></td>
                <td class="text-end"></td>
              <td class="text-end">{{group.uom_id.name}}</td>
              <td class="text-end fw-bold">{{group.total_quantity | number:'1.2-2'}}</td>
              <td class="text-end fw-bold">{{group.total_cost | number:'1.2-2'}} €</td>
              <td class="text-end fw-bold">
                <input type="number" 
                       class="form-control form-control-sm text-end"
                       [ngModel]="group.total_cost"
                       (ngModelChange)="group._tempTotalCost = $event"
                       (blur)="updateGroupTotalCost(group, group._tempTotalCost)"
                       [ngModelOptions]="{ updateOn: 'blur' }"
                       [disabled]="loading"
                       style="width: 100px; display: inline-block">
                €
              </td>
              <td class="text-end">{{group.unit_cost | number:'1.2-2'}} €</td>
            </tr>
  
            <!-- Variant rows -->
            <ng-container *ngIf="group.is_expanded">
              <tr *ngFor="let move of group.moves" class="small">
                <td class="ps-5"></td>
                
                  
                  <td>
                    <!-- Show variant attributes -->
                    <ng-container *ngFor="let name of ['Lunghezza', 'Larghezza', 'Altezza']">
                      <span *ngIf="getVariantAttribute(move, name)" class="badge bg-light text-dark me-1">
                        {{name}}: {{getVariantAttribute(move, name).name}}
                      </span>
                    </ng-container>
                  </td>
                  <td class="text-end">

                    <td class="text-end">
                        <!-- Packaging quantity -->
                        <div *ngIf="move.product_packaging_id" class="d-flex align-items-center justify-content-end gap-2">
                          <input type="number" 
                                 class="form-control form-control-sm text-end"
                                 [ngModel]="move._product_packaging_qty"
                                 (ngModelChange)="move._tempPackageQty = $event"
                                 (blur)="updatePackagingQuantity(move, move._tempPackageQty)"
                                 [ngModelOptions]="{ updateOn: 'blur' }"
                                 [disabled]="loading"
                                 style="width: 100px">
                          
                                 <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button"
                                            [disabled]="loading"
                                            (click)="updateSelectablePackaging(move)"
                                            data-bs-toggle="dropdown">
                                      {{move.product_packaging_id.name}}
                                    </button>
                                    <ul class="dropdown-menu" *ngIf="move._selectablePackagings">
                                      <li *ngFor="let pkg of move._selectablePackagings">
                                        <a class="dropdown-item" 
                                           href="javascript:void(0)"
                                           (click)="updateMovePackage(move, pkg)">
                                          {{pkg.name}}
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                        </div>
                      </td>
                    <!--  UoM  -->
                      <td class="text-end">
                        {{move.product_uom.name}}
                    </td>
                        
                    <td class="text-end">
                        <!-- Base UoM quantity -->
                        <input type="number" 
                               class="form-control form-control-sm text-end"
                               [ngModel]="move.product_uom_qty"
                               (ngModelChange)="move._tempQty = $event"
                               (blur)="updateMoveQuantity(move, move._tempQty)"
                               [ngModelOptions]="{ updateOn: 'blur' }"
                               [disabled]="loading"
                               style="width: 100px; display: inline-block">
                        {{move.product_uom.value?.name}}
                      </td>
                    <td class="text-end">
                      {{calculateMoveCost(move, group) | number:'1.2-2'}} €
                    </td>
                    <td class="text-end">
                      {{calculateMoveUnitCost(move, group) | number:'1.2-2'}} €
                    </td>
               
              </tr>
            </ng-container>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
        <!-- Inventory selector -->
        <app-order-inventory *ngIf="showInventorySelector" [domain]="[['type', '=', 'product']]" [noDrag]="false"
            (addOrderLine)="onProductSelected($event)">
        </app-order-inventory>
   

    <!-- Components tab -->
    <div *ngIf="activeTab === 'components'" class="tab-pane fade show active">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Prodotto</th>
                        <th>Quantità Richiesta</th>
                        <th>Quantità Fatta</th>
                        <th>UdM</th>
                        <th>Stato</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let component of components">
                        <td>{{component.product_id?.name}}</td>
                        <td>{{component.product_uom_qty}}</td>
                        <td>{{component.quantity_done}}</td>
                        <td>{{component.product_uom?.name}}</td>
                        <td>{{component.state}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
</div>