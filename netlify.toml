# sample netlify.toml
[build]
  command = "yarn run build"
  functions = "functions/" # netlify dev uses this to know where to scaffold and serve your functions
  publish = "dist"

# note: each of these fields are OPTIONAL
[dev]
  framework="#custom"
  command = "yarn start" # Command to start your dev server
  port = 8888 # Port that the dev server will be listening on
  functionsPort = 34567 # port for functions server
  targetPort = 4201 # Port of target app server
  publish = "dist" # If you use a _redirect file, provide the path to your static content folder
  functionsSource = "functions/"
[[headers]]
  for = "/*"
  [headers.values]
    Content-Security-Policy = "default-src *  data: blob: filesystem: about: ws: wss: 'unsafe-inline' 'unsafe-eval' 'unsafe-dynamic'; script-src * data: blob: 'unsafe-inline' 'unsafe-eval'; connect-src * data: blob: 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src * data: blob: ; style-src * data: blob: 'unsafe-inline'; font-src * data: blob: 'unsafe-inline';"

