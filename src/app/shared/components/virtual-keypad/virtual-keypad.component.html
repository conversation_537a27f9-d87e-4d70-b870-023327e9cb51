<div (click)="open2 = true" class="text-end">{{innerValue ? innerValue : '-'}}</div>

<div class="overlay modal-backdrop p-4" *ngIf="open2">
    <table class="table table-borderless w-100 text-white fs-1">
        <tbody class="mx-2">
            <tr>
                <td colspan="4">
                    <input class="text-white"  inputmode='none' [ngModel]="innerValue" (ngModelChange)="onInputChange($event)" #input>
                </td>
            </tr>
            <tr>
                <td>
                    <button class="btn btn-primary w-100" (click)="click(1)">1</button>
                </td>

                <td>
                    <button class="btn btn-primary w-100" (click)="click(2)">2</button>
                </td>

                <td>
                    <button class="btn btn-primary w-100" (click)="click(3)">3</button>
                </td>
            </tr>
            <tr>
                <td>
                    <button class="btn btn-primary w-100" (click)="click(4)">4</button>
                </td>

                <td>
                    <button class="btn btn-primary w-100" (click)="click(5)">5</button>
                </td>

                <td>
                    <button class="btn btn-primary w-100"  (click)="click(6)">6</button>
                </td>
            </tr>
            <tr>
                <td>
                    <button class="btn btn-primary w-100"  (click)="click(7)">7</button>
                </td>

                <td>
                    <button class="btn btn-primary w-100"  (click)="click(8)">8</button>
                </td>

                <td>
                    <button class="btn btn-primary w-100"  (click)="click(9)">9</button>
                </td>
            </tr>
            <tr>
                <td>
                    <button class="btn btn-primary w-100" (click)="comma()">,</button>
                </td>
                
                <td>
                    <button class="btn btn-primary w-100" (click)="click(0)">0</button>
                </td>
                
                <td>
                    <button class="btn btn-primary w-100" (click)="backspace()"><i class="fa fa-backspace"></i></button>
                </td>
            </tr>
            <tr>
                <td>
                    <a class="btn btn-danger w-100" (click)="ok()"><i class="fa fa-xmark"></i></a>
                </td>
                <!-- <button class="btn btn-primary w-100" (click)="backspace()"><i class="fa fa-backspace"></i></button> -->
                
                <td>
                    <!-- <button class="btn btn-primary w-100" (click)="comma()">0</button> -->
                </td>

                <td>
                    <a class="btn btn-success w-100"  (click)="ok()"><i class="fa fa-check"></i></a>
                </td>

            </tr>
        </tbody>
    </table>
</div>