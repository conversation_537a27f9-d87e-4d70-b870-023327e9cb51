{"extends": "tslint:recommended", "rulesDirectory": ["codelyzer"], "rules": {"align": {"options": ["parameters", "statements"]}, "array-type": false, "arrow-parens": false, "arrow-return-shorthand": true, "deprecation": {"severity": "warn"}, "curly": true, "import-blacklist": [true, "rxjs/Rx"], "interface-name": false, "max-classes-per-file": false, "eofline": true, "max-line-length": [false, 140], "import-spacing": true, "indent": {"options": ["spaces"]}, "member-access": false, "member-ordering": [true, {"order": ["static-field", "instance-field", "static-method", "instance-method"]}], "no-consecutive-blank-lines": false, "no-console": [true, "debug", "info", "time", "timeEnd", "trace"], "no-empty": false, "no-inferrable-types": [true, "ignore-params"], "no-non-null-assertion": true, "no-redundant-jsdoc": true, "no-switch-case-fall-through": true, "no-var-requires": false, "object-literal-key-quotes": [true, "as-needed"], "object-literal-sort-keys": false, "ordered-imports": false, "quotemark": [true, "single"], "trailing-comma": false, "disable-next-line": "max-line-length", "no-output-on-prefix": true, "use-input-property-decorator": true, "use-output-property-decorator": true, "use-host-property-decorator": true, "semicolon": {"options": ["always"]}, "space-before-function-paren": {"options": {"anonymous": "never", "asyncArrow": "always", "constructor": "never", "method": "never", "named": "never"}}, "no-input-rename": true, "typedef-whitespace": {"options": [{"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}, {"call-signature": "onespace", "index-signature": "onespace", "parameter": "onespace", "property-declaration": "onespace", "variable-declaration": "onespace"}]}, "no-output-rename": true, "use-life-cycle-interface": true, "use-pipe-transform-interface": true, "component-class-suffix": true, "directive-class-suffix": true, "variable-name": {"options": ["ban-keywords", "check-format", "allow-pascal-case"]}, "whitespace": {"options": ["check-branch", "check-decl", "check-operator", "check-separator", "check-type", "check-typecast"]}}}