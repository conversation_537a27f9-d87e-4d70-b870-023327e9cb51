// Protractor configuration file, see link for more information
// https://github.com/angular/protractor/blob/master/lib/config.ts

const { SpecReporter } = require('jasmine-spec-reporter');
var failFast = require('jasmine-fail-fast');

exports.config = {
  allScriptsTimeout: 30000,
  plugins: [{
    package: 'protractor-fail-fast'
  }],
  baseUrl: 'http://localhost:4200',
  specs: [
    './src/**/sales.e2e-spec.ts' 
  ],
  capabilities: {
    'browserName': 'chrome',
     chromeOptions: {
       args: ["--disable-extensions" ,"--disable-gpu", "--window-size=800,600" ]
    }
  },
  directConnect: true,
  framework: 'jasmine',
  jasmineNodeOpts: {
    showColors: true,
    realtimeFailure: true,
    defaultTimeoutInterval: 15000,
    print: function() {}
  },
  onPrepare() {
    require('ts-node').register({
      project: require('path').join(__dirname, './tsconfig.e2e.json')
    });
    jasmine.getEnv().addReporter(new SpecReporter({ spec: { displayStacktrace: true } }));
    jasmine.getEnv().addReporter(failFast.init());

  }
};