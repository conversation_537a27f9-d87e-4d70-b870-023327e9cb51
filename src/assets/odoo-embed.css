/* https://localhost:4201/leads/5427?search=sal */


/* 1. Base Styles */
.o_web_client {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    font-size: 1.3rem !important;
    font-weight: 200;
    line-height: 1.6;
    color: #333;
    background-color: white;
    margin: 0;
    padding: 1rem;
    max-width: 100%;
}

/* composer is where i write stuff */

.o_Composer_coreMain {
    font-size: 1.5em !important;
}

/* chattter is the option selector on top */
.o_ChatterTopbar {
    margin-bottom: 10px !important;
}

/* 2. Hidden Elements */
.o_navbar,
.o_control_panel,
.o_debug_manager,
/* .o_Message_sidebar, 
.o_Activity_sidebar, */
.o_popover_container,
.web.FieldTooltip,
.o_ChatterTopbar_buttonSendMessage,
.o_MessageActionView_actionToggleStar,
.o_TrackingValue_fieldName,
.o_Message_notificationIcon,
.o_PersonaImStatusIcon,
.o_external_button,
.o_Composer_sidebarMain,
.o_Composer_currentPartnerAvatar,
.o_Activity_detailsUserAvatar,
.o_Activity_detailsAssignationUserAvatar,
/* .o_Activity_detailsUserAvatar,
.o_Activity_detailsCreatorAvatar, */
.o_kanban_image,
.o_Activity_mailTemplates,
button[name="action_create_calendar_event"]
{
    display: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Override grid template for composer without avatar otherwise it keeps 2 columns and the first one is empty*/
.o_Composer.o-has-current-partner-avatar {
    grid-template-columns: 0 1fr !important;  /* Change from 48px 1fr to 0 1fr */
    display: block !important;  /* Or alternatively remove the grid completely */
}

/* 3. Message Layout */
/* 3.1 Container Styles */
.o_ThreadView {
    background-color: white !important;
    border-radius: 0.25rem !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.o_MessageList {
    max-height: calc(100vh - 200px) !important;
    overflow-y: auto !important;
}

/* 3.2 System Notification Messages */
.o_Message.o-not-discussion.o-notification,
.o_Message[aria-label="Notifica di sistema"] {
    background-color: white !important;
    padding: 0.5 !important; 
    border-radius: 0.25rem !important;
    border: none !important;
    margin: 0.5rem 0 !important;
    opacity: 0.85 !important;
}

.o_Message_trackingValues {
    color: #6c757d !important;
    padding-left: 1.3rem !important;
    font-size: 1rem !important;
}

.o_TrackingValue_oldValue {
    color: #999 !important;
    font-weight: normal !important;
}

.o_TrackingValue_newValue {
    color: #666 !important;
    font-weight: 500 !important;
}

/* 3.3 User Messages */
.o_Message:not(.o-notification):not([aria-label="Notifica di sistema"]) {
    background-color: #f8f9fa !important;
    padding: 0.5rem !important;
    border: 1px solid #e9ecef !important;
    border-radius: 0.5rem !important;
    margin: 1rem 0 !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
    font-size: 1.3rem !important;
}

.o_Message:not(.o-notification):not([aria-label="Notifica di sistema"]) .o_Message_content {
    font-size: 1.3rem !important;
    color: #333 !important;
    line-height: 1.8 !important;
}

/* 3.4 Message Author & Date */
.o_Message_authorName {
    color: #333 !important; 
    font-weight: 500 !important;
    font-size: 1.2rem !important;
}

.o_Message_date {
    font-size: 0.9rem !important;
    opacity: 0.7 !important;
}

/* Remove clickable behavior from author names */
.o_Message_authorName {
    pointer-events: none !important;
    cursor: default !important;
    text-decoration: none !important;
}

/* Remove hover effects */
.o_Message_authorName:hover {
    text-decoration: none !important;
    color: inherit !important;
}

/* Make sure the color stays consistent */
.o_redirect.o_Message_authorName {
    color: #333 !important; /* Keep your brand color */
    cursor: default !important;
}

/* 3.5 Date Separators */
.o_MessageList_separatorDate {
    text-align: center !important;
    padding: 1rem 0 !important;
    color: #666 !important;
    font-size: 1.2rem !important;
    border-bottom: 1px solid #eee !important;
}

/* 3.6 Reactions */
.o_MessageReactionGroup, 
.o-hasUserReacted{
    gap: 0.5rem !important;
    margin-top: 0.5rem !important;
    padding: 0rem  !important;
}

.o_MessageReactionGroup_count {
    font-size: 1.2rem !important;
    color: #333 !important;
}

/* 4. Tags, Mentions and links */
.o_mail_redirect {
    pointer-events: none !important;
    text-decoration: none !important;
    background-color: #f7d6c5 !important;
    color: #333 !important;
    padding: 0.2rem 0.6rem !important;
    border-radius: 0.25rem !important;
    font-size: 1.1rem !important;
    font-weight: 500 !important;
}

.o_mail_redirect:hover {
    text-decoration: none !important;
    color: white !important;
    cursor: default !important;
}

/* Style for links */
a, a:visited {
    color: #f47e43 !important;  /* Orange brand color */
    text-decoration: none !important;
}

/* Hover state */
a:hover {
    color: #de520d !important;  /* Darker shade of orange */
    text-decoration: underline !important;
}

/* If you want to exclude certain links (like @mentions) */
a:not(.o_mail_redirect) {
    color: #f47e43 !important;
}

/* 5. Buttons */

/* PRIMARY */
.btn-fill-odoo, 
.btn-odoo, 
.btn-primary,
.o_loading_indicator,
.o_ChatterTopbar_buttonLogNote,
.o_ChatterTopbar_buttonScheduleActivity,
.o_Activity_markDoneButton {
    background-color: #f47e43 !important;
    border: none !important;
    color: white !important;
    font-weight: 200 !important;
    padding: 0.65rem 1.3rem !important;  
    border-radius: 0.5rem !important;
    transition: opacity 0.15s ease-in-out !important;
    font-size: 1.2rem !important;
}
 /* SECONDARY */
.btn-secondary, 
.o_clear_button,
.o_Activity_editButton,
.o_Activity_cancelButton,
.o_MessageReactionGroup,
.o_AttachmentBox_buttonAdd,  
.o_ActivityBox_title,
.o_ActivityMarkDonePopoverContent_doneScheduleNextButton,
.o_ActivityMarkDonePopoverContent_discardButton,
.o_ActivityMarkDonePopoverContent_doneButton {
    background-color: transparent !important;
    border: none !important;
    color: #f47e43 !important;
    font-weight: 200 !important;
    padding: 0.65rem 1.3rem !important;
    font-size: 1.2rem !important;
    transition: opacity 0.15s ease-in-out !important;
    /* round border */
    border-radius: 0.5rem !important;
}

/* 6. Status Colors */
.text-danger,
[data-delay-label*="ritardo"] {
    color: #dc3545 !important;
    font-size: 1.2rem !important;
}

.text-success {
    color: #28a745 !important;
    font-size: 1.2rem !important;
}

/* 7. Activities (buttons at the bottom already take css declared in 5. buttons */
/* Modal Header Styling */
.o_ActivityBox{
    border: 1px solid #f47e43 !important;
    background-color:  #f8f9fa !important;
    
    color: #333; 
    border-radius: 0.25rem !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    padding: 1rem !important;
}

.o_Activity_core {
    width: 100% !important;
}

.o_ActivityBox .o_Activity_info .btn-link.btn-primary {
    margin-left:auto;
}


.o_FormRenderer_chatterContainer:not(.o-aside):not(.o-full-width) .o_ChatterContainer {
    min-width: 100% !important;
}
.o_ActivityBox * {
    /* color: #333;  */
    border-radius: 1px !important;
}

.o_ActivityBox .o_ActivityBox_title {
    margin-top: 0px !important;
    padding-top: 0px !important;

}

.o_ActivityBox .o_ActivityBox_activity {
    margin-bottom: 10px !important;
}

.o_ActivityBox  .o_Activity_tools {
    padding-top: 10px !important;
}

html .o_web_client > .o_action_manager > .o_action .o_content {
    overflow: visible !important;
}


.o_Activity_info{
    
}

.o_form_view {

}
 .o_MessageList {
    max-height: none !important;
    overflow-y: visible !important;
   
   }

.o_ActivityBox .btn-link.btn-primary {
    border-color: #f47e43 !important;
    border: 1px solid #f47e43 !important;
    color: #111 !important;
    margin-right: 5px;
  
    border-radius: 0.5rem !important;
}

.o_ActivityBox .btn-link.btn-primary:hover {
    background-color: #f47e43 !important;
    color: white !important;
}




.o_form_view {
    overflow-y: scroll !important;
}
.btn-link.btn-primary.o_Activity_detailsButton {
    border: none !important;
}

.o_ActivityBox button i  {
    display:none !important;
}

/* complete activity */
/* Activity Mark Done Popover */
.o_ActivityMarkDonePopoverContent {
    border-radius: 0.5rem !important;
    background-color: white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    font-size: 1.3rem !important;
}

/* Header */
.o_ActivityMarkDonePopoverContent_header {
    background-color: white !important;
    color: #333 !important;
    font-size: 1.4rem !important;
    font-weight: 500 !important;
    padding: 1rem !important;
    border-bottom: 1px solid #e9ecef !important;
}

/* Content area */
.o_ActivityMarkDonePopoverContent_content {
    padding: 1rem !important;
}

/* Textarea */
.o_ActivityMarkDonePopoverContent_feedback {
    font-size: 1.3rem !important;
    padding: 0.8rem !important;
    border: 1px solid #e9ecef !important;
    border-radius: 0.5rem !important;
    box-shadow: none !important;
}


.modal-header {
    background-color: white !important;  /* White background */
    border-bottom: 1px solid #e9ecef !important;  /* Light border */
    padding: 1rem !important;
}

/* Modal Title */
.modal-header .modal-title {
    color: #333 !important;  /* Dark text color */
    font-size: 1.4rem !important;
    font-weight: 500 !important;
}

/* Close Button */
.modal-header .btn-close {
    color: #666 !important;
    opacity: 0.7 !important;
}

/* Debug Button */
.modal-header .o_debug_manager button {
    color: #666 !important;
    opacity: 0.7 !important;
}

.modal-header .o_debug_manager button:hover {
    opacity: 1 !important;
}

/* 8. Contact selector kanban for activities */
.fa-circle,
.badge.rounded-pill {
    display: none !important;
}

/* Contact Cards */
.oe_kanban_card, 
.oe_kanban_global_click {
    background-color: transparent !important;
    border: none !important;
    padding: 0.2rem !important;
    transition: all 0.2s ease-in-out !important;
}

.o_kanban_record {
    background-color: white !important;
    border: 1px solid transparent !important;
    border-radius: 0.5rem !important;
    padding: 0.5rem !important;
    transition: all 0.2s ease-in-out !important;
}

.o_kanban_record:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    border-color: #f47e43 !important;
    cursor: pointer !important;
}

/* kanban details */
.oe_kanban_details {
    padding: 0.2rem !important;
}

/* Contact Name */
.oe_kanban_details strong {
    color: #333 !important;
    font-size: 1.5rem !important;
    font-weight: 500 !important;
    display: block !important;
    margin-bottom: 0.5rem !important;
}

/* Contact Email */
.oe_kanban_details span {
    color: #666 !important;
    font-size: 1.3rem !important;
}

/* Modal Header */
.modal-header {
    background-color: white !important;
    border-bottom: 1px solid #e9ecef !important;
    padding: 1rem !important;
}

.modal-title {
    color: #333 !important;
    font-size: 1.4rem !important;
    font-weight: 500 !important;
}

/* Search Input */
.o_searchview {
    border: 1px solid #e9ecef !important;
    border-radius: 0.5rem !important;
    padding: 0.5rem !important;
}

.o_searchview_input {
    border: none !important;
    font-size: 1.2rem !important;
    color: #333 !important;
}

/* List Container */
.o_kanban_renderer {
    padding: 1rem !important;
    gap: 1rem !important;
}

/* Remove ghost elements styling */
.o_kanban_ghost {
    border: none !important;
    background: transparent !important;
}

/* 9. Calendar Styling */
.datepicker {
    font-size: 1.3rem !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    background-color: white !important;
}

/* Header styling */
.datepicker .table-sm thead {
    background-color: white !important;
    border-bottom: 1px solid #e9ecef !important;
}

.datepicker .table-sm thead th {
    padding: 0.8rem !important;
    font-weight: 500 !important;
    color: #333 !important;
}

/* Navigation arrows */
.datepicker .prev,
.datepicker .next {
    color: #f47e43 !important;
    font-size: 1.2rem !important;
}

/* Month/Year selector */
.datepicker .picker-switch {
    color: #333 !important;
    font-weight: 500 !important;
    font-size: 1.3rem !important;
}

/* Calendar days */
.datepicker .day {
    padding: 0.8rem !important;
    border-radius: 0.25rem !important;
    transition: all 0.2s ease-in-out !important;
    font-weight: 400 !important;
}

/* Today's date */
.datepicker .day.today {
    background-color: #f7d6c5 !important;
    color: #333 !important;
    font-weight: 500 !important;
}

/* Selected date */
.datepicker .day.active {
    background-color: #f47e43 !important;
    color: white !important;
    font-weight: 500 !important;
}

/* Hover state */
.datepicker .day:hover {
    background-color: #f8f9fa !important;
    cursor: pointer !important;
}

/* Weekend days */
.datepicker .weekend {
    color: #999 !important;
}

/* Previous/Next month days */
.datepicker .old,
.datepicker .new {
    color: #ccc !important;
}

/* Month/Year selection view */
.datepicker .month,
.datepicker .year,
.datepicker .decade {
    padding: 0.8rem !important;
    border-radius: 0.25rem !important;
    font-weight: 400 !important;
    transition: all 0.2s ease-in-out !important;
}

.datepicker .month.active,
.datepicker .year.active,
.datepicker .decade.active {
    background-color: #f47e43 !important;
    color: white !important;
}

.datepicker .month:hover,
.datepicker .year:hover,
.datepicker .decade:hover {
    background-color: #f8f9fa !important;
    cursor: pointer !important;
}

/* Week numbers */
.datepicker .cw {
    color: #999 !important;
    font-size: 1rem !important;
    padding: 0.8rem !important;
}