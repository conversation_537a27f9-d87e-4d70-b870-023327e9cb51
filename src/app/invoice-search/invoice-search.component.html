<!-- Navbar -->
<app-navbar [loading]="loading" backroute="..">
  <a class="navbar-brand">
      Fatture e Sal
  </a>
</app-navbar>
  
<!-- Filters Section -->
<div class="bg-light px-3">
  <div class="container-fluid py-3">
    <!-- First Row: Area and State Buttons -->
    <div class="row g-3 align-items-center">
      <!-- Area Buttons -->
      <div class="col-md-6">
        <div class="d-flex align-items-center flex-wrap">
          <span class="me-2">Settore:</span>
          <button 
            [ngClass]="!selectedArea ? 'btn-primary' : 'btn-dark'"
            class="btn ms-2"
            (click)="selectedArea = ''; loadInvoices()">
            <i class="fa fa-asterisk"></i>
          </button>
          <button *ngFor="let area of areas" 
            (click)="selectedArea = area.name; loadInvoices()"
            [ngClass]="selectedArea == area.name ? getAreaBadgeClass(area.name) : 'btn-dark'" 
            class="btn ms-2">
            {{ area.name }}
          </button>
        </div>
      </div>

      <!-- State Buttons -->
      <div class="col-md-6">
        <div class="d-flex align-items-center flex-wrap btn-group gap-0">
          <span class="me-2 ">Stato:</span>
          <button *ngFor="let state of ga_states" 
            (click)="toggleStateSelection(state.value)"
            [ngClass]="isStateSelected(state.value) 
              ? 'btn-primary text-white' 
              : 'btn-outline-muted'" 
            class="btn btn-sm">
            {{ state.label }}
          </button>
        </div>
      </div>
    </div>

    <!-- Second Row: Search and Filters -->
    <div class="row g-3 py-3">
      <!-- Search by Customer -->
      <div class="col-md-3 col-lg-3">           
        <div class="input-group">
          <span class="input-group-text">Cerca</span>
          <input class="form-control" placeholder="Cliente, commessa o numero di ordine" 
            [ngModel]="searchInput | async" 
            (ngModelChange)="searchInput.next($event)">
        </div>
      </div>
  
      <!-- Search by User -->
      <div class="col-md-3 col-lg-2">
        <select class="form-select" [(ngModel)]="selectedUserId" (ngModelChange)="onUserChange($event)">
          <option [ngValue]="null">Tutti gli utenti</option>
          <option *ngFor="let user of users" [ngValue]="user.id">{{user.name}}</option>
        </select>
      </div>

      <!-- Date Range Filter -->
      <div class="col-md-3 col-lg-4">
        <div class="input-group">
          <span class="input-group-text">Data Fattura</span>
          <input type="date" class="form-control" [(ngModel)]="startDate" (change)="loadInvoices()">
          <span class="input-group-text">a</span>
          <input type="date" class="form-control" [(ngModel)]="endDate" (change)="loadInvoices()">
        </div>
      </div>
  
      <!-- Reset Filters -->
      <div class="col-md-1">
        <button class="btn btn-outline-secondary" (click)="resetFilters()">
          <i class="fa fa-refresh"></i>
        </button>
      </div>
    </div>
  </div>
</div>
  
<!-- Invoice Table -->
<div class="table-responsive mt-3">
  <table class="table table-hover align-middle mb-0">
    <thead>
      <tr>
        <th>Responsabile</th>
        <th>Commessa</th>
        <th>Fattura</th>
        <th>Data Fattura</th>
        <th>Stato</th>
        <th class="text-center">Pagamento</th>
        <th>Imponibile</th>
        <th colspan="2" class="text-center">IVA</th>
        <th>Totale</th>
      </tr>
    </thead>
    @if (!loading) {
    <tbody>
      <tr 
        (click)="redirectDeal(getLead(invoice.invoice_origin).id)" 
        target="_blank" 
        *ngFor="let invoice of filteredInvoices" 
        class="align-middle">
        
        <!-- User Column -->
        <td>{{ invoice.invoice_user_id.name }}</td>
        
        <!-- Deal/Customer Information -->
        <td>
          <span class="fw-bold">
            {{ invoice.invoice_origin }} - 
            {{ getLead(invoice.invoice_origin)?.partner_id?.name }} - 
            {{ getLead(invoice.invoice_origin)?.name }}
          </span>
          <br>
          <span>
            {{ getLead(invoice.invoice_origin)?.street }} - 
            {{ getLead(invoice.invoice_origin)?.city }}
          </span>
          <span 
            class="ms-3 badge" 
            [ngClass]="getAreaBadgeClass(getLead(invoice.invoice_origin)?.area)"
            style="white-space: nowrap;">
            {{ getLead(invoice.invoice_origin)?.area }}
          </span>
        </td>
        
        <!-- Narration Column -->
        <td class="align-middle" [innerHTML]="invoice.narration"></td>
        
        <!-- Invoice Date Column -->
        <td>{{ invoice.invoice_date | Date4Humans }}</td>

        <!-- State Column -->
        <td>
          <span class="badge" 
            [ngClass]="{
              'bg-warning': invoice._ga_state === 'draft',
              'bg-success': invoice._ga_state === 'posted',
              'bg-danger': invoice._ga_state === 'cancel',
              'bg-primary': invoice._ga_state === 'toEmit',
              'bg-info': invoice._ga_state === 'paid'
            }"
          >
            {{ 
               getStateLabel(invoice._ga_state)
            }}
          </span>
        </td>

        <!-- Payment Column -->
        <td>{{invoice.invoice_payment_term_id?.name}}</td>
        
        <!-- Untaxed Amount Column -->
        <td>{{ invoice.amount_untaxed | currency: 'EUR' }}</td>
        
        <!-- Tax Amount Column -->
        <td>{{ invoice.amount_tax | currency: 'EUR' }}</td>
        
        <!-- Tax Percentage Column -->
        <td>
          {{ (invoice.amount_tax / invoice.amount_untaxed * 100) | number: '1.0-2' }}%
        </td>
        
        <!-- Total Amount Column -->
        <td>{{ invoice.amount_total | currency: 'EUR' }}</td>
      </tr>
    </tbody>
    }

    <!-- Totals Row -->
    <tfoot *ngIf="filteredInvoices.length > 0">
      <tr class="table-light fw-bold">
        <td colspan="6" class="text-end">Totali:</td>
        <td>
          {{ filteredInvoices | calcTotal:'amount_untaxed' | currency:'EUR' }}
        </td>
        <td colspan="2" class="text-center">
          {{ filteredInvoices | calcTotal:'amount_tax' | currency:'EUR' }}
        </td>
        <td>
          {{ filteredInvoices | calcTotal:'amount_total' | currency:'EUR' }}
        </td>
      </tr>
    </tfoot>
  </table>

  <!-- No Results Message -->
  <div 
    *ngIf="!loading && filteredInvoices.length === 0" 
    class="text-center py-5"
  >
    <h4 class="text-muted">
      <i class="fa fa-file-invoice me-2"></i>
      Nessuna fattura trovata
    </h4>
    <p class="text-muted">
      Prova a modificare i filtri di ricerca
    </p>
  </div>
</div>
