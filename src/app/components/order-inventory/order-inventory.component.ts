import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnInit, Output, Pipe, PipeTransform, TemplateRef, ViewChild } from '@angular/core';
import { Product, ProductWithOnlyVariants } from '../../models/product.model';
import {  debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { BehaviorSubject, firstValueFrom} from 'rxjs';
import { OdooEntityManager } from '../../shared/services/odoo-entity-manager.service';
import { ProductTemplate } from 'src/app/models/product.template.model';
import { SaleOrder } from 'src/app/models/sale-order.model';
import { SaleOrderEditorComponent } from 'src/app/sale-order/sale-order-editor/sale-order-editor.component';
import { ProductTemplateAttributeValue } from 'src/app/models/product.template.attribute.value.model';
import { ProductAttributeValue } from 'src/app/models/product.attribute.value';
import { ProductTemplateAttributeLine } from 'src/app/models/product.template.attribute.line';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { PriceList } from 'src/app/models/price.list.model';
import { OdoorpcService } from 'src/app/shared/services/odoorpc.service';
import { ODOO_IDS } from 'src/app/models/deal';
import { StockQuantPackage } from 'src/app/models/stock-quant-package';
import { StockQuant } from 'src/app/models/stock-quant';
import { ProductTag } from 'src/app/models/product.tag.model';
import { ProductPackaging } from 'src/app/models/product.packaging.model';


interface TemplateGroup {
  category: string;
  templates: ProductTemplate[];
}

interface GroupedTemplates {
  [key: string]: ProductTemplate[];
}
@Pipe({
    name: 'sortAsNumber',
    standalone: false
})
export class SortAsNumber implements PipeTransform {
  transform(value: any[], order = '', column: string = ''): any[] {
    return value.sort((a,b) => Number(a.name) - Number(b.name))
  }
}

@Component({
    selector: 'app-order-inventory',
    templateUrl: './order-inventory.component.html',
    standalone: false
})

export class OrderInventoryComponent implements OnInit {
  @Output() addOrderLine = new EventEmitter<Product>()
  @Input() sales: SaleOrder[]
  @Input() saleIds: string[]
  @Input() saleEditor: SaleOrderEditorComponent
  @Output() loading: EventEmitter<boolean> = new EventEmitter(false)
  inventoryClosed = true;
  @ViewChild(TemplateRef) template: TemplateRef<any>;
  @Input() noDrag:boolean = false
  @Input() noTemplate:boolean = false
  @Input() domain = []
  @Output() toggleInventory = new EventEmitter<boolean>();

  @Input () from : string = 'sale'


  ODOO_IDS = ODOO_IDS
  inputSearch:BehaviorSubject<string> = new BehaviorSubject<string>("");
  activeTemplate: ProductTemplate = null;
  productToShow: ProductTemplate = new ProductTemplate(45643,'Travatura in abete lamellare');
  showPhotos: boolean = false;



  templates: ProductTemplate[] = [];
  productTemplates: GroupedTemplates = {};

  products: ProductWithOnlyVariants[];
  groups: any;
  // TODO bad way to init prefs
  criteria: {
    toCreate?: any;attributeLine:ProductTemplateAttributeLine, attributeValue:ProductTemplateAttributeValue
    }[] = [];
  isMouseDown: boolean;
  lastValues: ProductTemplateAttributeValue[];
  name: any;
  // selectedTemplate: ProductTemplate = new ProductTemplate(45643,'Travatura in abete lamellare');
  runningSearch$: any;
  refresh$: BehaviorSubject<boolean>= new BehaviorSubject(false);
  searchGroups: {};
  lastSearch: number;
  showAttrs: boolean = true;
  loaded: boolean = false;
  loadedTemplates: boolean = false;
  loadingAttributes: boolean = false

  selectedAreaFilter: string = 'EDI'; // 'EDI' or 'HD'
  AreaTags: ProductTag[] = [];
  selectedAreaTags: ProductTag[] = [];
  onlyFondi: boolean = false;
  offertaTags: ProductTag[] = [];

  // New properties for section toggle functionality
  misureExpanded: boolean = false;
  caratteristicheTecnicheExpanded: boolean = false;

  constructor(
    private odooEm: OdooEntityManager,
    private elRef:ElementRef,
    private odoorpcService: OdoorpcService
  ) {
    console.log('OrderInventoryComponent constructor initialized');
  }

  async noComms(n:string) {
    // replace ", " fonund in string with ""
    return n.replace(", ","")
  }
    
async ngOnInit() {
  console.log('OrderInventoryComponent ngOnInit started');

  let tags = await firstValueFrom(this.odooEm.search<ProductTag>(new ProductTag(), [['name', 'ilike', 'offerta']]));
  this.offertaTags = tags;
  console.log('Loaded offerta tags:', tags.length);
  
  // area tags are the ones with id 2,3,5,12,9
  // let areatags = await firstValueFrom(this.odooEm.search<ProductTag>(new ProductTag(), [['id', 'in', [2,3,5,12,9]]]));
  // this.AreaTags = areatags;

  // Load search conditions from local storage
  let search = JSON.parse(localStorage.getItem('orderInventorySearch') || '{}');
  console.log("search saved is", search);
  
  if (search) {
    if (search.input) this.inputSearch.next(search.input);
    if (search.onlyFondi) this.onlyFondi = search.onlyFondi;
    if (search.selectedAreaFilter) this.selectedAreaFilter = search.selectedAreaFilter;
    if (search.products) this.products = search.products;
    if (search.templates) this.templates = search.templates;
  }

  await this.loadTemplates();

  console.log ("variables after search load", this.inputSearch.value, this.onlyFondi, this.selectedAreaTags, this.products, this.templates)


  // Rest of your ngOnInit code...
  this.refresh$.pipe(debounceTime(400)).subscribe(async () => {
    await this.refresh()
  })

    window.addEventListener("mousedown",e => {
      if (
        this.elRef.nativeElement.querySelector("table").contains(e.target) ||
        this.elRef.nativeElement.querySelector("input") == e.target ||
        this.elRef.nativeElement.querySelector("#inventory") == e.target ||
        this.elRef.nativeElement.querySelector("button") == e.target
      ) {
          this.inventoryClosed = false
        } else {
          this.inventoryClosed = true
        this.isMouseDown = false
      }
    })
  
    this.inputSearch.pipe(
      debounceTime(500),
      distinctUntilChanged(),
    ).subscribe(async x => {
      this.refresh$.next(true)
    });
  }
  
  openQuants(p:Product) {
    this.odooEm.call2("product.product","action_open_quants",[p.id])
  }

  closeInventory() {
    this.toggleInventory.emit(false);
  }

  // New methods for section toggle functionality
  toggleMisureSection() {
    this.misureExpanded = !this.misureExpanded;
    console.log('Misure section toggled:', this.misureExpanded);
    this.saveSearchState();
  }

  toggleCaratteristicheTecnicheSection() {
    this.caratteristicheTecnicheExpanded = !this.caratteristicheTecnicheExpanded;
    console.log('Caratteristiche Tecniche section toggled:', this.caratteristicheTecnicheExpanded);
    this.saveSearchState();
  }

  togglePhotos() {
    this.showPhotos = !this.showPhotos;
    console.log('Photos toggled:', this.showPhotos);
  }

  // Method to calculate totals
  getTotals() {
    if (!this.products || this.products.length === 0) {
      return { totalQty: 0, totalFreeQty: 0, totalVariants: 0 };
    }

    const totalQty = this.products.reduce((sum, p) => sum + (p.qty_available || 0), 0);
    const totalFreeQty = this.products.reduce((sum, p) => sum + (p._freeForInventory || 0), 0);
    const totalVariants = this.products.filter(p => p.qty_available > 0).length;
    
    console.log('Calculated totals - Qty:', totalQty, 'Free Qty:', totalFreeQty, 'Variants:', totalVariants);
    return { totalQty, totalFreeQty, totalVariants };
  }

  // Updated saveSearchState method to include section states
  private saveSearchState() {
    localStorage.setItem('orderInventorySearch', JSON.stringify({
      input: this.inputSearch.value,
      onlyFondi: this.onlyFondi,
      selectedAreaFilter: this.selectedAreaFilter,
    }));
    console.log("saved search state with section toggles");
  }

  async loadTemplates(): Promise<void> {
this.loadedTemplates = false

      console.log("loading templates")
    this.templates = await firstValueFrom(this.odooEm.search<ProductTemplate>(
      new ProductTemplate(),
      [
        ['product_tag_ids', 'in', [ODOO_IDS.tag_variant_search]]

      ]
    ));
  
  // Group templates by category
  this.productTemplates = this.templates.reduce((groups: GroupedTemplates, template: ProductTemplate) => {
    const category = template.categ_id.name.split(' ')[0];
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(template);
    return groups;
  }, {});

  // Sort categories and templates within each category
  Object.keys(this.productTemplates).forEach(category => {
    this.productTemplates[category].sort((a, b) => a.name.localeCompare(b.name));
  });

  this.loadedTemplates = true
}

  async loadPrice(res:ProductWithOnlyVariants[]) {
    var kwargs = kwargs || {};
    kwargs.context = [];

    var params = {
      model: new PriceList().ODOO_MODEL,
      method: "get_products_price",
      args: [[2],res.map(x => x.id)],
      kwargs: {
        context: null
      },
    };
    var r: any = await this.odoorpcService.sendRequest('/api/web/dataset/call_kw/' + params.model + "/" + params.method, params)
    return r.result ? r.result : []
    
  }


  async getFree(prods: ProductWithOnlyVariants[]) {
    console.log("aaaaaaa1free for inventory",this.products)

    prods.forEach(p => {

    let available = 0
    // if available - outgoing >0 return available - outgoing, else 0
    if (p.detailed_type === 'consu')
      available = 9999

    else if (p.detailed_type === 'product') {
      available = p.qty_available - p.outgoing_qty
      if (available < 0)
        available = 0
    }
    p._freeForInventory = available
  })
  console.log("aaaaaaa2  free for inventory",this.products)
  }

  getOriginalCost(p: ProductWithOnlyVariants) {
    //if's not an offer, we return the list price, otherwise we find it's orignal cost
    if (this.isOffer(p)) {
      return p.list_price / (1 - this.getDiscountFondo(p) / 100);
    }
  }

  isOffer(p: ProductWithOnlyVariants) {
    return p.additional_product_tag_ids.ids?.some(x => this.offertaTags.map(x => x.id).includes(x));
  }

  isAlmostFondo(p: ProductWithOnlyVariants) {
    // return p.additional_product_tag_ids.ids?.includes(ODOO_IDS.prodTagsFondi.prodTagConsumaPrima);
  //all products not fondo and with reordering min qty = 0
  return !this.isOffer(p) && p.reordering_min_qty == 0 && p.qty_available > 0;
  
  }

  isFondoOrOffer(p: ProductWithOnlyVariants) {
    // if it contains at least one offerta tag or if it's consumaprima, it's a fondo
    return this.isOffer(p) || this.isAlmostFondo(p);
  }

  getDiscountFondo(p: ProductWithOnlyVariants) {
    //if it's an offer, search for it's tag in our offertaTags array 
    if (this.isOffer(p)) {
      let tag = p.additional_product_tag_ids.ids.find(x => this.offertaTags.map(x => x.id).includes(x));
      let discountUnit = this.offertaTags.find(x => x.id === tag).name.replace('OFFERTA', '');
      return parseFloat(discountUnit) * 10;
    }
  }

  toggleFondi() {
    this.onlyFondi = !this.onlyFondi
    //remove active template
    this.activeTemplate = null
    this.refresh()
  }
  
  toggleAreaFilter() {
    this.selectedAreaFilter = this.selectedAreaFilter === 'EDI' ? 'HD' : 'EDI';
    this.refresh();
  }
  
  getReorderingClass(p: ProductWithOnlyVariants) {
    if (p.reordering_min_qty > 0)
      return 'fa-star text-success';
    if (this.isOffer(p))
      return 'fa-trash-clock text-danger';
    if (this.isAlmostFondo(p))
      return 'fa-trash text-primary';
    if (!(p.reordering_min_qty > 0) && !this.isFondoOrOffer(p) && !p.free_qty)
      return 'fa-cart-shopping text-body-secondary';
    if (!(p.reordering_min_qty > 0) && !this.isFondoOrOffer(p) && p.free_qty)
      return 'fa-down-to-line text-warning';
    return '';
  }
  
  dragstart(p:Product, ev) {
    ev.dataTransfer.setData("app-product", "dassad")
    return true
  }
  async drop(el:CdkDragDrop<any>) {
  } 
  mouseup() {
    this.isMouseDown = false
  }
  mousedown(ev) {
    this.isMouseDown = true
  }
  mouseover(ev, a:ProductAttributeValue) {
  }

  async getProducts(input): Promise<ProductWithOnlyVariants[]> {
    let filters = [];
    
    if (this.activeTemplate)
      filters.push(['product_tmpl_id', "=", this.activeTemplate.id])
    
    var domain = []

    var cs = this.criteria

    // translate criteria in odoo query
    cs.forEach((x,i) => {
        domain.push('&')
        domain.push(['name', '=', x.attributeValue.name])
        domain.push('&')
        domain.push(['product_tmpl_id', '=', this.activeTemplate?.id])
        domain.push(['attribute_id', '=',x.attributeValue.attribute_id.id])
    });
    
    // odoo use logic operator 
    cs.slice(0,-1).forEach(x => {
      domain.unshift("|")
    })

    if (domain.length) {
      this.lastValues = await firstValueFrom(this.odooEm.search<ProductTemplateAttributeValue>(new ProductTemplateAttributeValue(), domain))
      this.lastValues.map(x => x.id).forEach(v => {
          filters.push(['product_template_attribute_value_ids','in',v])
        })
    }
    
    if (input)
      input.split(' ').forEach(v => {
        filters.push(
          ['sale_ok', '=', 'true'],
          ['active', '=', 'true'],
          '|',
          '|',
          ['name', 'ilike', v.replace(/[\s\*]/g, "%25")],
          ['product_template_attribute_value_ids', "ilike", v.replace(/[\s\*]/g, "%25")],
          // search also in category categ_id name
          ['categ_id.complete_name', 'ilike', v.replace(/[\s\*]/g, "%25")]
        );

        if (this.domain)
          filters = filters.concat(this.domain)
      });

  // Add area filter logic - always apply one of the filters
  if (this.selectedAreaFilter === 'EDI') {
    // EDI: searches all products without tag 9, 10, 5 (no pavimenti)
    filters.push(['all_product_tag_ids', 'not in', [9, 10, 5]]);
  } else {
    // HD: searches all products without tag 2, 3 (no edilizia)
    filters.push(['all_product_tag_ids', 'not in', [2, 3]]);
  }

  if (this.onlyFondi) {
    //for now lwt's take all available products withour reordering min qty (not services)
    filters.push(['reordering_min_qty', '=', 0]);
    filters.push(['qty_available', '>', 0]);
    filters.push(['detailed_type', '=', 'product']);
    // also don't show products with ric estesa
    filters.push(['all_product_tag_ids', 'not in', [ODOO_IDS.tag_variant_search]]);
  }

   let products = await this.odooEm.search<ProductWithOnlyVariants>(new ProductWithOnlyVariants(), filters, 1000,null,"free_qty").toPromise();
      
   if (this.onlyFondi) {
    products = products.filter(p => this.isFondoOrOffer(p));
  }

  //solve packagings 
  await firstValueFrom(
    this.odooEm.resolveArray(new ProductPackaging(), products, "packaging_ids")
  );

    //save all search inputs and tags in local storage to restore them on reload
  this.saveSearchState();
  console.log("saved search",JSON.parse(localStorage.getItem('orderInventorySearch')));
    console.log("search conditions :" ,filters)
    console.log("products",products)
    return products
}

   getInPzFast(p) {

    if (p.packaging_ids.values.find(x => x.name === "Pz")) {
      return Math.round(p._freeForInventory / p.packaging_ids.values.find(x => x.name === "Pz").qty).toFixed(0) + " pz";
    }



    let description = p.display_name;
    // Trova la parte della stringa tra parentesi
    const regex = /\(([^)]+)\)/;
    const matches = description.match(regex);
  
    if (matches && matches[1]) {
      // Estrae i numeri all'inizio della stringa trovata
      const numbers = matches[1].split(',', 3);

        //il primo numero è la larghezza, il secondo l'altezza, il terzo la lunghezza in mm. li converto da stringhe a numero
      let la = parseInt(numbers[0])
      let al = parseInt(numbers[1])
      let lu = parseInt(numbers[2])
      
      let pz = 0;

      //calcolo il numero di pezzi dalla quantità disponibile
      //se unità misura = metri cubi (uom id = 11) moltiplico tutte e 3 le dimensioni e divido per 1000000000
      if (p.uom_id.id == 11 && la && al && lu) {
         pz = la * al * lu / 1000000000;}
      //se unità misura = metri quadrati (uom id = 9) moltiplico le prime due dimensioni e divido per 1000000
      if (p.uom_id.id == 9 && la && lu) {
         pz = la * lu / 1000000;}
      //se unità misura = metri lineari (uom id = 5) divido la lunghezza per 1000
      if (p.uom_id.id == 5 && lu) {
         pz = lu / 1000;}
      
      
      
         if (pz > 0 && p._freeForInventory>0){ 
        const result = Math.round((p._freeForInventory / pz) * 100) / 100;
        return result + " pz";}
      }
    // Restituisce null se non sono stati trovati tre numeri all'inizio della descrizione tra parentesi
    return null;
  }

  getCostBasedOnSector(p: ProductWithOnlyVariants) {
    //edi wants list price, hd wants cost price
    return this.selectedAreaFilter === 'EDI' ? p.list_price : p.standard_price;

  }

  getDimensionalAttrs(activeTemplate: ProductTemplate) {
    let attrs = activeTemplate.attribute_line_ids.values.filter(x => x.attribute_id.name.startsWith('Lunghezza') || x.attribute_id.name.startsWith('Larghezza') || x.attribute_id.name.startsWith('Altezza'))
    return attrs
  }

  canCreateVariant() {
    if (!this.activeTemplate)
      return false
    return (this.criteria.length == this.activeTemplate.attribute_line_ids.ids.length)
  }

  async createVariant() {
    // First, handle any new lunghezza values that need to be created
    const lunghezzaCriteria = this.criteria.find(c => 
      c.toCreate && c.attributeLine.attribute_id.name.startsWith('Lunghezza')
    );
  
    if (lunghezzaCriteria) {
      // Create the new attribute value
      let newValue = await this.odooEm.create<ProductAttributeValue>(
        new ProductAttributeValue(), 
        {
          name: lunghezzaCriteria.attributeValue.name,
          attribute_id: lunghezzaCriteria.attributeLine.attribute_id.id
        }
      ).toPromise();

      //refetch it suing the same filters
     let  newValues = await this.odooEm.search<ProductAttributeValue>(
        new ProductAttributeValue(),
        [
          ['name', '=', lunghezzaCriteria.attributeValue.name],
          ['attribute_id', '=', lunghezzaCriteria.attributeLine.attribute_id.id]
        ]
      ).toPromise();

  newValue = newValues[0]


      // Add it to the template's attribute line
      await this.odooEm.update(
        lunghezzaCriteria.attributeLine, 
        { value_ids: [[4, newValue.id]] }
      ).toPromise();
  
      // Create product template attribute value
      await firstValueFrom(
        this.odooEm.create(new ProductTemplateAttributeValue(), {
          product_attribute_value_id: newValue.id,
          attribute_line_id: lunghezzaCriteria.attributeLine.id,
          ptav_active: true
        })
      );

      console.log("created lunghezza", lunghezzaCriteria.attributeValue.name)

      console.log("now we search for the created value", newValue.id, lunghezzaCriteria, lunghezzaCriteria.attributeLine.attribute_id.id, this.activeTemplate.id)
  
      // Get the created template attribute value
      let ptav = await this.odooEm.search<ProductTemplateAttributeValue>(
        new ProductTemplateAttributeValue(),
        [
          ['attribute_id', '=', lunghezzaCriteria.attributeLine.attribute_id.id],
          ['product_attribute_value_id', '=', newValue.id],
          ['product_tmpl_id', '=', this.activeTemplate.id],
          ['ptav_active', '=', true]
        ]
      ).toPromise();
  
      // Update the criteria with the real value
      lunghezzaCriteria.attributeValue = ptav[0];
      lunghezzaCriteria.toCreate = false;
    }
  
    // Continue with normal variant creation
    var domain = [];
    
    this.criteria.forEach((x,i) => {
      domain.push('&');
      domain.push(['name', '=', x.attributeValue.name]);
      domain.push('&');
      domain.push(['product_tmpl_id', '=', this.activeTemplate.id]);
      domain.push(['attribute_id', '=', x.attributeValue.attribute_id.id]);
    });
  
    this.criteria.slice(0,-1).forEach(x => {
      domain.unshift('|');
    });
  
    var val = await firstValueFrom(this.odooEm.search<ProductTemplateAttributeValue>(
      new ProductTemplateAttributeValue(), 
      domain
    ));
    
    var aa = val.map(v => v.id);

    console.log("create variant", aa)
  
    var r:any = await this.odooEm.odoorpcService.sendRequest('/api/sale/create_product_variant', {
      "product_template_attribute_value_ids": JSON.stringify(aa),
      "product_template_id": this.activeTemplate.id
    });
    
    await this.odooEm.run(666, r.result, "product.product");
    this.refresh();
  }

  hasCriteria(c) {
    return this.criteria.find(x => {
      return x.attributeLine.id == c.attributeLine.id && x.attributeValue.id == c.attributeValue.id
    })
  }


  getIconClass(a: any): string {
    if (!this.canCreateVariant() && this.products.length === 0 && this.loaded) {
      const hasCriteria = this.criteria.find(x => x.attributeLine.id === a.id);
      if (!hasCriteria) {
        return 'fa-times text-danger';
      }
      return 'fa-check text-success';
    }
    return this.criteria.find(x => x.attributeLine.id === a.id) ? 
      'fa-check text-primary' : '';
  }
  
  getTextClass(a: any): string {
    if (!this.canCreateVariant() && this.products.length === 0 && this.loaded) {
      const hasCriteria = this.criteria.find(x => x.attributeLine.id === a.id);
      if (!hasCriteria) {
        return 'text-danger';
      }
      return 'text-success';
    }
    return this.criteria.find(x => x.attributeLine.id === a.id) ? 
      'text-primary' : '';
  }


  getCriteriaName(a: ProductTemplateAttributeLine): string {
    let selectedValue = '';
    a.value_ids.values.forEach(v => {
      if (this.hasCriteria({attributeLine: a, attributeValue: v})) {
        selectedValue = v.name;
      }
    });
    return selectedValue || `Qualsiasi`;
  }

  setLunghezza(a: ProductTemplateAttributeLine, inputValue: string) {
    // Remove any existing lunghezza criteria first
    this.criteria = this.criteria.filter(x => 
      !x.attributeLine.attribute_id.name.startsWith('Lunghezza')
    );
  
    // If input is empty, just refresh to show all results
    if (!inputValue) {
      this.refresh$.next(true);
      return;
    }
  
    // Search for matching lunghezza value in the attribute line's values
    console.log("searching for value", inputValue, a.value_ids.values);
    const matchingValue = a.value_ids.values.find(x => x.name === inputValue);
    
    if (matchingValue) {
      // If we found a matching value, use it
      console.log("found value", matchingValue);
      this.toggleCriteria({
        attributeLine: a,
        attributeValue: matchingValue
      });
    } else {
      // Create a temporary attribute value for the new lunghezza. this allows us to keep all it's data but not show any result
      const tempAttributeValue = {
        id: Date.now(), // Temporary ID
        name: inputValue,
        attribute_id: a.attribute_id,
        toCreate: true // Flag to indicate this needs to be created
      } as unknown as ProductTemplateAttributeValue;
  
      // Add to criteria with toCreate flag
      this.toggleCriteria({
        attributeLine: a,
        attributeValue: tempAttributeValue,
        toCreate: true
      });
    }
    
    // Trigger refresh to update results
    this.refresh$.next(true);
  }

  onSelectChange(attributeLine: ProductTemplateAttributeLine, event: any) {
    const selectElement = event.target;
    const selectedValue = selectElement.value;
    
    // First, remove any existing criteria for this attribute line
    this.criteria = this.criteria.filter(x => x.attributeLine.id !== attributeLine.id);
    
    // If a value was selected (not the empty option)
    if (selectedValue) {
      // Find the corresponding attribute value
      const attributeValue = attributeLine.value_ids.values.find(v => v.id.toString() === selectedValue);
      
      if (attributeValue) {
        // Add the selected criteria
        this.toggleCriteria({
          attributeLine: attributeLine,
          attributeValue: attributeValue
        });
      }
    }
  }
  
  async toggleCriteria(c) {
    console.log("TOGGLE CRITER",c)
    var index = this.criteria.findIndex(
      x => x.attributeLine.id == c.attributeLine.id
        && x.attributeValue.id == c.attributeValue.id)    
    if (index === -1) {
      // clear last active
      this.criteria = this.criteria.filter(x => x.attributeLine.id != c.attributeLine.id)
      this.criteria.push(c);
    } else
      this.criteria.splice(index, 1)
    var x = []
    this.criteria.forEach(c => 
      x.push({a:c.attributeLine.id, v:c.attributeValue.id})
    )
    window.localStorage.setItem("order-inventory-criteria", JSON.stringify(x))
  }
  
  async toggleProductTemplate(id:number) {
    this.loading.next(true)
    console.log("TOGGLE TEMPLATE",id)
    this.onlyFondi = false
    this.inputSearch.next("")
    //if already selected, deselect it
    if (this.activeTemplate && this.activeTemplate.id) {
      
    if (this.activeTemplate.id === id) {
      this.productToShow = this.activeTemplate
      this.activeTemplate = null
      this.loading.next(false)
      return
    }

  }
  this.loadingAttributes = true

    //if no activeTemplate, put the last used one
    if (!id) {
      id = this.productToShow.id
    }

    //select the new template
    this.activeTemplate = null  
    //fint the template in the list of templates

    var res = this.templates.filter(x => x.id == id)
    this.activeTemplate = res[0]

    //if no attribute values, load them
    if (!this.activeTemplate.attribute_line_ids.values) {
    
    await this.odooEm.resolve(res[0].attribute_line_ids).toPromise()
    await this.odooEm.resolveArray(new ProductAttributeValue(), res[0].attribute_line_ids.values, "value_ids").toPromise()
    }

  
   
 this.loadingAttributes = false
    
      this.criteria = []
      // prefill hidden criterias
      this.activeTemplate.attribute_line_ids.values.map(a => {
        if (a.value_ids.ids.length == 1 )
        this.toggleCriteria({attributeLine: a, attributeValue: a.value_ids.values[0],toCreate: true})
        // return 
      })
  
    this.productToShow = this.activeTemplate
    
    this.inventoryClosed = false
    //set the area toggle to the one fount in product tags of the template
    this.selectedAreaFilter = this.activeTemplate.product_tag_ids.ids.find(x => x == 2 || x == 3) ? 'EDI' : 'HD'
    this.refresh$.next(true)
    this.loading.next(false)

  }

  getSortedValues(values: any[]): any[] {
    if (!values || !Array.isArray(values)) {
      return values;
    }
    
    return [...values].sort((a, b) => {
      const numA = parseInt(a.name);
      const numB = parseInt(b.name);
      
      if (isNaN(numA) || isNaN(numB)) {
        return a.name.localeCompare(b.name);
      }
      
      return numA - numB;
    });
  }


  close() {
    this.inventoryClosed = true  
  }

  async refresh() {
    this.loaded = false;
    if (this.inputSearch.value === "" && !this.activeTemplate && !this.onlyFondi) {
      this.products = [];
      return;
    }
   
    this.loading.next(true)

    let x= Math.random()
    this.lastSearch = x
    var products = await this.getProducts(this.inputSearch.value)
    console.log("aaaaaaaa0 products",products)
    //solve additional product tags
    if (this.lastSearch != x) {
      return
    }

    await this.getFree(products)

        // Assign sort sequence number to each product
        products.forEach(p => {
          if (this.isOffer(p) && p._freeForInventory>0) {
              p._sortSequence = 1; // First priority - products to be consumed and discounted
          } else if (this.isAlmostFondo(p) && p._freeForInventory > 0) {
              p._sortSequence = 2; // Second priority - products to be consumed not discounted
          } else if (!(p.reordering_min_qty > 0) && !this.isFondoOrOffer(p) && p._freeForInventory>0) {
              p._sortSequence = 3; // third priority - non-standard products with stock
          } else if (p.reordering_min_qty > 0 ) {
              p._sortSequence = 4; // fourth priority - standard products
          } else {
              p._sortSequence = 5; // Everything else
          }
      });
      // Sort products by sequence and free_qty
      products.sort((a, b) => {
          // First compare by sort sequence
          if (a._sortSequence !== b._sortSequence) {
              return a._sortSequence - b._sortSequence;
          }
          // Within same sequence, sort by free_qty descending
          return b._freeForInventory - a._freeForInventory;
      });
    
    this.products = products;

    var prices = await this.loadPrice(this.products)
    this.products.forEach(p => {
      p._lst_price = prices[p.id]
    })
    this.loading.next(false)


    
//here i basically can start working, i only need to solve adding the stock quants for app photos
await firstValueFrom(this.odooEm.resolveArray(new StockQuant(), this.products, "stock_quant_ids"))
let quants = this.products.map(p => p.stock_quant_ids.values).flat() 
if (quants.length > 0){
await firstValueFrom(this.odooEm.resolveArrayOfSingle(new StockQuantPackage(), quants, "package_id"))
}

this.loaded = true //uset to show pack photos
}

  async insert(p:Product) {

    if (!this.sales && this.addOrderLine) {
      this.addOrderLine.next(p)
      return
    }

    var s = this.sales.find(x => x._open)
    if (s) {
      this.saleEditor.insertProduct(s,p,10000)
    }
  }

  isAreaTagSelected(tag: ProductTag): boolean {
    return this.selectedAreaTags && this.selectedAreaTags.some(t => t.id === tag.id);
  }
  getAreaTagsDisplayText(): string {
    if (!this.selectedAreaTags || this.selectedAreaTags.length === 0) {
      return 'Tutte le aree';
    }
    if (this.selectedAreaTags.length === 1) {
      return this.selectedAreaTags[0].name;
    }
    return `${this.selectedAreaTags.length} aree selezionate`;
  }
}