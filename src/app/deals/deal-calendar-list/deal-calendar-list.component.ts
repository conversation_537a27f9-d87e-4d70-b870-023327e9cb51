import { Component, Input, OnInit } from '@angular/core';
import { CalendarEvent } from 'src/app/models/calendar-event.model';
import { Lead } from 'src/app/models/crm.lead.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
    selector: 'app-deal-calendar-list',
    templateUrl: './deal-calendar-list.component.html',
    standalone: false
})
export class DealCalendarListComponent implements OnInit {
  array_events: CalendarEvent[];
  @Input() deal : Lead
  @Input() update;

  constructor(public odooEm: OdooEntityManager) { }

  async ngOnInit() {
    this.refresh()
  }

  openCalendarOdoo(){

    var url = "https://o3.galimberti.eu/web?debug=1#cids=1&menu_id=229&action=316&active_id="+this.deal.id+"&model=calendar.event&view_type=calendar"
    window.open(url, '_blank').focus();
  }

  ngOnChanges() {
    if(this.update){
      this.refresh()
    }
  }

  async refresh() {
    this.array_events = (await this.odooEm.search<CalendarEvent>(new CalendarEvent(), [['opportunity_id', '=', this.deal.id]]).toPromise()).reverse()
  }


}
