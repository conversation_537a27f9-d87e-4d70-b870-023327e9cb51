import { AfterViewInit, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { MrpProduction } from 'src/app/models/mrp-production';
import { SaleOrder } from 'src/app/models/sale-order.model';
import { StockMove } from 'src/app/models/stock-move';
import { StockMoveLine } from 'src/app/models/stock-move-line';
import { StockQuant } from 'src/app/models/stock-quant';
import { StockQuantPackage } from 'src/app/models/stock-quant-package';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
  selector: 'app-picking-production',
  standalone: false,
  templateUrl: './picking-production.component.html',
  styleUrl: './picking-production.component.scss'
})


export class PickingProductionComponent implements OnInit, AfterViewInit {
  production: MrpProduction;
  preproduction: MrpProduction;
  @Input() loading: boolean = false;

  // Package management
  packages: StockQuantPackage[] = [];
  selectedPackages: StockQuantPackage[] = [];
  inventoryPackages: StockQuantPackage[] = [];
  
  // UI state management
  masterLine: StockMoveLine | null = null;
  showPackageList: boolean = false;
  scanningBarcode: boolean = false;
  scanningFrom: string[] = [];
  scanningTo: string[] = [];
  addingFrom: boolean = false;
  addingTo: boolean = false;
  
  // Selection and filtering
  selectedLines: StockMoveLine[] = [];
  searchTerm: string = '';
  autoReserved: boolean = true;

  // Grouped data for display
  packagesGroup: { [key: string]: any[] } = {};
  id: any;
  saleOrder: SaleOrder;
  preproductionbyproductsMoves: StockMove[];

  draftOutputMove:StockMove = new StockMove();


  constructor(private odooEm: OdooEntityManager,private route: ActivatedRoute) {
    console.log('PickingProductionComponent: Constructor initialized');
  }

  async ngOnInit(): Promise<void> {
    this.route.params.subscribe(async params => {
      this.id = params['production_id']
      this.production = (await firstValueFrom(this.odooEm.search<MrpProduction>(new MrpProduction(), [["id", "=", this.id]])))[0];
      if (!this.production) {
        throw new Error('Production not found');
      }

     

      this.saleOrder = (await firstValueFrom(this.odooEm.search<SaleOrder>(new SaleOrder(), [["name", "=", this.production.origin]])))[0];

      this.preproduction = (await firstValueFrom(this.odooEm.search<MrpProduction>(new MrpProduction(), [["origin", "=", this.production.name]])))[0];
      console.log("PickingProductionComponent: Preproduction", this.preproduction);

      await this.solvePreproduction();
      await this.solveProduction();
      this.updatePackageGroups();

      // get moves from preproduction byproduct moves
      this.preproductionbyproductsMoves = this.preproduction.move_byproduct_ids.values;


       // find move for production product_id (this is the move for the finished product)
      let finishedMove = this.production.move_finished_ids.values.find(move => move.product_id.id == this.production.product_id.id);
      console.log(" Move for finished product", finishedMove);
      

      this.groupByResultPackage()
    })
  }


  getPackageFromDraftOutput(packname:string) {
    return this.draftOutputMove.move_line_ids.values.find(line => line.result_package_id.name == packname);
  }

  updatePackageInDraft(packname:string) {
    let line = this.getPackageFromDraftOutput(packname);
    line.result_package_id.name = packname
  }

  getSumQty(pkgname: string): number {
    return this.groupByResultPackage()[pkgname].reduce((acc, line) => acc + line.qty_done, 0);
  }

  groupByResultPackage() {

    var x = this.preproduction.move_byproduct_ids.values.map(move => move.move_line_ids.values).flat().reduce((acc, line) => {
      (acc[line.result_package_id.name] = acc[line.result_package_id.name] || []).push(line);
      return acc;
    }, {});

    return x
  }


  async ngAfterViewInit(): Promise<void> {
    console.log('PickingProductionComponent: AfterViewInit started');
    this.loading = true;
    try {

     
    } catch (error) {
      console.error('PickingProductionComponent: Error in AfterViewInit:', error);
    } finally {
      this.loading = false;
    }
  }

  // ====== ODOO DATA RESOLUTION ======
  async solvePreproduction(): Promise<void> {
    if (!this.preproduction) {
      console.log('PickingProductionComponent: No preproduction data available');
      return;
    }

    console.log('PickingProductionComponent: Resolving preproduction data');
    await firstValueFrom(this.odooEm.resolve(this.preproduction.move_byproduct_ids));
    
    // Resolve move lines
    await firstValueFrom(this.odooEm.resolveArray(new StockMoveLine(), this.preproduction.move_byproduct_ids.values, "move_line_ids"));
    
    // Get all move lines
    let moveLines = this.preproduction.move_byproduct_ids.values.map(move => move.move_line_ids.values).flat();
    
    // Resolve result_package_id on move lines
    await firstValueFrom(this.odooEm.resolveArrayOfSingle(new StockQuantPackage(), moveLines, "result_package_id"));
    
    // Extract unique result packages
    this.packages = Array.from(new Set(moveLines.map(line => line.result_package_id.value))).filter(p => p != null);
    
    // Resolve quants of packages
    await firstValueFrom(this.odooEm.resolveArray(new StockQuant(), this.packages, "quant_ids"));
    
    console.log("PickingProductionComponent: Preproduction resolved", this.preproduction);
    console.log("PickingProductionComponent: Available packages", this.packages);
  }

  async solveProduction(): Promise<void> {
    if (!this.production) {
      console.log('PickingProductionComponent: No production data available');
      return;
    }

    console.log('PickingProductionComponent: Resolving production data');
    await firstValueFrom(this.odooEm.resolve(this.production.move_finished_ids));
    await firstValueFrom(this.odooEm.resolve(this.production.move_raw_ids));
    
    // Resolve existing move lines
    await firstValueFrom(this.odooEm.resolveArray(new StockMoveLine(), this.production.move_raw_ids.values, "move_line_ids"));
    
    console.log("PickingProductionComponent: Production resolved", this.production);
  }

  async refetchProduction(): Promise<void> {
    console.log('PickingProductionComponent: Refetching production data');
    await firstValueFrom(this.odooEm.search<MrpProduction>(new MrpProduction(), [['id', '=', this.production.id]]));
    await this.solveProduction();
    this.updatePackageGroups();
  }

  // ====== PACKAGE MANAGEMENT ======
  updatePackageGroups(): void {
    console.log('PickingProductionComponent: Updating package groups');
    this.selectedPackages = this.getSelectedPackages();
    this.inventoryPackages = this.getInventoryPackages();
    
    // Group packages by location or other criteria
    this.packagesGroup = {
      'selected': this.selectedPackages,
      'inventory': this.inventoryPackages
    };
  }

  async onPackageClick(pkg: StockQuantPackage): Promise<void> {
    console.log("PickingProductionComponent: Package clicked", pkg);
    this.loading = true;

    try {
      // Create input move lines with package content
      for (const quant of pkg.quant_ids.values) {
        // Check if we already have a move line for this quant
        const existingMoveLine = this.production.move_raw_ids.values
          .map(move => move.move_line_ids.values)
          .flat()
          .find(line => line.product_id.id == quant.product_id.id && line.package_id.id == quant.package_id.id);

        if (existingMoveLine) {
          console.log("PickingProductionComponent: Found existing move line", existingMoveLine);
          if (existingMoveLine.qty_done == 0) {
            // Reselect package and set quantity
            await firstValueFrom(this.odooEm.update<StockMoveLine>(existingMoveLine, {
              qty_done: quant.quantity
            }));
            continue;
          } else {
            // Deselect: set qty_done to 0
            await firstValueFrom(this.odooEm.update<StockMoveLine>(existingMoveLine, {
              qty_done: 0
            }));
          }
        } else {
          // Create new move and move line
          console.log("PickingProductionComponent: Creating new move for quant", quant);
          const newMove = await firstValueFrom(this.odooEm.create<StockMove>(new StockMove(), {
            name: quant.product_id.name,
            product_id: quant.product_id.id,
            product_uom_qty: quant.quantity,
            product_uom: quant.product_uom_id.id,
            location_id: quant.location_id.id,
            location_dest_id: this.production.location_dest_id.id,
            raw_material_production_id: this.production.id
          }));

          const newMoveLine = await firstValueFrom(this.odooEm.create<StockMoveLine>(new StockMoveLine(), {
            product_id: quant.product_id.id,
            qty_done: quant.quantity,
            product_uom_id: quant.product_uom_id.id,
            move_id: newMove.id,
            package_id: quant.package_id.id
          }));

          await firstValueFrom(this.odooEm.update<MrpProduction>(this.production, {
            move_raw_ids: [[4, newMove.id]]
          }));

          console.log("PickingProductionComponent: Created new move and move line", { newMove, newMoveLine });
        }
      }
      
      await this.refetchProduction();
    } catch (error) {
      console.error('PickingProductionComponent: Error handling package click:', error);
    } finally {
      this.loading = false;
    }
  }

  isPackageSelected(pkg: StockQuantPackage): boolean {
    if (!this.production?.move_raw_ids?.values) return false;
    if (!this.production.move_raw_ids.values[0]?.move_line_ids?.values) return false;
    
    return this.production.move_raw_ids.values
      .map(move => move.move_line_ids?.values ? move.move_line_ids?.values : [])
      .flat()
      .some(line => line.package_id.id == pkg.id && line.qty_done > 0);
  }

  getSelectedPackages(): StockQuantPackage[] {
    return this.packages.filter(pkg => this.isPackageSelected(pkg));
  }
  
  getInventoryPackages(): StockQuantPackage[] {
    return this.packages.filter(pkg => !this.isPackageSelected(pkg));
  }
  
  getTotalQuantity(pkg: StockQuantPackage): number {
    if (!pkg.quant_ids || !pkg.quant_ids.values) return 0;
    return pkg.quant_ids.values.reduce((total, quant) => total + (quant.quantity || 0), 0);
  }

  // ====== PICKING-LIKE INTERFACE METHODS ======
  getSelectedLines(): StockMoveLine[] {
    return this.selectedLines;
  }

  setSelectedAllLines(selected: boolean): void {
    console.log('PickingProductionComponent: Set all lines selected:', selected);
    if (selected) {
      this.selectedLines = this.getAllMoveLines();
    } else {
      this.selectedLines = [];
    }
  }

  getAllMoveLines(): StockMoveLine[] {
    if (!this.production?.move_raw_ids?.values) return [];
    return this.production.move_raw_ids.values
      .map(move => move.move_line_ids?.values || [])
      .flat();
  }

  onLine(line: StockMoveLine): void {
    console.log('PickingProductionComponent: Line clicked:', line);
    this.masterLine = line;
  }

  back(): void {
    console.log('PickingProductionComponent: Back button pressed');
    this.masterLine = null;
  }

  async save(): Promise<void> {
    console.log('PickingProductionComponent: Save button pressed');
    if (!this.masterLine) return;
    
    this.loading = true;
    try {
      // Save masterLine changes
      await firstValueFrom(this.odooEm.update<StockMoveLine>(this.masterLine, {
        qty_done: this.masterLine.qty_done
      }));
      this.masterLine = null;
      await this.refetchProduction();
    } catch (error) {
      console.error('PickingProductionComponent: Error saving:', error);
    } finally {
      this.loading = false;
    }
  }

  async confirmProduction(): Promise<void> {
    console.log('PickingProductionComponent: Confirming production');
    this.loading = true;
    try {
      // Implement production confirmation logic
      await firstValueFrom(this.odooEm.update<MrpProduction>(this.production, {
        state: 'done'
      }));
      await this.refetchProduction();
    } catch (error) {
      console.error('PickingProductionComponent: Error confirming production:', error);
    } finally {
      this.loading = false;
    }
  }

  reloadPage(): void {
    console.log('PickingProductionComponent: Reloading page');
    window.location.reload();
  }

  setQuantitiesReserved(): void {
    console.log('PickingProductionComponent: Setting quantities to reserved');
    // Implement auto-reserve logic
    this.getAllMoveLines().forEach(line => {
      if (line.qty_done === 0) {
        line.qty_done = line.reserved_uom_qty;
      }
    });
  }

  // ====== UTILITY METHODS ======
  getDescriptiveTodo(move: any, line?: any): string {
    if (!move) return '';
    return `${move.product_uom_qty || 0} ${move.product_id?.value?.uom_id?.name || ''}`;
  }

  getDescriptiveDone(line: any): string {
    if (!line) return '';
    return `${line.qty_done || 0}/${line.product_uom_qty || 0}`;
  }

  getProductType(move: any): string {
    return move?.product_id?.value?.type || 'product';
  }

  groupItemBy(items: any[], key: string): { [key: string]: any[] } {
    if (!items) return {};
    return items.reduce((groups, item) => {
      const groupKey = this.getNestedProperty(item, key) || 'undefined';
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
      return groups;
    }, {});
  }

  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, prop) => current?.[prop], obj);
  }

  // ====== SCANNER METHODS ======
  onScanBarcode(code: string): void {
    console.log('PickingProductionComponent: Barcode scanned:', code);
    // Implement barcode scanning logic
  }

  onFrom(code: string): void {
    console.log('PickingProductionComponent: From barcode scanned:', code);
    // Implement from package scanning
  }

  onTo(code: string): void {
    console.log('PickingProductionComponent: To barcode scanned:', code);
    // Implement to package scanning
  }

  onAddFrom(code: string): void {
    console.log('PickingProductionComponent: Add from barcode scanned:', code);
    // Implement add from package scanning
  }

  onAddTo(code: string): void {
    console.log('PickingProductionComponent: Add to barcode scanned:', code);
    // Implement add to package scanning
  }
}