import { Component, Input, Output, EventEmitter, OnInit, AfterViewInit, ViewChild } from '@angular/core';
import { MrpProduction } from 'src/app/models/mrp-production';
import { StockQuant } from 'src/app/models/stock-quant';
import { StockMove } from 'src/app/models/stock-move';
import { StockMoveLine } from 'src/app/models/stock-move-line';
import { Table } from 'primeng/table';
import { Product } from 'src/app/models/product.model';
import { ProductPackaging } from 'src/app/models/product.packaging.model';
import { firstValueFrom } from 'rxjs';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { ProductTemplateAttributeValue } from 'src/app/models/product.template.attribute.value.model';
import { ProductTemplate } from 'src/app/models/product.template.model';
import { StockLocation } from 'src/app/models/stock-location';
import { StockQuantPackage } from 'src/app/models/stock-quant-package';
import Decimal from 'decimal.js';

// Define interface for grouped components
interface TemplateGroup {
  templateId: number;
  templateName: string;
  totalQty: number;
  uom: string;
  averageCost: number;
  totalValue: number;
  costPercentage: number;
  template?: ProductTemplate;
  moves: StockMove[];
  moveLines: StockMoveLine[];
}



@Component({
  selector: 'app-production-input-tab',
  templateUrl: './production-input-tab.component.html',
  standalone: false
})
export class ProductionInputTabComponent implements OnInit, AfterViewInit {
  @ViewChild('dt') table: Table;
  
  sortField: string = '';
  sortOrder: number = 1; // 1 for ascending, -1 for descending

  @Input() production: MrpProduction;
  openPacklist = false;
  
  @Output() togglePacklist = new EventEmitter<void>();
  @Output() loadingChange = new EventEmitter<boolean>();
  loading: boolean = false;
  dataLoaded: boolean = false;

  // Add grouped components
  groupedByTemplateInput: TemplateGroup[] = [];
  expandedRowsInput: { [key: number]: boolean } = {};

  constructor(private odooEM: OdooEntityManager) {}

  async ngOnInit() {
    console.log('Production Input Tab initialized');
  }
  
  async ngAfterViewInit(): Promise<void> {
    console.log("Production in input tab:", this.production);
    
    try {
      this.loadingChange.emit(true);
      await this.loadMoveLines();
    } catch (error) {
      console.error("Error loading move lines:", error);
    } finally {
     this.loadingChange.emit(false);
    }
  }

  onTogglePacklist() {
    console.log("Toggling packlist visibility");
    this.openPacklist = !this.openPacklist;
    this.togglePacklist.emit();
  }

  // called by pEditableColumn
  onEditComplete($event, line) {
    this.updateMoveLine($event.data, this.getInUom($event.data["_qty_done"], $event.data));
  }

  async handleSelectedQuants(quants: number[]): Promise<void> {
    if (!quants || quants.length === 0) {
      console.log("No quants selected");
      return;
    }
  
    console.log(`Processing ${quants.length} selected quants`);
    this.loading = true;
  
    try {
      // Load selected quants from odooEM
      const selectedQuants = await firstValueFrom(
        this.odooEM.search<StockQuant>(
          new StockQuant(),
          [['id', 'in', quants]]
        )
      );
      console.log("Selected quants:", selectedQuants);
  
      for (const quant of selectedQuants) {
        // Create move data
        console.log("Processing quant:", quant);
  
        const moveData = {
          name: quant.product_id.name,
          product_id: quant.product_id.id,
          product_uom_qty: 0,
          quantity_done: 0,
          product_uom: quant.product_uom_id.id,
          location_id: quant.location_id.id,
          location_dest_id: this.production.location_dest_id?.id,
          // production_id: this.production.id,
          raw_material_production_id: this.production.id
        };
  
        // Create the move
        const createdMove = await firstValueFrom(
          this.odooEM.create<StockMove>(new StockMove(), moveData)
        );
        console.log("Created move:", createdMove);

        // Create the move line with package information
        const createdMoveLine = await firstValueFrom(
          this.odooEM.create<StockMoveLine>(new StockMoveLine(), {
            product_id: quant.product_id.id,
            move_id: createdMove.id,
            location_id: quant.location_id.id,
            package_id: quant.package_id?.id || false,
            qty_done: 0,
            product_uom_id: quant.product_uom_id.id,
            // raw_material_production_id: this.production.id
          })
        );
  
        // Add the move to production's move_raw_ids
        await firstValueFrom(
          this.odooEM.update<MrpProduction>(this.production, {
            move_raw_ids: [[4, createdMove.id, {}]]
          })
        );
      }
  
      this.openPacklist = false;
      console.log("Successfully added components to production");
      
      // Reload the production to refresh move_raw_ids
      await this.reloadProduction();
      
      // Set default packaging and update quantities for all new move lines
      const moveLines = this.getMoveLines();
      for (const line of moveLines) {
        if (!line.product_packaging_id?.id) {
          await this.setDefaultPackaging(line);
        }
        
        // TODO GIULIO VERIFICA COME INSERIRE
        // Set qty_done to match available quantity (newly added feature)
        // if (line._available_qty > 0) {
        //   await this.setQuantityToAvailable(line);
        // }
      }
      
    } catch (error) {
      console.error('Error creating moves:', error);
      alert('Failed to add components to production');
    } finally {
      this.loading = false;
    }
  }

  /**
   * New method to set quantity done to available quantity
   */
  // private async setQuantityToAvailable(line: StockMoveLine): Promise<void> {
  //   if (!line || !line._available_qty) return;
    
  //   console.log(`Setting quantity done to available (${line._available_qty}) for product ${line.product_id?.name}`);
    
  //   try {
  //     // Update the move line with available quantity
  //     await firstValueFrom(
  //       this.odooEM.update<StockMoveLine>(line, {
  //         qty_done: line._available_qty
  //       })
  //     );
      
  //     // Update local reference
  //     line.qty_done = line._available_qty;
      
  //     // Update package quantity if packaging is set
  //     if (line.product_packaging_id?.id && line.product_packaging_id.value) {
  //       line._package_qty = line.qty_done / line.product_packaging_id.value.qty;
  //     }
      
  //     console.log("Successfully updated quantity to match available quantity");
  //   } catch (error) {
  //     console.error("Error setting quantity to available:", error);
  //   }
  // }

  private async reloadProduction(): Promise<void> {
    console.log("Reloading production data");
    this.loading = true;
    try {
      if (this.production.id) {
        const freshProduction = await firstValueFrom(
          this.odooEM.search<MrpProduction>(
            new MrpProduction(), 
            [['id', '=', this.production.id]]
          )
        );
        
        if (freshProduction.length > 0) {
          console.log("Production found, refreshing data");
          Object.assign(this.production, freshProduction[0]);
          
          // Load move lines after production is reloaded
          await this.loadMoveLines();
        }
      }
    } catch (error) {
      console.error("Error reloading production:", error);
    } finally {
      this.loading = false;
    }
  }

  /**
   * Gets all move lines from the production's raw moves.
   * This optimized version ensures we're getting the properly loaded move lines.
   */
  getMoveLines(): StockMoveLine[] {
    // Early exit if no production or no raw moves or data is still loading
    if (!this.production?.move_raw_ids?.values || !this.dataLoaded) {
      return [];
    }

    // Collect all move lines from all raw moves
    const allLines: StockMoveLine[] = [];
    
    for (const move of this.production.move_raw_ids.values) {
      if (move.move_line_ids?.values?.length > 0) {
        // Add each properly loaded move line to our collection
        for (const line of move.move_line_ids.values) {
          allLines.push(line);
        }
      }
    }
    
    return allLines;
  }

  /**
   * Calculate total cost of all move lines
   */
  getTotalCost() {
    if (!this.dataLoaded) return 0;
    
    return this.getMoveLines().reduce((total, line) => {
      return total +  line.product_id.value.standard_price * line.qty_done;
    }, 0);
  }

  async deleteMoveLine(line: StockMoveLine) {
    console.log("Deleting move line:", line);
    try {
      this.loading = true;
      await firstValueFrom(this.odooEM.delete(new StockMoveLine(), [line.id]));
      console.log("Move line deleted successfully");
      // Refresh move lines
      await this.loadMoveLines();
    } catch (error) {
      console.error("Error deleting move line:", error);
      alert('Error deleting move line: ' + (error.message || 'Unknown error'));
    } finally {
      this.loading = false;
    }

  }

  async loadMoveLines() {
    try {
      this.loading = true;
      console.log("Starting optimized data loading...");
      
      // Step 1: Resolve moves first
      await firstValueFrom(this.odooEM.resolve(this.production.move_raw_ids));
      console.log(`Resolved ${this.production.move_raw_ids.values?.length || 0} moves`);
      
      // Step 2: Resolve move lines for all moves in one operation
      const movesWithLines = this.production.move_raw_ids.values?.filter(move => move.move_line_ids?.ids?.length > 0) || [];
      if (movesWithLines.length > 0) {
        await firstValueFrom(this.odooEM.resolveArray(new StockMoveLine(), movesWithLines, "move_line_ids"));
        console.log(`Resolved move lines for ${movesWithLines.length} moves`);
      }


      // console.log("XXXX", this.production, )
      let moveLines = []; 
      for (const move of this.production.move_raw_ids.values) {
        if (move.move_line_ids?.values) {
          moveLines.push(...move.move_line_ids.values);
        }
      }

      // Step 4: Resolve products of all moves
      await firstValueFrom(this.odooEM.resolveArrayOfSingle(new Product(), this.production.move_raw_ids.values, "product_id"));

      // Step 5: Collect all products to resolve their packagings and attributes
      const products = this.production.move_raw_ids.values.map(move => move.product_id.value);
    
      if (products.length > 0) {
        // Step 6: Resolve product templates
        await firstValueFrom(this.odooEM.resolveArrayOfSingle(new ProductTemplate(), products, "product_tmpl_id"));
        
        // Step 7: Resolve packagings for all products in one batch
        await firstValueFrom(this.odooEM.resolveArray(new ProductPackaging(), products, "packaging_ids"));
        console.log("Resolved packagings for all products");
        
        // Step 8: Resolve template attribute values for all products in one batch
        await firstValueFrom(this.odooEM.resolveArray(new ProductTemplateAttributeValue(), products, "product_template_attribute_value_ids"));
        console.log("Resolved attribute values for all products");
        
        // Step 9: Resolve attribute lines for templates
        const templates = products.map(p => p.product_tmpl_id.value).filter(t => t != null);
        for (const template of templates) {
          await firstValueFrom(this.odooEM.resolve(template.attribute_line_ids));
        }
        console.log("Resolved attribute lines for templates");
      }

      // Assign products values to move lines, searching it in products
      moveLines.forEach(line => {
        let product = products.find(p => p.id === line.product_id.id);
        if (product) {
          line.product_id.value = product;
        }
      });
      
      // Step 10: Resolve package information
      const moveLinesWithPackages = moveLines.filter(line => line.package_id?.id);
      
      if (moveLinesWithPackages.length > 0) {
        await firstValueFrom(this.odooEM.resolveArrayOfSingle(new StockQuantPackage(), moveLinesWithPackages, "package_id"));
        console.log("Resolved packages for move lines");
      }


   

      const packages = moveLinesWithPackages.map(line => line.package_id.value);

      // Solve quant ids of all packages
      if (moveLinesWithPackages.length > 0) {
        await firstValueFrom(this.odooEM.resolveArray(new StockQuant(), packages, "quant_ids"));
        console.log("Resolved quant ids for packages");
      }

      // Step 11: Resolve product packaging
      for (const line of moveLines) {
        if (line.product_packaging_id?.id) {
          await firstValueFrom(this.odooEM.resolveSingle(new ProductPackaging(), line.product_packaging_id));
        }
      }
      // Step 12: Calculate available quantities and initialize package quantities
      await this.assignAvailableQuantities(moveLines);
      // this.initializePackageQuantities(moveLines);
      moveLines.forEach(line => {
        line._qty_done = this.getInPz(line.qty_done, line.product_id.value);
      });
      // Step 13: Group data by template
      this.calculateGroupedData();
      
      this.dataLoaded = true;
      console.log("All data loaded successfully");
      
      // Log some debug data
      const lines = this.getMoveLines();
      console.log(`Retrieved ${lines.length} move lines ready for display`);
      if (lines.length > 0) {
        console.log("Sample line data:", lines[0]);
      }
      
    } catch (error) {
      console.error("Error loading move lines:", error);
    } finally {
      this.loading = false;
    }
  }

  private calculateGroupedData() {
    // Clear existing groupings
    this.groupedByTemplateInput = [];
    
    // Create a map to hold templates and their data
    const templateMap = new Map<number, TemplateGroup>();
    
    // Get all raw moves
    const moves = this.production?.move_raw_ids?.values || [];
    
    // Group moves by template ID
    for (const move of moves) {
      const product = move.product_id?.value;
      if (!product || !product.product_tmpl_id?.value) continue;
      
      const templateId = product.product_tmpl_id.value.id;
      const templateName = product.product_tmpl_id.value.name || 'Unknown Template';
      const quantity = move.product_uom_qty || 0;
      const price = product.standard_price || 0;
      const value = price * quantity;
      const uom = move.product_uom?.name || '';
      
      if (!templateMap.has(templateId)) {
        templateMap.set(templateId, {
          templateId,
          templateName,
          totalQty: 0,
          uom,
          averageCost: 0,
          totalValue: 0,
          costPercentage: 0,
          template: product.product_tmpl_id.value,
          moves: [],
          moveLines: []
        });
      }
      
      const group = templateMap.get(templateId);
      
      // Add move lines to the group
      if (move.move_line_ids?.values) {
        group.moveLines.push(...move.move_line_ids.values);
      }
    }
    
    // Convert map to array and sort by template name
    this.groupedByTemplateInput = Array.from(templateMap.values())
      .sort((a, b) => a.templateName.localeCompare(b.templateName));
    
    // Auto-expand the first group if there are any
    if (this.groupedByTemplateInput.length > 0) {
      this.expandedRowsInput = { [this.groupedByTemplateInput[0].templateId]: true };
    }

    this.calculateTotals();
    
    console.log("Grouped input data calculated:", this.groupedByTemplateInput);
  }

  private calculateTotals() {
    this.groupedByTemplateInput.forEach(group => {
      group.totalQty = group.moveLines.reduce((total, line) => total + line.qty_done, 0);
      group.totalValue = group.moveLines.reduce((total, line) => total + line.product_id.value.standard_price * line.qty_done, 0);
      group.averageCost = group.totalValue / group.totalQty;
    });
  }

  getAttributeColumns(template: ProductTemplate): any[] {
    if (!template.attribute_line_ids?.values) {
      return [];
    }

    return template.attribute_line_ids.values
      .map(line => ({
        field: line.attribute_id.name,
        header: line.attribute_id.name,
        valueGetter: (moveLine: StockMoveLine) => {
          const productAttributeValues = moveLine.product_id?.value?.product_template_attribute_value_ids?.values || [];
          const matchingValue = productAttributeValues.find(ptav => 
            line.value_ids.values.some(value => value.id === ptav.product_attribute_value_id.id)
          );
          return matchingValue?.name || '';
        }
      })).filter(col => !col.header.includes('extra'));
  }

  getAttributeValue(moveLine: StockMoveLine, attributeName: string): string {
    if (!moveLine.product_id?.value?.product_template_attribute_value_ids?.values) {
      return "";
    }
    
    const attributeValue = moveLine.product_id.value.product_template_attribute_value_ids.values.find(
      value => value.attribute_id.name === attributeName
    );
    
    return attributeValue ? attributeValue.name : "";
  }


  async getAllAvailableQuantities(moveLine: StockMoveLine): Promise<void> {
    console.log("Assigning available quantities to move lines:", moveLine);
    await this.updateMoveLine(moveLine,this.getAvailableQty(moveLine));
  }

  async getAllDoneQuantities(moveLine: StockMoveLine): Promise<void> {
    console.log("Assigning available quantities to move lines:", moveLine);
    await this.updateMoveLine(moveLine, 0);
  }


  getAvailableQty(line: StockMoveLine): number {
    if (line.package_id?.id && line.package_id.value?.quant_ids?.values) {
      return line.package_id.value.quant_ids.values
        .filter(quant => quant.product_id.id === line.product_id.id)
        .reduce((acc, quant) => acc + quant.quantity, 0);
    } else {
      return 0;
    }
  }
  
  /**
   * Assign available quantities to move lines
   */
  assignAvailableQuantities(moveLines: StockMoveLine[]): void {
    console.log("Assigning available quantities to move lines:", moveLines);
    // if package, return the quantity of the package.stock_quant_ids with the same product_id, otherwise return the available quantity for the product
    moveLines.forEach(line => {
      if (line.package_id?.id && line.package_id.value?.quant_ids?.values) {
        line._available_qty = this.getInPz(this.getAvailableQty(line)  - line.qty_done, line.product_id.value);
      } 
      // else if (line.product_id?.value) {
      //   line._available_qty = line.product_id.value.qty_available;
      // } 
      
      else {
        line._available_qty = 0;
      }
    });
  }

  /**
   * Initialize package quantities for move lines
   */
  async initializePackageQuantities(moveLines: StockMoveLine[]): Promise<void> {
    for (const line of moveLines) {
      // If line has no packaging set, try to set default packaging
      if (!line.product_packaging_id?.id && line.product_id?.value?.packaging_ids?.values?.length > 0) {
        await this.setDefaultPackaging(line);
      } else if (line.product_packaging_id?.id && line.product_packaging_id.value) {
        // Calculate package quantity based on product packaging
        if (line.qty_done > 0) {
          line._package_qty = line.qty_done / line.product_packaging_id.value.qty;
        } else {
          line._package_qty = 0;
        }
      } else {
        line._package_qty = 0;
      }
    }
  }
  /**
   * Get descriptive quantity representation
   */
  // getDescriptiveQuantity(product: Product, quantity: number): string {
  //   return product
  //   ? "(" + this.odooEM.getDescriptive(product, quantity) + ")"
  //   : "";
  // }

  getProductDimension(line: StockMoveLine, dimension: string): string {
    if (!line.product_id?.value) return null;
    if (!line.product_id.value.product_template_attribute_value_ids?.values) return null;
    
    const attr = line.product_id.value.product_template_attribute_value_ids.values.find(
      value => value.attribute_id?.name?.startsWith(dimension)
    );
    
    return attr ? attr.name : null;
  }

  // getQuantityBadgeClass(quantity: number): string {
  //   if (!quantity || quantity <= 0) return 'bg-danger';
  //   if (quantity < 10) return 'bg-warning';
  //   return 'bg-success';
  // }

  /**
   * Determine if the quantity exceeds available quantity
   */
  // isQuantityExceeded(line: StockMoveLine): boolean {
  //   if (!line) return false;
  //   return line.qty_done > line._available_qty;
  // }

  async onPackagingChange(line: StockMoveLine, packagingId: number | string): Promise<void> {
    if (!line || !packagingId) return;
    
    console.log("Packaging changed:", packagingId);
    this.loading = true;
    
    try {
      // Find the selected packaging object
      const packaging = line.product_id?.value?.packaging_ids?.values?.find(pkg => pkg.id === Number(packagingId));
      
      if (!packaging) {
        console.warn("Selected packaging not found");
        return;
      }
      
      // Update the move line's packaging
      await firstValueFrom(
        this.odooEM.update<StockMoveLine>(line, {
          product_packaging_id: Number(packagingId)
        })
      );
      
      // Update the local reference
      line.product_packaging_id = {
        id: packaging.id,
        name: packaging.name,
        value: packaging
      };
      
      // If there's a quantity, update the package quantity
      if (line.qty_done > 0) {
        line._package_qty = line.qty_done / packaging.qty;
      } else if (line._package_qty > 0) {
        // If there's a package quantity but no qty_done, update the qty_done
        const newQtyDone = line._package_qty * packaging.qty;
        
        await firstValueFrom(
          this.odooEM.update<StockMoveLine>(line, {
            qty_done: newQtyDone
          })
        );
        
        line.qty_done = newQtyDone;
      }
      
      console.log("Packaging updated successfully");
    } catch (error) {
      console.error("Error updating packaging:", error);
      alert('Error updating packaging: ' + (error.message || 'Unknown error'));
    } finally {
      this.loading = false;
    }
  }


  findParentMove(moveId: number): StockMove {
    return this.production.move_raw_ids.values.find(move => move.id === moveId);
  }

  async updateMoveLine(line: StockMoveLine,qty) {

    // update fucking move product_uom_qty
    var move = this.findParentMove(line.move_id.id)
    // sum all lines
   
    // this.findParentMove(line.move_id.id).product_uom_qty = qty;

    if (!line) return;
    console.log("Updating move line quantity:", line);
    this.loading = true;
    
    try {
      await firstValueFrom(
        this.odooEM.update<StockMoveLine>(line, {
          qty_done: qty
        })
      );
      
      // Update package quantity to match
      if (line.product_packaging_id?.id && line.product_packaging_id.value) {
        line._package_qty = line.qty_done / line.product_packaging_id.value.qty;
      }
      
      console.log("Move line updated successfully");
    } catch (error) {
      console.error("Error updating move line:", error);
      alert('Error updating quantity: ' + (error.message || 'Unknown error'));
      // Revert to previous value if update fails
    } finally {
      // await this.loadMoveLines(); too slow
      var lines = await firstValueFrom(this.odooEM.search<StockMoveLine>(new StockMoveLine(), [["id", "=", line.id]]))
      lines.forEach(line => {

        this.production.move_raw_ids.values.forEach(move => {
            if (move.move_line_ids?.values) {
              move.move_line_ids.values.forEach(l => {
                if (l.id === line.id) {
                  l.qty_done = line.qty_done;
                  l._qty_done = this.getInPz(line.qty_done, l.product_id.value);
                  l._available_qty = this.getInPz(this.getAvailableQty(l), l.product_id.value) - l._qty_done;
                }
              });
            }
        });
      });

      var product_uom_qty = move.move_line_ids.values.reduce((acc, line) => acc + line.qty_done, 0);
      await firstValueFrom(
        this.odooEM.update<StockMove>(move, {
          product_uom_qty: product_uom_qty
        })
      );

      this.loading = false;
    }
  }
   
  /**
   * Update line quantity based on package quantity
   */
  async updateLineByPackage(line: StockMoveLine) {
    if (!line || !line._package_qty) return;
    
    console.log("Updating move line by package quantity:", line);
    this.loading = true;
    
    try {
      let packageQty = line._package_qty;
      let packaging;
      
      // Use current packaging if set, or default to first available
      if (line.product_packaging_id?.id && line.product_packaging_id.value) {
        packaging = line.product_packaging_id.value;
      } else if (line.product_id?.value?.packaging_ids?.values?.length > 0) {
        packaging = line.product_id.value.packaging_ids.values[0];
        
        // Update the line's packaging
        await firstValueFrom(
          this.odooEM.update<StockMoveLine>(line, {
            product_packaging_id: packaging.id
          })
        );
        
        line.product_packaging_id = {
          id: packaging.id,
          name: packaging.name,
          value: packaging
        };
      } else {
        console.warn("No packaging available for this product");
        this.loading = false;return;
      }
      
      // Calculate the actual qty_done based on package quantity
      const newQtyDone = packageQty * packaging.qty;
      
      // Update the move line
      await firstValueFrom(
        this.odooEM.update<StockMoveLine>(line, {
          qty_done: newQtyDone,
          product_packaging_id: packaging.id
        })
      );
      
      // Update local values
      line.qty_done = newQtyDone;
      
      console.log("Move line updated by package quantity successfully");
    } catch (error) {
      console.error("Error updating move line by package:", error);
      alert('Error updating package quantity: ' + (error.message || 'Unknown error'));
      // Revert to previous value if update fails
      await this.loadMoveLines();
    } finally {
      this.loading = false;
    }
  }

  async selectPackage(line: StockMoveLine, packageInfo: ProductPackaging) {
    console.log("Selecting package:", packageInfo, "for line:", line);
    
    if (!packageInfo || !line) return;
    
    this.loading = true;
    
    try {
      // Calculate new quantity based on package
      const newQty = packageInfo.qty;
      
      // Update move line
      await firstValueFrom(
        this.odooEM.update<StockMoveLine>(line, {
          qty_done: newQty,
          product_packaging_id: packageInfo.id
        })
      );
      
      // Update local values
      line.qty_done = newQty;
      line._package_qty = 1; // One package
      line.product_packaging_id = {
        id: packageInfo.id,
        name: packageInfo.name,
        value: packageInfo
      };
      
      console.log("Package selected and quantity updated");
    } catch (error) {
      console.error("Error selecting package:", error);
      alert('Error selecting package: ' + (error.message || 'Unknown error'));
    } finally {
      this.loading = false;
    }
  }


  formatProductName(name: string): string {
    if (!name) return '';
    
    // If the name contains parentheses, extract the part before the first parenthesis
    const parenthesisPos = name.indexOf('(');
    if (parenthesisPos > 0) {
      return name.substring(0, parenthesisPos).trim();
    }
    
    return name;
  }
  
   // Extracts the attributes from product name (the part in parentheses)
  // extractAttributes(name: string): string {
  //   if (!name) return '';
    
  //   // Extract the content between parentheses and replace ', -' with ''
  //   const match = name.match(/\(([^)]+)\)/);
  //   if (match) {
  //     return match[1].replaceAll(', -', '');
  //   }

  //   return '';
  // }



  getInPz(qty:number, product: Product): number {
    // truncate decimal
    
    if (!product.packaging_ids.values.find(p => p.name === "Pz")) return 0;
    return Math.floor(qty / product.packaging_ids.values.find(p => p.name === "Pz").qty) || 0;

  }

  getInUom(qty:number, line) {
    console.log("get in uom ",qty, line.product_id.value.packaging_ids.values.find(p => p.name === "Pz"))
    return qty * line.product_id.value.packaging_ids.values.find(p => p.name === "Pz").qty;
  }

  
  /**
   * When adding a new move line, select the "Pz" packaging by default
   */
  async setDefaultPackaging(line: StockMoveLine): Promise<void> {
    if (!line.product_id?.value?.packaging_ids?.values) return;
    
    // Look for the "Pz" packaging
    let pzPackaging = line.product_id.value.packaging_ids.values.find(pkg => pkg.name === 'Pz');
    
    // If not found, use the first available packaging
    if (!pzPackaging && line.product_id.value.packaging_ids.values.length > 0) {
      pzPackaging = line.product_id.value.packaging_ids.values[0];
    }
    
    if (pzPackaging) {
      await firstValueFrom(
        this.odooEM.update<StockMoveLine>(line, {
          product_packaging_id: pzPackaging.id
        })
      );
      
      line.product_packaging_id = {
        id: pzPackaging.id,
        name: pzPackaging.name,
        value: pzPackaging
      };
      
      // Calculate package quantity
      if (line.qty_done > 0) {
        line._package_qty = line.qty_done / pzPackaging.qty;
      }
    }
  }

  onSort(event: any) {
    this.sortField = event.field;
    this.sortOrder = event.order;
    console.log(`Table sorted by ${this.sortField} in ${this.sortOrder === 1 ? 'ascending' : 'descending'} order`);
  }
}
