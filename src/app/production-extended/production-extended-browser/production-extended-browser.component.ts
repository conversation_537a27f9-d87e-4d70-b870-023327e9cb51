import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { MrpProduction } from 'src/app/models/mrp-production';
import { Product } from 'src/app/models/product.model';
import { SaleOrder } from 'src/app/models/sale-order.model';
import { User } from 'src/app/models/user.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { Table } from 'primeng/table';
import { UomUom } from 'src/app/models/uom-uom.model';
import { StockMove } from 'src/app/models/stock-move';

// Interface for column definition
interface Column {
  field: string;
  header: string;
  width?: string;
  sortable?: boolean;
  filterable?: boolean;
  filterType?: string;
  filterPlaceholder?: string;
}

@Component({
  selector: 'app-production-extended-browser',
  templateUrl: './production-extended-browser.component.html',
  styleUrl: './production-extended-browser.component.scss',
  standalone: false
})
export class ProductionExtendedBrowserComponent implements OnInit {
  @ViewChild('dt') table: Table;
  searchValue: string = '';
  productions: MrpProduction[] = [];
  loading: boolean = false;
  sortField: string = 'name';
  sortOrder: number = 0; // Descending by default
  sales: SaleOrder[] = [];
  today: Date = new Date();


  //filters
  selectedStates: string[] = ['draft', 'confirmed', 'progress', 'to_close']; // Default selected states
selectedAvailability: string[] = [];
selectedUser: User | null = null;
users: User[] = [];
showMyProductions: boolean = false;
showLateOnly: boolean = false;
showTodayOnly: boolean = false;
startDate: string = '';
endDate: string = '';
filteredProductions: MrpProduction[] = [];

  // Column definitions
  cols: Column[] = [
    { field: 'name', header: 'Riferimento',  sortable: true, filterable: true, filterType: 'text', filterPlaceholder: 'Filtra per riferimento' },
    { field: 'origin', header: 'Descrizione',sortable: true, filterable: true, filterType: 'text', filterPlaceholder: 'Filtra per origine' },
    { field: 'product_id.name', header: 'Prodotto', sortable: true, filterable: true, filterType: 'text', filterPlaceholder: 'Filtra per prodotto' },
    { field: 'product_qty', header: 'Quantità',  sortable: true, filterable: true, filterType: 'numeric', filterPlaceholder: 'Filtra per quantità' },
    // { field: 'date_deadline', header: 'Scadenza',sortable: true, filterable: true, filterType: 'text', filterPlaceholder: 'Filtra per scadenza' },
    // { field: 'date_planned_start', header: 'Data Pianificata',  sortable: true, filterable: true, filterType: 'date', filterPlaceholder: 'Filtra per data pianificata' },
    { field: 'user_id.name', header: 'Responsabile',  sortable: true, filterable: true, filterType: 'text', filterPlaceholder: 'Filtra per responsabile' },
    // user that create
    // { field: 'create_uid.name', header: 'Creato da',  sortable: true, filterable: true, filterType: 'text', filterPlaceholder: 'Filtra per creato da' },
    { field: 'state', header: 'Stato',  sortable: true, filterable: true, filterType: 'text', filterPlaceholder: 'Filtra per stato' },    
  ];

  // Initially selected columns (all)
  selectedColumns: Column[] = [];
  currentUserId: any;

  constructor(
    private odooEM: OdooEntityManager,
    private router: Router,
    private cd: ChangeDetectorRef
  ) {
    console.log('ProductionExtendedBrowserComponent initialized');
  }

  async ngOnInit() {
    this.loading = true;

    // Set initial selected columns (all by default)
    this.selectedColumns = [...this.cols];
    
    await this.loadProductions();
    // this.applyFilters();
  }

  async loadUsers() {
    try {
      this.users = await firstValueFrom(
        this.odooEM.search<User>(new User(), [], 100)
      );
    } catch (error) {
      console.error('Error loading users:', error);
    }
  }

  toggleState(state: string) {
    const index = this.selectedStates.indexOf(state);
    if (index > -1) {
      this.selectedStates.splice(index, 1);
    } else {
      this.selectedStates.push(state);
    }
    this.applyFilters();
  }
  
  
  toggleMyProductions() {
    this.showMyProductions = !this.showMyProductions;
    if (this.showMyProductions) {
      console.log('Current user ID:', this.currentUserId);
      console.log('Users:', this.users);
      this.selectedUser = this.users.find(u => u.partner_id.id === this.currentUserId);
      console.log('Selected user:', this.selectedUser);
    } else {
      this.selectedUser = null;
    }
    this.applyFilters();
  }
  
  toggleLateOnly() {
    this.showLateOnly = !this.showLateOnly;
    this.applyFilters();
  }
  
  toggleTodayFilter() {
    this.showTodayOnly = !this.showTodayOnly;
    this.applyFilters();
  }
  resetFilters() {
    this.selectedStates = ['draft', 'confirmed', 'progress', 'to_close'];
    this.selectedAvailability = [];
    this.selectedUser = null;
    this.showMyProductions = false;
    this.showLateOnly = false;
    this.showTodayOnly = false;
    this.startDate = '';
    this.endDate = '';
    this.applyFilters();
  }
  
  applyFilters() {
    let filtered = [...this.productions];
  
    // Filter by state
    if (this.selectedStates.length > 0) {
      filtered = filtered.filter(p => this.selectedStates.includes(p.state));
    }
  
    // Filter by component availability
    if (this.selectedAvailability.length > 0) {
      filtered = filtered.filter(p => 
        this.selectedAvailability.includes(p.components_availability_state || '')
      );
    }
  
    // Filter by user
    if (this.selectedUser || this.showMyProductions) {
      filtered = filtered.filter(p => p.user_id?.id === this.selectedUser.id || p.create_uid.id === this.currentUserId || p.write_uid.id === this.currentUserId || p.activity_user_id.id === this.currentUserId );
    }
  
    // Filter by late productions
    if (this.showLateOnly) {
      filtered = filtered.filter(p => this.isLate(p));
    }
  
    // Filter by today
    if (this.showTodayOnly) {
      const today = new Date().toISOString().split('T')[0];
      filtered = filtered.filter(p => 
        p.date_deadline && p.date_deadline.startsWith(today)
      );
    }
  
    // Filter by date range
    if (this.startDate) {
      filtered = filtered.filter(p => 
        p.date_deadline && p.date_deadline >= this.startDate
      );
    }
    if (this.endDate) {
      filtered = filtered.filter(p => 
        p.date_deadline && p.date_deadline <= this.endDate
      );
    }
  
    this.filteredProductions = filtered;
  }

  async loadProductions() {
    this.loading = true;
    console.log('Loading productions...');
    try {
      var productions = await firstValueFrom(
        this.odooEM.search<MrpProduction>(
          new MrpProduction(), 
          [
            ['state', 'in', ['draft', 'confirmed', 'progress', 'to_close', 'done']]
          ], 
          500,
          null,
          "name DESC"
          
        )
      );
      //solve move finished
      await firstValueFrom(this.odooEM.resolveArray(new StockMove(), productions, "move_finished_ids"));
      console.log(`Found ${productions.length} productions`);


      productions = productions.filter(p => p.origin && !p.origin.startsWith('V') && !p.origin.startsWith('LOM'));

      // Get related sale orders
      const originValues = productions
        .map(p => p.origin)
        .filter(origin => origin && origin.startsWith('V') && !origin.startsWith('MO')); // Only get sale orders starting with V

      
      // Aggiungi metodo per il filtro globale
      this.table?.filterGlobal('', 'contains');

      if (originValues.length > 0) {
        console.log(`Fetching ${originValues.length} related sale orders`);
        this.sales = await firstValueFrom(
          this.odooEM.search<SaleOrder>(
            new SaleOrder(), 
            [['name', 'in', originValues]]
          )
        );
        console.log(`Found ${this.sales.length} related sale orders`);
      }



      this.productions = productions;
      //we have to change the product to match the list of components instead of the product, so
      //if product name contains "empty", we use the first finished move product that is not empty
      this.productions.forEach(p => {
        if (p.move_finished_ids.values.length > 0) {
          let validMove = p.move_finished_ids.values.find(m => !m.product_tmpl_id.name.toLowerCase().includes('empty'));
          if (validMove) {
            p.product_id.name = validMove.product_tmpl_id.name;
          }
        }
        //if still empty, write "prodotto da definire"
        if (p.product_id.name.toLowerCase().includes('empty')) {
          p.product_id.name = "Prodotto da definire";
        }
      })


      //builf production origin here with saleorders so that in the table we can filter for sale order details

      this.productions.forEach(p => {
        let sale = this.sales.find(s => s.name === p.origin);
        if (sale) {
         p.origin = `${p.origin} - ${sale.partner_id.name}`;
        }
      })

      this.filteredProductions = this.productions;


      console.log('Productions loaded successfully', this.productions);
      this.cd.markForCheck();
    } catch (error) {
      console.error('Error loading productions:', error);
    } finally {
      this.loading = false;
    }
  }

  async loadRelatedSaleOrders(productions: MrpProduction[]): Promise<void> {
    // Get related sale orders
    const originValues = productions
      .map(p => p.origin)
      .filter(origin => origin && origin.startsWith('V')); // Only get sale orders starting with V

    if (originValues.length > 0) {
      console.log(`Fetching ${originValues.length} related sale orders`);
      this.sales = await firstValueFrom(
        this.odooEM.search<SaleOrder>(
          new SaleOrder(), 
          [['name', 'in', originValues]]
        )
      );
    }
  }


  // Get the field value for a column (handles nested properties)
  getFieldValue(production: any, field: string): any {
    if (field.includes('.')) {
      const parts = field.split('.');
      let value = production;
      for (const part of parts) {
        if (value && value[part] !== undefined) {
          value = value[part];
        } else {
          return undefined;
        }
      }
      return value;
    }
    return production[field];
  }

  getProductionReadiness(production: MrpProduction){
    const componentsState = production.components_availability_state || '';
    if (componentsState === 'late')
      return 'In ritardo'
    if (componentsState === 'expected') 
      return 'In arrivo'
    if (componentsState === 'available') 
      return 'Disponibile'
    if (componentsState === '')
      return 'Non disponibile'
  }

  getProductionReadinessIcon(production: MrpProduction): string {
    const componentsState = production.components_availability_state || '';
    if (componentsState === 'late') 
      return 'fa-solid fa-alarm-exclamation';
    if (componentsState === 'expected') 
      return 'fa-solid fa-truck';
    if (componentsState === 'available')
      return 'fa-solid fa-check';
    if (componentsState === '')
      return 'fa-solid fa-xmark';
  }

  getProductionReadinessColor(production: MrpProduction): string {
    const componentsState = production.components_availability_state || '';
    if (componentsState === 'late') 
      return 'text-warning';
    if (componentsState === 'expected') 
      return 'text-primary';
    if (componentsState === 'available')
      return 'text-success';
    if (componentsState === '')
      return 'text-danger';
  }

  getStatusClass(state: string): string {
    switch (state) {
      case 'draft': return '';
      case 'confirmed': return 'text-secondary';
      case 'to_close': return 'text-primary';
      case 'planned': return 'text-warning';
      case 'progress': return 'text-warning';
      case 'done': return 'text-success';
      case 'cancel': return 'text-muted';
      default: return 'text-secondary';
    }
  }

  getStateLabel(state: string): string {
    switch (state) {
      case 'draft': return 'Bozza';
      case 'confirmed': return 'Confermato';
      case 'planned': return 'Pianificato';
      case 'progress': return 'In Corso';
      case 'to_close': return 'Da Chiudere';
      case 'done': return 'Completato';
      case 'cancel': return 'Annullato';
      default: return state;
    }
  }

  getComponentAvailabilityLabel(state: string): string {
    switch (state) {
      case 'available': return 'Disponibile';
      case 'expected': return 'In arrivo';
      case 'late': return 'In ritardo';
      case 'unavailable': return 'Non disponibile';
      default: return state || 'N/D';
    }
  }

  isLate(production: MrpProduction): boolean {
    if (!production.date_deadline) return false;
    
    const deadlineDate = new Date(production.date_deadline);
    return deadlineDate < this.today && production.state !== 'done';
  }

  getSaleOrder(production: MrpProduction): SaleOrder | undefined {
    if (!production.origin) return undefined;
    return this.sales.find(sale => sale.name === production.origin);
  }

  onRowClick(productionId: number) {
    this.router.navigate(['/productions-ext', productionId]);
    console.log('Navigating to production:', productionId);
  }

  async createNewProduction(): Promise<void> {
    try {
      this.loading = true;
      console.log('Creating new production order');
      
      // Create a new draft production with required fields
      const newProduction = await firstValueFrom(
        this.odooEM.create<MrpProduction>(new MrpProduction(), {
          state: 'draft',
          origin: 'Nuova produzione',
          picking_type_id: 59, // Sostituire con un ID di operazione valida
          product_id: 87948, // consumabile
          product_qty: 1, // Quantità di default
          product_uom_id: 1, // Sostituire con un ID di unità di misura valida
          location_src_id: 8, // Sostituire con un ID di posizione valida
          location_dest_id: 8 // Sostituire con un ID di posizione valida

        })
      );

      console.log('New production created with ID:', newProduction.id);
      
      // Navigate to the new production
      this.router.navigate(['/productions-ext', newProduction.id]);
    } catch (error) {
      console.error('Error creating new production:', error);
    } finally {
      this.loading = false;
    }
  }

  onSort(event: any) {
    this.sortField = event.field;
    this.sortOrder = event.order;
    console.log(`Table sorted by ${this.sortField} in ${this.sortOrder === 1 ? 'descending' : 'ascending'} order`);
  }

  // Save column preferences to localStorage
  saveColumnPreferences() {
    const columnPrefs = this.selectedColumns.map(col => col.field);
    localStorage.setItem('productionTableColumns', JSON.stringify(columnPrefs));
    console.log('Column preferences saved');
  }

  // Load column preferences from localStorage
  loadColumnPreferences() {
    const savedPrefs = localStorage.getItem('productionTableColumns');
    if (savedPrefs) {
      try {
        const columnFields = JSON.parse(savedPrefs);
        this.selectedColumns = this.cols.filter(col => columnFields.includes(col.field));
        console.log('Column preferences loaded');
      } catch (e) {
        console.error('Error loading column preferences', e);
        this.selectedColumns = [...this.cols]; // Default to all columns
      }
    }
  }

  // Optional: method to clear all filters
  clear(table: Table) {
    table.clear();
    console.log('Table filters cleared');
  }

  // Implementa il filtro personalizzato per gestire proprietà annidate
  customFilter(value: any, filter: any, filterMatchMode: string, field: string) {
    if (filter === undefined || filter === null || filter.trim() === '') {
      return true;
    }
    
    const fieldValue = this.getFieldValue(value, field);
    if (fieldValue === undefined || fieldValue === null) {
      return false;
    }
    
    const filterValue = filter.toString().toLowerCase();
    const stringValue = fieldValue.toString().toLowerCase();
    
    switch (filterMatchMode) {
      case 'contains':
        return stringValue.includes(filterValue);
      case 'startsWith':
        return stringValue.startsWith(filterValue);
      case 'endsWith':
        return stringValue.endsWith(filterValue);
      case 'equals':
        return stringValue === filterValue;
      case 'notEquals':
        return stringValue !== filterValue;
      default:
        return stringValue.includes(filterValue);
    }
  }
}
