import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { Lead } from 'src/app/models/crm.lead.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { firstValueFrom } from 'rxjs';
import { AnalyticAccountLine } from '../models/analytic-account.model';
import { HrEmployee } from '../models/hr-employee.model';

interface Period {
  key: string;
  label: string;
  totalHours: number;
}

interface EmployeeRow {
  name: string;
  totalHours: number;
  periodHours: { [key: string]: number };
}

interface HoursGroup {
  groupName: string;
  lines: AnalyticAccountLine[];
  isOpen: boolean;
  totalHours: number;
  totalCost: number;
  periods: Period[];
  employeeRows: EmployeeRow[];
}

@Component({
  selector: 'app-hours-on-lead',
  templateUrl: './hours-on-lead.component.html',
  styleUrls: ['./hours-on-lead.component.scss'],
  standalone: false
})
export class HoursOnLeadComponent implements OnInit {
  @Input() lead: Lead;
  @Output() loading: EventEmitter<boolean> = new EventEmitter();

  // Constants
  readonly PLAN_ID_PROGETTAZIONE = 4;
  
  // Data
  allHours: AnalyticAccountLine[] = [];
  showComponent: boolean = false;
  statusMessage: string = '';
  
  // Group structures
  mainGroups: HoursGroup[] = [];
  
  // Totals
  allTotal: number = 0;
  allCost: number = 0;
  
  // Time grouping
  timeGroupingMode: 'day' | 'week' | 'month' | 'year' = 'day';
  timeGroupingOptions = [
    { value: 'day', label: 'Per Giorno' },
    { value: 'week', label: 'Per Settimana' },
    { value: 'month', label: 'Per Mese' },
    { value: 'year', label: 'Per Anno' }
  ];

  constructor(private odooEm: OdooEntityManager) {}

  ngOnInit(): void {
    console.log('Hours on Lead component initialized with lead:', this.lead);
  }

  toggleComponent() {
    this.showComponent = !this.showComponent;
    if (this.showComponent && this.allHours.length === 0) {
      this.fetchHoursData();
    }
  }
  
  async fetchHoursData() {
    if (!this.lead || !this.lead.tracking_code) {
      this.statusMessage = "Dati della commessa non disponibili";
      this.loading.emit(false);
      return;
    }

    this.loading.emit(true);
    this.statusMessage = `Raccolgo i dati delle ore per la commessa ${this.lead.tracking_code}, attendere...`;
    
    const timeoutId = setTimeout(() => {
      this.statusMessage = "Elaborazione in corso, attendere ancora qualche secondo...";
    }, 5000);

    try {
      // Search for analytic lines with account code matching the lead tracking code
      const analyticsLines = await firstValueFrom(
        this.odooEm.search<AnalyticAccountLine>(
          new AnalyticAccountLine(),
          [['account_id.code', '=', this.lead.tracking_code]]
        )
      );

      clearTimeout(timeoutId);
      
      // Resolve employee data
      await firstValueFrom(this.odooEm.resolveArrayOfSingle(new HrEmployee(), analyticsLines, 'employee_id'));

      if (analyticsLines.length === 0) {
        this.statusMessage = "Nessun dato di ore trovato per questa commessa";
        this.loading.emit(false);
        return;
      }

      this.statusMessage = "";
      this.allHours = analyticsLines;
      console.log("Hours data fetched:", this.allHours);

      // Process the data
      this.organizeHoursByGroup();
      this.calculateTotals();
      
    } catch (error) {
      clearTimeout(timeoutId);
      this.statusMessage = "Si è verificato un errore durante la raccolta dei dati";
      console.error("Error fetching hours data:", error);
    } finally {
      this.loading.emit(false);
    }
  }

  organizeHoursByGroup() {
    // Create main groups: Progettazione and Produzione
    const progettazioneLines = this.allHours.filter(line => 
      line.plan_id?.id === this.PLAN_ID_PROGETTAZIONE
    );
    
    const produzioneLines = this.allHours.filter(line => 
      line.plan_id?.id !== this.PLAN_ID_PROGETTAZIONE
    );
    
    // Create the main groups
    this.mainGroups = [
      {
        groupName: 'Progettazione',
        lines: progettazioneLines,
        isOpen: true,
        totalHours: 0,
        totalCost: 0,
        periods: [],
        employeeRows: []
      },
      {
        groupName: 'Produzione',
        lines: produzioneLines,
        isOpen: true,
        totalHours: 0,
        totalCost: 0,
        periods: [],
        employeeRows: []
      }
    ];
    
    // Remove any empty groups
    this.mainGroups = this.mainGroups.filter(group => group.lines.length > 0);
    
    // For each main group, organize as a table
    this.mainGroups.forEach(group => {
      this.buildTableData(group);
      
      // Calculate totals for the main group
      group.totalHours = group.employeeRows.reduce((sum, e) => sum + e.totalHours, 0);
    });
    
    console.log("Organized hours by group:", this.mainGroups);
  }

  buildTableData(group: HoursGroup) {
    // Find all unique periods
    const periodMap = new Map<string, Period>();
    const employeeMap = new Map<number, EmployeeRow>();
    
    // First pass: collect all periods and employees
    group.lines.forEach(line => {
      const date = new Date(line.date);
      const periodKey = this.getPeriodKey(date);
      const periodLabel = this.getPeriodLabel(date);
      
      if (!periodMap.has(periodKey)) {
        periodMap.set(periodKey, {
          key: periodKey,
          label: periodLabel,
          totalHours: 0
        });
      }
      
      if (line.employee_id && line.employee_id.id) {
        if (!employeeMap.has(line.employee_id.id)) {
          employeeMap.set(line.employee_id.id, {
            name: line.employee_id.name || 'Dipendente Sconosciuto',
            totalHours: 0,
            periodHours: {}
          });
        }
      }
    });
    
    // Second pass: populate hours data
    group.lines.forEach(line => {
      const date = new Date(line.date);
      const periodKey = this.getPeriodKey(date);
      
      if (line.employee_id && line.employee_id.id) {
        const employee = employeeMap.get(line.employee_id.id);
        employee.periodHours[periodKey] = (employee.periodHours[periodKey] || 0) + line.unit_amount;
        employee.totalHours += line.unit_amount;
        
        const period = periodMap.get(periodKey);
        period.totalHours += line.unit_amount;
      }
    });
    
    // Convert maps to arrays and sort
    group.periods = Array.from(periodMap.values())
      .sort((a, b) => a.key.localeCompare(b.key));
    
    group.employeeRows = Array.from(employeeMap.values())
      .sort((a, b) => a.name.localeCompare(b.name));
  }

  getPeriodKey(date: Date): string {
    switch (this.timeGroupingMode) {
      case 'day':
        return date.toISOString().split('T')[0];
      case 'week':
        const weekNum = this.getWeekNumber(date);
        return `${date.getFullYear()}-W${weekNum.toString().padStart(2, '0')}`;
      case 'month':
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      case 'year':
        return `${date.getFullYear()}`;
      default:
        return date.toISOString().split('T')[0];
    }
  }

  getPeriodLabel(date: Date): string {
    switch (this.timeGroupingMode) {
      case 'day':
        return this.formatDate(date.toISOString());
      case 'week':
        const weekNum = this.getWeekNumber(date);
        return `S${weekNum} ${date.getFullYear()}`;
      case 'month':
        return date.toLocaleDateString('it-IT', { month: 'short', year: 'numeric' });
      case 'year':
        return `${date.getFullYear()}`;
      default:
        return this.formatDate(date.toISOString());
    }
  }
  
  getWeekNumber(date: Date): number {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  }
  
  changeTimeGrouping(mode: 'day' | 'week' | 'month' | 'year') {
    this.timeGroupingMode = mode;
    this.organizeHoursByGroup();
  }

  calculateTotals() {
    this.allTotal = this.allHours.reduce((sum, line) => sum + line.unit_amount, 0);
    this.allCost = this.allHours.reduce((sum, line) => sum + line.amount, 0);
  }

  toggleGroupExpansion(group: HoursGroup) {
    group.isOpen = !group.isOpen;
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT', { 
      day: '2-digit', 
      month: '2-digit', 
      year: '2-digit' 
    });
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('it-IT', { 
      style: 'currency', 
      currency: 'EUR' 
    }).format(value || 0);
  }

  formatHours(hours: number): string {
    return (hours || 0).toFixed(2);
  }
}