import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ProductTemplate } from '../../models/product.template.model';
import { Product } from '../../models/product.model';
import { OdooEntityManager } from '../../shared/services/odoo-entity-manager.service';
import { firstValueFrom } from 'rxjs';
import { ProductTemplateAttributeLine } from '../../models/product.template.attribute.line';
import { ProductTemplateAttributeValue } from 'src/app/models/product.template.attribute.value.model';

@Component({
  selector: 'app-product-create',
  templateUrl: './product-create.component.html',
  standalone: false
})
export class ProductCreateComponent implements OnInit {
  @Input() baseTemplate: ProductTemplate;
  @Output() productCreated = new EventEmitter<Product>();

  productForm: FormGroup;
  loading: boolean = false;
  attributeLines: ProductTemplateAttributeLine[] = [];
  
  constructor(
    private fb: FormBuilder,
    private odooEm: OdooEntityManager
  ) {
    this.productForm = this.fb.group({
      name: ['', Validators.required],
      type: ['product', Validators.required],
      list_price: [0, Validators.required],
      standard_price: [0],
      sale_ok: [true],
      purchase_ok: [true],
      detailed_type: ['product'],
      attributes: this.fb.group({})
    });
  }

  async ngOnInit() {
    if (this.baseTemplate) {
      await this.loadTemplateAttributes();
    }
  }

  private async loadTemplateAttributes() {
    this.loading = true;
    try {
      await firstValueFrom(
        this.odooEm.resolve(this.baseTemplate.valid_product_template_attribute_line_ids)
      );
      
      this.attributeLines = this.baseTemplate.valid_product_template_attribute_line_ids.values || [];
      
      // Load attribute values
      await firstValueFrom(
        this.odooEm.resolveArray(
          new ProductTemplateAttributeValue(),
          this.attributeLines,
          "value_ids"
        )
      );

      // Create form controls for each attribute
      const attributesGroup = this.fb.group({});
      this.attributeLines.forEach(line => {
        attributesGroup.addControl(
          line.attribute_id.name,
          this.fb.control('', Validators.required)
        );
      });
      
      this.productForm.setControl('attributes', attributesGroup);
    } finally {
      this.loading = false;
    }
  }

  async onSubmit() {
    if (this.productForm.invalid) return;
    
    this.loading = true;
    try {
      const formValues = this.productForm.value;
      
      // Create base product
      const productData = {
        name: formValues.name,
        type: formValues.type,
        list_price: formValues.list_price,
        standard_price: formValues.standard_price,
        sale_ok: formValues.sale_ok,
        purchase_ok: formValues.purchase_ok,
        detailed_type: formValues.detailed_type
      };

      if (this.baseTemplate) {
        // Create variant based on template
        const attributeValueIds = await this.getSelectedAttributeValueIds(formValues.attributes);
        const result: any = await this.odooEm.odoorpcService.sendRequest(
          "/api/sale/create_product_variant",
          {
            product_template_attribute_value_ids: JSON.stringify(attributeValueIds),
            product_template_id: this.baseTemplate.id,
          }
        );
        
        await this.odooEm.run(666, result.result, "product.product");
        const [newProduct] = await firstValueFrom(
          this.odooEm.search<Product>(new Product(), [["id", "=", result.result]])
        );
        this.productCreated.emit(newProduct);
      } else {
        // Create new product template
        const newProduct = await firstValueFrom(
          this.odooEm.create<Product>(new Product(), productData)
        );
        this.productCreated.emit(newProduct);
      }
    } finally {
      this.loading = false;
    }
  }

  private async getSelectedAttributeValueIds(attributeValues: any): Promise<number[]> {
    const ids: number[] = [];
    for (const line of this.attributeLines) {
      const selectedValue = line.value_ids.values.find(
        v => v.name === attributeValues[line.attribute_id.name]
      );
      if (selectedValue) {
        ids.push(selectedValue.id);
      }
    }
    return ids;
  }
}