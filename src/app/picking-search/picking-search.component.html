<!-- Keep existing app-navbar component -->
<app-navbar [loading]="loading" backroute="..">
  <div class="d-flex justify-content-between align-items-center w-100">
    <a class="navbar-brand">
      Movimenti di magazzino
    </a>
    <div class="dropdown ms-auto">
      <button class="btn btn-link text-white" data-bs-toggle="dropdown">
        <i class="fa fa-bars"></i>
      </button>
      <ul class="dropdown-menu dropdown-menu-end">
        <li><a class="dropdown-item" [routerLink]="['oddments']">Resto legno</a></li>
      </ul>
    </div>
  </div>
</app-navbar>

<!-- Enhanced search and filter section -->
<div class="row g-3 align-items-center m-1">
  <div class="col-8 col-lg-6">
    <!-- Search Input -->
    <div class="input-group">
      <span class="input-group-text bg-light border-end-0">
        <i class="fas fa-search text-muted"></i>
      </span>
      <input 
        class="form-control border-start-0" 
        placeholder="Cerca movimenti..."
        [ngModel]="searchInput | async"
        (ngModelChange)="searchInput.next($event); onSearchChange($event)" />
    </div>
  </div>

  <div class="col-4 col-lg-2">
    <!-- Grouped View Selector -->
    <button 
      class="btn btn-sm w-100"
      [ngClass]="{ 'btn-primary': isGroupedView, 'btn-outline-secondary': !isGroupedView }"
      (click)="isGroupedView = !isGroupedView">
      <i class="fa-regular fa-list-dropdown me-1"></i>
      <span class="d-none d-sm-inline">Vista raggruppata</span>
      <span class="d-sm-none">Raggruppa</span>
    </button>
  </div>

  <div class="col-6 col-lg-2">
    <!-- Area Toggle -->
    <div class="btn-group w-100" role="group" aria-label="Area filter toggle">
      <button 
        type="button" 
        class="btn btn-sm text-nowrap"
        [ngClass]="{
          'btn-primary': selectedAreaFilter === 'EDI',
          'btn-outline-primary': selectedAreaFilter !== 'EDI'
        }"
        (click)="toggleAreaFilter()">
        Edilizia
      </button>
      <button 
        type="button" 
        class="btn btn-sm text-nowrap"
        [ngClass]="{
          'btn-primary': selectedAreaFilter === 'HD',
          'btn-outline-primary': selectedAreaFilter !== 'HD'
        }"
        (click)="toggleAreaFilter()">
        Home Design
      </button>
    </div>
  </div>

  <div class="col-6 col-lg-2">
    <!-- Option Selector -->
    <select 
      [compareWith]="compareByString" 
      class="form-select form-select-sm" 
      [(ngModel)]="activePickingOption"
      (ngModelChange)="persist()">
      <option *ngFor="let o of this.picking_search_options | keyvalue" [ngValue]="o.value">
        {{ o.key }}
      </option>
    </select>
  </div>

  
</div>


<!-- Loading spinner -->
<div *ngIf="loading" class="d-flex justify-content-center align-items-center" style="height: 200px;">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Caricamento...</span>
  </div>
</div>

<!-- Main content when not loading -->
<div *ngIf="!loading" class="container-fluid p-3" style="height: calc(100vh - 180px); overflow-y: auto;">

  <!-- NON GROUPED VIEW -->
  <div *ngIf="!isGroupedView">
    
    <!-- Desktop table view (large screens) -->
<div class="d-none d-lg-block">
  <div class="card border-0 shadow-sm">
    <div class="table-responsive">
      <table class="table table-hover align-middle mb-0">
        <thead class="table-light">
          <tr>
            <th class="border-0 fw-semibold py-3">Origine</th>
            <th class="border-0 fw-semibold py-3">Data</th>
            <th class="border-0 fw-semibold py-3">Operazione</th>
            <th class="border-0 fw-semibold py-3">Tipo</th>
          </tr>
        </thead>
        <tbody *ngIf="partsLoaded">
          <tr 
            *ngFor="let p of pickings.concat(productions) | sortByActivityDate" 
            [routerLink]="[getPickingPage(p),p.id]"
            queryParamsHandling="preserve"
            class="cursor-pointer border-bottom">
            <td class="border-0 py-3">
              <div class="d-flex align-items-center">
                <!-- <i class="{{getPickingClass(p)}} me-3" style="font-size: 8px;"></i> -->
                <div [innerHTML]="getDisplayTextNoGroup(p)"></div>
              </div>
            </td>
            <td class="border-0 py-3">
              <div class="fw-medium text-dark">{{getDisplayDate(p) | Date4Humans}}</div>
              <small class="text-muted">{{getDisplayUser(p)}}</small>
            </td>
            <td class="border-0 py-3">
              <small class="text-muted">{{p.name}}</small>
            </td>
            <td class="border-0 py-3">
              <span class="badge bg-primary bg-opacity-10 text-primary fw-bold">
                {{p.picking_type_id.name.replace("LOMAGNA:","")}}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

    <!-- Mobile card view (small and medium screens) -->
    <div class="d-lg-none">
      <div class="row g-3" *ngIf="partsLoaded">
        <div 
          class="col-12" 
          *ngFor="let p of pickings.concat(productions) | sortByActivityDate"
          [routerLink]="[getPickingPage(p),p.id]"
          queryParamsHandling="preserve">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-body p-3">
              <div class="d-flex align-items-start">
                <!-- <i class="{{getPickingClass(p)}} me-3 mt-1" style="font-size: 8px; flex-shrink: 0;"></i> -->
                <div class="flex-grow-1 min-w-0">
                  <div class="mb-2" [innerHTML]="getDisplayTextNoGroup(p)"></div>
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <small class="text-muted fw-medium">{{getDisplayDate(p) | Date4Humans}}</small>
                    <span class="badge bg-primary bg-opacity-10 text-primary fw-bold">{{p.picking_type_id.name.replace("LOMAGNA:","")}}</span>
                  </div>
                  <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">{{getDisplayUser(p)}}</small>
                    <small class="text-muted">{{p.name}}</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- GROUPED VIEW -->
  <div *ngIf="isGroupedView">
    <div *ngIf="partsLoaded">
      <div *ngFor="let group of groupedPickings | keyvalue" class="mb-4">
        
        <!-- Group header -->
        <div class="bg-light p-2 mb-1">
          <h6 class="fw-bold text-primary  mb-0">{{ group.key }}</h6>
        </div>
        
      <!-- Desktop grouped table -->
<div class="d-none d-lg-block">
  <div class="card border-0 shadow-sm">
    <div class="table-responsive">
      <table class="table table-hover align-middle mb-0">
        <thead class="table-light">
          <tr>
            <th class="border-0 fw-semibold py-3">Origine</th>
            <th class="border-0 fw-semibold py-3">Data</th>
            <th class="border-0 fw-semibold py-3">Operazione</th>
            <th class="border-0 fw-semibold py-3">Tipo</th>
          </tr>
        </thead>
        <tbody>
          <tr 
            *ngFor="let p of group.value | sortByActivityDate" 
            [routerLink]="[getPickingPage(p), p.id]"
            queryParamsHandling="preserve"
            class="cursor-pointer border-bottom">
            <td class="border-0 py-3">
              <div class="d-flex align-items-center">
                <!-- <i class="{{getPickingClass(p)}} me-3" style="font-size: 8px;"></i> -->
                <div [innerHTML]="getDisplayTextGroup(p)"></div>
              </div>
            </td>
            <td class="border-0 py-3">
              <div class="fw-medium text-dark">{{getDisplayDate(p) | Date4Humans}}</div>
              <small class="text-muted">{{getDisplayUser(p)}}</small>
            </td>
            <td class="border-0 py-3">
              <small class="text-muted">{{p.name}}</small>
            </td>
            <td class="border-0 py-3">
              <span class="badge bg-primary bg-opacity-10 text-primary fw-bold">
                {{p.picking_type_id.name.replace("LOMAGNA:","")}}
              </span>
            </td>
            
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

        <!-- Mobile grouped cards -->
        <div class="d-lg-none">
          <div class="row g-2">
            <div 
              class="col-12" 
              *ngFor="let p of group.value | sortByActivityDate"
              [routerLink]="[getPickingPage(p), p.id]"
              queryParamsHandling="preserve">
              <div class="card border-0 shadow-sm">
                <div class="card-body p-3">
                  <div class="d-flex align-items-start">
                    <!-- <i class="{{getPickingClass(p)}} me-3 mt-1" style="font-size: 8px; flex-shrink: 0;"></i> -->
                    <div class="flex-grow-1 min-w-0">
                      <div class="mb-2" [innerHTML]="getDisplayTextGroup(p)"></div>
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted fw-medium">{{getDisplayDate(p) | Date4Humans}}</small>
                        <span class="badge bg-primary bg-opacity-10 text-primary fw-bold">{{p.picking_type_id.name.replace("LOMAGNA:","")}}</span>
                      </div>
                      <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">{{getDisplayUser(p)}}</small>
                        <small class="text-muted">{{p.name}}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>

/* Ensure proper card interactions on mobile */
@media (max-width: 991.98px) {
  .card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}
</style>