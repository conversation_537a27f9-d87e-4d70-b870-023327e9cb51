{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "esModuleInterop": true, "sourceMap": true, "declaration": false, "module": "es2020", "moduleResolution": "bundler", "experimentalDecorators": true, "importHelpers": true, "target": "es2020", "types": ["<PERSON>i", "gapi.auth2", "gapi.client.drive", "gapi.client.sheets"], "typeRoots": ["node_modules/@types", "src/@types"], "lib": ["es2021", "dom"]}}