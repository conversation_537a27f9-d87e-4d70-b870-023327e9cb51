    {"input_text": "What are the names of all active products in our system?","output_text": "SELECT name FROM product_template WHERE active = true;"}
    {"input_text": "Can you tell me the total sales for a particular product?","output_text": "SELECT SUM(product_uom_qty) FROM sale_order_line WHERE product_id = <product_id>;"}
    {"input_text": "Who are the customers from a particular country?","output_text": "SELECT name FROM res_partner WHERE country_id = <country_id> AND customer_rank > 0;"}
    {"input_text": "What is the current inventory level of a specific product?","output_text": "SELECT quantity FROM stock_quant WHERE product_id = <product_id>;"}
    {"input_text": "What is the cost of a specific product?","output_text": "SELECT standard_price FROM product_template WHERE id = <product_id>;"}
    {"input_text": "What are the variants of a particular product?","output_text": "SELECT name FROM product_product WHERE product_tmpl_id = <product_id>;"}
    {"input_text": "What is the sale price of a specific product?","output_text": "SELECT list_price FROM product_template WHERE id = <product_id>;"}
    {"input_text": "What are the products in a specific category?","output_text": "SELECT name FROM product_template WHERE categ_id = <category_id>;"}
    {"input_text": "What are the products supplied by a specific vendor?","output_text": "SELECT pt.name FROM product_supplierinfo ps INNER JOIN product_template pt ON ps.product_tmpl_id = pt.id WHERE ps.name = <supplier_id>;"}
    {"input_text": "What is the weight of a specific product?","output_text": "SELECT weight FROM product_template WHERE id = <product_id>;"}
    {"input_text": "What is the volume of a specific product?","output_text": "SELECT volume FROM product_template WHERE id = <product_id>;"}
    {"input_text": "Is a specific product a consumable?","output_text": "SELECT type FROM product_template WHERE id = <product_id> AND type = 'consu';"}
    {"input_text": "Is a specific product a service?","output_text": "SELECT type FROM product_template WHERE id = <product_id> AND type = 'service';"}
    {"input_text": "Is a specific product a storable product?","output_text": "SELECT type FROM product_template WHERE id = <product_id> AND type = 'product';"}
    {"input_text": "What are the products that can be purchased?","output_text": "SELECT name FROM product_template WHERE purchase_ok = true;"}
    {"input_text": "What are the products that can be sold?","output_text": "SELECT name FROM product_template WHERE sale_ok = true;"}
    {"input_text": "What are the products with warranty?","output_text": "SELECT name FROM product_template WHERE warranty > 0;"}
    {"input_text": "Which products have been archived?","output_text": "SELECT name FROM product_template WHERE active = false;"}
    {"input_text": "What products have a certain responsible person?","output_text": "SELECT name FROM product_template WHERE responsible_id = <responsible_id>;"}
    {"input_text": "What are the products with a specific internal reference?","output_text": "SELECT name FROM product_template WHERE default_code = '<default_code>';"}
    {"input_text": "What products have a specific barcode?","output_text": "SELECT name FROM product_product WHERE barcode = '<barcode>';"}
    {"input_text": "What are the products with a specific Public Category?","output_text": "SELECT pt.name FROM product_public_category_product_template_rel pcptr INNER JOIN product_template pt ON pcptr.product_template_id = pt.id WHERE pcptr.product_public_category_id = <public_category_id>;"}
    {"input_text": "What are the products with a specific EAN13 barcode?","output_text": "SELECT name FROM product_product WHERE ean13 = '<ean13>';"}
    {"input_text": "What is the supplier information for a specific product?","output_text": "SELECT name, delay, min_qty, price FROM product_supplierinfo WHERE product_tmpl_id = <product_id>;"}
    {"input_text": "What are the products with a certain Unit of Measure?","output_text": "SELECT name FROM product_template WHERE uom_id = <uom_id>;"}
    {"input_text": "What are the products with a certain Purchase Unit of Measure?","output_text": "SELECT name FROM product_template WHERE uom_po_id = <uom_po_id>;"}
    {"input_text": "What are the products with a specific route (like Buy, Manufacture, or Make to Order)?","output_text": "SELECT pt.name FROM stock_location_route_product_template_rel slrptr INNER JOIN product_template pt ON slrptr.product_template_id = pt.id WHERE slrptr.stock_location_route_id = <route_id>;"}
    {"input_text": "What products have a specific company as their company?","output_text": "SELECT name FROM product_template WHERE company_id = <company_id>;"}
    {"input_text": "What products have a specific taxes assigned?","output_text": "SELECT pt.name FROM account_tax_product_template_rel atr INNER JOIN product_template pt ON atr.product_template_id = pt.id WHERE atr.account_tax_id = <tax_id>;"}
    {"input_text": "What products have a specific vendor taxes assigned?","output_text": "SELECT pt.name FROM account_tax_product_template_rel atr INNER JOIN product_template pt ON atr.product_template_id = pt.id WHERE atr.account_tax_id = <vendor_tax_id>;"}
    {"input_text": "What products have a specific attribute?","output_text": "SELECT pt.name FROM product_template_attribute_line ptal INNER JOIN product_template pt ON ptal.product_tmpl_id = pt.id WHERE ptal.attribute_id = <attribute_id>;"}
    {"input_text": "What products have a specific attribute value?","output_text": "SELECT pt.name FROM product_template_attribute_value ptav INNER JOIN product_template pt ON ptav.product_tmpl_id = pt.id WHERE ptav.product_attribute_value_id = <attribute_value_id>;"}
    {"input_text": "What are the products with specific seller?","output_text": "SELECT pt.name FROM product_supplierinfo ps INNER JOIN product_template pt ON ps.product_tmpl_id = pt.id WHERE ps.name = <seller_id>;"}
    {"input_text": "What are the products with a specific route (like Buy, Manufacture, or Make to Order)?","output_text": "SELECT pt.name FROM stock_location_route_product_template_rel slrptr INNER JOIN product_template pt ON slrptr.product_template_id = pt.id WHERE slrptr.stock_location_route_id = <route_id>;"}
    {"input_text": "What are the products with a specific sales team?","output_text": "SELECT pt.name FROM product_template_sale_team_rel ptsr INNER JOIN product_template pt ON ptsr.product_template_id = pt.id WHERE ptsr.crm_team_id = <sales_team_id>;"}
    {"input_text": "What products are part of a specific pricelist?","output_text": "SELECT pt.name FROM product_pricelist_item ppi INNER JOIN product_template pt ON ppi.product_tmpl_id = pt.id WHERE ppi.pricelist_id = <pricelist_id>;"}
    {"input_text": "What are the products with a certain route (like Buy, Manufacture, or Make to Order)?","output_text": "SELECT pt.name FROM stock_location_route_product_template_rel slrptr INNER JOIN product_template pt ON slrptr.product_template_id = pt.id WHERE slrptr.stock_location_route_id = <route_id>;"}
    {"input_text": "What are the products with a specific sales team?","output_text": "SELECT pt.name FROM product_template_sale_team_rel ptsr INNER JOIN product_template pt ON ptsr.product_template_id = pt.id WHERE ptsr.crm_team_id = <sales_team_id>;"}
    {"input_text": "What products have a specific fiscal position?","output_text": "SELECT pt.name FROM product_template pt INNER JOIN account_fiscal_position_product_template_rel afp ON pt.id = afp.product_template_id WHERE afp.account_fiscal_position_id = <fiscal_position_id>;"}
    {"input_text": "What products have a specific product category?","output_text": "SELECT name FROM product_template WHERE categ_id = <category_id>;"}
    {"input_text": "What products have a specific internal category?","output_text": "SELECT name FROM product_template WHERE categ_id = <internal_category_id>;"}
    {"input_text": "What products have a specific internal reference (default_code)?","output_text": "SELECT name FROM product_template WHERE default_code = '<default_code>';"}
    {"input_text": "What products have a specific barcode?","output_text": "SELECT name FROM product_product WHERE barcode = '<barcode>';"}
    {"input_text": "What products have a specific EAN13 barcode?","output_text": "SELECT name FROM product_product WHERE ean13 = '<ean13>';"}
    {"input_text": "What products have a specific public category?","output_text": "SELECT pt.name FROM product_public_category_product_template_rel pcptr INNER JOIN product_template pt ON pcptr.product_template_id = pt.id WHERE pcptr.product_public_category_id = <public_category_id>;"}
    {"input_text": "What products have a specific weight?","output_text": "SELECT name FROM product_template WHERE weight = <weight>;"}
    {"input_text": "What products have a specific volume?","output_text": "SELECT name FROM product_template WHERE volume = <volume>;"}
    {"input_text": "What products are labeled as 'Can be Expensed'?","output_text": "SELECT name FROM product_template WHERE can_be_expensed = true;"}
    {"input_text": "What products have a specific sales unit of measure?","output_text": "SELECT name FROM product_template WHERE uom_id = <uom_id>;"}
    {"input_text": "What products are part of a specific product pack?","output_text": "SELECT pt.name FROM product_pack_line ppl INNER JOIN product_template pt ON ppl.product_id = pt.id WHERE ppl.parent_product_id = <parent_product_id>;"}
    {"input_text": "What products have a specific route (like Buy, Manufacture, Replenish on Order)?","output_text": "SELECT pt.name FROM stock_location_route_product_template_rel slrptr INNER JOIN product_template pt ON slrptr.product_template_id = pt.id WHERE slrptr.stock_location_route_id = <route_id>;"}
    {"input_text": "What products have a specific seller?","output_text": "SELECT pt.name FROM product_supplierinfo ps INNER JOIN product_template pt ON ps.product_tmpl_id = pt.id WHERE ps.name = <seller_id>;"}
    {"input_text": "What products have a specific attribute?","output_text": "SELECT pt.name FROM product_template_attribute_line ptal INNER JOIN product_template pt ON ptal.product_tmpl_id = pt.id WHERE ptal.attribute_id = <attribute_id>;"}
    {"input_text": "What products have a specific attribute value?","output_text": "SELECT pt.name FROM product_template_attribute_value ptav INNER JOIN product_template pt ON ptav.product_tmpl_id = pt.id WHERE ptav.product_attribute_value_id = <attribute_value_id>;"}
    {"input_text": "What products are labeled as 'Can be Purchased'?","output_text": "SELECT name FROM product_template WHERE purchase_ok = true;"}
    {"input_text": "What products are labeled as 'Can be Sold'?","output_text": "SELECT name FROM product_template WHERE sale_ok = true;"}
    {"input_text": "What products are labeled as 'Can be Expensed'?","output_text": "SELECT name FROM product_template WHERE can_be_expensed = true;"}
    {"input_text": "What products are labeled as 'Can be Rented'?","output_text": "SELECT name FROM product_template WHERE rental = true;"}
    {"input_text": "What products have a specific cost?","output_text": "SELECT name FROM product_template WHERE standard_price = <standard_price>;"}
    {"input_text": "What products have a specific sale price?","output_text": "SELECT name FROM product_template WHERE list_price = <list_price>;"}
    {"input_text": "What products have a specific tax?","output_text": "SELECT pt.name FROM account_tax_product_template_rel atr INNER JOIN product_template pt ON atr.product_template_id = pt.id WHERE atr.account_tax_id = <tax_id>;"}
    {"input_text": "What products have a specific internal category?","output_text": "SELECT name FROM product_template WHERE categ_id = <internal_category_id>;"}
    {"input_text": "What products have a specific external category?","output_text": "SELECT pt.name FROM product_public_category_product_template_rel pcptr INNER JOIN product_template pt ON pcptr.product_template_id = pt.id WHERE pcptr.product_public_category_id = <external_category_id>;"}    
    {"input_text": "What products have a specific responsible?","output_text": "SELECT name FROM product_template WHERE responsible_id = <responsible_id>;"}
    {"input_text": "What products have a specific sales margin?","output_text": "SELECT name FROM product_template WHERE margin = <margin>;"}
    {"input_text": "What products have a specific image?","output_text": "SELECT name FROM product_template WHERE image_1920 = '<image_1920>';"}
    {"input_text": "What products have a specific product manager?","output_text": "SELECT name FROM product_template WHERE product_manager = <product_manager>;"}
    {"input_text": "What products have a specific supplier?","output_text": "SELECT pt.name FROM product_supplierinfo ps INNER JOIN product_template pt ON ps.product_tmpl_id = pt.id WHERE ps.name = <supplier_id>;"}
    {"input_text": "What products have a specific product category?","output_text": "SELECT name FROM product_template WHERE categ_id = <category_id>;"}
    {"input_text": "What products have a specific internal reference (default_code)?","output_text": "SELECT name FROM product_template WHERE default_code = '<default_code>';"}
    {"input_text": "What products have a specific barcode?","output_text": "SELECT name FROM product_product WHERE barcode = '<barcode>';"}
    {"input_text": "What products have a specific EAN13 barcode?","output_text": "SELECT name FROM product_product WHERE ean13 = '<ean13>';"}
    {"input_text": "What products have a specific public category?","output_text": "SELECT pt.name FROM product_public_category_product_template_rel pcptr INNER JOIN product_template pt ON pcptr.product_template_id = pt.id WHERE pcptr.product_public_category_id = <public_category_id>;"}
    {"input_text": "What products have a specific weight?","output_text": "SELECT name FROM product_template WHERE weight = <weight>;"}
    {"input_text": "What products have a specific volume?","output_text": "SELECT name FROM product_template WHERE volume = <volume>;"}
    {"input_text": "What products are labeled as 'Can be Expensed'?","output_text": "SELECT name FROM product_template WHERE can_be_expensed = true;"}
    {"input_text": "What products have a specific sales unit of measure?","output_text": "SELECT name FROM product_template WHERE uom_id = <uom_id>;"}
    {"input_text": "What products have a specific purchase unit of measure?","output_text": "SELECT name FROM product_template WHERE uom_po_id = <uom_po_id>;"}
    {"input_text": "What are the variants of a specific product?","output_text": "SELECT name_template FROM product_product WHERE product_tmpl_id = <product_template_id>;"}
    {"input_text": "What are the attribute values of a specific product variant?","output_text": "SELECT pav.name FROM product_attribute_value_product_product_rel pavppr INNER JOIN product_attribute_value pav ON pavppr.product_attribute_value_id = pav.id WHERE pavppr.product_product_id = <product_variant_id>;"}
    {"input_text": "What are the products associated with a specific attribute?","output_text": "SELECT pt.name FROM product_template_attribute_line ptal INNER JOIN product_template pt ON ptal.product_tmpl_id = pt.id WHERE ptal.attribute_id = <attribute_id>;"}
    {"input_text": "What are the product variants associated with a specific attribute value?","output_text": "SELECT pp.name_template FROM product_attribute_value_product_product_rel pavppr INNER JOIN product_product pp ON pavppr.product_product_id = pp.id WHERE pavppr.product_attribute_value_id = <attribute_value_id>;"}
    {"input_text": "What are the attributes associated with a specific product template?","output_text": "SELECT pa.name FROM product_template_attribute_line ptal INNER JOIN product_attribute pa ON ptal.attribute_id = pa.id WHERE ptal.product_tmpl_id = <product_template_id>;"}
    {"input_text": "What are the attributes associated with a specific product variant?","output_text": "SELECT pa.name FROM product_attribute_value_product_product_rel pavppr INNER JOIN product_attribute_value pav ON pavppr.product_attribute_value_id = pav.id INNER JOIN product_attribute pa ON pav.attribute_id = pa.id WHERE pavppr.product_product_id = <product_variant_id>;"}
    {"input_text": "What are the attribute values of a specific attribute?","output_text": "SELECT name FROM product_attribute_value WHERE attribute_id = <attribute_id>;"}
    {"input_text": "What is the product variant with a specific internal reference (default_code)?","output_text": "SELECT name_template FROM product_product WHERE default_code = '<default_code>';"}
    {"input_text": "What is the product variant with a specific barcode?","output_text": "SELECT name_template FROM product_product WHERE barcode = '<barcode>';"}
    {"input_text": "What are the product variants of a specific product category?","output_text": "SELECT pp.name_template FROM product_product pp INNER JOIN product_template pt ON pp.product_tmpl_id = pt.id WHERE pt.categ_id = <category_id>;"}
    {"input_text": "What are the product variants with a specific weight?","output_text": "SELECT name_template FROM product_product WHERE weight = <weight>;"}
    {"input_text": "What are the product variants with a specific volume?","output_text": "SELECT name_template FROM product_product WHERE volume = <volume>;"}
    {"input_text": "What are the product variants with a specific purchase unit of measure?","output_text": "SELECT name_template FROM product_product WHERE uom_id = <uom_id>;"}
    {"input_text": "What are the product variants with a specific sales unit of measure?","output_text": "SELECT name_template FROM product_product WHERE uom_po_id = <uom_po_id>;"}