

<!-- <div class="d-flex align-items-center mb-3 p-3">
  <h4 class="mb-0">Output</h4>

 <p-button class="ms-auto" (onClick)="op.toggle($event)" icon="fa fa-plus" label="Nuovo output" />
    <p-overlayPanel #op appendTo="body">
      <ul>
        <li *ngFor="let template of candidateTemplates">
          <a class="dropdown-item" (click)="createFirstDraft(template)">
            {{template.name}}
          </a>
        </li>
      </ul>
    </p-overlayPanel>
</div> -->

<div class="d-flex flex-column h-100" style="height: 100vh;">

  <!-- Contenitore scrollabile che occupa tutto lo spazio sotto la navbar -->
  <div class="flex-grow-1" style="padding: 1rem;">
    <div *ngIf="groupedByTemplateOutput.length === 0" class="p-3 text-center">
      <p>Nessun prodotto di output trovato</p>
      <p-button class="ms-auto" (onClick)="op.toggle($event)" icon="fa fa-plus" label="Nuovo output" />
      <p-overlayPanel #op appendTo="body">
        <ul class="list-group list-group-flush">
          <li class="list-group-item" *ngFor="let template of candidateTemplates">
            <a  (click)="createFirstDraft(template)">
              {{template.name}}
            </a>
          </li>
        </ul>
      </p-overlayPanel>
    </div>
    <div
      *ngFor="let group of groupedByTemplateOutput"
      class="card mb-3"
    >
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{{group.templateName}}</h5>
        <div>
          
          <p-button class="me-3" (onClick)="importFromComponents2(group)" icon="fa fa-down-to-line" label="Componenti" />
          <p-button class="me-3" *ngIf="imballaggio" (onClick)="importFromSaleAttributes(group)" icon="fa fa-down-to-line" label="Attributi" />
          <p-button (click)="createFirstDraft(group.template)" icon="fa fa-plus" class="me-3"></p-button>
          <p-button [disabled]="selectedNodes.length === 0" (click)="deleteMoveLines(group, selectedNodes)" icon="fa fa-trash " class="me-3"></p-button>
          <p-button [disabled]="selectedNodes.length === 0" (click)="duplicateMoveLines(group, selectedNodes)" icon="fa fa-copy" class=""></p-button>
          <p-button  
            severity="success"
            [disabled]="!hasDraft(group)"
            (click)="persistDraft(group)" icon="fa fa-cloud-upload" class="ms-3"></p-button>
        </div>
      </div>
     
      <div class="card-body p-0">
        <p-table 

          sortField="_pz_done"
          [sortMode]="'multiple'"
          [metaKeySelection]="metaKey"
          [value]="getMoveLines(group)"
          dataKey="id"
          selectionMode="multiple"
          [(selection)]="selectedNodes"
          [columns]="group.cols"
          [scrollable]="true" 
          [resizableColumns]="true"
          [reorderableColumns]="true"
          [columnResizeMode]="'expand'"
          [showGridlines]="true"
          [tableStyle]="{'min-width':'50rem'}"
          styleClass="p-datatable-sm"
        >
          
          <ng-template pTemplate="header" #header>
            <tr>
              <th><p-tableHeaderCheckbox ></p-tableHeaderCheckbox></th>
                <th pReorderableColumn
                  pResizableColumn
                  pSortableColumn="result_package_id.name"
                  >
                  Collo
                  <p-sortIcon field="result_package_id.name" />
                </th>
              <th >Codice</th>
              <th 
                  pSortableColumn="_pz_done"
                  >
                  Qty
                  <p-sortIcon field="_pz_done" />
              </th>
           
                  <!-- uom qty (line.qty_done) -->
              <th pReorderableColumn
                  pResizableColumn
                  pSortableColumn="qty_done"
                  >Qty UoM
                  <p-sortIcon field="qty_done" />

                </th>     

              <th pReorderableColumn
                  pResizableColumn
                  >€/UoM</th> 

              <!-- line cost -->
               <th pReorderableColumn
                  pResizableColumn
                  >€ Tot</th>
              
              <!-- Dynamic attribute columns -->
              
              <ng-container *ngIf="group.template && group.template.valid_product_template_attribute_line_ids?.values">
                <th pReorderableColumn
                    pResizableColumn
                    pSortableColumn="{{attr.header}}"
                    class="text-no-wrap"
                    [ngClass]="{'bg-light': attr.header == 'Lunghezza' || attr.header == 'Larghezza' || attr.header == 'Altezza'}"
                    *ngFor="let attr of getAttributeColumns(group.template)"
                >
                  {{attr.header}}
                  <p-sortIcon field="{{attr.header}}"></p-sortIcon>
                </th>
              </ng-container>
            </tr>
          </ng-template>
        
          <ng-template #body pTemplate="body" let-moveline let-columns="columns" let-rowIndex="rowIndex">
            <!-- <ng-container *ngFor="let line of move.move_line_ids?.values"> -->
            <!-- orange background if in selectedNodes -->
              <tr 
                [pSelectableRowIndex]="rowIndex"  
                [class.bg-light]="selectedNodes.includes(moveline)"
              >
                <td>
                  <p-tableCheckbox [value]="moveline" styleClass="orange-checkbox"></p-tableCheckbox>
                  <span *ngIf="isDraft(moveline)" class="ms-2 fa fa-exclamation-circle text-danger"></span>
                  <!-- <input class="form-check-input" type="checkbox" [checked]="selectedNodes.includes(moveline)" style="pointer-events: none;"> -->
                </td>
                <td [pEditableColumn]>
                  <!-- <span *ngIf="moveline.result_package_id?.name" class="badge bg-dark fs-6">{{moveline.result_package_id?.name}}</span> -->
                    <p-cellEditor>
                      <ng-template pTemplate="input" #input>
                        <input
                          class="form-control" 
                          style="width: auto;"
                          [ngModelOptions]="{'updateOn':'blur'}"
                          [ngModel]="getResultPackage(moveline)"
                          (ngModelChange)="setResultPackage(this.selectedNodes.length > 0 ? this.selectedNodes : [moveline], $event)"
                        >
                      </ng-template>
                      <ng-template pTemplate="output" #output>
                        {{getResultPackage(moveline)}}
                        <span *ngIf="moveline.result_package_id?.id < 0" class="text-danger fa fa-exclamation-circle"></span>
                      </ng-template>
                    </p-cellEditor>
                </td> 

                <td>
                  {{moveline.product_id.id > 0 ? moveline.product_id.value.default_code : '' }}
                </td>
                <td [pEditableColumn]>
                    <p-cellEditor>
                      <ng-template pTemplate="input" #input>
                        <input
                          type="number"
                          class="form-control" 
                          style="width: auto"
                          [(ngModel)]="moveline._pz_done"
                          [ngModelOptions]="{'updateOn':'blur'}"
                          (ngModelChange)="moveline['dirty_qty_done'] = true"
                        >
                      </ng-template>
                      <ng-template pTemplate="output" #output>
                        {{moveline._pz_done | number:'1.0-2'}}
                        <span *ngIf="moveline['dirty_qty_done']" class="ms-2 fa fa-exclamation-circle text-danger"></span>
                      </ng-template>
                    </p-cellEditor>
                </td>
                
                <td>
                  {{moveline.qty_done | number:'1.0-2'}} {{moveline.product_id.value.uom_id.name}}
                </td>

                <td class="text-end">
                  @if(getMove(moveline)) {
                    {{(getMove(moveline).cost_share * totalCost / getMove(moveline).quantity_done ) | currency:'EUR':'symbol':'1.2-2'}}
                  }
                </td>

                <!-- for now add simple cost cell with cost_share of the move * total cost * qty done -->
                <td  class="text-end">
                  @if(getMove(moveline)) {
                  {{( getMove(moveline).cost_share * totalCost / getMove(moveline).quantity_done * moveline.qty_done) | currency:'EUR':'symbol':'1.2-2'}}
                  }
                  <!-- dropdown with details of cost calculation -->
                   <div 
                     class="dropdown d-inline-block"
                     (click)="op.toggle($event)"
                     >
                    <button class="btn btn-link p-0" type="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                      <i class="fa fa-caret-down"></i>
                    </button>
                    <p-overlayPanel #op appendTo="body">
                      <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                          Costo totale Produzione: {{totalCost | currency:'EUR':'symbol':'1.2-2'}}
                        </li>
                        <li class="list-group-item">
                          @if(getMove(moveline)) {
                            Costo assegnato: {{getMove(moveline).cost_share * 100 / getMove(moveline).quantity_done * moveline.qty_done}}%
                          }
                        </li>
                        <li class="list-group-item">
                          @if(getMove(moveline)) {
                            Costo unitario: {{getMove(moveline).cost_share * totalCost / getMove(moveline).quantity_done  | currency:'EUR':'symbol':'1.2-2'}}
                          }
                        </li>
                        <li class="list-group-item">
                          Quantità: {{moveline.qty_done}}
                        </li>
                        <li class="list-group-item">
                          @if(getMove(moveline)) {
                            Costo totale: {{( getMove(moveline).cost_share * totalCost * moveline.qty_done) | currency:'EUR':'symbol':'1.2-2'}}
                          }
                        </li>
                      </ul>
                    </p-overlayPanel>
                  </div>
                </td>
               
                <!-- Dynamic attribute values -->
                <ng-container *ngFor="let col of columns">

                  <td pEditableColumn 
                    class="overflow-visible"
                    [ngClass]="{'bg-light': col.field == 'Lunghezza' || col.field == 'Larghezza' || col.field == 'Altezza'}"
                  >
                    
                    <p-cellEditor>
                      <ng-template pTemplate="input" #input>
                        @if(col.display_type == 'select') {
                          <select
                            class="form-select"
                            [ngModel]="getAttributeValue2(moveline, col.field).name"
                            (ngModelChange)="setAttributeValues(this.selectedNodes.length > 0 ? this.selectedNodes : [moveline], col.field, $event)">
                            <option
                              *ngFor="let option of getOptions2(group, col.field)"
                              [ngValue]="option?.name">
                              {{ option?.name }}
                            </option>
                          </select>

                        } @else {
                          <input
                            [ngModelOptions]="{'updateOn':'blur'}"
                            (ngModelChange)="setAttributeValues(this.selectedNodes.length > 0 ? this.selectedNodes : [moveline], col.field, $event)"
                            pInputText
                            class="form-control"
                            style="width: auto"
                            [ngModel]="formatNumber(getAttributeValue2(moveline, col.field).name)"
                          >
                        }
                      </ng-template>
                      <ng-template pTemplate="output" #output>
                        <!-- with getAttributeValue2 -->
                        <span *ngIf="getAttributeValue2(moveline, col.field)?.id < 0" class="text-danger fa fa-exclamation-circle" ></span>
                        {{formatNumber(getAttributeValue2(moveline, col.field).name)}}
                      </ng-template>
                    </p-cellEditor>
                  </td>
                </ng-container>
              </tr>
          </ng-template>
          <!-- footer with totals for each group -->
        <ng-template pTemplate="footer">
          <tr> 
            <td  colspan = "2"class="fw-bold bg-light">Totale</td>
            <td  class="fw-bold text-end bg-light"></td>
            <td class="fw-bold text-end bg-light"> {{group.totalQty | number:'1.0-2'}} {{group.uom}}</td>
            <td class="fw-bold text-end bg-light"> {{group.averageCost | currency:'EUR':'symbol':'1.2-2'}} </td>
            <td class="fw-bold text-end bg-light"> {{group.totalValue | currency:'EUR':'symbol':'1.2-2'}} </td>
            <td class="fw-bold text-end bg-light"   *ngFor="let attr of getAttributeColumns(group.template)"></td>
          </tr>
        </ng-template>
        </p-table>
      </div>
    </div>

    <!-- Statistiche dei componenti -->
<div class="d-flex justify-content-between align-items-center p-3 bg-light border-top">
  <div class="text-muted small">
    <span *ngIf= "groupedByTemplateOutput.length > 0">
      Totale {{groupedByTemplateOutput.length}} prodotti
    </span>
  </div>
  <div class="text-muted small">
    <span *ngIf=" totalCost > 0">
      Costo totale  {{totalCost | currency:'EUR':'symbol':'1.2-2'}}
    </span>
    <!-- update cost button -->
      <button class="btn btn-sm btn-light ms-auto" title="Aggiorna" (click)="updateCosts()">
      <i class="fa fa-sync-alt"></i>
      Aggiorna Costi
  </button>
  </div>
  <div class="d-flex align-items-center gap-2">

  </div>
</div>
  </div>
</div>
