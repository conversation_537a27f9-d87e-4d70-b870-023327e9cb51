import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {AuthenticationService} from '../shared/services/authentication.service';

@Component({
    selector: 'app-callback',
    templateUrl: './callback.component.html',
    styleUrls: ['./callback.component.scss'],
    standalone: false
})
export class CallbackComponent implements OnInit {

  constructor(private router: Router, private authenticationService: AuthenticationService) { }

  ngOnInit() {

    this.authenticationService.requestToken(window.location.href).subscribe(() => {
      console.log( localStorage )
      this.router.navigate(['/home']);
    });
  }

}
