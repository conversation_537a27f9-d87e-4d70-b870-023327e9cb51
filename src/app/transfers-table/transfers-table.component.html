<!-- <button 
  class="btn btn-link w-100" 
  type="button" 
  data-bs-toggle="collapse" 
  [attr.data-bs-target]="'#transfers-' + orderLine.id"
  [attr.aria-controls]="'transfers-' + orderLine.id"
  (click)="onButtonClick($event)">
  Vedi trasferimenti
</button> -->

<div  [id]="'transfers-' + orderLine.id">
  <div class="table-responsive">
    <table class="table table-sm table-hover mb-0">
      <thead>
        <tr>
          <th colspan="4">Trasferimenti di {{orderLine.product_id.value.display_name.replaceAll(", -","") }}</th>
        </tr>
      </thead>
      <thead>
        <tr>
          <th class="text-start bg-light">Tipo</th>
          <th class="text-center bg-light">Da -> a</th>
          <th class=" bg-light">{{orderLine.product_uom.name}}</th>
          <th class="bg-light">Stato</th>          
          <th class=" bg-light">Data</th>
          <th class=" bg-light">Rif</th>

        </tr>
      </thead>
      <tbody>

        @if (preProductions.length > 0) {
        <tr *ngFor="let preProduction of preProductions" >
          <td class="text-end">{{preProduction.picking_type_id.name.replace('LOMAGNA: ', '')}}</td>
          <td ></td>
          <td class="text-end">{{preProduction.product_uom_qty | number:'1.0-2':'it-IT'}}</td>
          <td class="text-center" [ngClass]="getStateClass(preProduction.state)">{{getStateDisplay(preProduction.state)}}</td>
          <td class="text-center">{{preProduction.date_planned_start | date:'dd/MM/yyyy'}}</td>
          <td class="text-start text-primary" (click)="onClickPreProduction(preProduction)">{{preProduction.name}}
              <i class="fa fa-external-link"></i>
          </td>
        </tr>
        }
        @if (productions.length > 0) {
        <tr *ngFor="let production of productions" >
          <td class="text-end">Imballaggio</td>
          <td ></td>
          <td class="text-end">{{production.product_uom_qty | number:'1.0-2':'it-IT'}}</td>
          <td class="text-center" [ngClass]="getStateClass(production.state)">{{getStateDisplay(production.state)}}</td>
          <td class="text-center">{{production.date_planned_start | date:'dd/MM/yyyy'}}</td>
          <td class="text-start text-primary" (click)="onClickProduction(production)">{{production.name}}
              <i class="fa fa-external-link"></i>
          </td>
        </tr>
        }
        @if (purchases.length > 0) {
        <tr *ngFor="let purchase of purchases"  >
          <td class="text-end align-middle">Acquisto</td>
          <td class="text-center align-middle"> Vendor -> Giacenza  </td>
          <td class="text-end align-middle">{{purchase.product_uom_qty | number:'1.0-2':'it-IT' }} </td>
          <td class="text-center align-middle" [ngClass]="getStateClass(purchase.order_id.value?.receipt_status)">{{getStateDisplay(purchase.order_id.value?.receipt_status)}}</td>
          <td class="text-center align-middle">{{getArrivedDate(purchase) | date:'dd/MM/yyyy'}}</td>
          <td class="text-start text-wrap text-primary align-middle" (click)="onClickPurchase(purchase)">{{purchase.order_id.name}} 
            <br>
             {{purchase.partner_id?.name}}
            
              <i class="fa fa-external-link ms-1 "></i>
          </td>
        </tr>
        }

        <tr *ngFor="let move of getMoves()" 
        (click)="onMoveClick(move)"
        style="cursor: pointer;">
        <td class="text-end">{{move.picking_type_id.name.replace('LOMAGNA: ', '')}}</td>
        <td class="text-center">{{formatLocation(move.location_id?.name)}} -> {{formatLocation(move.location_dest_id?.name)}}</td>
        <td class="text-end">{{move.quantity_done | number:'1.0-2':'it-IT'}} </td>
        <td class="text-center" [ngClass]="getStateClass(move.state)">{{getStateDisplay(move.state)}}</td>
        <td class="text-center"
        >
          @if(move.state === 'done') {
            {{move.date | date:'dd/MM/yyyy'}}
          }
          @else {
            -
          }
        </td>
        <td class="text-start">{{move.picking_id.name}}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>