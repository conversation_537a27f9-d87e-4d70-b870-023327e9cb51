import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef, OnDestroy } from '@angular/core';

const Dynamsoft = (window as any).Dynamsoft;
// Dynamsoft.DBR.BarcodeScanner.organizationID = "367928";

@Component({
    selector: 'app-barcode-reader-dynamsoft',
    templateUrl: './barcode-reader-dynamsoft.component.html',
    styleUrls: ['./barcode-reader-dynamsoft.component.scss'],
    standalone: false
})
export class BarcodeReaderDynamsoftComponent implements OnInit, OnDestroy {

  id: number = 1;
  loading: boolean = false;
  detected: string = null;
  
  show: boolean = true;
  scanner: any;
  barcodes : Array<string> = []
  index: number = 1 // keep the index of the current barcode in barcodesNumber 

  @Input() title: string;
  @Input() barcodesNumber: number = 1
  @Output() onBarCodeSuccess: EventEmitter<Array<string>> = new EventEmitter<Array<string>>()
  @Output() appendMessage = new EventEmitter();

  public isScanning: boolean = true;
  public result:string;
  isKeyboard: boolean = false;
  keyboardInput: string;
  canvas;
  ctx: any;
  bDestroyed: any;
  rsl: any;

  constructor(
    private elRef: ElementRef
  ) { 
    
  }


  ngOnDestroy(): void {
    this.scanner.destroy()
  }


  ngOnInit() {
    this.canvas = this.elRef.nativeElement.querySelector("canvas") as HTMLElement
    this.ctx = this.canvas.getContext('2d');
    this.scan()
  }

  public cancel() {
    this.onBarCodeSuccess.emit(null)
  }

   emitEvent(text: string) {

    if (!text) {
      this.cancel()
      return
    }

    if (this.index <= this.barcodesNumber) {
      this.barcodes.push(text)
      this.index++
      this.keyboardInput = ""
      this.result = ""
    }
    if (this.index <= this.barcodesNumber)
      return

    
    this.onBarCodeSuccess.emit([].concat(this.barcodes))
    this.barcodes = []
  }

  scan() {
    return
    let videoContainer = document.querySelector('.dbrScanner-cvs-scanarea') as HTMLElement
  
    Dynamsoft.DBR.BarcodeScanner.createInstance({
      UIElement : videoContainer,
      onFrameRead: (results: any[]) => {
        
        console.log("read")
        this.ctx.canvas.width  =  this.rsl.width;
        this.ctx.canvas.height =  this.rsl.height;
        console.log("RSL ", this.rsl);
        this.ctx.canvas.width  =  this.rsl[0]
        this.ctx.canvas.height =  this.rsl[1]

        

        this.ctx.fillStyle = 'rgba(254,254,254,1)'
        this.ctx.beginPath()
        this.ctx.moveTo(this.rsl[0] / 2 , 0)
        this.ctx.lineTo(this.rsl[0] / 2 , this.rsl[1])
        this.ctx.stroke();


        this.ctx.beginPath()
        this.ctx.moveTo(0,this.rsl[1] / 2 )
        this.ctx.lineTo(this.rsl[0], this.rsl[1]/ 2)
        this.ctx.stroke();

        if (!results || results.length == 0)
          return
        
        if (results.length == 0)
          return

        for (let i = 0; i < results.length; i++) {

          let result = results[i]

          console.log("__", result.localizationResult.x1 < (this.rsl[0] / 2), result.localizationResult.x2 < (this.rsl[0] / 2),
          result.localizationResult.x3 > (this.rsl[0] / 2),result.localizationResult.x4 > (this.rsl[0] / 2) )

          if (result.localizationResult.x1 < (this.rsl[0] / 2) &&
              result.localizationResult.x2 < (this.rsl[0] / 2) &&
              result.localizationResult.x3 > (this.rsl[0] / 2) &&
              result.localizationResult.x4 > (this.rsl[0] / 2)
          ) {

            let x1 = result.localizationResult.x1
            let y1 = result.localizationResult.y1
            let x2 = result.localizationResult.x2
            let y2 = result.localizationResult.y2
            let x3 = result.localizationResult.x3
            let y3 = result.localizationResult.y3
            let x4 = result.localizationResult.x4
            let y4 = result.localizationResult.y4

            this.ctx.fillStyle = 'rgba(254,254,254,1)'
            this.ctx.beginPath()
            this.ctx.moveTo(x1, y1)
            this.ctx.lineTo(x2, y2)
            this.ctx.lineTo(x3, y3)
            this.ctx.lineTo(x4, y4)
            this.ctx.fill()
            this.scanner.pause()
            this.scanner.pauseScan()
            this.result = results[i].barcodeText
            var snd = new Audio("data:audio/wav;base64,//uQRAAAAWMSLwUIYAAsYkXgoQwAEaYLWfkWgAI0wWs/ItAAAGDgYtAgAyN+QWaAAihwMWm4G8QQRDiMcCBcH3Cc+CDv/7xA4Tvh9Rz/y8QADBwMWgQAZG/ILNAARQ4GLTcDeIIIhxGOBAuD7hOfBB3/94gcJ3w+o5/5eIAIAAAVwWgQAVQ2ORaIQwEMAJiDg95G4nQL7mQVWI6GwRcfsZAcsKkJvxgxEjzFUgfHoSQ9Qq7KNwqHwuB13MA4a1q/DmBrHgPcmjiGoh//EwC5nGPEmS4RcfkVKOhJf+WOgoxJclFz3kgn//dBA+ya1GhurNn8zb//9NNutNuhz31f////9vt///z+IdAEAAAK4LQIAKobHItEIYCGAExBwe8jcToF9zIKrEdDYIuP2MgOWFSE34wYiR5iqQPj0JIeoVdlG4VD4XA67mAcNa1fhzA1jwHuTRxDUQ//iYBczjHiTJcIuPyKlHQkv/LHQUYkuSi57yQT//uggfZNajQ3Vmz+Zt//+mm3Wm3Q576v////+32///5/EOgAAADVghQAAAAA//uQZAUAB1WI0PZugAAAAAoQwAAAEk3nRd2qAAAAACiDgAAAAAAABCqEEQRLCgwpBGMlJkIz8jKhGvj4k6jzRnqasNKIeoh5gI7BJaC1A1AoNBjJgbyApVS4IDlZgDU5WUAxEKDNmmALHzZp0Fkz1FMTmGFl1FMEyodIavcCAUHDWrKAIA4aa2oCgILEBupZgHvAhEBcZ6joQBxS76AgccrFlczBvKLC0QI2cBoCFvfTDAo7eoOQInqDPBtvrDEZBNYN5xwNwxQRfw8ZQ5wQVLvO8OYU+mHvFLlDh05Mdg7BT6YrRPpCBznMB2r//xKJjyyOh+cImr2/4doscwD6neZjuZR4AgAABYAAAABy1xcdQtxYBYYZdifkUDgzzXaXn98Z0oi9ILU5mBjFANmRwlVJ3/6jYDAmxaiDG3/6xjQQCCKkRb/6kg/wW+kSJ5//rLobkLSiKmqP/0ikJuDaSaSf/6JiLYLEYnW/+kXg1WRVJL/9EmQ1YZIsv/6Qzwy5qk7/+tEU0nkls3/zIUMPKNX/6yZLf+kFgAfgGyLFAUwY//uQZAUABcd5UiNPVXAAAApAAAAAE0VZQKw9ISAAACgAAAAAVQIygIElVrFkBS+Jhi+EAuu+lKAkYUEIsmEAEoMeDmCETMvfSHTGkF5RWH7kz/ESHWPAq/kcCRhqBtMdokPdM7vil7RG98A2sc7zO6ZvTdM7pmOUAZTnJW+NXxqmd41dqJ6mLTXxrPpnV8avaIf5SvL7pndPvPpndJR9Kuu8fePvuiuhorgWjp7Mf/PRjxcFCPDkW31srioCExivv9lcwKEaHsf/7ow2Fl1T/9RkXgEhYElAoCLFtMArxwivDJJ+bR1HTKJdlEoTELCIqgEwVGSQ+hIm0NbK8WXcTEI0UPoa2NbG4y2K00JEWbZavJXkYaqo9CRHS55FcZTjKEk3NKoCYUnSQ0rWxrZbFKbKIhOKPZe1cJKzZSaQrIyULHDZmV5K4xySsDRKWOruanGtjLJXFEmwaIbDLX0hIPBUQPVFVkQkDoUNfSoDgQGKPekoxeGzA4DUvnn4bxzcZrtJyipKfPNy5w+9lnXwgqsiyHNeSVpemw4bWb9psYeq//uQZBoABQt4yMVxYAIAAAkQoAAAHvYpL5m6AAgAACXDAAAAD59jblTirQe9upFsmZbpMudy7Lz1X1DYsxOOSWpfPqNX2WqktK0DMvuGwlbNj44TleLPQ+Gsfb+GOWOKJoIrWb3cIMeeON6lz2umTqMXV8Mj30yWPpjoSa9ujK8SyeJP5y5mOW1D6hvLepeveEAEDo0mgCRClOEgANv3B9a6fikgUSu/DmAMATrGx7nng5p5iimPNZsfQLYB2sDLIkzRKZOHGAaUyDcpFBSLG9MCQALgAIgQs2YunOszLSAyQYPVC2YdGGeHD2dTdJk1pAHGAWDjnkcLKFymS3RQZTInzySoBwMG0QueC3gMsCEYxUqlrcxK6k1LQQcsmyYeQPdC2YfuGPASCBkcVMQQqpVJshui1tkXQJQV0OXGAZMXSOEEBRirXbVRQW7ugq7IM7rPWSZyDlM3IuNEkxzCOJ0ny2ThNkyRai1b6ev//3dzNGzNb//4uAvHT5sURcZCFcuKLhOFs8mLAAEAt4UWAAIABAAAAAB4qbHo0tIjVkUU//uQZAwABfSFz3ZqQAAAAAngwAAAE1HjMp2qAAAAACZDgAAAD5UkTE1UgZEUExqYynN1qZvqIOREEFmBcJQkwdxiFtw0qEOkGYfRDifBui9MQg4QAHAqWtAWHoCxu1Yf4VfWLPIM2mHDFsbQEVGwyqQoQcwnfHeIkNt9YnkiaS1oizycqJrx4KOQjahZxWbcZgztj2c49nKmkId44S71j0c8eV9yDK6uPRzx5X18eDvjvQ6yKo9ZSS6l//8elePK/Lf//IInrOF/FvDoADYAGBMGb7FtErm5MXMlmPAJQVgWta7Zx2go+8xJ0UiCb8LHHdftWyLJE0QIAIsI+UbXu67dZMjmgDGCGl1H+vpF4NSDckSIkk7Vd+sxEhBQMRU8j/12UIRhzSaUdQ+rQU5kGeFxm+hb1oh6pWWmv3uvmReDl0UnvtapVaIzo1jZbf/pD6ElLqSX+rUmOQNpJFa/r+sa4e/pBlAABoAAAAA3CUgShLdGIxsY7AUABPRrgCABdDuQ5GC7DqPQCgbbJUAoRSUj+NIEig0YfyWUho1VBBBA//uQZB4ABZx5zfMakeAAAAmwAAAAF5F3P0w9GtAAACfAAAAAwLhMDmAYWMgVEG1U0FIGCBgXBXAtfMH10000EEEEEECUBYln03TTTdNBDZopopYvrTTdNa325mImNg3TTPV9q3pmY0xoO6bv3r00y+IDGid/9aaaZTGMuj9mpu9Mpio1dXrr5HERTZSmqU36A3CumzN/9Robv/Xx4v9ijkSRSNLQhAWumap82WRSBUqXStV/YcS+XVLnSS+WLDroqArFkMEsAS+eWmrUzrO0oEmE40RlMZ5+ODIkAyKAGUwZ3mVKmcamcJnMW26MRPgUw6j+LkhyHGVGYjSUUKNpuJUQoOIAyDvEyG8S5yfK6dhZc0Tx1KI/gviKL6qvvFs1+bWtaz58uUNnryq6kt5RzOCkPWlVqVX2a/EEBUdU1KrXLf40GoiiFXK///qpoiDXrOgqDR38JB0bw7SoL+ZB9o1RCkQjQ2CBYZKd/+VJxZRRZlqSkKiws0WFxUyCwsKiMy7hUVFhIaCrNQsKkTIsLivwKKigsj8XYlwt/WKi2N4d//uQRCSAAjURNIHpMZBGYiaQPSYyAAABLAAAAAAAACWAAAAApUF/Mg+0aohSIRobBAsMlO//Kk4soosy1JSFRYWaLC4qZBYWFRGZdwqKiwkNBVmoWFSJkWFxX4FFRQWR+LsS4W/rFRb/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////VEFHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAU291bmRib3kuZGUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMjAwNGh0dHA6Ly93d3cuc291bmRib3kuZGUAAAAAAAAAACU=");  
            snd.play();
          }
        }

      },
      
      onUnduplicatedRead: (txt, result) => { console.log("dupli", txt); }
    }).then(async s => {
      this.scanner = s;

     
      this.scanner.setUIElement(this.elRef.nativeElement);
      
      let scanSettings = await this.scanner.getScanSettings();
      scanSettings.intervalTime = 180;
      scanSettings.duplicateForgetTime = 2000;
      scanSettings.frameFilter = true;

      await this.scanner.updateScanSettings(scanSettings);
      await this.scanner.updateRuntimeSettings('balance');

      // this.scanner.bPlaySoundOnSuccessfulRead = true
      this.scanner.open().then(async x => {

        let camera = await this.scanner.getCurrentCamera();
        console.log("++ camera", camera)

        this.rsl = await this.scanner.getResolution();
        console.log("RSL2 ", this.rsl)
        


      }).catch(ex => {
        console.log(ex);
        alert(ex.message || ex);
        // this.deactivate();
      });
    });
  }

  scanSuccess(e) {
    console.log(e)
  }


  keyboard() {
    console.log("keyboard")
    this.isKeyboard = true
  }
}

