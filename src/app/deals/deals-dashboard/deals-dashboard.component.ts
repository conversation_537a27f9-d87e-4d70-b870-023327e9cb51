import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { RestapiService, QuerySearchOptions, ApiEvent, EventType, QueryPostOptions, QueryCriteria } from 'src/app/shared/services/rest-api.service';
import { ActivatedRoute, Router } from '@angular/router';
import { AREAS_CFG, PRINT_LEADS_CFG } from '../../models/deal'
import { ViewChild } from '@angular/core';
import { BehaviorSubject, firstValueFrom, Observable, ReplaySubject, Subject } from 'rxjs';
import { fromEvent } from 'rxjs';
import { debounceTime, first } from 'rxjs/operators';
import { ChangeDetectorRef } from '@angular/core';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { Lead } from 'src/app/models/crm.lead.model';
import { CrmStage } from 'src/app/models/crm.lead.stage.model';
import { SaleOrder } from 'src/app/models/sale-order.model';
import { OdooRelationship } from 'src/app/models/odoo-relationship.model';
import { AccountMove } from 'src/app/models/account-move.model';
import { ODOO_IDS } from '../../models/deal';
import { MailFollower } from 'src/app/models/mail.followers';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { Partner } from 'src/app/models/partner';
import { User } from 'src/app/models/user.model';
import { MailActivity } from 'src/app/models/mail.message';
import { table } from 'console';
import * as _ from 'lodash';
import { GapiService } from 'src/app/shared/services/g-api.service';


interface DealFilters { // Updated filter interface
  searchText?: string;
  selectedUser: Partner | null;
  selectedResponsable: Partner | null;
  startDate: string;
  endDate: string;
  approvedStartDate: string;  // Add this new field
  approvedEndDate: string;    // Add this new field
  doneBefore?: string;
  justRecents: boolean;
  withActivity: boolean;
  selectedStages: number[];
  selectedAreas: string[];
  sortField: string;
  sortAscending: boolean;
  tableView: boolean;

}

interface LeadWithInvoicing extends Lead {
  _contractTotal?: number;
  _contractsExist?: boolean;
  _invoicedTotal?: number;
  _leftToInvoice?: number;
}

@Component({
    selector: 'app-deals-dashboard',
    templateUrl: './deals-dashboard.component.html',
    standalone: false
})
export class DealsDashboardComponent implements OnInit, AfterViewInit {
  stages: CrmStage[] = []
  cards: LeadWithInvoicing[] = []
  filteredCards: Lead[] = []
  @Input() loading: boolean = true
  newDeal:Lead
  filter: BehaviorSubject<string> 
  justRecents = false
  withActivity = false
  doneBefore = 0
  @Input() title = "Fascicoli"
  @ViewChild('searchInput', {static:false}) searchInput: ElementRef

  @Input() embedded:boolean = false
  @Output() onSelect:EventEmitter<boolean> = new EventEmitter()
  lastSearch: number;
  stageDropIds: string[] = [];
  cardsLoaded: boolean = false;
  printMessage: string = '';

  showInvoices: boolean = false;
  allInvoices: AccountMove[] = [];
  contractInvoices: AccountMove[] = [];
  salInvoices: AccountMove[] = [];
  excludeActivitiesIds = [6, 19, 18, 4]; //activity types to exclude from the list 6 = eccezione, 19 = inizio posa, 18 = fine posa, 4 = da preparare


  //totals
  totalExpected: number = 0;
  totalContract: number = 0;
  totalInvoiced: number = 0;
  totalLeftToInvoice: number = 0;
  portfolioPassword: string = '';
  megaBigTotal: number = 0; //total of all expected revenues for all stages
  megaBigCount: number = 0; //total of all leads for all stages
  totalSentAmount: number = 0;  //amount of sent preventivi
  totalSentCount: number = 0; //number of sent preventivi

  
  // Updated filter variables
  followers: Partner[] = [];
  selectedUser: Partner | null = null;
  selectedResponsable: Partner | null = null;
  startDate: string = '';
  endDate: string = '';
  isTableView: boolean = false;
  selectedStages: Set<number> = new Set();
  selectedAreas: string[] = [];
  approvedStartDate: string = '';
  approvedEndDate: string = ''; 


  // osservabile per debounce searchdealsinput  
  searchDealsInput$: Subject<string> = new Subject<string>();
  searchDealsInput: string = ''
  filtersVisible: boolean = true;
  areas: { name: string; src: string; dst: string; }[]
  sortField: string = 'tracking_code'; // default sort field
  sortAscending: boolean = false;
  userId: number;
  currentFilters: DealFilters = { //default filters
    selectedUser: null,
    selectedResponsable: null,
    justRecents: false,
    withActivity: false,
    selectedStages: [6, 2, 12, 4, 3, 10, 9, 1],
    selectedAreas: [],
    sortField: "tracking_code",
    searchText: '',
    endDate: '',
    startDate: '',  approvedStartDate: '',  // Add this
    approvedEndDate: '',    // Add this
    sortAscending: false,
    tableView: false
  };

  queryParameters: any = {};


  constructor(
    public restapi : RestapiService,
    private router: Router,
    private route: ActivatedRoute,
    private _cdr: ChangeDetectorRef,
    private odooEM: OdooEntityManager,
    private gapiService: GapiService
  ) {
    // Cache filter value
    const i = localStorage.getItem('deals-dashboard-filter') || '';
    this.filter = new BehaviorSubject(i);
    this.filter.subscribe((f) => {
      // Sync new deal area default with active filter
      localStorage.setItem('deals-dashboard-filter', f);
      if (this.newDeal) this.newDeal.area = f;
      this.refresh(); // Call refresh to update the results
    });
  }

  // Initialize all stages as selected in ngOnInit
  async ngOnInit() {
    this.areas = AREAS_CFG;
    this.newDeal = new Lead();
    this.newDeal.area = this.filter.value;
    this.newDeal.partner_id = new OdooRelationship();
    this.stages = await firstValueFrom(this.odooEM.search<CrmStage>(new CrmStage(), []));

    // Load area filter from local storage FIRST
  const storedAreaFilter = localStorage.getItem('dealAreaFilter');
  if (storedAreaFilter) {
    this.selectedAreas = JSON.parse(storedAreaFilter);
  } else {
    this.selectedAreas = []; // or your default
  }

    // Initialize selection of stages: do not show "Persa" stage and "Terminata" stage
    this.stages.forEach(stage => {
      if (stage.id !== 9 && stage.id !== 4) {
        this.selectedStages.add(stage.id);
      }
    });

    // Search for partners with @galimberti.eu emails
    const partners = await firstValueFrom(
      this.odooEM.search<Partner>(new Partner(), [
        ['email', 'ilike', '@galimberti.eu']
      ])
    );
    this.followers = partners;
    console.log('followers:', this.followers);

    // Get the current user's name
    const result: any = await this.odooEM.odoorpcService.getSessionInfo();
    console.log('session info:', result);
    this.userId = result.result.partner_id;
    this.stageDropIds = this.stages.map(s => s.id.toString());


    //this section is for now disabled

    let abled = false;
    if (abled) {
    // Subscribe to query parameters and update filter values
    this.route.queryParams.subscribe(params => {
      console.log('Query params changed:', params);

      // Only apply if params exist
      if (Object.keys(params).length > 0) {
        this.searchDealsInput = params.search || '';
        this.startDate = params.startDate || '';
        this.endDate = params.endDate || '';
        this.approvedStartDate = params.approvedStartDate || '';  // Add this
        this.approvedEndDate = params.approvedEndDate || '';     // Add this
        this.justRecents = params.justRecents === 'true';
        this.withActivity = params.withActivity === 'true';
        this.sortField = params.sortField || 'tracking_code';
        this.sortAscending = params.sortAscending === 'true';
        this.isTableView = params.tableView === 'true';

        if (params.selectedStages) {
          try {
            // Convert comma-separated list back to a Set<number>
            this.selectedStages = new Set(
              params.selectedStages.split(',').map(val => Number(val))
            );
          } catch (e) {
            console.error('Error parsing stage IDs:', e);
          }
        }

        if (params.selectedAreas) {
          this.selectedAreas = params.selectedAreas.split(',');
        }
      

        this.refresh();
      } else {
       this.loadFilters();
      }

    });
  }
   
  this.refresh();


  }


  async toggleOpen() {
    //set stage filter only on confermato and toggle table view + not only recents
    this.showInvoices = true;
    this.selectedStages.clear();
    this.selectedStages.add(3);
    this.isTableView = true;
    this.justRecents = false;
    this.refresh();
}

toggleFiltersVisible() {
  this.filtersVisible = !this.filtersVisible;
}

// Utility function to check viewport size
  isMobileView() {
  console.log('window width:', window.innerWidth);
  if(window.innerWidth < 768){
    this.filtersVisible = false;
  }// Standard Bootstrap md breakpoint
}

async assignInvoiceData() {
      //if invoices array is empty, fetch them
      if (!this.allInvoices.length) {
        // 3. Fetch all invoices
        this.allInvoices = await firstValueFrom(
         this.odooEM.search<AccountMove>(
           new AccountMove(),
           [['move_type', '=', 'out_invoice']],
           5000
         )
       );
      }
  this.contractInvoices = this.allInvoices.filter(
    inv => inv.journal_id[0] === ODOO_IDS.contratti_id && inv.state !== 'cancel'
  );
  this.salInvoices = this.allInvoices.filter(
    inv => inv.journal_id[0] === ODOO_IDS.sal_id && inv.state === 'posted'
  );

  // Group by invoice_origin
  const contractsByCode = _.groupBy(this.contractInvoices, 'invoice_origin');
  const invoicesByCode = _.groupBy(this.salInvoices, 'invoice_origin');

  // For each lead, compute sums
  this.cards.forEach(lead => {
    const contractTotal = _.sumBy(contractsByCode[lead.tracking_code] || [], 'amount_untaxed');
    const invoicedTotal = _.sumBy(invoicesByCode[lead.tracking_code] || [], 'amount_untaxed');

    //initialize all invoice-related fields
    lead._contractsExist = false;
    lead._contractTotal = 0;
    lead._invoicedTotal = 0;
    lead._leftToInvoice = 0;

    if (contractTotal > 0) {
      lead._contractsExist = true;

      lead._contractTotal = contractTotal
    }
    else if( (lead.stage_id.id === 3 || lead.stage_id.id === 10) ) {
      //if lead is confirmed (or conferma verbale) but no contract exists, set contract total to expected revenue
      lead._contractsExist = false;
      lead._contractTotal = lead.expected_revenue;
     }
    lead._invoicedTotal = invoicedTotal;

    //left to invoice gets set to 0 if lead is terminated or persa
    if (lead.stage_id.id === 9 || lead.stage_id.id === 4) {
      lead._leftToInvoice = 0;
    }
    else {
    lead._leftToInvoice = lead._contractTotal - invoicedTotal;
    }
    console.log('lead:', lead.tracking_code, lead._contractTotal, lead._invoicedTotal, lead._leftToInvoice);
  });

  //calculate grand totals
  this.totalExpected = _.sumBy(this.cards, 'expected_revenue');
  this.totalContract = _.sumBy(this.cards, '_contractTotal');
  this.totalInvoiced = _.sumBy(this.cards, '_invoicedTotal');
  this.totalLeftToInvoice = _.sumBy(this.cards, '_leftToInvoice')
  

  console.log("FINISHED ANALYZING INVOICES", this.cards, this.totalContract, this.totalInvoiced, this.totalLeftToInvoice);
}

async printView() { 
  this.loading = true;
    this.printMessage = "Carico i dati di fatturazione...";
    //run again invoice toggle
    this.allInvoices = await firstValueFrom(
      this.odooEM.search<AccountMove>(
        new AccountMove(),
        [['move_type', '=', 'out_invoice']],
        5000
      )
    );
   await  this.assignInvoiceData();
    let leads = this.cards;
    let filteredLeads = this.filteredCards;


    
    //only fetch leads that are in the filtered cards
    if (filteredLeads.length) {
      leads = leads.filter(l => filteredLeads.includes(l));
    }
    console.log('leads to print:', leads);

  this.printMessage = "Creo il foglio di calcolo...";
    // printa header with all the fields name for the cards
    const header :string [] = 
   ["Settore", "Codice", "Cliente", "Descrizione", "Luogo", "Fase",  "Data creazione", "Responsabile", "Valore atteso", "Valore contratti", "Fatture emesse" , "Residuo", "Data di consegna"];

    const Data = [];
    Data.push(header);
    //push costlines into Data  , but take string values!
    leads.forEach((l) => {
      Data.push(
        [l.area,
        l.tracking_code,  
         l.partner_id.name,
         l.name,
         l.city, 
         l.stage_id.name, 
         l.ga_sent_date, 
         l.user_id.name, 
         l.expected_revenue, 
         l._contractsExist? l._contractTotal :  0,
         l._invoicedTotal,
         l._leftToInvoice,
         l.date_deadline,        
        ]
      );
    }
    );
    var sheetid = await this.gapiService.printGenericSingleSheet(
      PRINT_LEADS_CFG.template_sheet_id,
      PRINT_LEADS_CFG.spool_folder_id,
      Data
      )
    //open sheet id in a new tab
    this.printMessage = "";
    window.open('https://docs.google.com/spreadsheets/d/' + sheetid, '_blank');
    this.loading = false;
}
  // Save current filters
  saveFilters() {
    // Update currentFilters with all current values
    this.currentFilters = {
      selectedUser: this.selectedUser,
      selectedResponsable: this.selectedResponsable,
      startDate: this.startDate,
      endDate: this.endDate,
      approvedStartDate: this.approvedStartDate,  // Add this
      approvedEndDate: this.approvedEndDate,     // Add this
      justRecents: this.justRecents,
      withActivity: this.withActivity,
      selectedStages: Array.from(this.selectedStages),
      selectedAreas: this.selectedAreas,
      sortField: this.sortField,
      sortAscending: this.sortAscending,
      tableView: this.isTableView
    };
  
    localStorage.setItem('leadFilters', JSON.stringify({
      filters: this.currentFilters,
      lastUpdated: new Date().toISOString()
    }));
    alert('Filtri salvati');
    console.log('filters saved:', this.currentFilters);
  }
  

// Load saved filters
loadFilters() {
  const saved = localStorage.getItem('leadFilters');
  if (saved) {
    const parsed = JSON.parse(saved);
    this.currentFilters = parsed.filters;
    
    // Apply saved filters to component state

    this.selectedUser = this.currentFilters.selectedUser;
    this.startDate = this.currentFilters.startDate;
    this.endDate = this.currentFilters.endDate;
    this.approvedStartDate = this.currentFilters.approvedStartDate;  // Add this
    this.approvedEndDate = this.currentFilters.approvedEndDate;     // Add this
    this.justRecents = this.currentFilters.justRecents;
    this.selectedStages = new Set(this.currentFilters.selectedStages);
    this.selectedAreas = this.currentFilters.selectedAreas;
    this.sortField = this.currentFilters.sortField;
    this.sortAscending = this.currentFilters.sortAscending;
    this.isTableView = this.currentFilters.tableView;
  }
    // Refresh the view with loaded filters
    this.route.queryParams.subscribe((params) => {
      this.searchDealsInput = params.search || '';
    });

    this.router.navigate([], { queryParams: { search: this.searchDealsInput } });

    this.refresh();
  }

  

  ngAfterViewInit(): void {
    this.isMobileView();


     // Debounce search input changes
  this.searchDealsInput$.pipe(debounceTime(400)).subscribe((value) => {
    this.searchDealsInput = value;
    // this.updateQueryParams();
    this.refresh();
  });
  
  // Setup Bootstrap dropdown event listener for stage filter
  const stageDropdownElements = document.querySelectorAll('#stageFilterDropdown');
  stageDropdownElements.forEach(element => {
    element.addEventListener('hidden.bs.dropdown', () => {
      this.updateQueryParams();
      this.refresh();
    });
  });
  
  // run invoicing if already in table view
  if (this.isTableView) {
    this.showInvoices = true;
    this.assignInvoiceData();
  }
}


  onSearchChange(value: any) {
    this.searchDealsInput$.next(value);
  }

  updateQueryParams() {
    // Convert Set to array for serialization
    const stageIdsArray = Array.from(this.selectedStages);
    
    // Only include params that have values
    const queryParams: any = {};
    
    if (this.searchDealsInput) queryParams.search = this.searchDealsInput;
    if (this.startDate) queryParams.startDate = this.startDate;
    if (this.endDate) queryParams.endDate = this.endDate;
    if (this.approvedStartDate) queryParams.approvedStartDate = this.approvedStartDate;  // Add this
    if (this.approvedEndDate) queryParams.approvedEndDate = this.approvedEndDate;      // Add this
    if (this.justRecents) queryParams.justRecents = this.justRecents.toString();
    if (this.withActivity) queryParams.withActivity = this.withActivity.toString();
    if (stageIdsArray.length > 0) queryParams.selectedStages = stageIdsArray.join(',');
    if (this.selectedAreas && this.selectedAreas.length > 0) queryParams.selectedAreas = this.selectedAreas.join(',');
    if (this.sortField) queryParams.sortField = this.sortField;
    if (this.sortAscending) queryParams.sortAscending = this.sortAscending.toString();
    if (this.isTableView) queryParams.tableView = this.isTableView.toString();
    
    // Navigate preserving query params
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: queryParams,
      replaceUrl: false // This prevents adding to browser history for every filter change
    });

    this.buildQueryParamsForRedirect();
  }
  

  isAreaSelected(areaName: string): boolean {
    return this.selectedAreas && this.selectedAreas.includes(areaName);
  }

  toggleAreaFilter(areaName: string) {
    if (!this.selectedAreas) {
      this.selectedAreas = [];
    }
    
    if (this.isAreaSelected(areaName)) {
      // Remove area if already selected
      this.selectedAreas = this.selectedAreas.filter(area => area !== areaName);
    } else {
      // Add area if not already selected
      this.selectedAreas.push(areaName);
    }

    //save area filter to local storage
    localStorage.setItem('dealAreaFilter', JSON.stringify(this.selectedAreas));
    
    // Update filters and refresh
    this.refresh();
  }
  toggleAllAreas() {
    if (this.selectedAreas && this.selectedAreas.length > 0) {
      // If any areas are selected, clear the selection
      this.selectedAreas = [];
    } else {
      // If no areas are selected, select all areas
      this.selectedAreas = this.areas.map(area => area.name);
    }
    
    this.refresh();
  }

  
  // Method to toggle all stages
  toggleAllStages() {
    const allSelected = this.selectedStages.size === this.stages.length;
    
    if (allSelected) {
      // If all stages are selected, clear selection
      this.selectedStages.clear();
    } else {
      // If not all stages are selected, select all
      this.stages.forEach(stage => {
        this.selectedStages.add(stage.id);
      });
    }
    
  }

  
  
  async refresh() {
    this.cardsLoaded = false;
    console.log('refreshing... with selected user:', this.selectedUser);
    this.loading = true;
    const x = Math.random();
    this.lastSearch = x;

    let conditions: any[] = [['company_id', '=', 1]];

  // Apply area filter for multiple selections
  if (this.selectedAreas && this.selectedAreas.length > 0) {
    conditions.push(['area', 'in', this.selectedAreas]);
  }

    // Apply stage filter - only if specific stages are selected
    if (this.selectedStages.size < this.stages.length) {
      conditions.push(['stage_id', 'in', Array.from(this.selectedStages)]);
    }
  // Apply date filters
  if (this.startDate) {
    conditions.push(['ga_sent_date', '>=', this.startDate]);
  }
  if (this.endDate) {
    conditions.push(['ga_sent_date', '<=', this.endDate]);
  }
  
  // Add new filters for approved date
  if (this.approvedStartDate) {
    conditions.push(['ga_approved_date', '>=', this.approvedStartDate]);
  }
  if (this.approvedEndDate) {
    conditions.push(['ga_approved_date', '<=', this.approvedEndDate]);
  }

    //last activity filter searches for leads with last activity date before today and not false
    if (this.doneBefore) {
      conditions.push(['ga_last_activity_date', '<=', this.doneBefore]);
    }

  
    // Apply "Solo Recenti" filter (based on write_date)
    if (this.justRecents) {
      const d = new Date();
      d.setMonth(d.getMonth() - 3);
      conditions.push(['write_date', '>=', d.toISOString()]);
    console.log("FILTERING RECENTS >=", d.toISOString())
    }
  
    // Check if the search input matches a Sale Order number or tracking code
    const saleOrderRegex = /^[vVfF]?\d{4,}$/;
    if (this.searchDealsInput && saleOrderRegex.test(this.searchDealsInput)) {
      // Remove leading V/v/F/f if present to get the numeric part
      const numberPart = this.searchDealsInput.replace(/^[vVfF]/, '');
  
      // Search for Sale Orders matching the number part
      const saleOrderConditions = [['name', 'ilike', numberPart]];
      const saleOrders = await firstValueFrom(
        this.odooEM.search<SaleOrder>(new SaleOrder(), saleOrderConditions)
      );
  
      if (saleOrders.length > 0) {
        const saleOrderIds = saleOrders.map((so) => so.id);
        // Combine conditions to find leads associated with these sale orders or with matching tracking code
        conditions = [
          ...conditions,
          '|',
          ['order_ids', 'in', saleOrderIds],
          ['tracking_code', 'ilike', numberPart],
        ];
      } else {
        // If no sale orders are found, search for leads with matching tracking code
        conditions.push(['tracking_code', 'ilike', numberPart]);
      }
    } else if (this.searchDealsInput) {
      // Apply search input to all leads
      conditions.push('|', '|', '|');
      conditions.push(['partner_id.name', 'ilike', this.searchDealsInput]);
      conditions.push(['name', 'ilike', this.searchDealsInput]);
      conditions.push(['tracking_code', 'ilike', this.searchDealsInput]);
      conditions.push(['street', 'ilike', this.searchDealsInput]);
    }

    try {
      conditions.push(['active', 'in', [true, false]]);
    const sortDirection = this.sortAscending ? 'asc' : 'desc';
    const r = await firstValueFrom(
      this.odooEM.search<Lead>(new Lead(), conditions, 1000, null, `${this.sortField} ${sortDirection}`)
    );
  
    if (this.lastSearch !== x) {
      return;
    }
    this.cards = r;

      // resolve mail message followers
      await firstValueFrom(this.odooEM.resolveArray(new MailFollower, this.cards, 'message_follower_ids'))

      //filter for user: selectedUser.name has to be in the message_follower_ids.values.display_name
      if (this.selectedUser) {
        this.cards = this.cards.filter(c => c.message_follower_ids.values.find(f => f.display_name === this.selectedUser.name))
      }

      //filter for responsable: selectedResponsable.name has to be in the user_id.name
      if (this.selectedResponsable) {
        this.cards = this.cards.filter(c => c.user_id.name === this.selectedResponsable.name)
      }

      //solve activities to show bells
      await firstValueFrom(this.odooEM.resolveArray(new MailActivity, this.cards, 'activity_ids'))

      await this.checkActivities()

      if (this.withActivity) {
        this.cards = this.cards.filter(c => c._activityCount > 0)
      }

      if (this.showInvoices){
      this.assignInvoiceData();}

      //if sorting for expected_revenue, run contract total calculation and re-sort based on _totalContract
      if (this.sortField === 'expected_revenue') {
        if (this.sortAscending) {
          this.cards.sort((a, b) => a._contractTotal - b._contractTotal);
        }
        else {
          this.cards.sort((a, b) => b._contractTotal - a._contractTotal);
      }
    }

      console.log('cards after user filter and activities:', this.cards);

      this.loading = false;
      this.cardsLoaded = true;
      this.calculateTotals();


    } catch (error) {
      console.error('Error loading deals:', error);
    } finally {
      this.loading = false;
      this._cdr.detectChanges(); // Force change detection
    }
  }

  async checkActivities(){

    for (let c of this.cards) {
      //number of activities should be the number of activities excluding the ones in excludeActivitiesIds
      c._activityCount = c.activity_ids.values?.filter(activity => !this.excludeActivitiesIds.includes(activity.activity_type_id.id)).length
      c._isOverdue = c.activity_ids.values?.some(activity => activity.state === 'overdue')
    }

    this.updateQueryParams();
  }

  onContact(c) {
    if (c == null)
      this.newDeal.partner_id = new OdooRelationship()
    else {
      this.newDeal.partner_id = new OdooRelationship(c.id, c.name)
    }
    // this.newDeal.partner_id = [c.id,''];
  }
  
  openPortfolio() {
    console.log('portfolioPassword in openPortfolio() is:', this.portfolioPassword);
    if (this.portfolioPassword == 'BetaGennaio') {
    // Using relative navigation
    this.router.navigate(['portfolio'], { relativeTo: this.route });
    }
    else {
      alert('Password errata');
    }
  }

  onCreate() {

    if(!this.newDeal.partner_id.id || !this.newDeal.area) {
      alert('Compila il cliente e il settore');
      return;
    }
    this.createDeal()
  }

    // Calculate the total for each stage and the big total
  async calculateTotals()  {
    this.stages.forEach(stage => {
      stage._total_revenue = 0;
      stage._number_of_leads = 0;
    });

    for (let card of this.cards) {
      const stage = this.stages.find(s => s.id === card.stage_id.id);
      if (stage) {
        stage._total_revenue += card.expected_revenue;
        stage._number_of_leads += 1
      }
    }

    this.megaBigTotal = this.stages.reduce((acc, stage) => acc + stage._total_revenue, 0);
    this.megaBigCount = this.stages.reduce((acc, stage) => acc + stage._number_of_leads, 0);
    // this.totalSentAmount = this.cards.reduce((acc, card) => acc + card.ga_sent_amount, 0);
    // this.totalSentCount = this.cards.reduce((acc, card) => acc + (card.ga_sent_amount > 0 ? 1 : 0), 0);
  
  }
  
  pickContact() {
    var w = window.open('/contact?mode=embedded','_blank','height=700,width=500,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes' );
    
    var h = event => {
      var d = JSON.parse(event.data)
      this.newDeal.partner_id = new OdooRelationship(d.id,d.name)
      w.close()
    }
    window.addEventListener("message", h, false);
    w.onbeforeunload = () => {
      window.removeEventListener("message", h)
    }
  }

  async createDeal() {
    this.loading = true
    // todo move in abstarct model
    var x = {

              "area": this.newDeal.area, 
              "partner_id": this.newDeal.partner_id.id,
              "name":"",
              "description":"",
              "city":"",
              "street":"",
              "contact_name":  this.newDeal.partner_id.name,
              "type": "opportunity"
            }
    
    await this.odooEM.create<Lead>(new Lead(),x).subscribe(async (res) => {

      this.loading = false
      // Add the new deal to the cards array
      // this.cards.unshift(JSON.parse(JSON.stringify(res)))
      this.redirectDeal(res.id)
    })

  }

  buildQueryParamsForRedirect(): any {
    const stageIdsArray = Array.from(this.selectedStages);
    const queryParams: any = {};
    
    if (this.searchDealsInput) queryParams.search = this.searchDealsInput;
    if (this.startDate) queryParams.startDate = this.startDate;
    if (this.endDate) queryParams.endDate = this.endDate;
    if (this.justRecents) queryParams.justRecents = this.justRecents.toString();
    if (this.withActivity) queryParams.withActivity = this.withActivity.toString();
    if (stageIdsArray.length > 0) queryParams.selectedStages = stageIdsArray.join(',');
    if (this.selectedAreas && this.selectedAreas.length > 0) queryParams.selectedAreas = this.selectedAreas.join(',');
    if (this.sortField) queryParams.sortField = this.sortField;
    if (this.sortAscending) queryParams.sortAscending = this.sortAscending.toString();
    if (this.isTableView) queryParams.tableView = this.isTableView.toString();
    
    this.queryParameters = queryParams;
  
  }


  redirectDeal(id) { 
    if (!this.embedded) {
      // Get current route query params to preserve them
      const stageIdsArray = Array.from(this.selectedStages);
      
      // Build query params to maintain current filters
      const queryParams: any = {};
      
      if (this.searchDealsInput) queryParams.search = this.searchDealsInput;
      if (this.startDate) queryParams.startDate = this.startDate;
      if (this.endDate) queryParams.endDate = this.endDate;
      if (this.justRecents) queryParams.justRecents = this.justRecents.toString();
      if (this.withActivity) queryParams.withActivity = this.withActivity.toString();
      if (stageIdsArray.length > 0) queryParams.selectedStages = stageIdsArray.join(',');
      if (this.selectedAreas && this.selectedAreas.length > 0) queryParams.selectedAreas = this.selectedAreas.join(',');
      if (this.sortField) queryParams.sortField = this.sortField;
      if (this.sortAscending) queryParams.sortAscending = this.sortAscending.toString();
      
      this.router.navigate(['leads/'+id], { queryParams });
    } else {
      this.onSelect.emit(id);
    }
  }

  // Reset filters with query params update
resetFilters() {
  this.searchDealsInput = '';
  this.selectedUser = null;
  this.selectedResponsable = null;
  this.startDate = '';
  this.endDate = '';
  this.approvedStartDate = '';  // Add this
  this.approvedEndDate = '';   // Add this
  this.justRecents = false;
  this.withActivity = false;
  this.filter.next('');
  this.sortField = 'tracking_code';
  this.sortAscending = false;
  this.isTableView = false;
  
  // Reset selectedStages to default (all except 'Persa' and 'Terminata')
  this.selectedStages.clear();
  this.stages.forEach(stage => {
    if (stage.id !== 9 && stage.id !== 4) {
      this.selectedStages.add(stage.id);
    }
  });

  // Update the URL to remove query params
  this.router.navigate([], {
    relativeTo: this.route,
    queryParams: {},
    replaceUrl: true
  });
  
  this.refresh();
}

  filterCards(stageId: number) {
    // Only show cards for selected stages
    if (!this.selectedStages.has(stageId)) {
      return [];
    }
    else{
    this.calculateTotals()
    return this.cards.filter((c) => c.stage_id.id === stageId);
    }
  
  }

  isStageSelected(stageId: number): boolean {
    return this.selectedStages.has(stageId);
  }

  myDealsPart() {
    //selected user gets the current user
    // if i'm the user, deselect the user
    if (this.selectedUser?.id === this.userId) {
      this.selectedUser = null;
    }
    else {
    this.selectedUser = this.followers.find(f => f.id === this.userId);
    }
    this.refresh();
  }

  myDealsResp() {
    //selected responsable gets the current user
    //if i'm the responsable, deselect the user
    if (this.selectedResponsable?.id === this.userId) {
      this.selectedResponsable = null;
    }
    else {
    this.selectedResponsable = this.followers.find(f => f.id === this.userId);
    }
    this.refresh();
  }

  // Helper method to get stage name
getStageName(lead:Lead ): string {
  //if archived, stage is "Persa"
  if (lead.active == false) {
    return 'Persa';
  }
  const stage = this.stages.find(s => s.id === lead.stage_id?.id );
  return stage ? stage.name : '';
}

updateStageFilter(stageId: number) {
  if (this.selectedStages.has(stageId)) {
    this.selectedStages.delete(stageId);
  } else {
    this.selectedStages.add(stageId);
  }
  
  // If no stages are selected, show all stages
  if (this.selectedStages.size === 0) {
    this.stages.forEach(stage => this.selectedStages.add(stage.id));
  }
  
  // Don't refresh immediately - will be triggered by Apply button
}

  toggleRecents() {
    this.justRecents = !this.justRecents
    this.refresh()
  }

  toggleSortDirection() {
    this.sortAscending = !this.sortAscending;
    this.refresh();
  }  

  toggleView() { //should fix this... basically i'm forcing it to show invoices if it's in table view
    this.isTableView = !this.isTableView;
    if(this.isTableView){
      this.showInvoices = true;
      this.assignInvoiceData();
    }
    if (!this.isTableView){
      this.showInvoices = false;
    }
  }

  toggleActivity() {
    this.withActivity = !this.withActivity;
    this.refresh();
  }

  getInitials(name: string): string {
    //find the follower (partner) with the name (the follower in lead are hrEmployees)
    const follower = this.followers.find(f => f.name === name);
    return follower?.ref ? follower.ref : '';
  }


  getAreaBadgeClass(area: string): string {
    switch (area) {
      case 'Tetti':
        return 'bg-success text-white';
      case 'Case':
        return 'bg-danger text-white';
      case 'Facciate e Decking':
        return 'bg-secondary text-white';
      case 'Aziendale':
        return 'bg-muted text-white'; // 'bg-muted' is typically light gray; using 'bg-light' for clarity
      case 'Massello':
        return 'bg-dark text-white'; 
      default:
        return 'bg-warning text-white';
    }
  }

  /* here all methos to manage drag and drop for cstage changes */
  async validateStageMove(lead: Lead, targetStage: CrmStage): Promise<boolean> {
    

    if (targetStage.sequence >= 1 && targetStage.id !== 9) {
      console.log("CHECKING ", lead.name, lead.partner_id, lead.street, lead.city);
      if (!lead.name || !lead.partner_id.id || !lead.street || !lead.city) {
        alert("Non hai compilato tutti i campi. Compila la descrizione, il cliente e l'indirizzo");
        return false;
      }
    }

    if (targetStage.sequence > 2 && targetStage.id !== 9) {
      if (!lead.tag_ids?.ids?.length) {
        alert("Non hai compilato tutti i campi. Compila la modalità di fornitura");
        return false;
      }
       //if parner name is BANCO, block it
       if (lead.partner_id.name == "BANCO") {
        alert("Non puoi spostare la commessa in produzione se il cliente è BANCO");
      return false;
    }
  }
  
    return confirm("Sei sicuro di spostare il fascicolo in " + targetStage.name + "?");
  }

  async onCardDrop(event: CdkDragDrop<Lead[]>) {
    if (event.previousContainer === event.container) {
      // Reorder within same stage - we might want to implement order preservation later
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      const lead = event.previousContainer.data[event.previousIndex];
      const targetStageId = parseInt(event.container.id);
      const targetStage = this.stages.find(s => s.id === targetStageId);
  
      if (!targetStage) {
        console.error('Target stage not found');
        return;
      }
  
      // Validate the move
      const isValid = await this.validateStageMove(lead, targetStage);
      if (!isValid) {
        return;
      }
  
      this.loading = true;
      try {
        // Update the lead's stage
        const updatedLead = { ...lead };
        updatedLead.stage_id.id = targetStage.id;
        
        await firstValueFrom(this.odooEM.update(lead, { stage_id: targetStage.id }));
        //if stage 9 (persa) run odoo function 
        
        // Move the item in the UI
        transferArrayItem(
          event.previousContainer.data,
          event.container.data,
          event.previousIndex,
          event.currentIndex,
        );
  
        // Refresh to ensure everything is in sync
        await this.refresh();
      } catch (error) {
        console.error('Error updating lead stage:', error);
        alert('Si è verificato un errore durante lo spostamento del fascicolo');
      } finally {
        this.loading = false;
      }
    }
  }

}
