<!-- Complete product-list.component.html -->
<div class="p-table-container border">
  <!-- Debug information (you can remove this in production) -->
  <div class="p-2 bg-light border-bottom" *ngIf="!product">
    <div class="alert alert-warning mb-0">
      <i class="pi pi-exclamation-triangle me-2"></i>
      Nessun prodotto selezionato. Verifica la configurazione del componente.
    </div>
  </div>
  
  <div class="p-2 bg-light border-bottom" *ngIf="product && (!attributes || attributes.length === 0)">
    <div class="alert alert-warning mb-0">
      <i class="pi pi-exclamation-triangle me-2"></i>
      Prodotto selezionato ma nessun attributo trovato.
    </div>
  </div>

  <!-- Main PrimeNG Table -->
  <p-table [value]="rows" responsiveLayout="scroll" styleClass="p-datatable-gridlines">
    <ng-template pTemplate="header">
      <tr>
        <th style="width: 40px">#</th>
        <th pSortableColumn="pieces" (click)="sort('pieces')">
          Pezzi
          <p-sortIcon field="pieces" [sortOrder]="sortField === 'pieces' ? sortOrder : 0"></p-sortIcon>
        </th>

        <th *ngFor="let attr of attributes" pSortableColumn="{{attr.attribute_id.name}}" (click)="sort(attr.attribute_id.name)">
          {{attr.attribute_id.name}}
          <span *ngIf="isDimensionalAttribute(attr.attribute_id.name)" class="text-muted">(mm)</span>
          <p-sortIcon [field]="attr.attribute_id.name" [sortOrder]="sortField === attr.attribute_id.name ? sortOrder : 0"></p-sortIcon>
        </th>

        <!-- Show piece volume column only if dimensional attributes are available -->
        <th *ngIf="showPieces" pSortableColumn="pieceUoM">
          Pezzo (m³)
          <p-sortIcon field="pieceUoM" [sortOrder]="sortField === 'pieceUoM' ? sortOrder : 0"></p-sortIcon>
        </th>

        <!-- Show quantity column only if dimensional attributes are available -->
        <th *ngIf="showPieces" pSortableColumn="quantity">
          Quantità
          <p-sortIcon field="quantity" [sortOrder]="sortField === 'quantity' ? sortOrder : 0"></p-sortIcon>
        </th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-row let-i="rowIndex">
      <tr>
        <td class="text-center">{{i + 1}}</td>

        <!-- Number of pieces -->
        <td>
          <input pInputText type="number" [(ngModel)]="row.pieces" [name]="'pieces-' + i"
                 (ngModelChange)="calculateQuantity(row)" min="0"
                 class="w-100 border-0">
        </td>

        <!-- Attribute columns -->
        <td *ngFor="let attr of attributes">
          <ng-container [ngSwitch]="isDimensionalAttribute(attr.attribute_id.name)">
            <!-- Number input for dimensional attributes -->
            <input
              *ngSwitchCase="true"
              pInputText
              type="number"
              [id]="'cell-' + i + '-' + attr.id"
              [(ngModel)]="row[attr.attribute_id.name]"
              [name]="attr.attribute_id.name + '-' + i"
              (ngModelChange)="onDimensionalValueChange(row)"
              (keydown)="onKeyDown($event, i, attr)"
              min="0"
              class="w-100 border-0">

            <!-- Simple text input for non-dimensional attributes -->
            <input
              *ngSwitchDefault
              pInputText
              type="text"
              [id]="'cell-' + i + '-' + attr.id"
              [(ngModel)]="row[attr.attribute_id.name]"
              [name]="attr.attribute_id.name + '-' + i"
              (ngModelChange)="onValueChange(row)"
              (keydown)="onKeyDown($event, i, attr)"
              class="w-100 border-0">
          </ng-container>
        </td>

        <!-- Piece UoM - only shown if dimensional attributes are available -->
        <td *ngIf="showPieces" class="text-right">
          {{ calculatePieceUoM(row) | number:'1.6-6' }}
        </td>

        <!-- Total Quantity - only shown if dimensional attributes are available -->
        <td *ngIf="showPieces" class="text-right">
          {{ row.quantity | number:'1.6-6' }}
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
      <tr>
        <td [attr.colspan]="getColspan()" class="text-center text-muted p-3">
          <i class="pi pi-info-circle fs-4"></i>
          <p class="mt-2">Nessun prodotto, aggiungi per proseguire</p>
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="summary">
      <div class="d-flex justify-content-between align-items-center">
        <button pButton
                type="button"
                icon="pi pi-plus"
                label="Aggiungi riga"
                class="p-button-outlined"
                (click)="addRow()"></button>
        <button pButton
              
                icon="pi pi-check"
                [disabled]="!hasCompletedRows() || loading"
                (click)="confirmVariants()">
          <span *ngIf="loading" class="pi pi-spin pi-spinner" style="font-size: 1rem;"></span>
          <span *ngIf="!loading">Conferma</span>
        </button>
      </div>
    </ng-template>
  </p-table>
</div>