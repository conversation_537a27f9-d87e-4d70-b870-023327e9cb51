import { Component, ElementRef, EventEmitter, HostBinding, HostListener, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';

@Component({
    selector: 'app-keyboard-scanner',
    templateUrl: './keyboard-scanner.component.html',
    styleUrls: ['./keyboard-scanner.component.scss'],
    standalone: false
})

export class KeyboardScannerComponent implements OnInit, OnDestroy {
  
  listener;
  resetListener;
  showKeyboard:boolean = false

  @Output() @HostBinding('class.active') active: boolean = true;
  @Input() @HostBinding('class.invalid') invalid?:boolean = false;
  @Input() @HostBinding('class.valid') valid?:boolean = false;

  @ViewChild('input') input: ElementRef<HTMLInputElement>;

  // @HostListener('click', ['$event']) onClick(e) {
  //   this.active = !this.active
  //   if (this.active) this.activate()
  //   else this.deactivate()
  // }


  @Output() onCode: EventEmitter<string> = new EventEmitter();
  keybuffer: string = "";

  constructor(
    private elref:ElementRef<KeyboardScannerComponent>,
  ) { }
  


  ngOnInit(): void {
   if (this.activate)
    this.activate()

  }

  ngOnDestroy(): void {
    if (this.activate)
      this.deactivate()
  }


  onInput(e:Event) {
    console.log("oninput")
    e.preventDefault()
    // if (e.key == "Enter") {
    //   console.log("EMIT ", this.keybuffer)
    //   this.onCode.emit(this.keybuffer)
    //   this.keybuffer = ""
    // }
    return false
  }

  onKeyDown(e:KeyboardEvent) {
    console.log("eee",e)

    if (e.key == "Enter") {
      console.log("EMIT ", this.keybuffer)
      this.onCode.emit(this.keybuffer)
      this.keybuffer = ""
    }

    // var e = this.input
    
    // if (e.target != document.body) {
    //   console.log("RETURN")
    //   return
    // }
    
    // todo bad way 
    if (e.key.length > 1)
      return

    var regex = new RegExp("^[a-zA-Z0-9 ]+$");
    if (regex.test(e.key) ) {
      this.keybuffer = this.keybuffer + e.key
    }
    return false
  }

  private activate() {
    this.listener = this.onKeyDown.bind(this)
    window.addEventListener("keydown", this.listener)
    window.addEventListener("mousedown", this.resetListener)
  }

  private deactivate() {
    window.removeEventListener("keydown", this.listener)
    this.listener = null
  }

}
