import { OdooSerializableInterface } from '../shared/interfaces/odoo-serializable-interface';
import { OdooRelationship } from './odoo-relationship.model';
import { GenericOrderLine } from './generic-order-line.model';
import { OdooMultiRelationship } from './odoo-multi-relationship.model';
import { Product } from './product.model';
import { StockLocationRoute } from './stock-location-route.model';
import { StockMove } from './stock-move';
import { ProductPackaging } from './product.packaging.model';
import { AccountTax } from './account-tax.model';
import { PurchaseOrderLine } from './purchase-order-line.model';
import { MrpProduction } from './mrp-production';
import { StockQuantPackage } from './stock-quant-package';
import { StockMoveLine } from './stock-move-line';

export interface Line_cost_data {
  order: string;
  ga_title: string;
  state: string;
  id: string;
  product: string;
  category: string;
  uom: string;
  requested: number;
  delivered: number;
  cost: number;
  total: number;
  origin: string;
  supplier: string;
}

// cost fetched data is made of these, as an output of odoo server function with id 830:
  // line_data = {
  //   'order': record.order_id.name,
  //   'state': record.state,
  //   'product': product.display_name,
  //   'category': product.categ_id.display_name,
  //   'uom': product.uom_id.name,
  //   'requested': qty_requested,
  //   'delivered': qty_delivered,
  //   'cost': cost,
  //   'total': total,
  //   'origin': origin
  // }

export class SaleOrderLine extends GenericOrderLine implements OdooSerializableInterface<SaleOrderLine> {

  route_id:OdooRelationship<StockLocationRoute> = new OdooRelationship<StockLocationRoute>()
  
  move_ids:OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove)

  public ODOO_MODEL = 'sale.order.line';
  virtual_available_at_date:number = 0;
  purchase_line_count:number = 0;
  forecast_expected_date:string = "";
  is_mto:boolean = false;
  scheduled_date:string="";
  qty_available_today:number = 0;
  free_qty_today:number = 0;
  price_subtotal: number = 0;
  price_unit:number = 0;
  product_packaging_id : OdooRelationship<ProductPackaging> = new OdooRelationship<ProductPackaging>()
  tax_id : OdooMultiRelationship<AccountTax> = new OdooMultiRelationship<AccountTax>(AccountTax)
  product_packaging_qty: number = 0;
  qty_delivered:number = 0;
  qty_to_deliver:number = 0;
  purchase_price: number = 0;
  _checked:boolean;
  _dragtarget:boolean;
  discount:number = 0;
  product_type:string = "";
  margin:number// = 0;
  margin_percent:number //= 0;
  write_date: string = "";
  display_type:string = ""; // line_section"
  purchase_line_ids:OdooMultiRelationship<PurchaseOrderLine> = new OdooMultiRelationship<PurchaseOrderLine>(PurchaseOrderLine);
  _quantity_arrived: null|number = null;
  _purchaselineids: number[] = [];
  _purchase_line_values: PurchaseOrderLine[];
  _connected_productions: MrpProduction[];
  _connected_packages: StockMoveLine[];
  _package_info: { total_packages: number, total_reserved: number, package_details: { name: string, quantity: number }[] } = null;
  _line_cost_fetched_data: Line_cost_data ;
  _resolved: boolean = false;
  _reserved_qty: number = 0;

  

  static createFromProduct(orderId: number, product?: Product) {
    return {
      order_id: orderId,
      product_id: product ? product.id : null,
      date_planned: new Date().toDateString(),
      product_uom_qty: 0,
      pezzi: 1,
      price_unit: null,
      product_qty: 0,
      product_qty_po: 0,
      product_uom: (product && product.uom_id) ? product.uom_id.id : null,
      ODOO_MODEL: 'sale.order.line'
    }
  }

  create(): SaleOrderLine {
    return new SaleOrderLine();
  }
}