@if (!embedded) {
  <app-navbar [loading]="loading" backroute="..">
    <a class="navbar-brand"> Distinta pacchi </a>
  </app-navbar>
} @else {
  <bar-loader [loading]="loading"></bar-loader>
}

<!-- Main container -->
<div class="main-container d-flex flex-column h-100">

  <div class="d-flex align-items-center gap-2 p-2 border-bottom">
  
    <!-- Left group: Template and refresh -->
    <div class="d-flex align-items-center gap-2">
      <!-- Product Template Selector -->
      <select id="productTemplateSelect" class="form-select form-select-sm" 
          [(ngModel)]="selectedProductTemplate" 
          (ngModelChange)="persistSelectedTemplate()"
          (change)="onProductTemplateChange()"
          [compareWith]="compareById"
          style="min-width: 200px;">
        <option disabled>Seleziona template</option>
        <option *ngFor="let option of productTemplateOptions" [ngValue]="option">
          {{option.name}}
        </option>
      </select>
  
      <!-- Refresh button -->
      <button type="button" class="btn btn-outline-primary btn-sm" (click)="refreshQuants()" title="Aggiorna dati">
        <i class="fa fa-refresh"></i>
      </button>

      <!-- show photos and messages -->
        <button type="button" class="btn btn-sm btn-outline-primary" (click)="togglePhotos()" title="Mostra foto e messaggi"
        [ngClass]="{'btn-primary text-white': showPhotos}"
        >
        <i class="fa  fa-camera"></i>
      </button>
      
    </div>
  
    <!-- Center group: Actions dropdown -->
    <div class="dropdown">
      <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
              data-bs-toggle="dropdown" aria-expanded="false" title="Azioni">
        <i class="fa fa-cog"></i>
        Azioni
      </button>
      <ul class="dropdown-menu" style="min-width: 300px; z-index: 3000000;">
        <li>
          <a class="dropdown-item" href="#" (click)="toggleEditMode(); $event.preventDefault()">
            <i class="fa fa-edit me-2"></i>
            {{editMode ? 'Annulla spostamento' : 'Spacca o unisci'}}
          </a>
        </li>
        <li>
          <a class="dropdown-item" href="#" 
             [class.disabled]="!canMoveInBlockTo || canMoveInBlockTo.length === 0"
             (click)="canMoveInBlockTo && canMoveInBlockTo.length > 0 ? moveLocationPanel.toggle($event) : null; $event.preventDefault()">
            <i class="fa-solid fa-truck-container me-2"></i>
            Sposta locazione
          </a>
        </li>
        <li>
          <a class="dropdown-item" href="#" 
             [class.disabled]="selectedQuantIds.length === 0"
             (click)="selectedQuantIds.length > 0 ? openSellFromListPanel($event) : null; $event.preventDefault()">
            <i class="fa fa-shopping-cart me-2"></i>
            Vendi selezione
          </a>
        </li>
      </ul>
    </div>
  
    <!-- Edit mode confirm button (when visible) -->
    <button type="button" 
            class="btn btn-success btn-sm" 
            *ngIf="editMode && hasOperations"
            (click)="confirmOperations()" 
            title="Conferma operazioni">
      <i class="fa fa-check me-1"></i>
      Conferma
    </button>
  
    <!-- Right group: Selection info and buttons -->
    <div class="ms-auto d-flex align-items-center gap-2">
      <!-- Selection counter -->
      <!-- <span class="badge bg-secondary" *ngIf="selectedQuantIds.length > 0">
        {{selectedQuantIds.length}} 
      </span> -->
  
      <!-- Confirm Selection button (only when embedded) -->
      <button type="button" 
              class="btn btn-primary btn-sm"
              *ngIf="embedded"
              [disabled]="selectedQuantIds.length === 0"
              (click)="exportSelectedQuants()">
        <i class="fa fa-check me-1"></i>

        Conferma Selezione
      </button>
  
      <!-- Export button (only when NOT embedded) -->

      <!-- <button type="button" 
              class="btn btn-outline-primary btn-sm"
              *ngIf="!embedded"
              [disabled]="selectedQuantIds.length === 0"
              (click)="exportSelectedQuants()">
        <i class="fa fa-download me-1"></i>
        Esporta
      </button> -->
    </div>
  </div>
  
    <!-- Selettore colonne -->
    <!-- <p-multiSelect [options]="allColumns" [(ngModel)]="selectedColumns" optionLabel="label"
      placeholder="Seleziona colonne" [showToggleAll]="false" (onChange)="updateColumns()">
    </p-multiSelect> -->




<p-overlayPanel #moveLocationPanel appendTo="body" [style]="{width: '500px', minHeight: '200px'}">
<div class="p-3">
<h5 class="mb-3">
  <i class="fa-solid fa-truck-container me-2"></i> Spostamento Pacchi
</h5>
@if (canMoveInBlockTo && canMoveInBlockTo.length > 0) {
  <!-- Summary of movements -->
  <div class="mb-4">
    <!-- From/To locations -->
    <div class="row mb-3">
      <div class="col-6">
        <label class="form-label fw-semibold">Da locazione:</label>
        <div class="p-2 bg-light rounded border">
          <i class="fa fa-map-marker-alt me-2 text-danger"></i>
          {{fromLocation?.name || 'N/A'}}
        </div>
      </div>
      <div class="col-6">
        <label class="form-label fw-semibold">A locazione:</label>
        <select class="form-select" [(ngModel)]="selectedDestinationLocation">
          <option value="" disabled>Seleziona destinazione</option>
          <option *ngFor="let loc of canMoveInBlockTo" [ngValue]="loc">
            {{loc.name}}
          </option>
        </select>
      </div>
    </div>

    <!-- Package list -->
    <div class="mb-3">
      <label class="form-label fw-semibold">Pacchi da spostare:</label>
      <div class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
        <div *ngFor="let pkg of getSelectedPackages(); let i = index" 
             class="d-flex justify-content-between align-items-center py-1 px-2 mb-1 bg-light rounded">
          <span>
            <i class="fa fa-cube me-2 text-primary"></i>
            {{pkg.Collo}}
          </span>
          <small class="text-muted">
            {{pkg.Locazione}}
          </small>
        </div>
      </div>
    </div>

    <!-- Confirm buttons -->
    <div class="d-flex justify-content-end gap-2">
      <button type="button" 
              class="btn btn-muted" 
              (click)="moveLocationPanel.hide()">
        <i class="fa fa-times me-1"></i>
        Annulla
      </button>
      <button type="button" 
              class="btn btn-primary text-white" 
              (click)="confirmLocationMove(); moveLocationPanel.hide()"
              [disabled]="!selectedDestinationLocation">
        <i class="fa fa-check me-1"></i>
        Conferma Spostamento
      </button>
    </div>
  </div>
} 
</div>
</p-overlayPanel>

<p-overlayPanel #sellFromListPanel appendTo="body" [style]="{width: '800px', minHeight: '400px'}">
  <div class="p-3">
    <h5 class="mb-3">
      <i class="fa fa-shopping-cart me-2"></i> 
      Vendita Colli selezionati
    </h5>
    
    <app-sell-from-list 
    *ngIf="showSellFromListComponent"
    [selectedQuantIds]="selectedQuantIds"
    (onComplete)="onSaleComplete($event)">
  </app-sell-from-list>
  </div>
</p-overlayPanel>

  <!-- tabella nodes wrapper -->
  <div class="flex-grow-1 d-flex flex-column overflow-hidden">

  <!-- Replace your existing p-table section with this updated version -->
  <!-- [selection]="selectedNodes" -->
<p-table 
  [value]="treenodes"
  [frozenValue]="selectedPackages"
  dataKey="key"
  selectionMode="checkbox"
  sortField="key" 
  sortMode="single" 
  rowGroupMode="subheader" 
  groupRowsBy="key" 
  [tableStyle]="{'min-width': '70rem'}"
  [resizableColumns]="true"
  [reorderableColumns]="true"
  [columns]="selectedColumns"
  [columnResizeMode]="'expand'"
  (onColReorder)="onColReorder($event)"
  [virtualScroll]="false" 
  showGridlines
  [expandedRowKeys]="expandedRows"
  scrollHeight="calc(100vh - 110px)"
>

<!-- Header template with totals -->
<ng-template #header let-columns>

  @if(selectedQuantIds.length > 0) {
    <tr class="bg-light  border-top border-success" style="position: sticky; bottom: -30px; z-index: 1000000 !important;">
      <th class="fw-semibold text-primary p-2 border-top  border-dark text-nowrap">
        <i class="fa fa-check-square me-2"></i>
        ({{selectedQuantIds.length}})
      </th>
      
      <th *ngFor="let col of selectedColumns; trackBy: trackByColumn" 
          class="p-2 fw-semibold text-primary border-top  border-dark text-nowrap">
        @if(shouldShowTotal(col)) {
          {{ makeSum(selectedQuantIds, col) }}
          <!-- {{ formatTotal(selectedQuantities[col.field] || 0, col) }} -->
        }
      </th>
    </tr>
  }


  <tr style="position: sticky; top: 0; z-index: 1;">
    <th></th>
    <th 
      pReorderableColumn
      pResizableColumn
      pSortableColumn
      *ngFor="let col of selectedColumns; trackBy: trackByColumn" 
      (click)="sort(col)" 
      class="p-1" 
      style="cursor: pointer;"
     >
      <span class="d-flex align-items-center text-nowrap">
        {{ col.label }}
        <i *ngIf="col.sortable" class="fa fa-sort ms-1 text-secondary" 
          [ngClass]="{'fa-sort-up text-primary': col.sort === 'asc', 'fa-sort-down text-primary': col.sort === 'desc'}"></i>
      </span>
    </th>
  </tr>

  <tr class="bg-light">
    <th>
      <p-tableHeaderCheckbox class="me-3" (click)="toggleAll()"></p-tableHeaderCheckbox>
      
      <span
        [ngClass]="{'text-success': hasSomeFilter()}"
         class="fa fa-filter "></span>
    </th>
    <th *ngFor="let col of selectedColumns; trackBy: trackByColumn" class="p-0  bg-white">
      <input type="text" pInputText class="form-control-sm border-0 m-0 w-100 bg-white"
        [(ngModel)]="col.searchInput" (keyup.enter)="refreshQuants()" />
    </th>
  </tr>

 
</ng-template>

<ng-template #frozenbody let-customer let-index="rowIndex">
    <tr class="font-bold">
      <td>
          <button pButton pRipple type="button" [icon]="'pi pi-lock-open'" (click)="toggleLock(customer, true, index)" size="small" text></button>
      </td>
    </tr>
  </ng-template>



<!-- Group header template (unchanged) -->
<ng-template #groupheader let-package let-rowIndex="rowIndex" let-expanded="expanded" >
  <tr>
    <td class="bg-light">
      <input 
        type="checkbox"
        [ngModel]=" package.selected"
        (change)="nodeSelect(package)"
        class="me-3 form-check-input"/>
      <!-- <p-tableCheckbox  styleClass="orange-checkbox"></p-tableCheckbox> -->
      <button
        type="button"
        [pRowToggler]="package"
        class="ms-3 btn btn-sm p-0"
        >
        <i class="fa fa-chevron-down" [ngClass]="expanded ? 'fa fa-chevron-down' : 'fa fa-chevron-right'"></i>
      </button>
    </td>
    <td *ngFor="let col of selectedColumns; trackBy: trackByColumn; let i = index;" 
        class="p-1 text-nowrap bg-light ">
      
      <div class="d-flex align-items-center gap-2">
        <!-- Column content -->
        <span>
          @if(col.type == 'number') {
            {{ getQuantValue4Table(package, col) | number:'1.0-2':'it-IT'}}
          } 
          @else if(col.type == 'currency') {
            {{ getQuantValue4Table(package, col) | currency:'EUR':'symbol':'1.1-1':'it-IT'}}
          }
          @else {   
            {{ getQuantValue4Table(package, col)}}
            @if(col.field === 'Collo') {
              <a
                target="_blank" 
                class="text-decoration-none text-dark fw-bold"
                title="Apri collo in Odoo"
                (click)="getPackageUrl(package)">
              <i class="fa fa-external-link ms-1 text-primary small"></i>
            </a>
            }
          }
        </span>     

        <!-- Photo button for Collo column when photos are enabled -->
        @if(col.field === 'Collo' && showPhotos && loadedPhotos) {
          <button 
            class="btn btn-link p-0 ms-1 d-flex align-items-center gap-1" 
            (click)="showPackagePhotos($event, package)"
            title="Mostra foto e messaggi"
            type="button"
            [disabled] = "getPackageFromQuants(package)?.package_id?.value?.message_attachment_count === 0 && getPackageFromQuants(package)?.package_id?.value?.message_ids?.ids?.length <= 1">
            
            <!-- Camera with count -->
            <span class="d-flex align-items-center">
              <i class="fa fa-camera me-1" 
                 [class.text-primary]="getPackageFromQuants(package)?.package_id?.value?.message_attachment_count > 0"
                 [class.text-info]="!getPackageFromQuants(package)?.package_id?.value?.message_attachment_count || getPackageFromQuants(package)?.package_id?.value?.message_attachment_count === 0"></i>
              @if(getPackageFromQuants(package)?.package_id?.value?.message_attachment_count > 0) {
                <small class="text-primary">{{getPackageFromQuants(package)?.package_id?.value?.message_attachment_count}}</small>
              }
            </span>
            
            <!-- Messages with count -->
            <span class="d-flex align-items-center ms-1">
              <i class="fa fa-envelope me-1"
                 [class.text-primary]="getPackageFromQuants(package)?.package_id?.value?.message_ids?.ids?.length > 1"
                 [class.text-info]="!getPackageFromQuants(package)?.package_id?.value?.message_ids?.ids?.length || getPackageFromQuants(package)?.package_id?.value?.message_ids?.ids?.length === 1"></i>
              @if(getPackageFromQuants(package)?.package_id?.value?.message_ids?.ids?.length > 1) {
                <small class="text-primary">{{getPackageFromQuants(package)?.package_id?.value?.message_ids?.ids?.length}}</small>
              }
            </span>
          </button>
        }
      </div>
    </td>
  </tr>
</ng-template>

<!-- Expanded row template (unchanged) -->
<ng-template #expandedrow let-line let-expanded="expanded">
  <tr *ngFor="let line of line.children">
    <td class="text-end">

      <input 
        type="checkbox"
        [ngModel]="selectedQuantIds.includes(line.data.id)"
        (change)="nodeSelect(line)"
        class="me-3 form-check-input"/>

      <!-- <p-tableCheckbox [value]="line" styleClass="orange-checkbox"></p-tableCheckbox> -->
    </td>
    <td *ngFor="let col of selectedColumns; trackBy: trackByColumn; let i = index;" 
        class="p-1 text-nowrap ">

      @if(editMode && col.editable && col.field === 'selectedPieces' && line.data.nodeType === 'quant'){
        <!-- Editable pieces input for quants in edit mode -->
        <input type="number" 
               class="form-control form-control-sm bg-transparent border-primary" 
               [value]="line.data.selectedPieces || ''" 
               (blur)="onPiecesChange(line.data.id, $any($event.target).value)"
               min="0"
               [max]="line.data.Pz || 999999"
               placeholder="Pz">
      } @else if(editMode && col.editable && col.field === 'destPackage' && line.data.nodeType === 'quant'){
        <!-- Editable destination package input for quants in edit mode -->
        <input type="text" 
               class="form-control form-control-sm bg-transparent border-primary" 
               [value]="line.data.destPackage || ''" 
               (blur)="onDestPackageChange(line.data.id, $any($event.target).value)"
               placeholder="Collo dest.">
      } @else if(col.type == 'number'){
        <span>{{ getQuantValue4Table(line, col) | number:'1.0-2':'it-IT'}}</span>
      } 
      @else if(col.type == 'currency'){
        <span>{{ getQuantValue4Table(line, col) | currency:'EUR':'symbol':'1.1-1':'it-IT'}}</span>
      }
      @else {
        {{ getQuantValue4Table(line, col)}}
      }
    </td>
  </tr>
</ng-template>

<ng-template pTemplate="footer" #footer style="position: sticky; bottom: -30px; z-index: 1000000 !important;">
   <!-- Total quantities row -->
 
  
  <!-- Selected quantities row (only show if there are selections) -->
  <!-- @if(selectedQuantIds.length > 0) {
    <tr class="bg-light  border-top border-success" style="position: sticky; bottom: -30px; z-index: 1000000 !important;">
      <th class="fw-semibold text-primary p-2 border-top  border-dark text-nowrap">
        <i class="fa fa-check-square me-2"></i>
        ({{selectedQuantIds.length}})
      </th>
      <th *ngFor="let col of selectedColumns; trackBy: trackByColumn" 
          class="p-2 fw-semibold text-primary border-top  border-dark text-nowrap">
        @if(shouldShowTotal(col)) {
          {{ formatTotal(selectedQuantities[col.field] || 0, col) }}
        }
      </th>
    </tr>
  } -->


   <tr class="bg-light border-top border-2 border-primary" style="position: sticky; bottom: -30px; z-index: 1000000 !important;">
    <th class=" text-secondary p-2 border-top  border-dark text-nowrap">
      Tot
    </th>
    <th *ngFor="let col of selectedColumns; trackBy: trackByColumn" 
        class="p-2 fw-semibold text-secondary  border-top  border-dark">
      @if(shouldShowTotal(col)) {
        {{ formatTotal(totalQuantities[col.field] || 0, col) }}
      }
    </th>
  </tr>


</ng-template>


</p-table>

  </div>
</div>



<!-- OUTSIDE the ngFor, once per table (at root of your component template) -->
<p-overlayPanel #photosPanel [showCloseIcon]="true" [dismissable]="true" [style]="{width: '400px', minHeight: '100px'}">
  <ng-container *ngIf="showingQuantPhotos">
    <app-packs-photos 
      [from]="'package'" 
      [singleQuant]="showingQuantPhotos"
      style="display: block;">
    </app-packs-photos>
  </ng-container>
</p-overlayPanel>
