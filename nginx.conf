server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Add proper MIME type for JavaScript modules
    location ~ \.m?js$ {
        types { application/javascript js; }
        types { application/javascript mjs; }
        add_header X-Content-Type-Options nosniff;
    }

    include /etc/nginx/mime.types;

    types {
        application/javascript js mjs;
        application/json json;
        text/css css;
    }

    # Proxy for Odoo API - remove /api prefix and forward to Odoo server
    location /api/ {
        rewrite ^/api/(.*) /$1 break;
        proxy_pass https://*************/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Serve index.html for Angular routing
    location / {
        try_files $uri $uri/ /index.html;
    }
}
