import { Component, OnInit } from '@angular/core';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';


@Component({
  selector: 'app-portfolio',
  templateUrl: './portfolio.component.html',
  standalone: false,
})

export class PortfolioComponent implements OnInit {
  loading = false;
  selectedTab: 'settori' | 'vendite' | 'storico' = 'settori';
  
  constructor(private odooEM: OdooEntityManager) {}

  async ngOnInit() {

  }

  selectTab(tab: 'settori' | 'vendite' | 'storico') {
    this.selectedTab = tab;
  }
}