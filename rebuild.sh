#!/bin/bash

# Log the webhook trigger
echo "Webhook triggered at $(date)"

# Navigate to your project directory
cd /path/to/your/project

# Pull the latest changes from Git
# This is assuming you're using the webhook outside the container

# Rebuild the Docker container with a new cache-busting value
CACHEBUST=$(date +%s)
docker-compose build --no-cache

# Restart the container
docker-compose up -d

echo "Rebuild completed at $(date)"
