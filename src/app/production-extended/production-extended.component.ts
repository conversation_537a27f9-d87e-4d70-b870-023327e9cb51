import { Component, OnInit, OnDestroy } from '@angular/core';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { firstValueFrom } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { MrpProduction } from '../models/mrp-production';
import { ProductTemplate } from '../models/product.template.model';
import { StockMove } from '../models/stock-move';
import { SaleOrder } from '../models/sale-order.model';
import { Title } from '@angular/platform-browser';

export interface Worker {
  name: string;
  hours: number;
}

@Component({
  selector: 'app-production-extended',
  standalone: false,
  templateUrl: './production-extended.component.html',
  styleUrls: ['./production-extended.component.scss']
})
export class ProductionExtendedComponent implements OnInit, OnDestroy {

  // Core production data
  id: number = 0;
  production: MrpProduction;
  
  // UI state
  loading: boolean = false;
  activeTab: string = 'input';
  
  // Template search
  openTmplSearch: boolean = false;
  templateSearchTerm: string = '';
  templates: ProductTemplate[] = [];
  searchingTemplates: boolean = false;

  // Production statistics
  mainProduct: string = '';
  nonMainTotal = 0;
  mainTotal = 0;
  mainQty = 0;
  mainUnitCost = 0;
  totalProductionCost: number = 0;

  // Origin handling
  sales: SaleOrder[] = [];
  editingOrigin: boolean = false;
  tempOrigin: string = '';
  preproduction: any = null;
  imballaggio: MrpProduction;

  constructor(
    private odooEM: OdooEntityManager,
    private router: Router,
    private route: ActivatedRoute,
    private titleService: Title
  ) {}

  async ngOnInit() {
    console.log('[DEBUG] Initializing production-extended component');
    this.loading = true;

    // Subscribe to params and get the production id
    this.route.params.subscribe(async (params) => {
      this.id = params["production_id"];
      await this.reloadProduction();

      // Handle tab from URL hash
      const hash = window.location.hash.substring(1);
      if (hash && ['input', 'workers', 'output'].includes(hash)) {
        this.activeTab = hash;
      }
      
      this.loading = false;
    });
  }

  ngOnDestroy(): void {
    // Component cleanup if needed
  }

  /**
   * Reload the production data from the server
   */
  async reloadProduction(): Promise<void> {
    console.log('[DEBUG] Reloading production with ID:', this.id);

    if (!this.id) {
      console.error('[ERROR] Cannot reload production: No production ID');
      return;
    }

    try {
      // Search production by id
      const prods = await firstValueFrom(this.odooEM.search<MrpProduction>(
        new MrpProduction(), [["id", "=", this.id]]
      ));

      console.log('[DEBUG] Production found:', prods);

      if (prods.length > 0) {
        
        await firstValueFrom(this.odooEM.resolve(prods[0].move_raw_ids));
        await firstValueFrom(this.odooEM.resolve(prods[0].move_byproduct_ids));
        await firstValueFrom(this.odooEM.resolve(prods[0].move_finished_ids));
        
        // search for a production with a name like my origin
        this.imballaggio = await this.checkImballaggio(prods[0]);
        
        // Load related sale orders if connected
        if (this.imballaggio) {
          await this.loadRelatedSaleOrders([this.imballaggio]);
          console.log("SALES ", this.sales)
        }
        // this.preproduction = preprod;
        this.production = prods[0];

        this.titleService.setTitle(`${this.production.name.replace('LOM/', '')}`);

        console.log('[DEBUG] Production loaded with byproducts:',
          this.production.move_byproduct_ids.values?.length,
          'and finished products:',
          this.production.move_finished_ids.values?.length);
      } else {
        console.error('[ERROR] Production not found with ID:', this.id);
      }
    } catch (error) {
      console.error('[ERROR] Failed to reload production:', error);
    }
  }
  
  
  async checkImballaggio(prod:MrpProduction)  {
    // search preproduction it's a production with origin == final_prod
    let imballaggio = (await firstValueFrom(this.odooEM.search<MrpProduction>(new MrpProduction(), [["name", "=", prod.origin]])))[0];
    if (!imballaggio) return;

    // console.log("PREPRODUCTION", preproduction);
    return imballaggio;
  }

  /**
   * Load related sale orders for productions that have origins starting with 'V'
   */
  async loadRelatedSaleOrders(productions: MrpProduction[]): Promise<void> {
    console.log("LOAD RELATED SALE ORDERS", productions);
    // Get related sale orders
    const originValues = productions
      .map(p => p.origin)
      .filter(origin => origin && origin.startsWith('V')); // Only get sale orders starting with V

    if (originValues.length > 0) {
      console.log(`Fetching ${originValues.length} related sale orders`);
      this.sales = await firstValueFrom(
        this.odooEM.search<SaleOrder>(
          new SaleOrder(), 
          [['name', 'in', originValues]]
        )
      );
    }
  }

  /**
   * Get the sale order related to a production
   */
  getSaleOrder(production: MrpProduction): SaleOrder | undefined {
    if (!production.origin) return undefined;
    return this.sales.find(sale => sale.name === production.origin);
  }

  /**
   * Check if production is connected to a sale order
   */
  isConnectedToSaleOrder(): boolean {
    return this.sales.length > 0;
  }

  /**
   * Start editing the origin field
   */
  startEditingOrigin(): void {
    if (this.isConnectedToSaleOrder()) return; // Cannot edit if connected to sale order
    
    this.editingOrigin = true;
    this.tempOrigin = this.production?.origin || '';
  }

  /**
   * Cancel editing the origin field
   */
  cancelEditingOrigin(): void {
    this.editingOrigin = false;
    this.tempOrigin = '';
  }

  /**
   * Save the origin field changes
   */
  async saveOrigin(value): Promise<void> {
    if (!this.production || this.isConnectedToSaleOrder()) return;

    try {
      this.loading = true;
      
      // Update the production origin
      await firstValueFrom(
        this.odooEM.update<MrpProduction>(this.production, {
          origin: value
        })
      );

      // Update local model
      this.production.origin = value

    } finally {
      this.loading = false;
    }
  }

  /**
   * Handle Enter key press in origin input
   */
  // onOriginKeyPress(event: KeyboardEvent): void {
  //   if (event.key === 'Enter') {
  //     this.saveOrigin();
  //   } else if (event.key === 'Escape') {
  //     this.cancelEditingOrigin();
  //   }
  // }

  handleTabChange(value: any) {
    console.log('Tab cambiato:', value);
    this.activeTab = value;
    this.router.navigate([], { 
      fragment: value,
      replaceUrl: true
    });
    window.location.hash = '#' + this.activeTab;
  }

  // Template search methods
  toggleTmplSearch(): void {
    this.openTmplSearch = !this.openTmplSearch;
  }

  async searchTemplates() {
    this.searchingTemplates = true;
    this.templates = await firstValueFrom(this.odooEM.search<ProductTemplate>(
      new ProductTemplate(), [["name", "ilike", this.templateSearchTerm]]
    ));
    console.log('Found templates:', this.templates);
    this.searchingTemplates = false;
  }

  async handleSelectedTemplate(template: ProductTemplate) {
    console.log('Selected template:', template);
    this.templateSearchTerm = '';
    this.openTmplSearch = false;
    // Add template handling logic here
  }

  // Production statistics methods
  getStatusBadgeClass(): string {
    if (!this.production) return 'bg-secondary';

    switch (this.production.state) {
      case 'draft':
        return 'bg-secondary';
      case 'confirmed':
        return 'bg-primary';
      case 'planned':
        return 'bg-info';
      case 'progress':
        return 'bg-warning';
      case 'done':
        return 'bg-success';
      case 'cancel':
        return 'bg-danger';
      default:
        return 'bg-secondary';
    }
  }

  // Calculate total input cost
  calculateInputCost(): number {
    // Implement based on your production data
    return 0;
  }

  // Calculate total worker hours
  calculateTotalHours(): number {
    // Implement based on your production data
    return 0;
  }

  // Calculate total labor cost (assuming a hourly rate of 25€)
  calculateLaborCost(): number {
    const hourlyRate = 25;
    return this.calculateTotalHours() * hourlyRate;
  }

  // Method to calculate total production cost
  calculateTotalProductionCost(): void {
    const inputCost = this.calculateInputCost();
    const laborCost = this.calculateLaborCost();
    this.totalProductionCost = inputCost + laborCost;
  }

  // Event handlers for child components
  togglePacklist(): void {
    // Implement packlist toggle logic
  }

  onLoadingChange(loading: boolean): void {
    this.loading = loading;
  }

  // Calculate total output value
  calculateOutputValue(): number {
    // Implement based on your production data
    return 0;
  }

  // Count methods for summary display
  getInputNumber(): number {
    if (!this.production?.move_raw_ids?.values) return 0;
    return this.production.move_raw_ids.values.length;
  }

  getWorkersNumber(): number {
    if (!this.production?.workorder_ids?.values) return 0;
    return this.production.workorder_ids.values.length;
  }

  getOutputNumber(): number {
    if (!this.production) return 0;
    const finishedCount = this.production.move_finished_ids?.values?.length || 0;
    const byproductCount = this.production.move_byproduct_ids?.values?.length || 0;
    return finishedCount + byproductCount;
  }

  // Production validation methods
  canConfirmProduction(): boolean {
    if (!this.production) return false;
    
    // Check if production has input materials (move_raw_ids)
    const hasInput = this.production.move_raw_ids?.values?.length > 0;
    
    // Check if production has output products (move_finished_ids or move_byproduct_ids)
    const hasOutput = (this.production.move_finished_ids?.values?.length > 0) || 
                     (this.production.move_byproduct_ids?.values?.length > 0);
    
    return hasInput && hasOutput;
  }

  canCompleteProduction(): boolean {
    if (!this.production) return false;
    
    // Can complete if production is confirmed, planned, or in progress
    return ['confirmed', 'planned', 'progress', 'to_close'].includes(this.production.state);
  }

  getValidationMessage(): string {
    if (!this.production) return 'Produzione non trovata';
    
    const hasInput = this.production.move_raw_ids?.values?.length > 0;
    const hasOutput = (this.production.move_finished_ids?.values?.length > 0) || 
                     (this.production.move_byproduct_ids?.values?.length > 0);
    
    if (!hasInput && !hasOutput) {
      return 'La produzione deve avere almeno un componente in input e un prodotto in output';
    }
    if (!hasInput) {
      return 'La produzione deve avere almeno un componente in input';
    }
    if (!hasOutput) {
      return 'La produzione deve avere almeno un prodotto in output';
    }
    
    return '';
  }

  // Production action methods
  async confirmProduction(): Promise<void> {
    if (!confirm("Stai per confermare la produzione. \n Procedi?")) return;
    if (!this.production || this.production.state !== 'draft') return;
    
    try {
      this.loading = true;
      await this.odooEM.call2(new MrpProduction().ODOO_MODEL, "action_confirm", [[this.production.id]]);
      await this.reloadProduction();
      console.log('[DEBUG] Production confirmed successfully');
    } catch (error) {
      console.error('[ERROR] Failed to confirm production:', error);
      // You might want to show a user-friendly error message here
    } finally {
      this.loading = false;
    }
  }

  getOrigin(production: MrpProduction): string {
    if (this.sales.length) {
      let sale = this.sales[0];
      if (sale) {
        return `${production.origin} - ${sale.partner_id.name}`;
      }
    } else 
      return production.origin || '';
  }


  // Production status methods
  getProductionReadiness(production: MrpProduction): string {
    if (!production) return 'N/D';
    const componentsState = production.components_availability_state || '';
    if (componentsState === 'late')
      return 'In ritardo';
    if (componentsState === 'expected') 
      return 'In arrivo';
    if (componentsState === 'available') 
      return 'Disponibile';
    if (componentsState === '')
      return 'Non disponibile';
    return 'N/D';
  }

  getProductionReadinessIcon(production: MrpProduction): string {
    if (!production) return 'fa-solid fa-question';
    const componentsState = production.components_availability_state || '';
    if (componentsState === 'late') 
      return 'fa-solid fa-alarm-exclamation';
    if (componentsState === 'expected') 
      return 'fa-solid fa-truck';
    if (componentsState === 'available')
      return 'fa-solid fa-check';
    if (componentsState === '')
      return 'fa-solid fa-xmark';
    return 'fa-solid fa-question';
  }

  getProductionReadinessColor(production: MrpProduction): string {
    if (!production) return 'text-muted';
    const componentsState = production.components_availability_state || '';
    if (componentsState === 'late') 
      return 'text-warning';
    if (componentsState === 'expected') 
      return 'text-primary';
    if (componentsState === 'available')
      return 'text-success';
    if (componentsState === '')
      return 'text-danger';
    return 'text-muted';
  }

  getStatusClass(state: string): string {
    if (!state) return 'text-secondary';
    switch (state) {
      case 'draft': return '';
      case 'confirmed': return 'text-secondary';
      case 'to_close': return 'text-primary';
      case 'planned': return 'text-warning';
      case 'progress': return 'text-warning';
      case 'done': return 'text-success';
      case 'cancel': return 'text-muted';
      default: return 'text-secondary';
    }
  }

  getStateLabel(state: string): string {
    if (!state) return 'N/D';
    switch (state) {
      case 'draft': return 'Bozza';
      case 'confirmed': return 'Confermato';
      case 'planned': return 'Pianificato';
      case 'progress': return 'In Corso';
      case 'to_close': return 'Da Chiudere';
      case 'done': return 'Completato';
      case 'cancel': return 'Annullato';
      default: return state;
    }
  }

  getComponentAvailabilityLabel(state: string): string {
    if (!state) return 'N/D';
    switch (state) {
      case 'available': return 'Disponibile';
      case 'expected': return 'In arrivo';
      case 'late': return 'In ritardo';
      case 'unavailable': return 'Non disponibile';
      default: return state || 'N/D';
    }
  }

  // Off-canvas control methods
  openProductionPanel(): void {
    const offcanvasElement = document.getElementById('productionControlPanel');
    if (offcanvasElement) {
      const offcanvas = new (window as any).bootstrap.Offcanvas(offcanvasElement);
      offcanvas.show();
    }
  }

  closeProductionPanel(): void {
    const offcanvasElement = document.getElementById('productionControlPanel');
    if (offcanvasElement) {
      const offcanvas = new (window as any).bootstrap.Offcanvas(offcanvasElement);
      offcanvas.hide();
    }
  }
}