:host {
    display: block;
    width: 100%;
  }
  .table-container {
    width: 100%;
    border: 1px solid #dee2e6;
  }
  .table {
    width: 100%;
    margin-bottom: 0;
    border-collapse: collapse;
  }
  th {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 4px 8px;
    font-weight: 500;
    font-size: 0.9rem;
    white-space: nowrap;
  }
  td {
    border: 1px solid #dee2e6;
    padding: 0;
    height: 32px;
  }
  .form-control, .form-select {
    border: none;
    border-radius: 0;
    height: 100%;
    padding: 4px 8px;
    background: none;
    box-shadow: none !important;
  }
  .form-select {
    background-image: none;
    cursor: pointer;
  }
  .number-cell {
    text-align: right;
  }
  /* Editable cells styles */
  .editable-cell {
    background-color: #fff;
  }
  .dimensional-cell {
    background-color: #f0f7ff;  /* Light blue background */
  }
  .selection-cell {
    background-color: #fff3e6;  /* Light orange background */
  }
  /* Calculated cells */
  .calculated-cell {
    background-color: #f8f9fa;  /* Light gray background */
    color: #6c757d;  /* Slightly muted text */
    cursor: default;
  }
  .calculated-cell:focus {
    background-color: #f8f9fa;
  }
  /* Remove spinner from number inputs */
  input[type=number]::-webkit-inner-spin-button, 
  input[type=number]::-webkit-outer-spin-button { 
    -webkit-appearance: none; 
    margin: 0; 
  }
  input[type=number] {
    -moz-appearance: textfield;
  }
  /* Hover states */
  .editable-cell:hover {
    background-color: #f8f9fa;
  }
  .dimensional-cell:hover {
    background-color: #e6f0ff;
  }
  .selection-cell:hover {
    background-color: #ffe6cc;
  }
  .plus-button {
    border: none;
    background: none;
    color: #0d6efd;
    padding: 0 8px;
  }
  .plus-button:hover {
    color: #0a58ca;
  }