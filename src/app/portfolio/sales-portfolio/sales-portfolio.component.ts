import { Component, OnInit } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { TreeNode } from 'primeng/api';

import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { Lead } from 'src/app/models/crm.lead.model';
import { AccountMove } from 'src/app/models/account-move.model';
import { ODOO_IDS } from 'src/app/models/deal';

interface SalesTrend {
  direction: 'up' | 'down' | 'stable';
  percentage: number;                // abs change %
  currentPeriodValue: number;        // sum over last 3 months
  previousPeriodValue: number;       // sum same window last year
  difference: number;                // currentPeriodValue - previousPeriodValue
  currentBreakdown: Array<{ month: string; value: number }>;
  previousBreakdown: Array<{ month: string; value: number }>;
  noHistoricalData?: boolean;        // Flag per indicare l'assenza di dati storici
}

interface SalesRow {
  label: string;
  sentCount: number;
  sentValue: number;
  sentConfirmedCount: number;
  sentConfirmedValue: number;
  approvedCount: number;
  approvedValue: number;
  sentTrend?: SalesTrend;
  approvedTrend?: SalesTrend;
}

@Component({
  selector: 'app-portfolio-sales',
  templateUrl: './sales-portfolio.component.html',
  styleUrls: ['./sales-portfolio.component.scss'],
  standalone: false,
})
export class PortfolioSalesComponent implements OnInit {
  loading = false;
  nodes: TreeNode<SalesRow>[] = [];

  allLeads: Lead[] = [];
  filteredLeads: Lead[] = [];
  sectorOptions: string[] = [];
  userOptions: string[] = [];
  selectedSector: string | null = null;
  selectedUser: string | null = null;

  contracts: AccountMove[] = [];
  private monthNames = ['Gen','Feb','Mar','Apr','Mag','Giu','Lug','Ago','Set','Ott','Nov','Dic'];
  private monthMap = new Map<string, TreeNode<SalesRow>>();

  constructor(private odooEM: OdooEntityManager) {}

  async ngOnInit() {
    this.loading = true;
    try {
      await this.loadData();
      this.filteredLeads = [...this.allLeads];
      this.initializeFilterOptions();
      this.generateTreeNodes();
      this.annotateTrends();
    } catch (err) {
      console.error('Init error', err);
    } finally {
      this.loading = false;
    }
  }

  private async loadData() {
    // 1) fetch leads that have ga_sent_date and area != 'Aziendale'
    this.allLeads = await firstValueFrom(
      this.odooEM.search<Lead>(
        new Lead(),
        [['ga_sent_date','!=',null], ['area','!=','Aziendale']],
        5000
      )
    );
    // 2) fetch all contract invoices
    this.contracts = await firstValueFrom(
      this.odooEM.search<AccountMove>(
        new AccountMove(),
        [['journal_id','=', ODOO_IDS.contratti_id]],
        5000
      )
    );
    // 3) compute total contract amount per lead
    this.allLeads.forEach(lead => {
      lead._totalContractAmount = this.contracts
        .filter(c => c.invoice_origin === lead.tracking_code)
        .reduce((sum, c) => sum + (c.amount_total||0), 0);
    });
  }

  private initializeFilterOptions() {
    this.sectorOptions = Array.from(new Set(this.allLeads.filter(l => l.area).map(l => l.area))).sort();
    this.userOptions = Array.from(new Set(this.allLeads.filter(l => l.user_id && l.user_id.name).map(l => l.user_id.name))).sort();
  }

  onSectorChange() {
    // Ensure user filter is cleared when sector is selected
    this.selectedUser = null;
    this.applyFilters();
  }

  onUserChange() {
    // Ensure sector filter is cleared when user is selected
    if (this.selectedUser !== null) {
      this.selectedSector = null;
    }
    this.applyFilters();
  }

  private applyFilters() {
    // Reset to all leads first
    this.filteredLeads = [...this.allLeads];
    
    // Apply sector filter if selected
    if (this.selectedSector) {
      this.filteredLeads = this.filteredLeads.filter(
        lead => lead.area === this.selectedSector
      );
    }
    
    // Apply user filter if selected
    if (this.selectedUser) {
      this.filteredLeads = this.filteredLeads.filter(
        lead => lead.user_id && lead.user_id.name === this.selectedUser
      );
    }
    
    // Regenerate tree nodes with filtered data and recalculate trends
    this.generateTreeNodes();
    this.annotateTrends();
  }

  resetFilters() {
    this.selectedSector = null;
    this.selectedUser = null;
    this.filteredLeads = [...this.allLeads];
    this.generateTreeNodes();
    this.annotateTrends();
  }

  private generateTreeNodes() {
    const now = new Date();
    const keys: string[] = [];
    for (let y = 2023; y <= now.getFullYear(); y++) {
      const maxM = (y === now.getFullYear() ? now.getMonth()+1 : 12);
      for (let m = 1; m <= maxM; m++) {
        keys.push(`${y}-${m}`);
      }
    }
    keys.sort((a,b)=>{
      const [y1,m1]=a.split('-').map(Number), [y2,m2]=b.split('-').map(Number);
      return y2!==y1 ? y2-y1 : m2-m1;
    });
  
    this.nodes = keys.map(k => {
      const [year, month] = k.split('-').map(Number);
      const monthLabel = `${this.monthNames[month-1]} ${year}`;
  
      // filter leads in this month
      const inMonth = this.filteredLeads.filter(l =>
        (l.ga_sent_date && this.isInMonth(l.ga_sent_date, year, month)) ||
        (l.ga_approved_date && this.isInMonth(l.ga_approved_date, year, month))
      );
  
      const emptyRow: SalesRow = {
        label: monthLabel,
        sentCount: 0, sentValue: 0,
        sentConfirmedCount: 0, sentConfirmedValue: 0,
        approvedCount: 0, approvedValue: 0
      };
      
      if (!inMonth.length) {
        return { data: emptyRow, children: [] };
      }
  
      // ─── month aggregates ───
      const sent = inMonth.filter(l => l.ga_sent_date && this.isInMonth(l.ga_sent_date, year, month));
      const sentCount = sent.length;
      const sentValue = sent.reduce((s, l) => s + (l.ga_sent_amount || 0), 0);
      const sentConf = sent.filter(l => [3, 4].includes(l.stage_id.id));
      const sentConfirmedCount = sentConf.length;
      const sentConfirmedValue = sentConf.reduce((s, l) => s + (l.ga_sent_amount || 0), 0);
  
      const appr = inMonth.filter(l => l.ga_approved_date && this.isInMonth(l.ga_approved_date, year, month));
      const approvedCount = appr.length;
      const approvedValue = appr.reduce((s, l) => s + (l._totalContractAmount || 0), 0);
  
      let children: TreeNode<SalesRow>[] = [];
  
      // Se è selezionato un settore, non creare nodi settore, vai direttamente agli utenti
      if (this.selectedSector) {
        // Filtra solo gli utenti di questo settore
        const usersByName = new Map<string, Lead[]>();
        
        inMonth.forEach(l => {
          if (!l.user_id || !l.user_id.name) return;
          const userName = l.user_id.name;
          if (!usersByName.has(userName)) {
            usersByName.set(userName, []);
          }
          usersByName.get(userName)!.push(l);
        });
  
        // Crea direttamente i nodi degli utenti sotto il mese
        children = Array.from(usersByName.entries())
          .sort(([userA], [userB]) => userA.localeCompare(userB))
          .map(([user, userLeads]) => {
            const uSent = userLeads.filter(l => l.ga_sent_date && this.isInMonth(l.ga_sent_date, year, month));
            const uSentCount = uSent.length;
            const uSentValue = uSent.reduce((s, l) => s + (l.ga_sent_amount || 0), 0);
            const uSentConf = uSent.filter(l => [3, 4].includes(l.stage_id.id));
            const uSentConfirmedCount = uSentConf.length;
            const uSentConfirmedValue = uSentConf.reduce((s, l) => s + (l.ga_sent_amount || 0), 0);
  
            const uAppr = userLeads.filter(l => l.ga_approved_date && this.isInMonth(l.ga_approved_date, year, month));
            const uApprovedCount = uAppr.length;
            const uApprovedValue = uAppr.reduce((s, l) => s + (l._totalContractAmount || 0), 0);
  
            return {
              data: {
                label: user,
                sentCount: uSentCount,
                sentValue: uSentValue,
                sentConfirmedCount: uSentConfirmedCount,
                sentConfirmedValue: uSentConfirmedValue,
                approvedCount: uApprovedCount,
                approvedValue: uApprovedValue
              }
            };
          });
      } 
      // Altrimenti, struttura normale settori -> utenti
      else if (!this.selectedUser) {
        // ─── group by area → user ───
        const byArea = new Map<string, Lead[]>();
        inMonth.forEach(l => {
          if (!l.area) return;
          if (!byArea.has(l.area)) {
            byArea.set(l.area, []);
          }
          byArea.get(l.area)!.push(l);
        });
  
        children = Array.from(byArea.entries())
          .sort(([areaA], [areaB]) => areaA.localeCompare(areaB))
          .map(([area, areaLeads]) => {
            const aSent = areaLeads.filter(l => l.ga_sent_date && this.isInMonth(l.ga_sent_date, year, month));
            const aSentCount = aSent.length;
            const aSentValue = aSent.reduce((s, l) => s + (l.ga_sent_amount || 0), 0);
            const aSentConf = aSent.filter(l => [3, 4].includes(l.stage_id.id));
            const aSentConfirmedCount = aSentConf.length;
            const aSentConfirmedValue = aSentConf.reduce((s, l) => s + (l.ga_sent_amount || 0), 0);
  
            const aAppr = areaLeads.filter(l => l.ga_approved_date && this.isInMonth(l.ga_approved_date, year, month));
            const aApprovedCount = aAppr.length;
            const aApprovedValue = aAppr.reduce((s, l) => s + (l._totalContractAmount || 0), 0);
  
            const byUser = new Map<string, Lead[]>();
            areaLeads.forEach(l => {
              if (!l.user_id || !l.user_id.name) return;
              const userName = l.user_id.name;
              if (!byUser.has(userName)) {
                byUser.set(userName, []);
              }
              byUser.get(userName)!.push(l);
            });
  
            const userNodes: TreeNode<SalesRow>[] = Array.from(byUser.entries())
              .sort(([userA], [userB]) => userA.localeCompare(userB))
              .map(([user, userLeads]) => {
                const uSent = userLeads.filter(l => l.ga_sent_date && this.isInMonth(l.ga_sent_date, year, month));
                const uSentCount = uSent.length;
                const uSentValue = uSent.reduce((s, l) => s + (l.ga_sent_amount || 0), 0);
                const uSentConf = uSent.filter(l => [3, 4].includes(l.stage_id.id));
                const uSentConfirmedCount = uSentConf.length;
                const uSentConfirmedValue = uSentConf.reduce((s, l) => s + (l.ga_sent_amount || 0), 0);
  
                const uAppr = userLeads.filter(l => l.ga_approved_date && this.isInMonth(l.ga_approved_date, year, month));
                const uApprovedCount = uAppr.length;
                const uApprovedValue = uAppr.reduce((s, l) => s + (l._totalContractAmount || 0), 0);
  
                return {
                  data: {
                    label: user,
                    sentCount: uSentCount,
                    sentValue: uSentValue,
                    sentConfirmedCount: uSentConfirmedCount,
                    sentConfirmedValue: uSentConfirmedValue,
                    approvedCount: uApprovedCount,
                    approvedValue: uApprovedValue
                  }
                };
              });
  
            return {
              data: {
                label: area,
                sentCount: aSentCount,
                sentValue: aSentValue,
                sentConfirmedCount: aSentConfirmedCount,
                sentConfirmedValue: aSentConfirmedValue,
                approvedCount: aApprovedCount,
                approvedValue: aApprovedValue
              },
              children: userNodes
            };
          });
      }
      // Se è selezionato un utente specifico, non aggiungere figli
  
      // month node
      return {
        data: {
          label: monthLabel,
          sentCount,
          sentValue,
          sentConfirmedCount,
          sentConfirmedValue,
          approvedCount,
          approvedValue
        },
        children
      };
    });
  
    // Aggiorna monthMap
    this.monthMap.clear();
    this.nodes.forEach(mn => this.monthMap.set(mn.data.label, mn));
  }
  private annotateTrends() {
    // Assicurati che monthMap sia aggiornato
    this.monthMap.clear();
    this.nodes.forEach(mn => this.monthMap.set(mn.data.label, mn));

    this.nodes.forEach(monthNode => {
      const [mon, yr] = monthNode.data.label.split(' ');
      const year = +yr;
      const month = this.monthNames.indexOf(mon) + 1;

      // month row
      monthNode.data.sentTrend = this.buildTrend(monthNode.data.label, year, month, 'sentValue');
      monthNode.data.approvedTrend = this.buildTrend(monthNode.data.label, year, month, 'approvedValue');

      // area & user
      monthNode.children?.forEach(areaNode => {
        areaNode.data.sentTrend = this.buildTrend(areaNode.data.label, year, month, 'sentValue');
        areaNode.data.approvedTrend = this.buildTrend(areaNode.data.label, year, month, 'approvedValue');
        
        areaNode.children?.forEach(userNode => {
          userNode.data.sentTrend = this.buildTrend(userNode.data.label, year, month, 'sentValue');
          userNode.data.approvedTrend = this.buildTrend(userNode.data.label, year, month, 'approvedValue');
        });
      });
    });
  }
  private buildTrend(
    entityLabel: string,
    year: number,
    month: number,
    metric: 'sentValue' | 'approvedValue'
  ): SalesTrend {
    const winCurr = this.get3MonthWindow(year, month);
    const winPrev = this.get3MonthWindow(year - 1, month);
  
    const lookup = (y: number, m: number) => {
      const monthLabel = `${this.monthNames[m - 1]} ${y}`;
      const mn = this.monthMap.get(monthLabel);
      if (!mn) return 0;
  
      // Se l'entità è un mese (contiene spazio e un numero dell'anno)
      if (entityLabel.includes(' ') && /\d{4}/.test(entityLabel)) {
        // Se è lo stesso mese che stiamo cercando
        if (entityLabel === monthLabel) {
          return mn.data[metric];
        }
        // Altrimenti è un altro mese, ma stiamo cercando un mese
        // Dobbiamo verificare se stiamo cercando dati per il trend di un mese
        const isMonthEntity = this.monthNames.some(mnth => entityLabel.includes(mnth));
        if (isMonthEntity) {
          // Cerca il mese esatto nel monthMap
          const targetMonth = this.monthMap.get(monthLabel);
          if (targetMonth) {
            return targetMonth.data[metric];
          }
        }
      }
  
      // Se l'entità è il mese corrente (caso di lookup sullo stesso mese)
      if (entityLabel === mn.data.label) {
        return mn.data[metric];
      }
  
      // Se l'entità è un'area (escluso i mesi che contengono spazi)
      if (!entityLabel.includes(' ')) {
        const area = mn.children?.find(a => a.data.label === entityLabel);
        if (area) {
          return area.data[metric];
        }
      }
  
      // Se l'entità è un utente (cerca in tutte le aree)
      for (const a of mn.children || []) {
        if (a.data.label === entityLabel) {
          return a.data[metric];
        }
        
        const user = a.children?.find(u => u.data.label === entityLabel);
        if (user) {
          return user.data[metric];
        }
      }
  
      // Se abbiamo filtrato per settore, gli utenti sono direttamente sotto i mesi
      if (this.selectedSector) {
        const user = mn.children?.find(u => u.data.label === entityLabel);
        if (user) {
          return user.data[metric];
        }
      }
  
      return 0;
    };
  
    // build breakdown arrays
    const currentBreakdown = winCurr.map(({ year: y, month: m }) => ({
      month: `${this.monthNames[m - 1]} ${y}`,
      value: lookup(y, m)
    }));
  
    const previousBreakdown = winPrev.map(({ year: y, month: m }) => ({
      month: `${this.monthNames[m - 1]} ${y}`,
      value: lookup(y, m)
    }));
  
    const currSum = currentBreakdown.reduce((s, x) => s + x.value, 0);
    const prevSum = previousBreakdown.reduce((s, x) => s + x.value, 0);
    const diff = currSum - prevSum;
  
    // Verifica se non ci sono dati per l'intero periodo precedente
    const hasPreviousData = previousBreakdown.some(item => item.value > 0);
    
    // Se non ci sono dati nel periodo precedente, imposta un trend "undefined". questo solo per i settori treenodes
    if (!hasPreviousData && this.sectorOptions.includes(entityLabel)) {
      return {
        direction: 'stable', // Usiamo 'stable' ma lo mostreremo in modo diverso nell'UI
        percentage: 0,
        currentPeriodValue: currSum,
        previousPeriodValue: 0,
        difference: currSum,
        currentBreakdown,
        previousBreakdown,
        // Aggiungi una flag per indicare che non ci sono dati precedenti
        noHistoricalData: true
      };
    }
  
    // Calcolo normale del trend quando ci sono dati precedenti
    let pct = 0;
    if (prevSum > 0) pct = (diff / prevSum) * 100;
    else if (currSum > 0) pct = 100;
  
    const direction: 'up' | 'down' | 'stable' =
      pct > 5 ? 'up' :
        pct < -5 ? 'down' : 'stable';
  
    return {
      direction,
      percentage: Math.round(Math.abs(pct)),
      currentPeriodValue: currSum,
      previousPeriodValue: prevSum,
      difference: diff,
      currentBreakdown,
      previousBreakdown,
      noHistoricalData: false
    };
  }

  private get3MonthWindow(year: number, month: number) {
    const out: Array<{ year: number, month: number }> = [];
    for (let i = 0; i < 3; i++) {
      let y = year, m = month - i;
      if (m < 1) { m += 12; y -= 1; }
      out.push({ year: y, month: m });
    }
    return out;
  }

  private isInMonth(dateStr: string, year: number, month: number) {
    const d = new Date(dateStr);
    return d.getFullYear() === year && d.getMonth() + 1 === month;
  }

  getConfirmedPercentage(rowData: SalesRow): number {
    if (!rowData.sentCount || rowData.sentCount === 0) {
      return 0;
    }
    return (rowData.sentConfirmedCount / rowData.sentCount) * 100;
  }

  getTotalSentCount(): number {
    return this.nodes.reduce((sum, node) => sum + node.data.sentCount, 0);
  }

  getTotalSentValue(): number {
    return this.nodes.reduce((sum, node) => sum + node.data.sentValue, 0);
  }

  getTotalSentConfirmedCount(): number {
    return this.nodes.reduce((sum, node) => sum + node.data.sentConfirmedCount, 0);
  }

  getTotalSentConfirmedValue(): number {
    return this.nodes.reduce((sum, node) => sum + node.data.sentConfirmedValue, 0);
  }

  getTotalApprovedCount(): number {
    return this.nodes.reduce((sum, node) => sum + node.data.approvedCount, 0);
  }

  getTotalApprovedValue(): number {
    return this.nodes.reduce((sum, node) => sum + node.data.approvedValue, 0);
  }

  getOverallConfirmedPercentage(): number {
    const totalSent = this.getTotalSentCount();
    if (!totalSent || totalSent === 0) {
      return 0;
    }
    return (this.getTotalSentConfirmedCount() / totalSent) * 100;
  }

  getRowClass(level: number): string {
    if (level === 0) return 'month-row fw-bold';
    if (level === 1) return 'area-row bg-light';
    return 'user-row';
  }

  // styles ----

  getAreaTextClass(area: string): string {
    switch (area) {
      case 'Tetti': return 'text-success';
      case 'Case': return 'text-danger';
      case 'Facciate e Decking': return 'text-secondary';
      case 'Massello': return 'text-warning';
      case 'Pavimenti': return 'text-warning';
      default: return 'text-dark';
    }
  }

  getConfirmationPercentageClass(rowData: SalesRow): string {
    const percentage = this.getConfirmedPercentage(rowData);
    if (percentage >= 35) return 'text-success';
    if (percentage >= 25) return 'text-warning';
    if (percentage >= 0) return 'text-danger';
    return 'text-muted';
  }

  getOverallPercentageClass(): string {
    const percentage = this.getOverallConfirmedPercentage();
    if (percentage >= 35) return 'fw-bolder';
    if (percentage >= 25) return 'fw-bold';
    return '';
  }

  getTrendIconClass(direction: 'up' | 'down' | 'stable', noHistoricalData?: boolean): string {
    // Se non ci sono dati storici, mostra un'icona diversa
    if (noHistoricalData) {
      return 'fa-circle-question text-muted';
    }
    
    // Altrimenti, usa le icone normali
    if (direction === 'up') {
      return 'fa-arrow-trend-up text-success';
    } else if (direction === 'down') {
      return 'fa-arrow-trend-down text-danger';
    } else {
      return 'fa-right-left text-warning';
    }
  }
  
  getTrendTextClass(direction: 'up' | 'down' | 'stable', noHistoricalData?: boolean): string {
    // Se non ci sono dati storici, usa un colore neutro
    if (noHistoricalData) {
      return 'text-muted';
    }
    
    // Altrimenti, usa i colori normali
    if (direction === 'up') {
      return 'text-success';
    } else if (direction === 'down') {
      return 'text-danger';
    } else {
      return 'text-warning';
    }
  }
  
  getTrendText(direction: 'up' | 'down' | 'stable', percentage: number, noHistoricalData?: boolean): string {
    // Se non ci sono dati storici, mostra un messaggio specifico
    if (noHistoricalData) {
      return 'Dati storici non disponibili';
    }
    
    // Altrimenti, usa i messaggi normali
    if (direction === 'up') {
      return `Aumento del ${percentage}%`;
    } else if (direction === 'down') {
      return `Diminuzione del ${percentage}%`;
    } else {
      return `Stabile (±${percentage}%)`;
    }
  }
}