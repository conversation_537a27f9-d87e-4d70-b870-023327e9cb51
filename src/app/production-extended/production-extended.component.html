<app-navbar backroute=".." class="w-100" [loading]="loading">
  <div class="d-flex justify-content-between align-items-center w-100">
    <a class="navbar-brand">
      <span>{{ production?.name || 'Nuova produzione' }} - {{ production?.origin }} </span>

      <!-- <span *ngIf="production?.state" class=" badge ms-5" [ngClass]="getStatusBadgeClass()">
        {{ getStateLabel(production?.state) }}
      </span> -->
      
    </a>
    
    <!-- <app-production-status class="ms-auto w-50" [production]="production" /> -->
    <!-- Button to open off-canvas -->
    <!-- <button class="btn btn-outline-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#productionControlPanel" aria-controls="productionControlPanel">
      <i class="bi bi-gear-fill me-2"></i>Controllo Produzione
    </button> -->
  </div>
</app-navbar>


<div class="container-fluid overflow-scroll m-0 p-0 h-100" style="z-index: 1000;" *ngIf="production">

  <div class="row bg-light px-4 border-bottom" >
    <!-- order header-->
    <div  class="container-fluid  my-3">
      <div class="d-flex justify-content-between align-items-center w-100">
        
        <h2>Produzione {{ production?.name }}
          
        </h2>
        <sup>
          <a class="dropdown-item" target="_blank"
               href="//o3.galimberti.eu/web#id={{production.id}}&cids=1&menu_id=355&action=552&model=mrp.production&view_type=form">
              <i class="fa fa-search text-primary ms-2"></i>
            </a>
        </sup>

        @defer(when production) {
          <app-production-status class="ms-auto w-25 " [production]="production"  [preproduction]="preproduction" />
        }
      </div>
      
      <ng-container *ngIf="isConnectedToSaleOrder()">
        <p class="lead mb-0" >
          <b>Origine:</b>
          <a 
            class="lead" 
            target="_blank" 
            [routerLink]="['/', 'immediate-sale', 's', getSaleOrder(this.imballaggio)?.id]">
              {{getOrigin(imballaggio)}}
          </a>
        </p> 

        <p class="lead mb-0" *ngIf="imballaggio?.product_id?.name != 'EMPTY'">
          <b>Prodotto vendita: </b>
            <span class="me-2 badge bg-muted" >
            {{ imballaggio?.product_qty | number:'1.0-0' }} {{ production?.product_uom_id.name }} 
          </span>
          {{ imballaggio?.product_id?.name }}
        </p> 
      </ng-container>

      
      <ng-container *ngIf="!isConnectedToSaleOrder()">
        <div class=" mb-0 d-flex mt-2 align-items-center w-100 gap-3" >
          <div class="d-flex align-items-center w-50 ">
            <span>Origine</span>
            <input 
              class="ms-3 form-control" 
              size="30" 
              [ngModel]="production.origin ? production.origin : ''"
              (ngModelChange)="saveOrigin($event)"
              [ngModelOptions]="{'updateOn':'blur'}"
            >
          </div>
          <div class="d-flex align-items-center w-50">
            <span>Responsabile</span>
            <select disabled class="ms-3 form-select" [(ngModel)]="production.user_id" >
              <option *ngFor="let user of users" [ngValue]="user">{{user.name}}</option>
            </select>
          </div>
        </div> 
      </ng-container>

    </div>
  </div>

  <div class="row vh-100 mt-2">
    <!-- Main content area (now full width) -->
    <div class="col-12 p-0">
      <!-- Tabs navigation -->
      <p-tabs [value]="activeTab" [lazy]="true" #tabComponent>  
        <p-tablist class="d-flex justify-content-between align-items-center px-3">
          <div class="d-flex ps-4">
            <p-tab (click)="handleTabChange('input')" value="input">Componenti</p-tab>
            <p-tab  (click)="handleTabChange('workers')" value="workers">Lavorazioni</p-tab>
            <p-tab (click)="handleTabChange('output')" value="output">Output</p-tab>
          </div>
        </p-tablist>
        <p-tabpanels>
          <p-tabpanel value="input">
            @defer(when tabComponent.value() === "input") {
              <app-production-input-tab 
                *ngIf="production"
                [production]="production"
                (togglePacklist)="togglePacklist()"
                (loadingChange)="onLoadingChange($event)">
              </app-production-input-tab>
            }
          </p-tabpanel>
          <p-tabpanel value="workers">
            @defer(when tabComponent.value() === "workers") {
              <app-production-workers-tab
                *ngIf="production"
                [production]="production">
              </app-production-workers-tab>
            }
          </p-tabpanel>
          <p-tabpanel value="output">
            @defer(when tabComponent.value() === "output") {
              <app-production-output-tab
                (loadingChange)="loading = $event"
                *ngIf="production"
                [imballaggio]="imballaggio"
                [production]="production">
              </app-production-output-tab>
            }
          </p-tabpanel>
        </p-tabpanels>
      </p-tabs>
    </div>
  </div>
</div>

<!-- Off-canvas Production Control Panel -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="productionControlPanel" aria-labelledby="productionControlPanelLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="productionControlPanelLabel">Controllo di produzione</h5>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <!-- Production Status -->
    <div class="card mb-3">
      <div class="card-header">
        <h5 class="card-title mb-0">Stato Produzione</h5>
      </div>
      <div class="card-body">
        <div class="mb-2">
          <strong>Riferimento:</strong> {{ production?.name || 'N/D' }}
        </div>
        <div class="mb-2">
          <strong>Data pianificata:</strong> {{ production?.date_planned_start ? (production.date_planned_start | date:'dd/MM/yyyy HH:mm') : 'N/D' }}
        </div>
        <div class="mb-2">
          <strong>Stato:</strong>
          <span [ngClass]="getStatusClass(production?.state)">
            {{ getStateLabel(production?.state) }}
          </span>
          <ng-container *ngIf="production?.workorder_ids?.values?.length > 0">
            <div class="mt-1 small text-muted">
              {{ production.workorder_ids.values.length }} lavorazioni
            </div>
          </ng-container>
        </div>
        <div class="mb-2">
          <strong>Responsabile:</strong> {{ production?.user_id?.name || 'N/D' }}
        </div>
        
        <!-- Component Availability -->
        <div class="mb-0">
          <strong>Disponibilità componenti:</strong>
          <div class="d-flex align-items-center mt-1">
            <span [ngClass]="getProductionReadinessColor(production)">
              <i class="fa me-2" [ngClass]="getProductionReadinessIcon(production)"></i>
              {{ getProductionReadiness(production) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Production Summary -->
    <div class="card mb-3">
      <div class="card-header">
        <h5 class="card-title mb-0">Riepilogo Costi</h5>
      </div>
      <div class="card-body">
        <div class="row mb-2">
          <div class="col-6">
            <strong>Input:</strong>
          </div>
          <div class="col-6 text-end">
            <span class="text-primary">
              {{ calculateInputCost() | currency:'EUR':'symbol':'1.2-2' }}
            </span>
          </div>
        </div>
        
        <div class="row mb-2">
          <div class="col-6">
            <strong>Lavorazioni:</strong>
          </div>
          <div class="col-6 text-end">
            <span class="text-warning">
              {{ calculateLaborCost() | currency:'EUR':'symbol':'1.2-2' }}
            </span>
          </div>
        </div>
        
        <div class="row mb-2">
          <div class="col-6">
            <strong>Output:</strong>
          </div>
          <div class="col-6 text-end">
            <span class="text-info">
              {{ calculateOutputValue() | currency:'EUR':'symbol':'1.2-2' }}
            </span>
          </div>
        </div>
        
        <hr>
        
        <div class="row">
          <div class="col-6">
            <strong>Totale:</strong>
          </div>
          <div class="col-6 text-end">
            <strong class="text-success">
              {{ totalProductionCost | currency:'EUR':'symbol':'1.2-2' }}
            </strong>
          </div>
        </div>
      </div>
    </div>

    <!-- Production Actions -->
    <div class="card mb-3" *ngIf="production">
      <div class="card-header">
        <h5 class="card-title mb-0">Azioni Produzione</h5>
      </div>
      <div class="card-body">
        <!-- Validation Messages -->
        <div *ngIf="!canConfirmProduction()" class="alert alert-warning mb-3" role="alert">
          <small>
            <i class="fa fa-exclamation-triangle me-2"></i>
            {{ getValidationMessage() }}
          </small>
        </div>

        <!-- Confirm Production Button -->
        <!-- <div class="d-grid gap-2 mb-2" *ngIf="production.state === 'draft'">
          <button 
            type="button" 
            class="btn btn-primary"
            [disabled]="!canConfirmProduction() || loading"
            (click)="confirmProduction()">
            <i class="fa fa-check me-2"></i>
            <span *ngIf="loading">Confermando...</span>
            <span *ngIf="!loading">Conferma Distinte</span>
          </button>
        </div> -->

        <!-- Complete Production Button -->
        <!-- <div class="d-grid gap-2" *ngIf="production.state !== 'draft' && production.state !== 'done' && production.state !== 'cancel'">
          <button 
            type="button" 
            class="btn btn-success"
            [disabled]="!canCompleteProduction() || loading"
            (click)="completeProduction()">
            <i class="fa fa-flag-checkered me-2"></i>
            <span *ngIf="loading">Completando...</span>
            <span *ngIf="!loading">Completa Produzione</span>
          </button>
        </div> -->

        <!-- Production Already Completed -->
        <div class="alert alert-success mb-0" role="alert" *ngIf="production.state === 'done'">
          <i class="fa fa-check-circle me-2"></i>
          <small>Produzione già completata</small>
        </div>

        <!-- Production Cancelled -->
        <div class="alert alert-secondary mb-0" role="alert" *ngIf="production.state === 'cancel'">
          <i class="fa fa-times-circle me-2"></i>
          <small>Produzione annullata</small>
        </div>
      </div>
    </div>
  </div>
</div>
