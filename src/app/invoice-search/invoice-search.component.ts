import { 
  ChangeDetectorRef, 
  Component, 
  EventEmitter, 
  Input, 
  OnInit, 
  Output 
} from "@angular/core";
import { ODOO_IDS, AREAS_CFG } from "../models/deal";
import { OdooEntityManager } from "../shared/services/odoo-entity-manager.service";
import { BehaviorSubject, debounceTime, firstValueFrom } from "rxjs";
import { AccountMove } from "../models/account-move.model";
import { Lead } from "src/app/models/crm.lead.model";
import { ActivatedRoute, Router } from "@angular/router";
import { User } from "../models/user.model";

@Component({
    selector: "app-invoice-search",
    templateUrl: "./invoice-search.component.html",
    styleUrls: ["./invoice-search.component.scss"],
    standalone: false
})
export class InvoiceSearchComponent implements OnInit {
  // Existing properties
  loading: boolean = false;
  leads: Lead[] = [];
  selectedUser: User | null = null;
  selectedUserId: number | null = null;
  startDate: string | null = null;
  users: User[] = [];
  endDate: string | null = null;
  filteredInvoices: AccountMove[] = [];
  searchInput: BehaviorSubject<string> = new BehaviorSubject("");
  invoices: any[] = [];
  areas: { name: string; src: string; dst: string }[];
  selectedArea: string = "";

  // State management
  ga_states = [
    { value: "", label: "Tutti" },
    { value: "paid", label: "Pagate" },
    { value: "posted", label: "Registrate" },
    { value: "toEmit", label: "Da Emettere" },
    { value: "draft", label: "Bozza" },
    { value: "cancel", label: "Annullate" },
  ];
  
  // Multi-select for states
  selectedStates: string[] = ["toEmit"];

  @Output() onSelect: EventEmitter<boolean> = new EventEmitter();
  @Input() embedded: boolean = false;

  constructor(
    private odooEm: OdooEntityManager,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.areas = AREAS_CFG;
    this.loadInvoices();
    this.searchInput.pipe(debounceTime(250)).subscribe(() => {
      this.loadInvoices();
    });
  }

  async loadInvoices() {
    this.loading = true;

    // Step 1: Search all invoices with journal_id[0] == ODOO_IDS.sal_id
    this.invoices = await firstValueFrom(
      this.odooEm.search<AccountMove>(new AccountMove(), [
        ["journal_id", "=", ODOO_IDS.sal_id]
      ])
    );

    await this.assignGaState();

    // Step 3: Collect unique users from filtered invoices
    this.users = [];
    for (const invoice of this.invoices) {
      if (invoice.invoice_user_id && invoice.invoice_user_id.id && 
          !this.users.some(user => user.id === invoice.invoice_user_id.id)) {
        this.users.push(new User(invoice.invoice_user_id.id, invoice.invoice_user_id.name));
      }
    }

    // Step 4: Fetch leads for tracking codes
    let trackingCodes = this.invoices.map(
      (invoice) => invoice.invoice_origin
    );

    let criteria = [["tracking_code", "in", trackingCodes]];
    if (this.selectedArea) {
      criteria.push(["area", "=", this.selectedArea]);
    }
    this.leads = await firstValueFrom(
      this.odooEm.search<Lead>(new Lead(), criteria)
    );

    // Step 5: Search input filter (Customer, tracking code, etc.)
    const searchInputLower = this.searchInput.value
      ? this.searchInput.value.toLowerCase()
      : "";
    const filteredLeads = this.leads.filter((lead) => {
      const partnerMatch = lead.partner_id.name
        ?.toLowerCase()
        .includes(searchInputLower);
      const trackingCodeMatch = lead.tracking_code
        ?.toLowerCase()
        .includes(searchInputLower);
      const nameMatch = lead.name?.toLowerCase().includes(searchInputLower);
      const streetMatch = lead.street?.toLowerCase().includes(searchInputLower);
      const cityMatch = lead.city?.toLowerCase().includes(searchInputLower);

      return (
        partnerMatch || trackingCodeMatch || nameMatch || streetMatch || cityMatch
      );
    });

    // Step 6: Get tracking codes from filtered leads
    const filteredTrackingCodes = filteredLeads.map(
      (lead) => lead.tracking_code
    );

    // Step 7: Filter invoices by tracking codes from leads
    let filteredInvoicesByLeads = this.invoices.filter((invoice) =>
      filteredTrackingCodes.includes(invoice.invoice_origin)
    );

    // Step 8: Apply user filter
    if (this.selectedUser) {
      filteredInvoicesByLeads = filteredInvoicesByLeads.filter(
        (invoice) => invoice.invoice_user_id.id === this.selectedUser.id
      );
    }

    // Step 9: Apply date filters
    if (this.startDate) {
      filteredInvoicesByLeads = filteredInvoicesByLeads.filter(
        (invoice) => new Date(invoice.invoice_date) >= new Date(this.startDate)
      );
    }
    if (this.endDate) {
      filteredInvoicesByLeads = filteredInvoicesByLeads.filter(
        (invoice) => new Date(invoice.invoice_date) <= new Date(this.endDate)
      );
    }    

    // Step 10: Filter by selected states
    if (this.selectedStates.length > 0) {
      filteredInvoicesByLeads = filteredInvoicesByLeads.filter(
        (invoice) => this.selectedStates.includes(invoice._ga_state)
      );
    }

    this.filteredInvoices = filteredInvoicesByLeads;
    this.loading = false;
  }

  async assignGaState() {
    this.invoices.forEach((invoice) => {
      if (invoice.activity_ids.ids.length > 0 && invoice.state != "posted") {
        invoice._ga_state = "toEmit";
      } else if (invoice.state == "posted") {
        invoice._ga_state = "posted";
      } else {
        invoice._ga_state = invoice.state;
      }
    });
    this.cdr.detectChanges();
  }

  // Toggle state selection
  toggleStateSelection(state: string) {
    const index = this.selectedStates.indexOf(state);
    //if "tutti" is selected, select all states
    if (state === "") {
      if (this.selectedStates.length === 6) {
        this.selectedStates = ["toEmit"];
      } 
      else  {
      this.selectedStates = ["", "posted", "draft", "cancel", "toEmit", "paid"];
      }
    } else if (index > -1) {
      // Remove state if already selected
      this.selectedStates.splice(index, 1);
    } else {
      // Add state if not selected
      this.selectedStates.push(state);
    }

    // If no states are selected, reset to default
    if (this.selectedStates.length === 0) {
      this.selectedStates = ["toEmit"];
    }

    this.loadInvoices();
  }

  // Check if a state is selected
  isStateSelected(state: string): boolean {
    return this.selectedStates.includes(state);
  }

  // Existing methods remain the same...
  getLead(trackingCode: string): Lead {
    const lead = this.leads.find((lead) => lead.tracking_code === trackingCode);
    return lead ? lead : null;
  }

  getStateLabel(state: string): string {
    return this.ga_states.find((s) => s.value === state)?.label || "";
  }

  onUserChange(userId: number | null) {
    this.selectedUser = userId ? this.users.find(user => user.id === userId) || null : null;
    this.loadInvoices();
  }

  getAreaBadgeClass(area: string): string {
    switch (area) {
      case 'Tetti': return 'bg-success text-white';
      case 'Case': return 'bg-danger text-white';
      case 'Facciate e Decking': return 'bg-secondary text-white';
      case 'Aziendale': return 'bg-muted text-dark';
      default: return 'bg-warning text-dark';
    }
  }

  redirectDeal(id) {
    if (!this.embedded) {
      window.open("leads/" + id, "_blank");
    } else {
      this.onSelect.emit(id);
    }
  }

  resetFilters() {
    this.selectedUser = null;
    this.selectedUserId = null;
    this.startDate = null;
    this.endDate = null;
    this.selectedArea = "";
    this.selectedStates = ["toEmit"];
    this.searchInput.next("");
    this.loadInvoices();
  }
}