
import {
  Component,
  ElementRef,
  EventEmitter,
  forwardRef,
  HostBinding,
  HostListener,
  Input,
  Output,
  Provider,
  ViewChild,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';


export const WRAPPER_CONTROL_VALUE_ACCESSOR: Provider = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => VirtualKeypadComponent),
  multi: true
};


@Component({
    // tslint:disable-next-line:component-selector
    selector: 'app-virtual-keypad',
    templateUrl: './virtual-keypad.component.html',
    providers: [WRAPPER_CONTROL_VALUE_ACCESSOR],
    standalone: false
})


export class VirtualKeypadComponent implements ControlValueAccessor {
  // open2: boolean = false;

  // @HostListener('click', ['$event']) onHostClick(event: MouseEvent) {
  //   this.open2 = true
  // //   if (!this.open2) {
  // //     event.stopPropagation();
  // //     this.open2 = true
  // //   }
    
  // }
  
  @HostBinding('class.open') open2 =  false;

  @ViewChild('input') input: ElementRef<Input>;

  @Input() disabled = false;
  @Input() ngModelOptions
  @Output() ngModelChange = new EventEmitter()
  public innerValue:string ;
  private _change: any;
  private _touched: any;
  public value = 'dsa';

  constructor() {
  }

  onInputChange(n:string) {
    this.writeValue(n)
  }

  writeValue(obj: any): void {
    obj = obj ? obj : ""
    this.innerValue = obj
  }

  registerOnChange(fn: any): void {
    this._change = fn;
  }

  registerOnTouched(fn: any): void {
    this._touched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
  }


  comma() {
    if (this.innerValue.indexOf(',') > -1 || this.innerValue.length == 0)
      return
    this.click(',')
  }

  click(c:number|string) {

    console.log("XXX1 ",this.innerValue, c)
    this.innerValue = this.innerValue + c
    console.log("XXX2 ", this.innerValue, c)
  }

  ok() {
    this.open2 = false
    console.log("OOOK", this.open2)

    console.log("XXXX", this.innerValue.replace(',','.'))
    this.ngModelChange.next(Number(this.innerValue.replace(',','.')))
  }

  backspace() {
    this.innerValue = this.innerValue.toString().slice(0,-1)
  }

  onChange() {
    // var p = this.innerValue.replace(/\./g, '').replace(/,/, '.');
    this._change(this.innerValue);
  }
}
