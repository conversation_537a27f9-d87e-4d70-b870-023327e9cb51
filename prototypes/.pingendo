{"netlify": {"id": "65c2d922-05e1-4452-9315-c3e26d400887", "site_id": "65c2d922-05e1-4452-9315-c3e26d400887", "plan": "nf_team_dev", "plan_data": {"title": "Netlify Team Free", "asset_acceleration": true, "form_processing": true, "cdn_propagation": "partial", "build_gc_exchange": "buildbot-gc", "build_node_pool": "buildbot-external-ssd", "build_cluster": "buildbot-3", "domain_aliases": true, "secure_site": false, "prerendering": true, "proxying": true, "ssl": "custom", "rate_cents": 0, "yearly_rate_cents": 0, "cdn_network": "free_cdn_network", "ipv6_domain": "cdn.makerloop.com", "branch_deploy": true, "managed_dns": true, "geo_ip": true, "split_testing": true, "id": "nf_team_dev"}, "ssl_plan": null, "premium": false, "claimed": true, "name": "dreamy-raman-fca42d", "custom_domain": null, "domain_aliases": [], "password": null, "notification_email": null, "url": "http://dreamy-raman-fca42d.netlify.com", "admin_url": "https://app.netlify.com/sites/dreamy-raman-fca42d", "deploy_id": "5ccafc24ca001a47ef2c0bbb", "build_id": "", "deploy_url": "http://5ccafc24ca001a47ef2c0bbb.dreamy-raman-fca42d.netlify.com", "state": "current", "screenshot_url": null, "created_at": "2019-04-30T11:22:13.891Z", "updated_at": "2019-05-02T14:18:13.909Z", "user_id": "5a05c4380b79b74b51e9690d", "error_message": null, "ssl": false, "ssl_url": "https://dreamy-raman-fca42d.netlify.com", "force_ssl": null, "ssl_status": null, "max_domain_aliases": 100, "build_settings": {}, "processing_settings": {"css": {"bundle": true, "minify": true}, "js": {"bundle": true, "minify": true}, "images": {"optimize": true}, "html": {"pretty_urls": true}, "skip": true}, "prerender": null, "prerender_headers": null, "deploy_hook": null, "published_deploy": {"id": "5ccafc24ca001a47ef2c0bbb", "site_id": "65c2d922-05e1-4452-9315-c3e26d400887", "build_id": null, "state": "ready", "name": "dreamy-raman-fca42d", "url": "http://dreamy-raman-fca42d.netlify.com", "ssl_url": "https://dreamy-raman-fca42d.netlify.com", "admin_url": "https://app.netlify.com/sites/dreamy-raman-fca42d", "deploy_url": "http://5ccafc24ca001a47ef2c0bbb.dreamy-raman-fca42d.netlify.com", "deploy_ssl_url": "https://*********************************************.netlify.com", "created_at": "2019-05-02T14:18:12.419Z", "updated_at": "2019-05-02T14:18:13.912Z", "user_id": "5a05c4380b79b74b51e9690d", "error_message": null, "required": [], "required_functions": [], "commit_ref": null, "review_id": null, "branch": null, "commit_url": null, "skipped": null, "locked": null, "log_access_attributes": {"type": "firebase", "url": "https://netlify.firebaseio.com/deploys/5ccafc24ca001a47ef2c0bbb/log"}, "title": null, "review_url": null, "published_at": "2019-05-02T14:18:13.719Z", "context": "production", "deploy_time": 1, "available_functions": [], "summary": {"status": "ready", "messages": [{"type": "info", "title": "1 new file uploaded", "description": "1 generated page changed.", "details": "New pages include:\n- search.html\n"}, {"type": "info", "title": "No redirect rules processed", "description": "This deploy did not include any redirect rules. [Learn more about redirects](https://www.netlify.com/docs/redirects/).", "details": ""}, {"type": "info", "title": "No header rules processed", "description": "This deploy did not include any header rules. [Learn more about headers](https://www.netlify.com/docs/headers-and-basic-auth/).", "details": ""}, {"type": "info", "title": "All linked resources are secure", "description": "Congratulations! No insecure mixed content found in your files.", "details": null}]}, "screenshot_url": null, "site_capabilities": {"title": "Netlify Team Free", "asset_acceleration": true, "form_processing": true, "cdn_propagation": "partial", "build_gc_exchange": "buildbot-gc", "build_node_pool": "buildbot-external-ssd", "build_cluster": "buildbot-3", "domain_aliases": true, "secure_site": false, "prerendering": true, "proxying": true, "ssl": "custom", "rate_cents": 0, "yearly_rate_cents": 0, "cdn_network": "free_cdn_network", "ipv6_domain": "cdn.makerloop.com", "branch_deploy": true, "managed_dns": true, "geo_ip": true, "split_testing": true, "id": "nf_team_dev"}, "committer": null, "skipped_log": null}, "managed_dns": true, "jwt_secret": null, "jwt_roles_path": "app_metadata.authorization.roles", "account_slug": "info-rexi8gq", "account_name": "info-rexi8gq's team", "account_type": "personal", "capabilities": {"title": "Netlify Team Free", "asset_acceleration": true, "form_processing": true, "cdn_propagation": "partial", "build_gc_exchange": "buildbot-gc", "build_node_pool": "buildbot-external-ssd", "build_cluster": "buildbot-3", "domain_aliases": true, "secure_site": false, "prerendering": true, "proxying": true, "ssl": "custom", "rate_cents": 0, "yearly_rate_cents": 0, "cdn_network": "free_cdn_network", "ipv6_domain": "cdn.makerloop.com", "branch_deploy": true, "managed_dns": true, "geo_ip": true, "split_testing": true, "id": "nf_team_dev"}, "active_subscription_ids": [], "external_contributors_enabled": false, "paid_individual_site_subscription": false, "dns_zone_id": null, "identity_instance_id": null, "use_functions": null, "parent_user_id": null, "automatic_tls_provisioning": null, "disabled": null, "lifecycle_state": "active", "id_domain": "65c2d922-05e1-4452-9315-c3e26d400887.netlify.com", "use_lm": null, "build_image": "xenial", "has_analytics_data": true, "analytics_enabled": false, "automatic_tls_provisioning_expired": false}}