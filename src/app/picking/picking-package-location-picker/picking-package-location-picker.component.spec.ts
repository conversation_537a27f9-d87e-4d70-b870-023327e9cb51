import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PickingPackageLocationPickerComponent } from './picking-package-location-picker.component';

describe('PickingPackageLocationPickerComponent', () => {
  let component: PickingPackageLocationPickerComponent;
  let fixture: ComponentFixture<PickingPackageLocationPickerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PickingPackageLocationPickerComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PickingPackageLocationPickerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
