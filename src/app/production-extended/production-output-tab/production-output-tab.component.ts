
import { Component, Input, Output, EventEmitter, OnInit, ViewChild, ElementRef } from '@angular/core';
import { MrpProduction, Mrp<PERSON>orkcenter, Mr<PERSON><PERSON><PERSON>kor<PERSON> } from 'src/app/models/mrp-production';
import { StockMove } from 'src/app/models/stock-move';
import { StockMoveLine } from 'src/app/models/stock-move-line';
import { Table } from 'primeng/table';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { defer, first, firstValueFrom, from, Observable } from 'rxjs';
import { Product } from 'src/app/models/product.model';
import { ProductTemplate } from 'src/app/models/product.template.model';
import { ProductTemplateAttributeValue } from 'src/app/models/product.template.attribute.value.model';
import { ProductAttributeValue } from 'src/app/models/product.attribute.value';
import { ODOO_IDS } from 'src/app/models/deal';
import { ProductAttribute } from 'src/app/models/product.attribute.model';
import { Select } from 'primeng/select';
import { OdooMultiRelationship } from 'src/app/models/odoo-multi-relationship.model';
import { ProductTemplateAttributeLine } from 'src/app/models/product.template.attribute.line';
import { StockQuantPackage } from 'src/app/models/stock-quant-package';
import { ProductPackaging } from 'src/app/models/product.packaging.model';
import { group } from 'console';
import { OdooRelationship } from 'src/app/models/odoo-relationship.model';
import { SaleOrder } from 'src/app/models/sale-order.model';

interface TemplateGroup {
  templateId: number;
  templateName: string;
  totalQty: number;
  uom: string;
  averageCost: number;
  cols: any[];
  totalValue: number;
  costPercentage: number;
  template?: ProductTemplate;
  moves: StockMove[]; // Add array of associated moves
  // draftMovesAttributes?:  { [attributeName: string]: any } ;
  moveLines?: StockMoveLine[]; // Add array of associated move lines
}

@Component({
  selector: 'app-production-output-tab',
  templateUrl: './production-output-tab.component.html',
  standalone: false
})
export class ProductionOutputTabComponent implements OnInit {
  @ViewChild('dt') table: Table;
  @ViewChild('treeTable') treeTable: Table;
  @Input() production: MrpProduction;
  // @Output() toggleTmplSearch = new EventEmitter<void>();
  @Output() loadingChange = new EventEmitter<boolean>();
  @Input() imballaggio?: MrpProduction;
  loading: boolean = false;
  // lazy
  @ViewChild('selectxx', { static: false}) select:ElementRef<Select> ;
  selectedNodes = [];
  sortField: string = 'templateName';
  sortOrder: number = 1;
  
  dataLoaded: boolean = false;
  candidateTemplates = ODOO_IDS.CAN_BE_PRODUCED_OPTIONS;
  groupedByTemplateOutput: TemplateGroup[] = [];
  expandedRowsOutput: { [key: number]: boolean } = {};
  options$ = new Observable<any[]>();
  totalCost: number = 0;

  constructor(
    private elRef: ElementRef,
    private odooEM: OdooEntityManager
  ) {}

  async ngOnInit() {

    console.log("xxxx13", await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [["name", "=", "xx19"]])));

    try {
      this.loadingChange.emit(true);
      // this.production?.move_byproduct_ids?.values?.forEach(move => {
      //   console.log(`Move ${move.id} lines:`, move.move_line_ids?.values);
      // });

      await firstValueFrom(this.odooEM.resolve(this.production.move_finished_ids))

      console.log("PROD ", this.production.move_finished_ids);
      await this.loadByproductMoves();
    } catch (error) {
      alert("Error in ngOnInit:" + error);
    } finally {
      this.loadingChange.emit(false);
    }

  }

  async calculateProductionCost() {
    await firstValueFrom(this.odooEM.resolve(this.production.move_raw_ids));
    await firstValueFrom(this.odooEM.resolveArrayOfSingle(new Product(), this.production.move_raw_ids.values, "product_id"));
    // then we sum all standard prices of products

    console.log("ML - move_raw_ids", this.production.move_raw_ids.values);

    this.production.move_raw_ids.values.forEach(move => {
      console.log("ML - Move line:", move.product_id.value.display_name, " costs: ", move.product_id.value.standard_price, "qty: ",  move.product_uom_qty , "total: ", move.product_id.value.standard_price * move.product_uom_qty);
    });

    let totalCostFromComponents = this.production.move_raw_ids.values.reduce((total, move) => total + move.product_id.value.standard_price * move.product_uom_qty, 0);
    console.log("ML - Total cost from components:", totalCostFromComponents);

    //we now have to solve workorders and workcenters
    await firstValueFrom(this.odooEM.resolve(this.production.workorder_ids));
    await firstValueFrom(this.odooEM.resolveArrayOfSingle(new MrpWorkcenter(), this.production.workorder_ids.values, "workcenter_id"));

    console.log("ML - workorder_ids", this.production.workorder_ids.values);

    this.production.workorder_ids.values.forEach(workorder => {
      console.log(" ML - Workorder:", workorder.name, " costs: ", workorder.workcenter_id.value.costs_hour, "duration: ",  workorder.duration/60 , "total: ", workorder.workcenter_id.value.costs_hour * workorder.duration/60);
    });

    let totalCostFromWorkcenters = this.production.workorder_ids.values.reduce((total, workorder) => total + workorder.workcenter_id.value.costs_hour * workorder.duration/60, 0);
    console.log("ML - Total cost from workcenters:", totalCostFromWorkcenters);

    this.totalCost = totalCostFromComponents + totalCostFromWorkcenters;
    console.log("ML -Total cost:", this.totalCost);
  }

  // isDimensionalAttribute() {
  //   return true;
  // }

  // isMainProduct(templateName: string): boolean {
  //   // Implement your logic to determine if this is the main product
  //   return false;
  // }

  // setMainProduct(templateName: string): void {
  //   // Implement your logic to set the main product
  //   console.log('Setting main product:', templateName);
  // }

  // prepareTemplateData(group: TemplateGroup): any {
  //   // Implement your logic to prepare template data
  //   return {
  //     template: group.template,
  //     // Add other necessary data
  //   };
  // }

  onEditComplete(e, line) {
    console.log("COMPL",e,line);
  }

  handleTemplateVariantsConfirmed(templateId: number, event: any): void {
    // Implement your logic to handle template variants confirmation
    console.log('Template variants confirmed:', templateId, event);
  }

  async loadByproductMoves() {
    try {
      this.loading = true;
      console.log("Starting loadByproductMoves...");

      // 1. Resolve byproduct moves
      await firstValueFrom(this.odooEM.resolve(this.production.move_byproduct_ids));
      console.log("1. Byproduct moves resolved:", this.production.move_byproduct_ids.values);

      // 2 resolve all move lines
      await firstValueFrom(this.odooEM.resolveArray(new StockMoveLine(), this.production.move_byproduct_ids.values, "move_line_ids"));
      console.log("2. Move lines resolved:", this.production.move_byproduct_ids.values);

       // 3. Raccolgo tutte le move lines
      const moveLines = this.production.move_byproduct_ids.values.flatMap(move => move.move_line_ids.values);

      await firstValueFrom(
        this.odooEM.resolveArrayOfSingle(new Product(), moveLines, "product_id")
      );

       // products
      const moveLineProducts = moveLines
        .map(line => line.product_id?.value)
        .filter(product => product != null);
      

      // resolve packaging ids
      await firstValueFrom(
        this.odooEM.resolveArray(new ProductPackaging(), moveLineProducts, "packaging_ids")
      );

      // find a package named pz
      // const pz = moveLineProducts[0].packaging_ids.values.find(p => p.name === "Pz");
      
      // update qty_done using pz
      moveLines.forEach(line => {
        var pz = line.product_id.value.packaging_ids.values.find(p => p.name === "Pz");
        if (line['qty_done']) {
          line['_pz_done'] = line.qty_done / pz.qty;
          // line['qty_done'] = line.qty_done / pz.qty;
        }
      });

      // resolve product_tmpl_id on products
      await firstValueFrom(
        this.odooEM.resolveArrayOfSingle(new ProductTemplate(), moveLineProducts, "product_tmpl_id")
      )

      // listt all templates
      const movelinetemplates = moveLineProducts
        .map(product => product.product_tmpl_id?.value)
        .filter(template => template != null);

      console.log("3. Move line templates resolved:", movelinetemplates);


      // resolve attribute_ids for all templates
      await firstValueFrom(this.odooEM.resolveArray(
        new ProductAttribute(), 
        movelinetemplates,
        "valid_product_template_attribute_line_ids"
      ));


      // all products in movelines
      const moveLineProducts2 = moveLines
        .map(line => line.product_id?.value)
        .filter(product => product != null);

      // resolve product_template_attribute_value_ids
      await firstValueFrom(this.odooEM.resolveArray(
        new ProductTemplateAttributeValue(), 
        moveLineProducts2, 
        "product_template_attribute_value_ids"
      ));



      // list all product template attribute values
      const productTemplateAttributeValues = moveLineProducts2
        .map(product => product.product_template_attribute_value_ids?.values)
        .flat()
        .filter(ptav => ptav != null);

      // 6. Resolve attribute_line_ids for all templates
      const templates = moveLineProducts2.map(p => p.product_tmpl_id.value);
      const templatesWithAttributes = templates.filter(t => t?.valid_product_template_attribute_line_ids?.ids);
      if (templatesWithAttributes.length > 0) {
        await firstValueFrom(this.odooEM.resolveArray(
          new ProductTemplateAttributeLine(), 
          templatesWithAttributes, 
          "valid_product_template_attribute_line_ids"
        ));
      }

      // list all valid attribute lines of all templates
      const attrLines = templatesWithAttributes
        .map(template => template.valid_product_template_attribute_line_ids?.values)
        .flat()
        .filter(line => line != null);

      await firstValueFrom(
        this.odooEM.resolveArray(new ProductAttributeValue(), attrLines, "value_ids")
      );

      await firstValueFrom(
        this.odooEM.resolveArrayOfSingle(new ProductAttribute(), attrLines, "attribute_id")
      );

      // 8. Calculate grouped data
      this.calculateGroupedData();

      this.dataLoaded = true;


    } catch (error) {
      console.error("Error in loadByproductMoves:", error);
    } finally {
      this.loading = false;
    }
  }

  async updateCosts() {

    await this.calculateProductionCost();

    //check if we have some custom cost set manually
    let theresSomeManualShit = false; //TO FIX IN THE FUTURE!! 

    if (theresSomeManualShit) {
      //do nothing FOR NOW
      return;
    }
else {
    //super simple: calculate cost % for every move based off of it's qty done on total wty done of all moves
    let totalQty = this.production.move_byproduct_ids.values.reduce((total, move) => total + move.move_line_ids.values.reduce((total, line) => total + line.qty_done, 0), 0);
    console.log("ML - Total qty:", totalQty);
    //now we run through every move byproduct of the production and assign cost share 
    for (const move of this.production.move_byproduct_ids.values) {
      move.cost_share = move.move_line_ids.values.reduce((total, line) => total + line.qty_done, 0) / totalQty * 100;
      console.log("ML - Move cost share:", move.cost_share);
      if (move.id > 0) {
        await firstValueFrom(this.odooEM.update(move, {
          cost_share: move.cost_share
        }));
      }
    }
  }
    // this.calculateGroupedData();
  }



  private calculateGroupedData() {
    // Clear existing groupings
    this.groupedByTemplateOutput = [];
    
    // Create a map to hold templates and their data
    const templateMap = new Map<number, TemplateGroup>();
    
    // Get all byproduct moves
    const moves = this.production?.move_byproduct_ids?.values || [];
    
    // Group moves by template ID
    for (const move of moves) {

      const product = move.move_line_ids?.values?.[0]?.product_id?.value;
      if (!product || !product.product_tmpl_id?.id){
        console.log("No product or template for move", move);
        continue;
      } ;
      
      const templateId = product.product_tmpl_id.id;
      const templateName = product.product_tmpl_id.name || 'Unknown Template';
      // const quantity = move.product_uom_qty || 0;
      // const price = product.standard_price || 0;
      const uom = move.product_uom?.name || '';
      
      if (!templateMap.has(templateId)) {
        templateMap.set(templateId, {
          cols: this.getAttributeColumns(product.product_tmpl_id.value),
          templateId,
          templateName,
          totalQty: 0,
          uom,
          averageCost: 0,
          totalValue: 0,
          costPercentage: 0,
          template: product.product_tmpl_id.value,
          moves: [], // Initialize empty array for moves saved in odoo
        });
      }
      const group = templateMap.get(templateId);
      group.moves.push(move); // Add the move to this template group
      
      // Resolve move lines for this move if they exist
      if (move.move_line_ids?.values?.length > 0) {
        move.move_line_ids.values.forEach(line => {
          // Process move lines similar to input tab component
          if (!group.moveLines) {
            group.moveLines = [];
          }
          group.moveLines.push(line);
        });
      }
    }

    // Convert map to array and sort by template name
    this.groupedByTemplateOutput = Array.from(templateMap.values())
      .sort((a, b) => a.templateName.localeCompare(b.templateName));
      this.calculateTotalsForGroups();

}
private calculateTotalsForGroups() {
  this.groupedByTemplateOutput.forEach(group => {
    group.totalQty = group.moveLines.reduce((total, line) => total + line.qty_done, 0);
    group.totalValue = group.moveLines.reduce((total, line) => total + line.qty_done * this.getMove(line).cost_share * this.totalCost / this.getMove(line).quantity_done  , 0);
    group.averageCost = group.totalValue / group.totalQty;
  });
}


  async createResultPackage(line) {

    var newId 
    // search if already exists
    console.log("XX22 SEARCH WITH ", JSON.stringify(line));
    if (!line.result_package_id.name) return alert("Error: no package name");

    var packages = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [["name", "=", line.result_package_id.name]]));
    if (packages.length > 0) {
      newId = packages[0].id;
      line.result_package_id.id = packages[0].id;
      line.result_package_id.value = packages[0];
    } else {
      console.log("XX22 CREATTTEEE", line.result_package_id.name);
      var newPackage = await firstValueFrom(this.odooEM.create<StockQuantPackage>(new StockQuantPackage(), {
          name: line.result_package_id.name
      }));
      // wait 1 sec
      newId = newPackage.id;
      line.result_package_id.id = newPackage.id;
      line.result_package_id.value = newPackage;
    }

    return newId;
  }

  async persistDraft(group: TemplateGroup) {
    
    this.loadingChange.emit(true);
    try {

      // find all moves with draft lines
      var drafts = group.moves.filter(move => move.move_line_ids.values.some(line => line.id < 0));
      console.log("Drafts to persist:", drafts);

      var alreadyThere = group.moves.filter(move => move.move_line_ids.values.some(line => line.id > 0));
      await this.updateDraftLine(alreadyThere, group);
      console.log("11 1main")

      await this.persistDraftLines(drafts, group);
console.log("11 2main")
      await this.updateProductUomQty(group)
      await this.persistPackages(group);
      console.log("11 3main")
      // await this.updateCosts(); TODO C'HA CAZZI @marco

      
    } finally {
      await this.loadByproductMoves();
      this.loadingChange.emit(false);
    }
    
}
  async persistPackages(group: TemplateGroup) {
    // extract unique packages
    var packages = Array.from(new Set(group.moveLines.map(line => line.result_package_id.name))).filter(p => p != null);

    // find the ones that already exists and removefron the list
    
    console.log("Packages to persist:", packages);
    
    // we want to update relative group lines
    
    // search for existing packages
    var existingPackages = await firstValueFrom(this.odooEM.search<StockQuantPackage>(new StockQuantPackage(), [["name", "in", packages]]));
    // console.log("Existing packages:", existingPackages);
    // // remove existing packages from list
    // packages = packages.filter(p => !existingPackages.find(ep => ep.name === p));
    // console.log("Packages to create:", packages);
    
    for (var p of packages) {
        console.log("55Creating package:", p);

        if (existingPackages.find(ep => ep.name === p)) {
          console.log("55Package already exists:", p);
          continue;
        }

        var id = await firstValueFrom(this.odooEM.create<StockQuantPackage>(new StockQuantPackage(), {
          name: p
        }));

        // p.id = id;
        
        // update odoo lines using promise all
        var promises = [];
        group.moveLines.forEach(async line => {
          if (line.result_package_id.name === p && line.result_package_id.id < 0) {
            console.log("55push " + line.id);
            promises.push(firstValueFrom(this.odooEM.update<StockMoveLine>(line, {
              result_package_id: id
            })));
          }
        });
        await Promise.all(promises);
        console.log("11 ppers")
    }
    console.log("11 -2 ppers")


    // update relative group lines
  }


  async updateProductUomQty(group: TemplateGroup) {
    for (var move of group.moves) {
      if (move.id > 0) {
        var product_uom_qty = move.move_line_ids.values.reduce((acc, line) => acc + line.qty_done, 0);

      await firstValueFrom(this.odooEM.update<StockMove>(move, {
          product_uom_qty: product_uom_qty
      }));
      }
      
    }
  }
  

  async updateDraftLine(alreadyThere: StockMove[], group: TemplateGroup) {

    // update attribute values in already existing moveline

    // sync  for
    for(var move of group.moves) {
    // group.moves.forEach(async move => {

      // sync for
      for (var line of move.move_line_ids.values) {
      // move.move_line_ids.values.forEach(async line => {

        // QUANDO ID E' MAGGIORE DI 0
        if (line.id > 0) {

          // persist attribute values
          var needUpdate = false;
          
          var mn = {};
          line.product_id.value.product_template_attribute_value_ids.values.forEach(async ptav => {
            if (ptav.id < 0) {
              needUpdate = true;
              // create the new variant
              ptav.id = 0;
              group.cols.forEach(col => {
                mn[col.field] = this.getAttributeValue2(line, col.field).name;
              });
            }
          });

          console.log("MN ", mn,needUpdate)

          if (needUpdate) {
              var newProduct_id = await this.odooEM.createProductProduct(line.product_id.value.product_tmpl_id.id, mn);

              var newProduct = await firstValueFrom(this.odooEM.search<Product>(
                new Product(), [["id", "=", Number(newProduct_id)]]
              ));

              // resolve packaging_ids
              await firstValueFrom(this.odooEM.resolve(newProduct[0].packaging_ids));
              line.product_id.value.packaging_ids = newProduct[0].packaging_ids;

              // se e' confirmed nn posso aggiornare product_id .. 
              // devo creare una nuova move e nuova moveline.. cancellando la precedente
              if (this.production.state == 'confirmed' || this.production.state == 'progress') {
                var newmove = await firstValueFrom(this.odooEM.create<StockMove>(new StockMove(), {
                  name: move.product_id.name,
                  product_id: newProduct_id,
                  // product_uom_qty: line.qty_done,
                  product_uom: move.product_uom.id,
                  location_id: ODOO_IDS.stock_location_stock,
                  location_dest_id: ODOO_IDS.stock_location_stock,
                  production_id: this.production.id,
                }));

                var newMoveLine = await firstValueFrom(this.odooEM.create<StockMoveLine>(new StockMoveLine(), {
                  product_id: newProduct_id,
                  qty_done: line.qty_done,
                  product_uom_id: line.product_uom_id.id,
                  result_package_id: line.result_package_id.id,
                  move_id:  newmove.id,
                  production_id: this.production.id
                }));

                // console.log("--Created new move for confirmed production:", newMove);
                // cancella vecchia move line
                await firstValueFrom(this.odooEM.delete(new StockMoveLine(), [line.id]));
                line.id = newMoveLine.id;
                
                // add new move to production
                await firstValueFrom(this.odooEM.update(this.production, {
                  move_byproduct_ids: [[4, newmove.id]]
                }));

              } else {

                console.log("--Updating move line with new product:", line, move);
                await firstValueFrom(this.odooEM.update<StockMove>(move, {
                      product_id: newProduct_id,
                      // product_uom_qty: line.qty_done,
                      move_line_ids: [
                        [1, line.id, {
                          qty_done: line.qty_done,
                          product_id: newProduct_id,
                          product_uom_id: line.product_uom_id.id,
                        }]
                      ]})) // end wait 
              } // else
          }

          if (!line.product_id.value.packaging_ids.values)
            await firstValueFrom(this.odooEM.resolve(line.product_id.value.packaging_ids));

          // find pz in product
          var pz = line.product_id.value.packaging_ids.values.find(p => p.name === "Pz");

          if (pz.qty) {

            line.qty_done = line._pz_done * pz.qty;

          } else {
            alert("Pz packaging not found for product:" + line.product_id.name);
            return
          }

          // persist qty_done
          if (line['dirty_qty_done']) {
            var newline = await firstValueFrom(this.odooEM.update<StockMoveLine>(line, {
              qty_done: line.qty_done
            }));
            // line.id = newline.id;
            line['dirty_qty_done'] = false;
          }
        }
      };
    } ;
  }

  async persistDraftLines(drafts: StockMove[],group : TemplateGroup) {

    for (const move of drafts) {
      var res = await firstValueFrom(this.odooEM.search<Product>(new Product(), [["id", "=", move.product_id.id]]))

      // se e' draft diamo per buono di avere una sola line
      var existingLine = move.move_line_ids.values[0];

      if (!res.length) {
        console.log("Product not found:", move.product_id.id);
        // buil map of attributes for create product product
        var mn = {};
        move.move_line_ids.values.forEach(line => {
          group.cols.forEach(col => {
            mn[col.field] = this.getAttributeValue2(line, col.field).name;
          });
        });

        // add extra for proper create
        this.getAttributeColumns(group.template).forEach(col => {
          if (!mn[col.field]) {
            mn[col.field] = "-";
          }
        });

        console.log("Creating product:", move.product_id.value.product_tmpl_id.id,mn);
        var newProduct_id = await this.odooEM.createProductProduct(move.product_id.value.product_tmpl_id.id, mn);

        console.log("Created product:", newProduct_id);
        
        var newProduct = await firstValueFrom(this.odooEM.search<Product>(
          new Product(), [["id", "=", Number(newProduct_id)]]
        ));


        if (!newProduct.length) {
          alert("Product not found:" + newProduct_id);
          return;
        }


        
        // se ho un new product creato adesso
        move.product_id.id = newProduct[0].id;
        move.product_id.value.id = newProduct[0].id;
        move.product_id.value.packaging_ids.ids = newProduct[0].packaging_ids.ids;

        // existingMove.product_id.value.packaging_ids = newProduct[0].packaging_ids;
        // move.product_id.value = newProduct[0];
        await firstValueFrom(this.odooEM.resolve(move.product_id.value.packaging_ids));

        move.move_line_ids.values.forEach(line => {
          line.product_id.id = newProduct[0].id;
          line.product_id.value.id = newProduct[0].id;
          line.product_id.value.packaging_ids = newProduct[0].packaging_ids;
          // line.product_id.value = newProduct[0];
        });
      }

      if (!move.product_id.value) {
        await firstValueFrom(this.odooEM.resolveSingle(new Product(), move.product_id));
      }



      await firstValueFrom(this.odooEM.resolve(move.product_id.value.packaging_ids));

      
        
      if (move.move_line_ids.values[0].result_package_id.id < 0) {
        var newId = await this.createResultPackage(move.move_line_ids.values[0]);
      }

        // find a move with id > 0 with same product_id
        var existingMove = this.production.move_byproduct_ids.values.find(m => m.product_id.id == move.product_id.id && m.id > 0);
        // var existingMove = this.production.move_byproduct_ids.values.find(m => m.product_id.id == move.product_id.id);

        if (!existingMove) {
          console.log("EXISTING NO")
          // create move
          var existingMove = await firstValueFrom(this.odooEM.create<StockMove>(new StockMove(), {
            name: move.product_id.name,
            product_id: move.product_id.id,
            // product_uom_qty: move.product_uom_qty,
            product_uom: move.product_uom.id,
            location_id: ODOO_IDS.stock_location_stock,
            location_dest_id: ODOO_IDS.stock_location_stock,
            production_id: move.production_id.id,
          }));

          await firstValueFrom(this.odooEM.update(this.production, {
            move_byproduct_ids: [[4, existingMove.id]]
          }));

          move.id = existingMove.id;
          move.product_id.id = newProduct[0].id;
          // move.product_id.value = newProduct[0];

          await firstValueFrom(this.odooEM.resolve(move.product_id.value.packaging_ids));

          // find pz and update qty_done
          var pz = move.product_id.value.packaging_ids.values.find(p => p.name === "Pz");
          if (!pz || !pz.qty) {
            alert("Pz packaging not found for product333:" + move.product_id.name);
            return;
          }

          var newMoveLine = await firstValueFrom(this.odooEM.create<StockMoveLine>(new StockMoveLine(), {
            product_id: move.product_id.id,
            qty_done: move.move_line_ids.values[0]._pz_done * pz.qty,
            product_uom_id: move.product_uom.id,
            result_package_id: newId,
            move_id: existingMove.id
          }));

          move.id = existingMove.id;
          move.move_line_ids.ids = [newMoveLine.id];
          move.move_line_ids.values[0].id = newMoveLine.id;


        } else {

          console.log("EXISTING SI")


          await firstValueFrom(this.odooEM.resolve(move.product_id.value.packaging_ids));
           var pz = move.product_id.value.packaging_ids.values.find(p => p.name === "Pz");
            if (!pz || !pz.qty) {

              console.log("222", move.product_id.value)

              alert("Pz packaging not found for product222:" + move.product_id.name);
              return;
            }

            
          // create move line
          var newMoveLine = await firstValueFrom(this.odooEM.create<StockMoveLine>(new StockMoveLine(), {
            product_id: move.product_id.id,
            qty_done: move.move_line_ids.values[0]._pz_done * pz.qty,
            product_uom_id: move.product_uom.id,
            result_package_id: newId,
            move_id: existingMove.id
          }));
          existingLine.id = newMoveLine.id;



          // this.production.move_byproduct_ids.values = [...this.production.move_byproduct_ids.values.filter(m => m.id != move.id)]

          // existingMove.move_line_ids.ids.push(existingLine.id);
          // existingMove.move_line_ids.values.push(existingLine);
          // existingLine.product_id.id = move.product_id.id;


          // remove draft move from byproduct_ids array, we alredy have one
          // group.moves = [...group.moves.filter(m => m.id != move.id)]
        } 
    };
}


hasDraft(group: TemplateGroup) {
  if (group.moves.some(move => move.move_line_ids.values.some(line => line['dirty_qty_done']))) {
    return true;
  }

  // look for draft line or draft template attribute value
  if (group.moves.some(move => move.move_line_ids.values.some(line => line.id < 0 || line.result_package_id?.id < 0))) {
    return true;
  }


  if (group.moves.some(move => move.move_line_ids.values.some(line => line.product_id.value.product_template_attribute_value_ids?.values.some(ptav => ptav.id < 0)))) {
    return true;
  }
  // return group.moves.some(move => move.move_line_ids.values.some(line => line.id < 0));
}

isDraft(line: StockMoveLine) {
  return line.id < 0;
}

getResultPackage(moveline:StockMoveLine) {
  return moveline.result_package_id?.name;
}

setResultPackage(lines:StockMoveLine[], value) {

  lines.forEach(line => {
    line.result_package_id = {
      id: -1,
      name: value,
      value: null
    }
  });
  // moveline.result_package_id = {
  //   id: -1,
  //   name: value,
  //   value: null
  // }
}

setAttributeValues(lines: StockMoveLine[], attrField: string, value: any) {
  lines.forEach(line => {
    this.setAttributeValue(line, attrField, value);
  });
}


setAttributeValue(line: StockMoveLine, attrField: string, value: any) {
  // we need to format number value as english
  if (value && value.includes(","))
    value = value.replace(",", ".");


  // need to create a new product with a copy of product_template_attribute_value_ids
  var p = {...line.product_id.value} as Product;
  p.product_template_attribute_value_ids = new OdooMultiRelationship(ProductTemplateAttributeValue);
  
  var newvalues = JSON.parse(JSON.stringify(line.product_id.value.product_template_attribute_value_ids.values));
  var oldptav = newvalues.find(
    value => value?.attribute_id.name === attrField
  )

  var newPtav = new ProductTemplateAttributeValue();
  newPtav.id = -1;
  newPtav.name = value;
  newPtav.attribute_id = oldptav.attribute_id;
  // substitute old ptav with new one
  newvalues[newvalues.indexOf(oldptav)] = newPtav;
  p.product_template_attribute_value_ids.values = newvalues;
  line.product_id.value = p;
}

getAttributeValue(move: StockMove, attrField: string) {
  return move.product_id.value.product_template_attribute_value_ids.values.find(
    value => value?.attribute_id.name === attrField
  );
}

getAttributeValue2(moveline: StockMoveLine, attrField: string) {
  return moveline.product_id.value.product_template_attribute_value_ids.values.find(
    value => value?.attribute_id.name == attrField
  );
}

formatNumber(value: string) {

  if (!isNaN(Number(value))) {
     let x = Number(value).toLocaleString('it-IT');
     return x
  } else  {
    return value;
  }
}


  getMoveLines(group:TemplateGroup): StockMoveLine[] {
    return group.moves.map(move => move.move_line_ids.values).flat();
  }

  getMove(moveline: StockMoveLine) {
    return this.production.move_byproduct_ids.values.find(move => move.move_line_ids.values.find(line => line.id === moveline.id));
  }


  getOptions2(group: TemplateGroup,  attrField: string)  {
    var y = group.cols.find(col => col.field === attrField).values;
    console.log("gou cols ", group.cols,attrField);
    console.log("YY", y)
    return y
  }

  async getOptions(move: StockMove, attrField: string)  {
      // Recupera l'attributo giusto
      const attr = move.product_id.value.product_tmpl_id.value.attribute_line_ids.values.find(
        line => line.attribute_id.name === attrField
      );
      // Assicura che l'array sia caricato
      // await firstValueFrom(this.odooEM.resolve(attr.value_ids));
      // Ritorna un array con tutti i valori dell'attributo
      return attr.value_ids.values;
  }
  
  getMoveLinesByTemplate(templateId: number): StockMoveLine[] {
    const allMoveLines: StockMoveLine[] = [];
    
    const moves = this.production?.move_byproduct_ids?.values || [];
    
    moves.forEach(move => {
      const moveLines = move.move_line_ids?.values || [];
      moveLines.forEach(line => {
        const productTemplateId = line.product_id?.value?.product_tmpl_id?.id;
        if (productTemplateId === templateId) {
          allMoveLines.push(line);
        }
      });
    });

    return allMoveLines;
  }


  async duplicateMoveLines(group, movelines:StockMoveLine[]) {
    
    for (var moveline of movelines) {
      // find move
      var move = this.getMove(moveline);
      // duplicate moveline and add to move
      var newdraft = await this.createDraftMove(moveline.product_id.name, group);
      // set attributes values of new draft
      group.cols.forEach(col => {
        this.setAttributeValue(newdraft.move_line_ids.values[0], col.field, this.getAttributeValue2(moveline, col.field).name);
      });
      // copy packaging
      newdraft.move_line_ids.values[0].product_packaging_id.id = moveline.product_packaging_id.id;
      newdraft.move_line_ids.values[0].product_packaging_id.value = moveline.product_packaging_id.value;

      // copy qty
      newdraft.move_line_ids.values[0].qty_done = moveline.qty_done;
      newdraft.move_line_ids.values[0]._pz_done = moveline._pz_done;
      newdraft.move_line_ids.values[0]['dirty_qty_done'] = true;
      // add new move to production
      this.production.move_byproduct_ids.values.push(newdraft);
      // // add new moves to group
      group.moves.push(newdraft);

      // var newmoveline = JSON.parse(JSON.stringify(moveline));
      // newmoveline.id = this.generateTempId();
      // newmoveline.move_id.id = move.id;
      // move.move_line_ids.values.push(newmoveline);
    }
    
    // var moves = movelines.map(moveline => this.getMove(moveline));

    // duplicate moves using deep copy
    // var newmoves = JSON.parse(JSON.stringify(moves));
    // newmoves.forEach(move => {
    //   move.id = this.generateTempId();
    //   move.move_line_ids.values.forEach(line => {
    //     line.id = this.generateTempId();
    //     line.move_id.id = move.id;
    //   });
    // });

    // add new moves to production
    
    
    
  }


  async deleteMoveLines(group, movelines:StockMoveLine[]) {
    this.selectedNodes = [];
    // confirm
    if (!confirm("Confermi eliminazione ?")) return;
    console.log("Movelines to delete pre:", todeleteMovelines);
    // get moves not draft
    var todeleteMovelines = movelines.filter(moveline => moveline.id > 0);
    console.log("Movelines to delete:", todeleteMovelines);

    // delete on odoo
    try {
      this.loadingChange.emit(true);
      await firstValueFrom(this.odooEM.delete<StockMoveLine>(new StockMoveLine(), todeleteMovelines.map(moveline => moveline.id)));
      console.log("Moves deleted successfully");
      // Reload data
    } catch (error) {
      console.error("Error deleting move:", error);
    } finally {
      // await this.loadByproductMoves(); !! can't reload because it reloads also draft moves
      this.loadingChange.emit(false);
    }

    // remove all selected movelines from group
    // this.production.move_byproduct_ids.values = [...this.production.move_byproduct_ids.values.filter(move => !group.moves.includes(move))];
    group.moves.forEach(move => {
      console.log("Move lines before filter:", move.move_line_ids.values);
      move.move_line_ids.values = [...move.move_line_ids.values.filter(line => !movelines.includes(line))];
      console.log("Move lines after filter:", move.move_line_ids.values);
    });

  }

  // async deleteMoveLine(line: StockMoveLine) {
  //   try {
  //     this.loading = true;
  //     await firstValueFrom(this.odooEM.delete(new StockMoveLine(), [line.id]));
  //     console.log("Move line deleted successfully");
  //     // Reload data
  //     await this.loadByproductMoves();
  //   } catch (error) {
  //     console.error("Error deleting move line:", error);
  //   } finally {
  //     this.loading = false;
  //   }
  // }

  // onToggleTmplSearch() {
  //   this.toggleTmplSearch.emit();
  // }

  
  async getAttributeColumns2(template:ProductTemplate) {
    var attributelines = template.valid_product_template_attribute_line_ids.values;
    
    // await firstValueFrom(this.odooEM.resolveArray(new ProductAttributeValue(), attributelines, "value_ids"));
    // await firstValueFrom(this.odooEM.resolveArrayOfSingle(new ProductAttribute(), attributelines, "attribute_id"));
    
    console.log("...!!", attributelines);

    return attributelines.map(line => ({
      field: line.attribute_id.name,
      header: line.attribute_id.name,
      values: line.value_ids.values,
      display_type: line.attribute_id.value.display_type
    }));
  }

  getAttributeColumns(template: ProductTemplate): any[] {
    let x = template?.valid_product_template_attribute_line_ids?.values
    .map(line => ({
      attribute_id: line?.attribute_id,
      field: line?.attribute_id.name,
      header: line?.attribute_id.name,
      display_type: line?.attribute_id.value.display_type,
      values: line?.value_ids.values
    }))
    x = x.filter(col => col.header);
    return x;
  }

  getProductPrice(move: StockMove): number {
    return move.product_id?.value?.standard_price || 0;
  }
 
 
  /**
 * Genera un ID temporaneo negativo casuale per elementi draft
 * Gli ID negativi distinguono i record non ancora salvati nel database
 */
private generateTempId(): number {
  return -Math.floor(Math.random() * 1000000) - 1;
}


  async importFromSaleAttributes(destinationGroup : TemplateGroup) {
  // if we have imballaggio get the product if production
  if (!this.imballaggio) 
    return
    
  await firstValueFrom(this.odooEM.resolveSingle(new Product(), this.imballaggio.product_id));
  // resolve attributes
  await firstValueFrom(this.odooEM.resolve(this.imballaggio.product_id.value.product_template_attribute_value_ids));

  // build a simple map of attributes and value
  var attributeMap = new Map<string, string>();
  this.imballaggio.product_id.value.product_template_attribute_value_ids.values.forEach(ptav => {
    attributeMap.set(ptav.attribute_id.name, ptav.name);
  });

  console.log("attributeMap", attributeMap);
  // for each move line of destination group, set attribute values
  for (const move of destinationGroup.moves) {
    for (const line of move.move_line_ids.values) {
      for (const ptav of line.product_id.value.product_template_attribute_value_ids.values) {
        // we want to call set attribute only if value is different
        if (ptav.name == attributeMap.get(ptav.attribute_id.name)) {
          continue;
        }
        var value = attributeMap.get(ptav.attribute_id.name);
        if (value) {
          this.setAttributeValue(line, ptav.attribute_id.name, value);
        }
      }
    }
  }


}

async importFromComponents2(destinationGroup : TemplateGroup) {

  // RESOLVE move_raw_ids ------------------------------------------------------------
  var importProduction = JSON.parse(JSON.stringify(this.production));
  
  // resolve move_raw_ids
  await firstValueFrom(this.odooEM.resolve<StockMove>(importProduction.move_raw_ids, StockMove));
  await firstValueFrom(this.odooEM.resolveArray(new StockMoveLine(), importProduction.move_raw_ids.values, "move_line_ids"));
  // get all movelines
  let moveLines = importProduction.move_raw_ids.values.map(move => move.move_line_ids.values).flat();
  console.log("Move lines:", moveLines);
  await firstValueFrom(this.odooEM.resolveArrayOfSingle(new Product(), moveLines, "product_id"));

  
  // get all products
  let products = moveLines.map(line => line.product_id.value);
  // resolve packaging_ids
  await firstValueFrom(this.odooEM.resolveArray(new ProductPackaging(), products, "packaging_ids"));
  // resolve attribute values
  await firstValueFrom(this.odooEM.resolveArray(new ProductTemplateAttributeValue(), products, "product_template_attribute_value_ids"));
  //resolve product_tmpl_id
  await firstValueFrom(this.odooEM.resolveArrayOfSingle(new ProductTemplate(), products, "product_tmpl_id"));
  // ------------------------------------------------------------------------------------------

  // get all moveline from importProduction
  let importMoveLines = importProduction.move_raw_ids.values.map(move => move.move_line_ids.values).flat();
  for (const importLine of importMoveLines) {
    var newmove = await this.createFirstDraft(destinationGroup.template);
    for (const ptav of newmove.move_line_ids.values[0].product_id.value.product_template_attribute_value_ids.values) {
      // ptav della nuova move, guardo se c'e' in importline e lo copio
      var importPtav = importLine.product_id.value.product_template_attribute_value_ids.values.find(
        value => value.attribute_id.id === ptav.attribute_id.id
      );
      if (importPtav) {
        this.setAttributeValue(newmove.move_line_ids.values[0], ptav.attribute_id.name, importPtav.name);
      }
    }

    // newmove.move_line_ids.values[0].qty_done = importLine.qty_done;
    newmove.move_line_ids.values[0]._pz_done = importLine.qty_done / importLine.product_id.value.packaging_ids.values.find(p => p.name === "Pz").qty;
  }
}


// async importFromComponents(group : TemplateGroup) {
//   // // find unique groups in this.production.move_raw_ids
//   // var groups = this.production.move_raw_ids.values.reduce((acc, move) => {
//   //   var group = acc.find(group => group.templateId == move.product_id.value.product_tmpl_id.id);
//   //   if (!group) {
//   //     group = {
//   //       templateId: move.product_id.value.product_tmpl_id.id,
//   //       templateName: move.product_id.value.product_tmpl_id.name,
//   //       moves: []
//   //     };
//   //     acc.push(group);
//   //   }
//   //   return acc;
//   // }, []);

//   // for(var group of groups) {
//   //   if (this.groupedByTemplateOutput.find(g => g.templateId == group.templateId)) continue;
//   //   await this.createNewTemplateGroup(group.templateId, group.templateName);
//   // }

//   // this.production.move_raw_ids.values.forEach(async move => {
//   //   var cloned = JSON.parse(JSON.stringify(move))
//   //   // this.production.move_byproduct_ids.ids.push(cloned.id);
//   //   cloned.id = -1;
//   //   cloned.move_line_ids.values.forEach(line => {
//   //     line.id = this.generateTempId();
//   //   });

    
//   //   this.production.move_byproduct_ids.values.push(cloned);
    
//   //   console.log("CLONE", cloned)

//   //   this.groupedByTemplateOutput.forEach(group => {
//   //     if (group.templateId == move.product_id.value.product_tmpl_id.id) {
//   //       group.moves.push(cloned);
//   //     }
//   //   });
//   // });


//   //take all moves from input components
//   // try {
//   //   console.log("Starting importFromComponents for group:", group.templateName);
    
//   //   // Get the output product template
//   //   let outputProd = group.template;
//   //   console.log("Output product template:", outputProd);

//   //   // Resolve product template of first input move
//   //   if (!this.production.move_raw_ids?.ids?.length) {
//   //     alert("No input moves found in production");
//   //     return;
//   //   }

//   //   // se nn ho risolto values di move_raw_ids

//   //   console.log("raws ",this.production.move_raw_ids.values)

//   //   await firstValueFrom(this.odooEM.resolve(this.production.move_raw_ids));

//   //     // resolve move lines
//   //     await firstValueFrom(this.odooEM.resolveArray(new StockMoveLine(), this.production.move_raw_ids.values, "move_line_ids"));
      
//   //     // get all move lines
//   //     let moveLines = this.production.move_raw_ids.values.map(move => move.move_line_ids.values).flat();
//   //     console.log("Move lines:", moveLines);
      
//   //     // resolve product
//   //     await firstValueFrom(this.odooEM.resolveArrayOfSingle(new Product(), moveLines, "product_id"));
      
//   //     console.log("after product_iod ", moveLines);




//   //   }
//       // resolve product template
//     // await firstValueFrom(this.odooEM.resolveArrayOfSingle(new ProductTemplate(), moveLines, "product_id.product_tmpl_id"));

//     // await firstValueFrom(this.odooEM.resolveSingle(new ProductTemplate(), this.production.move_raw_ids.values[0].product_tmpl_id));
//     // let inputProd = this.production.move_raw_ids.values[0].move_line_ids.values[0].product_id.value.product_tmpl_id.value;
//     // console.log("Input product template:", inputProd);

//     // // Resolve input product attributes
//     // await firstValueFrom(this.odooEM.resolve(inputProd.attribute_line_ids));
//     // let inputProdAttr = inputProd.attribute_line_ids.values;

//     // // Find matching attributes between input and output templates
//     // let outputProdAttr = outputProd.valid_product_template_attribute_line_ids.values.filter(line => {
//     //   return inputProdAttr.find(ipa => ipa.attribute_id.id == line.attribute_id.id);
//     // });


    
//   //   console.log("Matching output product attributes:", outputProdAttr);

//   //   // Create mapping for automatic attribute filling
//   //   let automaticFill = outputProdAttr.map(line => {
//   //     let inputAttr = inputProdAttr.find(ipa => ipa.attribute_id.id == line.attribute_id.id);
//   //     return {
//   //       attribute_id: line.attribute_id,
//   //       input_attribute_line_id: inputAttr?.id
//   //     };
//   //   });
//   //   console.log("Automatic fill mapping:", automaticFill);

//   //   // TODO SCELTA INPUT
//   //   // Find copiable move lines: input moves with qty_done > 0 and matching template
//   //   // let copiableMoveLines = this.production.move_raw_ids.values
//   //   //   .filter(move => move.product_tmpl_id.id == inputProd.id)
//   //   //   .flatMap(move => move.move_line_ids.values)
//   //   //   .filter(line => line.qty_done > 0);
//   //   // console.log("Copiable move lines:", copiableMoveLines);

//   //   var copiableMoveLines:StockMoveLine[] = this.production.move_raw_ids.values.flatMap(move => move.move_line_ids.values)

//   //   if (copiableMoveLines.length === 0) {
//   //     console.warn("No move lines with qty_done > 0 found for input template");
//   //     return;
//   //   }

//   //   // For each copiable move line, create a corresponding output move line
//   //   for (const inputLine of copiableMoveLines) {
//   //     // Create a new draft move for the output template
//   //     await this.createFirstDraft(outputProd);
      
//   //     // Get the newly created move (should be the last one in the group)
//   //     let newMove = group.moves[group.moves.length - 1];
//   //     let newMoveLine = newMove.move_line_ids.values[0];

//   //     // Set quantity from input line 
//   //     // todo prodotti omogenei
//   //     newMoveLine.qty_done = inputLine.qty_done;
//   //     newMoveLine._pz_done = inputLine._pz_done || inputLine.qty_done;
//   //     newMoveLine['dirty_qty_done'] = true;

//   //     // Copy package information if available
//   //     if (inputLine.result_package_id?.name) {
//   //       this.setResultPackage(newMoveLine, inputLine.result_package_id.name);
//   //     }

//   //     // Set attribute values based on input move line
//   //     for (const attr of outputProdAttr) {
//   //       let inputAttrValue = this.getInputAttributeValue(inputLine, attr.attribute_id.id);
//   //       if (inputAttrValue) {
//   //         this.setAttributeValue(newMoveLine, attr.attribute_id.name, inputAttrValue.name);
//   //       }
//   //     }

//   //     console.log("Created output move line from input:", newMoveLine);
//   //   }

//   //   console.log("Import from components completed successfully");
    
//   // } catch (error) {
//   //   console.error("Error in importFromComponents:", error);
//   //   throw error;
//   // }
//   }

  private getInputAttributeValue(inputLine: StockMoveLine, attributeId: number): ProductTemplateAttributeValue | null {
    if (!inputLine.product_id?.value?.product_template_attribute_value_ids?.values) {
      return null;
    }
    
    return inputLine.product_id.value.product_template_attribute_value_ids.values.find(
      ptav => ptav.attribute_id?.id === attributeId
    ) || null;
  }

/**
 * Crea il primo elemento draft per il template "Pavimento"
 * Se il template esiste già, aggiunge un nuovo move al gruppo esistente
 * Altrimenti crea un nuovo gruppo template con un move di esempio
 */
async createFirstDraft(templateOption) {
  // const PAVIMENTO_TEMPLATE_ID = 47619;
  // const PAVIMENTO_NAME = "Pavimento";

  // Controlla se il gruppo template "Pavimento" esiste già
  const existingGroup = this.groupedByTemplateOutput.find(
    group => group.templateId === templateOption.id
  );

  console.log("PP existingGroup", existingGroup);

  if (existingGroup) {
    // Se il gruppo esiste già, aggiungi un nuovo move al gruppo esistente
    if (existingGroup.moves.length == 0) {
      // var product = await this.createProductWithFirstAttributes(existingGroup.template, existingGroup.template.name);
      // var newMove = this.createDraftMoveForNewTemplate(product, existingGroup.template.name, templateOption.id);
      var newMove = await this.createDraftMove(existingGroup.template.name, existingGroup);
      console.log("PP 0 createDraftMoveForNewTemplate", newMove);
      existingGroup.moves.push(newMove);
     
    } else {
      var newMove = await this.createDraftMove(existingGroup.template.name, existingGroup);
      console.log("PP 1 createDraftMove", newMove);
      existingGroup.moves.push(newMove);
    }

  } else {
    // Se il gruppo non esiste, crea un nuovo gruppo completo
    await this.createNewTemplateGroup(templateOption.id, templateOption.name);
  }

  return newMove
}

/**
 * Crea un nuovo StockMove draft con i dati di base
 */
private async createDraftMove(productName: string, group: TemplateGroup): Promise<StockMove> {
  console.log("CREATE DRAFT MOVE ",productName, group);
  // Crea la move line associata
  const moveLine = new StockMoveLine();
  moveLine.id = this.generateTempId();
  moveLine.qty_done = 0;
  
  

  // Crea il move principale
  const move = new StockMove();
  move.id = this.generateTempId();
  move.move_line_ids.ids = [moveLine.id];
  move.move_line_ids.values = [moveLine];
  move.product_id.name = productName;
  move.product_id.id = this.generateTempId();
  
  // Crea il prodotto associato

  // const product = new Product();
  var  product = await this.createProductWithFirstAttributes(group.template, productName);
  product.name = productName;
  product.id = move.product_id.id;
  move.product_id.value = product;
  moveLine.product_id.value = product;
  console.log("CREATE DRAFT MOVE2 ",productName, group);
  // Copia gli attributi dal primo move del gruppo esistente (se disponibile)
  // if (group.moves.length > 0) {
  //   const referenceMove = group.moves[0];

  //   // clone 
  //   product.product_template_attribute_value_ids = new OdooMultiRelationship(ProductTemplateAttributeValue)
  //   product.product_template_attribute_value_ids.values = JSON.parse(JSON.stringify(referenceMove.move_line_ids.values[0].product_id.value.product_template_attribute_value_ids.values))
  //   console.log("CLONE ", product);

  //   product.product_tmpl_id.id = group.templateId;
  //   product.product_tmpl_id.value = group.template;
  // } else {
    
      group.cols.forEach(col => {
        // create ptav
        console.log("COL", col,product);
        var ptav = new ProductTemplateAttributeValue();
        ptav.id = this.generateTempId();
        ptav.name = "-";
        if (!ptav.attribute_id) {
          ptav.attribute_id = new OdooRelationship<ProductAttribute>();
        }

        
        // ptav.attribute_id.id = col.attribute_id.id;
        // ptav.attribute_id.name = col.attribute_id.name;
        if (!product.product_template_attribute_value_ids.values) {
          // product.product_template_attribute_value_ids = new OdooMultiRelationship(ProductTemplateAttributeValue);
          product.product_template_attribute_value_ids.values = [];
        }
        product.product_template_attribute_value_ids.values.push(ptav);
      });
  // }

  moveLine.move_id.id = move.id;
  moveLine.move_id.value = move;

  move.product_id.value = product;
  moveLine.product_id.value = product;


  console.log("MMMMM HO CRE", move)
  return move;
}

/**
 * Crea un nuovo gruppo template completo caricando i dati dal database
 */
private async createNewTemplateGroup(templateId: number, templateName: string) {
  try {
    // 1. Carica il template dal database
    console.log(`Caricamento template ${templateName} (ID: ${templateId})...`);
    const templateResults = await firstValueFrom(
      this.odooEM.search<ProductTemplate>(new ProductTemplate(), [["id", "=", templateId]])
    );
    
    if (templateResults.length === 0) {
      console.error(`Template ${templateId} non trovato`);
      return;
    }

    const template = templateResults[0];

    // 2. Carica gli attributi del template
    await this.loadTemplateAttributes(template);

    // 3. Crea il prodotto con tutti gli attributi possibili
    const product = await this.createProductWithAllAttributes(template, templateName);

    

    // // ?  prodotto
    // var product2 = await this.createProductWithAllAttributes(template, templateName);
    // draftMove.product_id.value = product2;
    // draftMove.product_id.id = product2.id;
    // draftMove.move_line_ids.values[0].product_id.value = product2;
    // draftMove.move_line_ids.values[0].product_id.id = product2.id;
    console.log("MMMM 5 ", product);

    // 5. Prepara le colonne per la tabella
    const columns = await this.getAttributeColumns2(template);

    // 6. Crea il nuovo gruppo e aggiungilo alla lista
    const newGroup: TemplateGroup = {
      templateId,
      templateName,
      totalQty: 1, // Quantità iniziale
      uom: '',
      averageCost: 0,
      totalValue: 0,
      costPercentage: 0,
      template: template,
      moves:[],
      cols: columns
    };


    // 4. Crea il move draft
    const draftMove = await  this.createDraftMove(templateName,newGroup);
    draftMove.product_id.value = product;
    draftMove.product_id.id = product.id;
    draftMove.move_line_ids.values[0].product_id.value = product;
    draftMove.move_line_ids.values[0].product_id.id = product.id;
    console.log("MMMM 6 ", draftMove);

    newGroup.moves.push(draftMove);
    this.groupedByTemplateOutput.push(newGroup);

    console.log(`Nuovo gruppo ${templateName} creato con successo`);

  } catch (error) {
    console.error(`Errore durante la creazione del gruppo ${templateName}:`, error);
  }
}

/**
 * Carica tutti gli attributi necessari per il template
 */
private async loadTemplateAttributes(template: ProductTemplate) {
  // Carica le linee di attributi valide
  await firstValueFrom(this.odooEM.resolve(template.valid_product_template_attribute_line_ids));
  await firstValueFrom(this.odooEM.resolveArrayOfSingle(new ProductAttribute(), template.valid_product_template_attribute_line_ids.values, "attribute_id"));
  
  // Carica i valori per ogni linea di attributi
  await firstValueFrom(this.odooEM.resolveArray(
    new ProductAttributeValue(), 
    template.valid_product_template_attribute_line_ids.values, 
    "value_ids"
  ));

  console.log("Attributi template caricati:", template);
}


// crea un prodotto con i primi valori per ogni attributo
async createProductWithFirstAttributes(template: ProductTemplate, productName: string): Promise<Product> {
  const product = new Product();
  product.name = productName;
  product.id = this.generateTempId();
  product.product_tmpl_id.id = template.id;
  product.product_tmpl_id.value = template;
  product.product_template_attribute_value_ids.values = [];

  // Per ogni linea di attributi, prendi il primo valore
  template.valid_product_template_attribute_line_ids.values.forEach(attributeLine => {
    console.log("PPP333",template);
    if (!attributeLine) {
      return;
    }
    const firstValue = attributeLine.value_ids.values[0];
    const templateAttrValue = new ProductTemplateAttributeValue();
    templateAttrValue.name = firstValue.name;
    templateAttrValue.id = firstValue.id;
    templateAttrValue.attribute_id.id = attributeLine.attribute_id.id;
    templateAttrValue.attribute_id.name = attributeLine.attribute_id.name;
    product.product_template_attribute_value_ids.values.push(templateAttrValue);
  });

  return product;
}

/**
 * Crea un prodotto con tutti i possibili valori di attributi del template
 */
private async createProductWithAllAttributes(template: ProductTemplate, productName: string): Promise<Product> {
  const product = new Product();
  product.name = productName;
  product.id = this.generateTempId();
  product.product_tmpl_id.id = template.id;
  product.product_tmpl_id.value = template;
  product.product_template_attribute_value_ids.values = [];

  // Per ogni linea di attributi, crea i valori possibili
  template.valid_product_template_attribute_line_ids.values.forEach(attributeLine => {
    const attributeValues = attributeLine.value_ids.values.map(value => {
      const templateAttrValue = new ProductTemplateAttributeValue();
      templateAttrValue.name = value.name;
      templateAttrValue.id = value.id;
      templateAttrValue.attribute_id.id = attributeLine.attribute_id.id;
      templateAttrValue.attribute_id.name = attributeLine.attribute_id.name;
      return templateAttrValue;
    });

    console.log(`Attributo ${attributeLine.attribute_id.name}: ${attributeValues.length} valori`);
    product.product_template_attribute_value_ids.values.push(...attributeValues);
  });

  return product;
}

/**
 * Crea un move draft completo per un nuovo template
 */
private createDraftMoveForNewTemplate(product: Product, productName: string, templateId: number): StockMove {
  console.log("createDraftMoveForNewTemplate", product, productName, templateId);
  // Crea la move line
  const moveLine = new StockMoveLine();
  moveLine.id = this.generateTempId();
  moveLine.qty_done = 1;

  // Crea il move
  const move = new StockMove();
  move.id = this.generateTempId();
  move.move_line_ids.ids = [moveLine.id];
  move.move_line_ids.values = [moveLine];
  move.product_id.name = productName;
  move.product_id.id = product.id;
  move.product_id.value = product;

  moveLine.product_id.id = product.id;
  moveLine.product_id.value = product;
  moveLine.move_id.id = move.id;
  moveLine.move_id.value = move;


  return move;
}


}
