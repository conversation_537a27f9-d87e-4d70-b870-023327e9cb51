<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>Ordine 1000</title>
  <link rel="icon" href="galimberti_favicon.png">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" type="text/css">
  <link rel="stylesheet" href="theme.css">
</head>

<body class="d-flex flex-column h-100">
  <nav class="navbar navbar-expand-md bg-dark navbar-dark">
    <div class="container-fluid"> <a class="navbar-brand" href="#">
        <img src="logo.png" width="30" height="30" class="d-inline-block align-top" alt="">
        <b>&nbsp;Ordine vendita 1000&nbsp;</b>
      </a> <button class="navbar-toggler navbar-toggler-right border-0" type="button" data-toggle="collapse" data-target="#navbar4">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbar4">
        <a class="btn btn-default navbar-btn ml-auto text-light" data-toggle="modal" data-target="#search"><i class="fa fa-fw fa-plus fa-lg"></i></a><a class="btn btn-default navbar-btn  text-light" data-toggle="modal" data-target="#search"><i class="fa fa-fw fa-lg fa-clone"></i></a><a class="btn btn-default navbar-btn  text-light" data-toggle="modal" data-target="#search"><i class="fa fa-fw fa-trash fa-lg"></i></a></div>
    </div>
  </nav>
  <div class="flex-grow-1 d-flex h-100">
    <div class="container-fluid h-100">
      <div class="row h-100">
        <div class="col-md-12 p-0 panel panel-short">
          <table class="table table-bordered " id="order">
            <thead>
              <tr>
                <th colspan="2" scope="colgroup" class="table-light">Prodotto</th>
                <th colspan="4" scope="colgroup" class="table-info">Dimensioni</th>
                <th colspan="1" scope="colgroup" class="table-light">UM</th>
                <th colspan="6" scope="colgroup" class="table-info">Quantità</th>
                <th colspan="4" scope="colgroup" class="table-light">Prezzo</th>
                <th colspan="2" scope="colgroup" class="table-info">Altro</th>
              </tr>
              <tr>
                <th class="table-light">ID</th>
                <th class="table-light">Descrizione</th>
                <th class="table-info">Pz</th>
                <th class="table-info">Lung</th>
                <th class="table-info">Larg</th>
                <th class="table-info">Spes</th>
                <th></th>
                <th class="table-info">Rich</th>
                <th class="table-info">Lorda</th>
                <th class="table-info">Desc</th>
                <th class="table-info">Ass</th>
                <th class="table-info">Ok</th>
                <th class="table-info">Manc</th>
                <th>Prezzo</th>
                <th>Sconto<br></th>
                <th>Finale</th>
                <th>Totale</th>
                <th class="table-info">Fornitore</th>
                <th class="table-info">Blocco</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th><input type="checkbox">&nbsp; &nbsp;6085</th>
                <td>Perline abete A/B dun cm 400x15.5x20.0</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td>mq</td>
                <td class="editable">3<br></td>
                <td>6,2</td>
                <td>2 Pkt<br></td>
                <td></td>
                <td></td>
                <td>6,2</td>
                <td>8,55&nbsp;<br></td>
                <td class="editable"><br></td>
                <td>8,55</td>
                <td>53,01<br></td>
                <td class="editable"></td>
                <td class="editable"></td>
              </tr>
              <tr>
                <th><input type="checkbox">&nbsp; &nbsp;2018</th>
                <td>Travi abete lamellare qualità a vista</td>
                <td class="editable">2</td>
                <td class="editable">1200</td>
                <td class="editable">10</td>
                <td class="editable">12</td>
                <td>mq</td>
                <td>0,288</td>
                <td>0,288</td>
                <td></td>
                <td></td>
                <td></td>
                <td>0,288</td>
                <td>557<br></td>
                <td class="editable"></td>
                <td>557</td>
                <td>160,42</td>
                <td class="editable"></td>
                <td class="editable"></td>
              </tr>
              <tr>
                <th><input type="checkbox">&nbsp; &nbsp;212</th>
                <td>Travi abete lamellare qualità a vista cm 1200x12x12</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td contenteditable="true">mc</td>
                <td class="editable">1</td>
                <td>1,536</td>
                <td>2 pz<br></td>
                <td></td>
                <td></td>
                <td>1,54</td>
                <td>513</td>
                <td class="editable"></td>
                <td>513</td>
                <td>787,97</td>
                <td class="editable"></td>
                <td class="editable"></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <nav class="navbar navbar-expand-md navbar-light bg-light fixed-bottom border-top border-dark">
    <div class="container-fluid"> <a class="navbar-brand text-primary" href="#">
        <b> Totale 100&nbsp;€</b>
      </a> <button class="navbar-toggler navbar-toggler-right border-0" type="button" data-toggle="collapse" data-target="#navbar4">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbar4">
        <ul class="navbar-nav ml-auto">
          <li class="nav-item"> </li>
        </ul>
      </div>
    </div>
  </nav>
  <div class="modal modal-overlay" id="search">
    <div class="modal-dialog modal-xl modal-dialog-scrollable" role="document">
      <div class="modal-content">
        <div class="modal-header d-flex ">
          <a href="browse.html" class="btn btn-dark" target="_blank"> Catalogo </a> <input class="ml-3 w-100 p-1" id="search-input"><button type="button" class="close" data-dismiss="modal"> <span>×</span> </button>
        </div>
        <div class="modal-body p-0">
          <div class="table-responsive">
            <table class="table table-bordered table-hover ">
              <thead class="thead-dark">
                <tr>
                  <th>ID</th>
                  <th>Descrizione</th>
                  <th>Prezzo</th>
                  <th>Unità</th>
                  <th>Qt</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <th>1</th>
                  <td>Vite inox 10 18 20</td>
                  <td>1</td>
                  <td>1</td>
                  <td>1</td>
                </tr>
                <tr>
                  <th>2</th>
                  <td>Barra</td>
                  <td>1</td>
                  <td>1</td>
                  <td>1</td>
                </tr>
                <tr>
                  <th scope="row">3</th>
                  <td>Larry</td>
                  <td>1</td>
                  <td>1</td>
                  <td>1</td>
                </tr>
                <tr>
                  <th scope="row">3</th>
                  <td>Larry</td>
                  <td>1</td>
                  <td>1</td>
                  <td>1</td>
                </tr>
                <tr>
                  <th scope="row">3</th>
                  <td>Larry</td>
                  <td>1</td>
                  <td>1</td>
                  <td>1</td>
                </tr>
                <tr>
                  <th scope="row">3</th>
                  <td>Larry</td>
                  <td class="px-1 pl-2">1</td>
                  <td class="px-1 pl-2">1</td>
                  <td class="px-1 pl-2">1</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js" integrity="sha384-wHAiFfRlMFy6i5SRaxvfOCifBUQy1xHdJ/yoi7FRNXMRBu5WHdZYu1hA6ZOblgut" crossorigin="anonymous"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
  <script src="https://cdn.datatables.net/1.10.18/js/jquery.dataTables.min.js"></script>
  <script style="">
    $(document).ready(function() {
      var table = $('#order').DataTable({
        searching: false,
        paging: false,
        info: false
      });
      $(".editable").attr("contenteditable", "true")
      $("#search").find("tr").on("click", function() {
        var $clone = $("#order").find("tr").last().clone()
        $clone.find("td:nth-child(2)").html($(this).find("td:nth-child(2)").html())
        $clone.find("td:nth-child(3)").html("0")
        $("#order").find("tr").last().after($clone)
        resortTable()
        refreshTable()
      })
      $("#search-input").on("input", function() {
        var val = $(this).val()
        $("#search").find("tr").each(function() {
          var text = $(this).text().toLowerCase()
          if (text.indexOf(val) > -1) $(this).show()
          else $(this).hide()
        })
      })
      var refreshTable = function() {
        console.log("re")
        var mylist = $('#order tbody');
        var listitems = mylist.find('tr');
        $.each(listitems, function(idx, itm) {
          if ($(itm).find("td:nth-child(3)").text() == "0") $(itm).addClass("bg-light")
          else $(itm).removeClass("bg-light")
        });
      }
      var resortTable = function() {
        console.log("re")
        var mylist = $('#order tbody');
        var listitems = mylist.find('tr');
        listitems.sort(function(a, b) {
          console.log($(a).find("td:nth-child(2)").text().toUpperCase())
          return $(a).find("td:nth-child(2)").text().toUpperCase().localeCompare($(b).find("td:nth-child(2)").text().toUpperCase());
        })
        $.each(listitems, function(idx, itm) {
          mylist.append(itm);
        });
      }
      $(window).on("input", function() {
        refreshTable()
      })
      resortTable()
      refreshTable()
    })
  </script>
</body>

</html>