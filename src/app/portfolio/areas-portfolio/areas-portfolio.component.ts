import { Component, OnInit } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { SortEvent } from 'primeng/api';
import { groupBy } from 'lodash';

import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';
import { Lead } from 'src/app/models/crm.lead.model';
import { AccountMove } from 'src/app/models/account-move.model';
import { ODOO_IDS } from 'src/app/models/deal';
import { AccountMoveLine } from 'src/app/models/account-move-line.model';
import { User } from 'src/app/models/user.model';

export interface MonthlyBreakdown {
  year: number;
  month: number;
  month_name: string;
  timeline: 'past' | 'future' | 'current';
  amount: number;
  typeOfData: 'posted' | 'draft' | 'residual' | '';
}

export interface LeadForPortfolio {
  lead: Lead;
  invoices: AccountMove[];
  montlhyStats: MonthlyBreakdown[];
  totalContracts?: number;
  totalInvoices?: number;
  totalPosted?: number;
  totalResidual?: number;
}

@Component({
  selector: 'app-areas-portfolio',
  templateUrl: './areas-portfolio.component.html',
  styleUrls: ['./areas-portfolio.component.scss'],
  standalone: false
})
export class AreasPortfolioComponent implements OnInit {
  loading = false;

  portfolioLeads: LeadForPortfolio[] = [];
  OdooLeads: Lead[] = [];
  OdooInvoices: AccountMove[] = [];

  months: string[] = [];
  years: number[] = [];

  allPortfolioLeads: LeadForPortfolio[] = [];
  sectorOptions: string[] = [];
  userOptions: string[] = [];

  selectedSector: string | null = null;
  selectedUser: string | null = null;

  editMode: boolean = false;
  user: User | null = null;

  expandedGroups: Record<string, boolean> = {};

  constructor(private odooEM: OdooEntityManager) {}

  async ngOnInit() {
    this.loading = true;
    console.log('ngOnInit: start');
    await this.loadUserInfo();
    console.log('User info loaded', this.user);
    await this.loadLeadData();
    console.log('Leads:', this.OdooLeads);
    this.generateMonthlyColumns();
    console.log('Months & Years:', this.months, this.years);
    this.createStructure();
    console.log('After createStructure:', this.portfolioLeads);
    this.fillMonthlyData();
    console.log('After fillMonthlyData:', this.portfolioLeads);

    this.groupAndFlatten();
    console.log('After groupAndFlatten:', this.portfolioLeads);

    this.portfolioLeads.forEach(lp => this.expandedGroups[lp.lead.area] = false);
    console.log('expandedGroups init:', this.expandedGroups);

    // init filtered = all
    this.allPortfolioLeads = [...this.portfolioLeads];

    // build dropdown options
    // unique, sorted list of areas
    this.sectorOptions = Array
      .from(new Set(this.portfolioLeads.map(lp => lp.lead.area)))
      .sort();

    // unique, sorted list of responsible users
    this.userOptions = Array
      .from(new Set(this.portfolioLeads.map(lp => lp.lead.user_id.name)))
      .sort();

    // collapse all groups initially
    this.portfolioLeads.forEach(lp => this.expandedGroups[lp.lead.area] = false);

    this.loading = false;

    console.log('ngOnInit: done');
  }

  async loadUserInfo() {
    console.log('Loading user info...');
    try {
      const result: any = await this.odooEM.odoorpcService.getSessionInfo();
      const userId = result.result.user_id[0];
      const users = await firstValueFrom(this.odooEM.search<User>(
        new User(),
      ));
      this.user = users.find(u => u.id === userId);
    } catch (error) {
      console.error('Error loading user info:', error);
    }
  }


  private async loadLeadData() {
    try {
      this.OdooLeads = await firstValueFrom(
        this.odooEM.search<Lead>(
          new Lead(),
          [['stage_id.id','=',3],['company_id','=',1], ['area','!=','Aziendale']],
          5000
        )
      );
      this.OdooInvoices = await firstValueFrom(
        this.odooEM.search<AccountMove>(
          new AccountMove(),
          [
            ['invoice_origin','in',this.OdooLeads.map(l=>l.tracking_code)],
            ['move_type','=','out_invoice'],
            ['state','!=','cancel'],
            
          ],
          5000
        )
      );
      this.OdooInvoices.forEach(inv => {
        if (inv.journal_id[0] === ODOO_IDS.contratti_id) inv._ga_type = 'contract';
        else if (inv.journal_id[0] === ODOO_IDS.sal_id)
          inv._ga_type = inv.state==='posted' ? 'posted' : inv.state==='draft' ? 'draft' : '';
      });
      console.log('Invoices:', this.OdooInvoices);
    } catch(err) {
      console.error('Error fetching:', err);
    }
  }

  private generateMonthlyColumns() {
    this.months = ['Gen','Feb','Mar','Apr','Mag','Giu','Lug','Ago','Set','Ott','Nov','Dic'];
    this.years  = [2023,2024,2025,2026];
  }

  private createStructure() {
    this.portfolioLeads = this.OdooLeads.map(lead => {
      const stats: MonthlyBreakdown[] = [];
      const now = new Date();
      for (const yr of this.years) {
        for (let m=1; m<=12; m++) {
          //set timeline to past, current or future
          const timeline = (yr<now.getFullYear() || (yr===now.getFullYear() && m<now.getMonth()+1))?'past':
            (yr>now.getFullYear() || (yr===now.getFullYear() && m>now.getMonth()+1))?'future':'current';

          stats.push({ year:yr, month:m, month_name:this.months[m-1], timeline, amount:0, typeOfData:'' });
        }
      }
      const invs = this.OdooInvoices.filter(i=>i.invoice_origin===lead.tracking_code);
      return { lead, invoices:invs, montlhyStats:stats };
    });

    this.portfolioLeads.forEach(lp => {
      lp.totalContracts=0; lp.totalInvoices=0; lp.totalPosted=0; lp.totalResidual=0;
      lp.invoices.forEach(inv => {
        if(inv._ga_type==='contract') lp.totalContracts!+=inv.amount_untaxed;
        else if(inv._ga_type==='posted'){ lp.totalPosted!+=inv.amount_untaxed; lp.totalInvoices!+=inv.amount_untaxed; }
        else if(inv._ga_type==='draft') lp.totalInvoices!+=inv.amount_untaxed;
      });
      if(lp.totalContracts===0) lp.totalContracts=lp.lead.expected_revenue;
      lp.totalResidual = lp.totalContracts - (lp.totalPosted||0);
      if (lp.totalResidual<0) lp.totalResidual=0;
    });
  }

  private fillMonthlyData() {
    this.portfolioLeads.forEach(lp => {
      let calcRes=0;
      lp.invoices.forEach(inv=>{
        if(inv._ga_type==='contract') return;
        const d=new Date(inv.invoice_date);
        const stat=lp.montlhyStats.find(s=>s.year===d.getFullYear()&&s.month===d.getMonth()+1);
        if(!stat) return;
        stat.amount+=inv.amount_untaxed;
        if(inv.state==='posted') stat.typeOfData='posted';
        else if(inv.state==='draft'){ stat.typeOfData='draft'; calcRes+=inv.amount_untaxed; }
      });
      if((lp.totalResidual||0)>calcRes){
        let ref=lp.lead.date_deadline?new Date(lp.lead.date_deadline):new Date();
        if(ref<new Date()) ref=new Date();
        const stat=lp.montlhyStats.find(s=>s.year===ref.getFullYear()&&s.month===ref.getMonth()+1);
        if(stat){ stat.amount+=lp.totalResidual!-calcRes; stat.typeOfData='residual'; }
      }
    });
  }

  /** group alpha + code order then flatten */
  private groupAndFlatten() {
    const byArea = groupBy(this.portfolioLeads, x=>x.lead.area);
    const areas = Object.keys(byArea).sort();
    this.portfolioLeads = areas.flatMap(ar=>{
      const grp=byArea[ar];
      grp.sort((a,b)=>a.lead.tracking_code.localeCompare(b.lead.tracking_code));
      return grp;
    });
  }

  /**
   * Sort only _inside_ each sector.
   */
  customSort(event: SortEvent) {
    let field = event.field!, order=event.order!;
    // if multiSortMeta present, take last
    if(event.multiSortMeta?.length){
      const m=event.multiSortMeta[event.multiSortMeta.length-1];
      field=m.field!; order=m.order!;
    }
    const byArea = groupBy(this.portfolioLeads, lp=>lp.lead.area);
    const areas = Object.keys(byArea);
    areas.forEach(ar=>{
      byArea[ar].sort((a,b)=>{
        const v1=this.resolveField(a,field), v2=this.resolveField(b,field);
        if(v1==null) return -1*order;
        if(v2==null) return  1*order;
        const r=v1<v2?-1:v1>v2?1:0;
        return r*order;
      });
    });
    this.portfolioLeads = areas.flatMap(ar=>byArea[ar]);
    console.log(`customSort by ${field}(${order})`, this.portfolioLeads);
  }

  onFilterChange() {
    this.portfolioLeads = this.allPortfolioLeads.filter(lp => {
      const bySector = this.selectedSector  ? lp.lead.area === this.selectedSector        : true;
      const byUser   = this.selectedUser    ? lp.lead.user_id.name === this.selectedUser  : true;
      return bySector && byUser;
    });
    console.log('onFilterChange:', this.selectedSector, this.selectedUser);
  }

  toggleEdit() {
    this.editMode = !this.editMode;
  }

  isDateInMonth(dateString: string | null, year: number, month: number): boolean {
    if (!dateString) return false;
    
    const date = new Date(dateString);
    return date.getFullYear() === year && date.getMonth() + 1 === month;
  }

  private resolveField(obj:any, field:string):any {
    return field.split('.').reduce((o,f)=>o?.[f], obj);
  }

  /** how many leads in a sector */
  getGroupCount(area: string): number {
    return this.portfolioLeads.filter(lp => lp.lead.area === area).length;
  }

  /** sum of a numeric field ('totalContracts' or 'totalResidual') in a sector */
  getGroupSum(area: string, field: 'totalContracts'|'totalResidual'): number {
    return this.portfolioLeads
      .filter(lp => lp.lead.area === area)
      .reduce((sum, lp) => sum + (lp[field] || 0), 0);
  }

  /** sum of a given month in a sector */
  getGroupMonthlySum(area: string, year: number, month: number) {

    let amount = this.portfolioLeads
      .filter(lp => lp.lead.area === area)
      .reduce((sum, lp) => sum + this.getMonthlyAmount(lp, year, month), 0);
    return amount > 0 ? amount : '';
  }

  toggleGroup(area: string) {
    this.expandedGroups[area] = !this.expandedGroups[area];
    console.log(`toggleGroup ${area}`, this.expandedGroups[area]);
  }

  getMonthlyAmount(lp: LeadForPortfolio, y: number, m: number) {
    const s = lp.montlhyStats.find(z => z.year === y && z.month === m);

    //if editmode and residual, return residual 0
    if (this.editMode && s && s.typeOfData === 'residual') {
      return 0;
    }



    return s ? s.amount : 0;
  }
  
  getMonthlyType(lp: LeadForPortfolio, y: number, m: number) {
    const s = lp.montlhyStats.find(z => z.year === y && z.month === m);
    return s ? s.typeOfData : '';
  }

  getTotalSum(field: 'totalContracts'|'totalResidual'): number {
    return this.portfolioLeads
      .reduce((sum, lp) => sum + (lp[field] || 0), 0);
  }
  
  getTotalMonthlySum(year: number, month: number) {
    let amount = this.portfolioLeads
      .reduce((sum, lp) => sum + this.getMonthlyAmount(lp, year, month), 0);
    return amount > 0 ? amount : '';
  }

  // Check if a month has residual value
  hasResidual(row: LeadForPortfolio, year: number, month: number): boolean {
    return this.getMonthlyType(row, year, month) === 'residual';
  }

  // Get residual amount for a specific cell
  getResidualAmount(row: LeadForPortfolio, year: number, month: number): number {
    if (this.hasResidual(row, year, month)) {
      return this.getMonthlyAmount(row, year, month);
    }
    return 0;
  }

  // Get posted invoices for a specific cell
  getPostedInvoices(invoices: AccountMove[]): AccountMove[] {
    return invoices.filter(inv => inv.state === 'posted') || [];
  }

  // Get draft invoices for a specific cell
  getDraftInvoices(invoices: AccountMove[]): AccountMove[] {
    return invoices.filter(inv => inv.state === 'draft') || [];
  }


// In your AreasPortfolioComponent

/**
 * Called from the template when the user edits a cell.
 */
async updateCellValue(row: LeadForPortfolio, year: number, month: number, valueStr: string) {
  const newValue = parseFloat(valueStr);
  if (isNaN(newValue)) return;

  // skip if no change
  const currentAmount = this.getMonthlyAmount(row, year, month);
  if (currentAmount === newValue) return;

  // find any existing draft invoice for that month
  const drafts = this.getDraftInvoicesForCell(row, year, month);
  try {
    if (drafts.length) {
      // existing draft → update or delete
      const draft = drafts[0];
      if (newValue === 0) {
        await this.handleDeleteInvoice(row, draft, year, month);
      } else {
        await this.handleUpdateDraftInvoice(row, draft, year, month, newValue);
      }
    } else {
      // no draft → create one if newValue > 0
      if (newValue > 0) {
        await this.handleCreateDraftInvoice(row, year, month, newValue);
      }
    }
  } catch (err) {
    console.error('Error updating cell:', err);
  }

  // in all cases: bump reference so Angular re-renders
  this.portfolioLeads = [...this.portfolioLeads];
}

/** Helpers to isolate each outcome **/

private async handleDeleteInvoice(
  row: LeadForPortfolio,
  draft: AccountMove,
  year: number,
  month: number
) {
  const ok = confirm(
    `Delete draft invoice ${draft.narration} for ${row.lead.tracking_code} ${this.months[month-1]} ${year}?`
  );
  if (!ok) return;

  await firstValueFrom(this.odooEM.delete(new AccountMove(), [draft.id]));
  // remove from the array
  row.invoices = row.invoices.filter(inv => inv.id !== draft.id);

  // recalc everything for this lead
  this.recalculateLeadTotals(row);
  this.recalculateMonthlyStats(row);
}

private async handleUpdateDraftInvoice(
  row: LeadForPortfolio,
  draft: AccountMove,
  year: number,
  month: number,
  newValue: number
) {
  const ok = confirm(
    `Change draft invoice ${draft.narration} amount to ${newValue.toFixed(2)} €?`
  );
  if (!ok) return;

  // ensure there's at least one line
  await this.ensureLineExists(draft, newValue, year, month);

  // update first line to the new value
  const line = draft.invoice_line_ids.values![0];
  await firstValueFrom(this.odooEM.update(line, { price_unit: newValue }));

  // fetch fresh totals for this invoice
  const [refreshed] = await firstValueFrom(
    this.odooEM.search<AccountMove>(
      new AccountMove(),
      [['id', '=', draft.id]]
    )
  );
  Object.assign(draft, refreshed);

  // recalc lead-level data
  this.recalculateLeadTotals(row);
  this.recalculateMonthlyStats(row);
}

/** Make sure the draft has exactly one line, creating it if missing */
private async ensureLineExists(
  draft: AccountMove,
  value: number,
  year: number,
  month: number
) {
  if (!draft.invoice_line_ids?.values?.length) {
    const line = new AccountMoveLine();
    const name = `Amount for ${this.months[month-1]} ${year}`;
    await firstValueFrom(
      this.odooEM.create<AccountMoveLine>(line, {
        move_id: draft.id,
        quantity: 1,
        price_unit: value,
        product_id: 67887,
        name
      })
    );
    // resolve to populate draft.invoice_line_ids.values
    await firstValueFrom(
      this.odooEM.resolve(draft.invoice_line_ids)
    );
  }
}

private async handleCreateDraftInvoice(
  row: LeadForPortfolio,
  year: number,
  month: number,
  newValue: number
) {
  const ok = confirm(
    `Create new draft invoice for ${row.lead.tracking_code} ${this.months[month-1]} ${year}?`
  );
  if (!ok) return;

  // build the draft payload
  const invoiceDate = new Date(year, month - 1, 15)
    .toISOString()
    .split('T')[0];
  const invoiceData = {
    invoice_origin: row.lead.tracking_code,
    invoice_date: invoiceDate,
    journal_id: ODOO_IDS.sal_id,
    partner_id: row.lead.partner_id.id,
    state: 'draft',
    move_type: 'out_invoice',
    invoice_line_ids: [
      [
        0,
        0,
        {
          quantity: 1,
          price_unit: newValue,
          product_id: 67887,
          name: `Amount for ${this.months[month-1]} ${year}`
        }
      ]
    ]
  };

  const draft = await firstValueFrom(
    this.odooEM.create<AccountMove>(new AccountMove(), invoiceData)
  );
  // attach lines
  await firstValueFrom(
    this.odooEM.resolve(draft.invoice_line_ids)
  );
  // add to array
  row.invoices.push(draft);

  // recalc
  this.recalculateLeadTotals(row);
  this.recalculateMonthlyStats(row);
}

/** Utility to find any draft invoices for this cell */
private getDraftInvoicesForCell(
  row: LeadForPortfolio,
  year: number,
  month: number
): AccountMove[] {
  return row.invoices.filter(inv => {
    if (inv.journal_id[0] !== ODOO_IDS.sal_id || inv.state !== 'draft') {
      return false;
    }
    const d = new Date(inv.invoice_date);
    return d.getFullYear() === year && d.getMonth() + 1 === month;
  });
}


// Method to reload lead data to update residual calculations
private async reloadLeadData(row: LeadForPortfolio) {
  try {
    // Reload lead's invoices
    const invoices = await firstValueFrom(
      this.odooEM.search<AccountMove>(
        new AccountMove(),
        [
          ['invoice_origin', '=', row.lead.tracking_code.toString()],
          ['move_type', '=', 'out_invoice'],
          ['state', '!=', 'cancel']
        ]
      )
    );
    
    // Update the invoices array
    row.invoices = invoices;
    
    // Tag invoices by type
    row.invoices.forEach(inv => {
      if (inv.journal_id[0] === ODOO_IDS.contratti_id) inv._ga_type = 'contract';
      else if (inv.journal_id[0] === ODOO_IDS.sal_id)
        inv._ga_type = inv.state === 'posted' ? 'posted' : inv.state === 'draft' ? 'draft' : '';
    });
    
    // Recalculate totals
    this.recalculateLeadTotals(row);
    
    // Update monthly stats
    this.recalculateMonthlyStats(row);
    
  } catch (error) {
    console.error('Error reloading lead data:', error);
  }
}

// Method to recalculate lead totals
private recalculateLeadTotals(row: LeadForPortfolio) {
  row.totalContracts = 0;
  row.totalInvoices = 0;
  row.totalPosted = 0;
  
  row.invoices.forEach(inv => {
    if (inv._ga_type === 'contract') row.totalContracts! += inv.amount_untaxed;
    else if (inv._ga_type === 'posted') {
      row.totalPosted! += inv.amount_untaxed;
      row.totalInvoices! += inv.amount_untaxed;
    }
    else if (inv._ga_type === 'draft') row.totalInvoices! += inv.amount_untaxed;
  });
  
  if (row.totalContracts === 0) row.totalContracts = row.lead.expected_revenue;
  row.totalResidual = row.totalContracts - (row.totalPosted || 0);
}

// Method to recalculate monthly stats
private recalculateMonthlyStats(row: LeadForPortfolio) {
  // Reset all monthly stats
  row.montlhyStats.forEach(stat => {
    stat.amount = 0;
    stat.typeOfData = '';
  });
  
  // Add invoices to monthly stats
  let calcRes = 0;
  row.invoices.forEach(inv => {
    if (inv._ga_type === 'contract') return;
    
    const d = new Date(inv.invoice_date);
    const stat = row.montlhyStats.find(s => s.year === d.getFullYear() && s.month === d.getMonth() + 1);
    
    if (!stat) return;
    
    stat.amount += inv.amount_untaxed;
    
    if (inv.state === 'posted') {
      stat.typeOfData = 'posted';
    } else if (inv.state === 'draft') {
      stat.typeOfData = 'draft';
      calcRes += inv.amount_untaxed;
    }
  });
  
  // Add residual to monthly stats
  if ((row.totalResidual || 0) > calcRes) {
    let ref = row.lead.date_deadline ? new Date(row.lead.date_deadline) : new Date();
    if (ref < new Date()) ref = new Date();
    
    const stat = row.montlhyStats.find(s => s.year === ref.getFullYear() && s.month === ref.getMonth() + 1);
    
    if (stat) {
      stat.amount += row.totalResidual! - calcRes;
      stat.typeOfData = 'residual';
    }
  }
}

  // Helper to update monthly stats after cell edit
  private updateMonthlyStats(row: LeadForPortfolio, year: number, month: number, newValue: number) {
    // Find the monthly stat for this year/month
    const stat = row.montlhyStats.find(s => s.year === year && s.month === month);
    
    if (stat) {
      // Calculate the delta (change in value)
      const oldValue = stat.amount;
      const delta = newValue - oldValue;
      
      // Update the stat with new amount
      stat.amount = newValue;
      stat.typeOfData = 'draft'; // Mark as draft since we just edited it
      
      // Update lead totals
      row.totalInvoices = (row.totalInvoices || 0) + delta;
      row.totalResidual = (row.totalContracts || 0) - row.totalPosted!;
    } else {
      // Create a new stat if one doesn't exist
      row.montlhyStats.push({
        year: year,
        month: month,
        month_name: this.months[month-1],
        timeline: this.getTimelineForDate(year, month),
        amount: newValue,
        typeOfData: 'draft'
      });
      
      // Update lead totals
      row.totalInvoices = (row.totalInvoices || 0) + newValue;
      row.totalResidual = (row.totalContracts || 0) - row.totalPosted!;
    }
  }

  // Helper to determine timeline for a date
  private getTimelineForDate(year: number, month: number): 'past' | 'future' | 'current' {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    if (year < currentYear || (year === currentYear && month < currentMonth)) {
      return 'past';
    } else if (year > currentYear || (year === currentYear && month > currentMonth)) {
      return 'future';
    } else {
      return 'current';
    }
  }

  // Method to show residual info in a simple dialog
  async showResidualInfo(row: LeadForPortfolio, year: number, month: number, event: Event) {
    event.stopPropagation();
    
    // Basic info about the residual
    const residualAmount = this.getResidualAmount(row, year, month);
    const totalContract = row.totalContracts || 0;
    const totalPosted = row.totalPosted || 0;
   
    const hasDeadline = !!row.lead.date_deadline;
    const deadlineDate = hasDeadline ? new Date(row.lead.date_deadline).toLocaleDateString() : 'Non specificata';
    
    // Create message
    let message = `Residuo per ${row.lead.tracking_code} \n\n`;
    message += `Totale contratto: ${totalContract.toLocaleString('it-IT')} €\n`;
    message += `Totale fatturato o pianificato: ${totalPosted.toLocaleString('it-IT')} €\n`;

    message += `Importo residuo da assegnare: ${residualAmount.toLocaleString('it-IT')} €\n`;

    
    if (hasDeadline) {
      message += `\nData di consegna inserita nella commessa: ${deadlineDate}`;
      const isInMonth = this.isDateInMonth(row.lead.date_deadline, year, month);
      if (isInMonth) {
        message += `\nIl residuo è attribuito a questo mese perché la data di consegna rientra in questo periodo.`;
      } else {
        message += `\nIl residuo è attribuito a questo mese anche se la data di consegna è in un altro periodo, perché questo è il mese corrente o il mese più vicino successivo.`;
      }
    } else {
      message += `\nIl residuo è attribuito a questo mese perché non è stata specificata una data di consegna.`;
    }
    
    // Show alert with the info
    alert(message);
  }

  // ------------ STYLES ------------

  getArea(a: string) {
    return a === 'Facciate e Decking' ? 'Esterni' : a;
  }
  

  getMonthlyBgClass(year: number, month: number): string {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1;
  
    if (year < currentYear || (year === currentYear && month < currentMonth)) {
      return 'bg-muted text-light';    // past
    }
    if (year === currentYear && month === currentMonth) {
      return 'bg-primary';  // current
    }
    return 'bg-light';      // future
  }
  
  getAreaBadgeClass(a: string) {
    switch(a) {
      case 'Tetti': return 'bg-success text-white';
      case 'Case': return 'bg-danger text-white';
      case 'Facciate e Decking': return 'bg-secondary text-white';
      case 'Aziendale': return 'bg-info text-white';
      default: return 'bg-warning text-white';
    }
  }
}