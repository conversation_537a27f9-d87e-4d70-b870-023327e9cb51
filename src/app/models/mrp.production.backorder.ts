import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { MrpProduction } from "./mrp-production";
import { OdooModel } from "./odoo-model.model";
import { OdooMultiRelationship } from "./odoo-multi-relationship.model";
import { OdooRelationship } from "./odoo-relationship.model";

export class MrpProductionBackorder extends OdooModel implements OdooSerializableInterface<MrpProductionBackorder> {
    
    create() {
        return new MrpProductionBackorder()
    }
    public readonly ODOO_MODEL = "mrp.production.backorder";
  
    id: number;
    mrp_production_ids: OdooMultiRelationship<MrpProduction> = new OdooMultiRelationship(MrpProduction)
    mrp_production_backorder_line_ids:OdooMultiRelationship<MrpProductionBackorderLine> = new OdooMultiRelationship<MrpProductionBackorderLine>(MrpProductionBackorderLine)
    // button_validate_picking_ids:OdooMultiRelationship<StockPicking> = new OdooMultiRelationship<StockPicking>(StockPicking)
    show_backorder_lines:boolean = false
}


export class MrpProductionBackorderLine extends OdooModel {

    public readonly ODOO_MODEL = "mrp.production.backorder.line";
    
    id:number
    mrp_production_backorder_id: OdooRelationship<MrpProductionBackorder> = new OdooRelationship<MrpProductionBackorder>()
    mrp_production_id:OdooRelationship<MrpProduction> = new OdooRelationship<MrpProduction>()
    to_backorder:boolean = false
    // fields.Many2one('stock.picking', 'Transfer')
    // to_backorder = fields.Boolean('To Backorder')

    create() {
        return new MrpProductionBackorderLine()
    }

}