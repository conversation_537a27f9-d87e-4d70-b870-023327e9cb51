def get_numeric_attribute_value(quant):
    # Extract numeric value of the attribute if a sort criterion exists
    if sort:
        attr = quant.product_id.product_template_attribute_value_ids.filtered(lambda a: a.attribute_id.id == sort[0][0])
        if attr:
            try:
                return float(attr.name)
            except Exception:
                return 0
    return 0

# Example function for calculating "Pezzi" (Pieces)
def getInPzFast(quant):
    piece_quant = 1  # Default to 1 to avoid division by zero
    product = quant.product_id

    # Look for a packaging named "Pz"
    pz_packaging = product.packaging_ids.filtered(lambda p: p.name == "Pz")
    if pz_packaging:
        try:
            piece_quant = pz_packaging[0].qty or 1
        except Exception:
            piece_quant = 1

    return quant.quantity / piece_quant

# Helper function to extract numeric value of an attribute from a product
def get_attribute_value(product, attribute_id):
    attr_values = product.product_template_attribute_value_ids.filtered(
        lambda a, attribute_id=attribute_id: a.attribute_id.id == attribute_id
    )
    if attr_values:
        try:
            return float(attr_values[0].name)
        except Exception:
            return None
    return None

# Get filter criteria and domain from context
criterias = env.context.get('criterias') or []  # e.g. [['attribute_id', '>', '20'], ['attribute_id2', '<', '1000']]
additional_domain = env.context.get('domain') or []
sort = env.context.get('sort') or []

# Dictionary to store matching attribute values for each attribute
matching_values_by_attribute = {}
debug = []

# Process criteria to filter attribute values
for criteria in criterias:
    attribute_id = criteria[0]
    operator = criteria[1]
    compare_value = criteria[2]

    values = env['product.template.attribute.value'].search([('attribute_id', '=', attribute_id)])
    if not values:
        raise Warning(f"No values found for attribute {attribute_id}!")

    matching_value_ids = []
    try:
        compare_value_num = float(compare_value)
    except Exception:
        raise Warning(f"The comparison value ({compare_value}) for attribute {attribute_id} is not numeric!")

    for value in values:
        try:
            num = float(value.name)
            if (
                (operator == '>' and num > compare_value_num) or
                (operator == '>=' and num >= compare_value_num) or
                (operator == '<' and num < compare_value_num) or
                (operator == '<=' and num <= compare_value_num) or
                (operator == '=' and num == compare_value_num)
            ):
                matching_value_ids.append(value.id)
        except Exception:
            continue

    if not matching_value_ids:
        raise Warning(f"No valid values found for attribute {attribute_id} that meet the filter criteria.")

    # MODIFICA QUI: Gestire più criteri sullo stesso attributo
    if attribute_id in matching_values_by_attribute:
        # Intersezione con eventuali risultati precedenti per lo stesso attributo
        matching_values_by_attribute[attribute_id] = list(
            set(matching_values_by_attribute[attribute_id]) & set(matching_value_ids)
        )
        if not matching_values_by_attribute[attribute_id]:
            raise Warning(f"No values left for attribute {attribute_id} after applying multiple criteria!")
    else:
        matching_values_by_attribute[attribute_id] = matching_value_ids


# Find product IDs that meet ALL attribute filters (AND logic)
valid_product_ids = None

# Apply each criterion as an AND filter
for attribute_id, matching_value_ids in matching_values_by_attribute.items():
    domain = [('product_template_attribute_value_ids', 'in', matching_value_ids)]
    current_products = env['product.product'].search(domain)
    
    if valid_product_ids is None:
        # First criterion
        valid_product_ids = set(current_products.ids)
    else:
        # Apply AND logic with intersection
        valid_product_ids &= set(current_products.ids)
    
    # Early exit if no products match the combined criteria
    if not valid_product_ids:
        break

# Convert to list or empty set if no criteria
valid_product_ids = list(valid_product_ids) if valid_product_ids is not None else []
products = env['product.product'].browse(valid_product_ids)

# Build domain for stock.quant based on found products
if criterias:
    quant_domain = [('product_id', 'in', products.ids)]
else:
    quant_domain = []

# Search stock quants with combined domain
stock_quants = env['stock.quant'].search(quant_domain + additional_domain)

# Sort if required
if sort:
    stock_quants = sorted(stock_quants, key=get_numeric_attribute_value, reverse=(sort[0][1]=='desc'))

# Costruisce un dict { nome_attributo: [lista di valori] }
attribute_map = {}


    
# Build list of dictionaries (table) with one row per stock.quant
table_data = []
for quant in stock_quants:
    tmpl = quant.product_id.product_tmpl_id

    # Costruisce un dict { nome_attributo: [lista di valori] } per questo template
    attribute_map = {}
    for line in tmpl.attribute_line_ids:
        values = line.value_ids.mapped('name')
        attribute_map[line.attribute_id.name] = values

    row = {
        'id': quant.id,
        'product_tmpl_id.id': tmpl.id,
        'product_id.id': quant.product_id.id,
        'product_tmpl_id.attribute_line_ids.ids': tmpl.attribute_line_ids.ids,
        'Prodotto': tmpl.name,
        'UdM': quant.product_id.uom_id.name,
        'Giacenza': tmpl.qty_available,
        'Collo': quant.package_id.name or '',
        'Quantità': quant.quantity,
        'Riservato': quant.reserved_quantity,
        'Ubicazione': quant.location_id.name,
        'Creazione': quant.create_date,
        'Valore': quant.value,
        'Pezzi': getInPzFast(quant),
        'Lunghezza': get_attribute_value(quant.product_id, 33),
        'Larghezza': get_attribute_value(quant.product_id, 5),
        'Altezza': get_attribute_value(quant.product_id, 6),
        # Nuovo campo "Attributi" con mappatura per questo template
        'Attributi': attribute_map,
    }
    table_data.append(row)

# Return an action with formatted table data
action = {
    "type": "ir.actions.client",
    "tag": "display_table",
    "params": {
        "title": "Stock Quants trovati (filtrati con criteri AND)",
        "data": table_data,
    }
}

action