import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import * as Papa from 'papapar<PERSON>';
import { ProductAttribute } from '../models/product.attribute.model';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { firstValueFrom } from 'rxjs';
import { ProductTemplateAttributeValue } from '../models/product.template.attribute.value.model';
import { ProductAttributeValue } from '../models/product.attribute.value';
import { Product } from '../models/product.model';
import { SaleOrder } from '../models/sale-order.model';
import { SaleOrderLine } from '../models/sale-order-line.model';
import { PurchaseOrder } from '../models/purchase-order.model';
import { PurchaseOrderLine } from '../models/purchase-order-line.model';
import { StockPicking } from '../models/stock-picking';
import { StockMoveLine } from '../models/stock-move-line';
import { ODOO_IDS } from '../models/deal';
import { StockQuantPackage } from '../models/stock-quant-package';
import { ProductTemplateAttributeLine } from '../models/product.template.attribute.line';

const ID_TEMPLATE = 47630;

@Component({
  selector: 'app-importer',
  standalone: true,
  imports: [CommonModule, TableModule],
  templateUrl: './importer.component.html',
  styleUrl: './importer.component.scss'
})
export class ImporterComponent implements OnInit {
  data: any[] = [];
  columns: any[] = [];
  logMessages: string[] = [];

  constructor(private odooEm:OdooEntityManager) { }

  ngOnInit(): void {
    // Initialize component
  }

  log(message: string, ...args: any[]): void {
    const formattedMessage = args.length > 0 
      ? `${message} ${args.map(arg => JSON.stringify(arg)).join(' ')}`
      : message;
    
    console.log(formattedMessage);
    this.logMessages.push(formattedMessage);
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.readCSV(file);
    }
  }

  readCSV(file: File): void {
    Papa.parse(file, {
      header: true,
      complete: (results) => {
        if (results.data && results.data.length > 0) {
          this.data = results.data;
          
          // Extract column headers from the first row
          if (results.meta && results.meta.fields) {
            this.columns = results.meta.fields.map(field => ({
              field: field,
              header: field
            }));
          }
        }
      },
      error: (error) => {
        console.error('Error parsing CSV:', error);
      }
    });
  }


  importAttributes() {
    this.columns.forEach(async col => {
      console.log("COL", col)
      if (col.field.startsWith('*')) {


        var cleanedData = this.data.map(row => row[col.field]).filter((row, index, self) =>
          index === self.findIndex((t) => (
            t === row
          ))
        );

        let r = await firstValueFrom(this.odooEm.search<ProductAttribute>(new ProductAttribute(), [["name","like",col.field.substring(1)]]))
        if (r.length == 0) 
          this.log("Non ho trovato l'attributo ", col.field.substring(1));
        else {
          this.log("Trovato l'attributo ", col.field.substring(1), " con id ", r[0].id);
        }
      }
    });
  }

  importAttributeValues() {

    this.columns.forEach(async col => {
      console.log("COL", col)
      if (col.field.startsWith('*')) {


        var cleanedData = this.data.map(row => row[col.field]).filter((row, index, self) =>
          index === self.findIndex((t) => (
            t === row
          ))
        );

        let r = await firstValueFrom(this.odooEm.search<ProductAttribute>(new ProductAttribute(), [["name","like",col.field.substring(1)]]))
        if (r.length == 0) 
          this.log("Non ho trovato l'attributo ", col.field.substring(1));
        else {
          this.log("Trovato l'attributo ", col.field.substring(1), " con id ", r[0].id);
        }

        
        // add product attribute values
        cleanedData.forEach(async (d) => {

          // search if exist
          let r2 = await firstValueFrom(this.odooEm.search<ProductAttributeValue>(new ProductAttributeValue(), 
          [["name","like",d],["attribute_id","=",r[0].id]]))
          if (r2.length > 0) {
            this.log("Valore gia' esistente ", d, " per l'attributo ", col.field.substring(1));
          } else {
            this.log("ATTENZIONE!!! Creo il valore ", d, " per l'atributo (non nel template per ora) ", col.field.substring(1));
            await firstValueFrom(this.odooEm.create<ProductAttributeValue>(new ProductAttributeValue(), {
              name: d,
              attribute_id: r[0].id
            }))
          }
        })
      }
    });
  }


  async importProductTemplateAttributeValues() {
    for(var col of this.columns) {
    // this.columns.forEach(async col => {
      console.log("COL", col)
      if (col.field.startsWith('*')) {
        
        // find attribute
        let attribute = await firstValueFrom(this.odooEm.search<ProductAttribute>(
          new ProductAttribute(), [["name","=",col.field.substring(1)]])
        )
        if (attribute.length == 0) 
          this.log("Non ho trovato l'attributo ", col.field.substring(1));
        else {
          this.log("Trovato l'attributo ", col.field.substring(1), " con id ", attribute[0].id);
        }

       

        // filter unique rows for this col row[col.field]  
        // 
        var uniquerows = this.data.filter((row, index, self) =>
          index === self.findIndex((t) => (
            t[col.field] === row[col.field]
          ))
        );

        console.log("VALORI UNICI PER L'ATTRIBUTO: ", col.field, uniquerows);

        for (var row of uniquerows) {


           // find related attribute value for tmpl and attribute
        let attributeValues = await firstValueFrom(this.odooEm.search<ProductAttributeValue>(
          new ProductAttributeValue(), [
            ["attribute_id","=",attribute[0].id],
            ["name","=",row[col.field]
          ]
          ])
        )
        if (attributeValues.length == 0) {
          
            this.log("ATTENZONE! Non ho trovato il valore  ", row[col.field], " NELL'ATTRIBUTO (NO TEMPLATE) ", col.field.substring(1), "LO CREO");

            //create attribute value
            attributeValues[0] = await firstValueFrom(this.odooEm.create<ProductAttributeValue>(new ProductAttributeValue(), {
              name: row[col.field],
              attribute_id: attribute[0].id
            }))



        } else {
          this.log("Trovato il valore ", attributeValues[0].name, " per l'attributo ", col.field.substring(1));
        }

        

        // obtain attribute line
        let attributeLine = await firstValueFrom(this.odooEm.search<ProductTemplateAttributeLine>(
          new ProductTemplateAttributeLine(), [
            ["product_tmpl_id","=",ID_TEMPLATE],
            ["attribute_id","=",attribute[0].id]
          ])
        )
        if (attributeLine.length == 0) {
          this.log("ATTENZONE! NON HO TROVATO L'ATTRIBUTO", attribute[0].id, " per il template ", ID_TEMPLATE, "STO AGGIUNGENDO LA LINEA!!! (MALE MALE)");
        } else {
          this.log("Trovata la linea di attributo ", attributeLine[0].display_name, " per il template ", ID_TEMPLATE);
        }



        // await this.odooEm.resolve(tmpl.attribute_line_ids).toPromise();
        var tal = attributeLine[0];
        // var ids = tal.value_ids.ids;
        // ids.splice(ids.length, 0, attributeValues[0].id);
    
        // faster than
        // PATCH ODOO
        // await this.odooEm.update(tmpl, {
        //   attribute_line_ids:[[1, tal.id, {value_ids: [[4, v.id]]}]]
        //     //  attribute_line_ids:[[1, tal.id, {value_ids: [[6, false, ids]]}]]
        // }).toPromise()

        console.log ("carico il valore dell'attributo ", attributeValues[0].name, " per la linea ", attributeLine[0].display_name, "anche se forse gia' esiste");
        await this.odooEm.update(tal, { value_ids: [[4,  attributeValues[0].id]] }).toPromise();
    
        
     

          await firstValueFrom(this.odooEm.create<ProductTemplateAttributeValue>(new ProductTemplateAttributeValue(), {
            product_attribute_value_id: attributeValues[0].id,
            product_tmpl_id: ID_TEMPLATE,
            ptav_active: true,
            name: row[col.field],
            attribute_line_id: attributeLine[0].id
          }))

       
        // create product template attribute value
        // let r3 = await firstValueFrom(this.odooEm.search<ProductTemplateAttributeValue>(
        //   new ProductTemplateAttributeValue(), [
        //     ["product_attribute_value_id","=",attributeValues[0].id],
        //     ["product_tmpl_id","=",ID_TEMPLATE]
        //   ])
        // )

         
        }

      }
    };
  }

  async importVariantsAndPurchase() {

    // create a variant for each row
    // this.data.forEach(async row => {
      var purchId = 4514
      await firstValueFrom(this.odooEm.search<PurchaseOrder>(new PurchaseOrder(), [["id","=",purchId]]))
      console

    for (var row of this.data) {
      let index = this.data.indexOf(row);
      
      let attributes = {};
      this.columns.forEach(col => {
        if (col.field.startsWith('*')) {
          attributes[col.field.substring(1)] = row[col.field];
        }
      });
      // keep a log count for the variant creation
      this.log("Analyzing row " + index +" of " + this.data.length + " with attributes " + JSON.stringify(attributes)); 
      let resultProductId = await this.odooEm.createProductProduct(ID_TEMPLATE, attributes);

      console.log("Variant found or created:", resultProductId);

        var line = await firstValueFrom(this.odooEm.create<PurchaseOrderLine>(new PurchaseOrderLine(), {
          order_id: purchId,
          product_id: resultProductId,
          product_qty: Number(row["qtà dist"].replace(",",".")),
          price_unit: Number(row["Nuovo costo um"].replace(",","."))
        }))

        console.log("Line added to the purchase order:", line);

    };


  }



//   async importSales() {

//     for (var row of this.data) {
//       let attributes = {};
//       this.columns.forEach(col => {
//         if (col.field.startsWith('*')) {
//           attributes[col.field.substring(1)] = row[col.field];
//         }
//       });


//       var names = Object.entries(attributes).map(([key, value]) => {
//         return ["name","like",value]
//       })

//       names.push(["product_tmpl_id","=",ID_TEMPLATE])
//       var ptavs = await firstValueFrom(this.odooEm.search<ProductTemplateAttributeValue>(new ProductTemplateAttributeValue(), 
//       names))



//       // create label tp sear
//       // let nameToSearch = "Pavimento Galimberti VERSIONE 2 (" + Object.values(attributes).join(", ") + ")"
//       // var product = await firstValueFrom(this.odooEm.search<Product>(new Product(), [["display_name","like",nameToSearch]]))

//       // console.log("product", product)
//       continue

//       let attributeIds = await Promise.all(Object.entries(attributes).map(async ([key, value]) => {



        


//         let r = await firstValueFrom(this.odooEm.search<ProductTemplateAttributeValue>(new ProductTemplateAttributeValue(), 
//           [["product_tmpl_id","=",ID_TEMPLATE], ["name","like",value],["attribute_id.name","like",key]]))
//         if (r.length == 0) {
//           this.log("Non ho trovato il valore ", value, " per l'attributo ", key);
//           return null;
//         } else {
//           this.log("Trovato (" +  r.length + ") il valore ", "--", value, " per l'attributo ", key, " con id ", r[0].id);
//           return r[0].id;
//         }
//       }));

//       console.log("attributeIds", attributeIds);

//       var domain = [
//         ["product_tmpl_id","=",ID_TEMPLATE], 
//       ]

//       attributeIds.forEach((id, index) => {
//         if (id) {
//           domain.push(["product_template_attribute_value_ids","in",id]);
//         }
//       });

//       let r = await firstValueFrom(this.odooEm.search<Product>(new Product(), domain))

//       if (r.length != 1) 
//         this.log("Non ho trovato il prodotto ", attributes);
//       else {
//         this.log("Trovato il prodotto ", r[0].id);
//         var sale = 4482
//         await firstValueFrom(this.odooEm.search<PurchaseOrder>(new PurchaseOrder(), [["id","=",sale]]))


//          console.log({order_id: sale,
//           product_id: r[0].id,
//           product_uom_qty: row["qtà dist"].replace(",","."),
//           price_unit: row["Nuovo costo um"]})


//         var line = await firstValueFrom(this.odooEm.create<PurchaseOrderLine>(new PurchaseOrderLine(), {
//           order_id: sale,
//           product_id: r[0].id,
//           product_qty: Number(row["qtà dist"].replace(",",".")),
//           price_unit: Number(row["Nuovo costo um"].replace(",","."))
//         }))

// //qtà dist	Nuovo costo um
//         // await firstValueFrom(this.odooEm.update(line, {
//         //   product_qty: line.product_uom_qty
//         // }))

        

//       }


//       // 47581 Pavimento
//       // await this.odooEm.createProductProduct(47581, attributes);
//     };

//   }


  async fillPicking() {

    let ps = await firstValueFrom(this.odooEm.search<StockPicking>(new StockPicking(), [["id","=",34937]]))

    if (ps.length != 1) 
      throw "Non ho trovato il picking"

    // resolve move_raw_ids
    await firstValueFrom(this.odooEm.resolve(ps[0].move_ids))

      // for (var row of this.data) {
        for (var x = 0; x < ps[0].move_ids.values.length;x++) {

        
          var m = ps[0].move_ids.values[x]
          var csvline = this.data[x]
          console.log("sto piccando la riga", x, "su ", ps[0].move_ids.values.length, " movimenti :", m.product_id.name, m.product_uom_qty)
          
          // search package if not exist create
          var packs = await firstValueFrom(this.odooEm.search<StockQuantPackage>(new StockQuantPackage(), [["name","=","C" + csvline["Collo"]]]))
          var p:StockQuantPackage
          if (packs.length == 0) {
            p = await firstValueFrom(this.odooEm.create<StockQuantPackage>(new StockQuantPackage(), {
              name: "C" + csvline["Collo"],
            }))
          } else {
            p = packs[0]
          }

          // delete all movelines before to recreate no need it takes up time for nothing. i delete them manually
          // await firstValueFrom(this.odooEm.delete(new StockMoveLine(), m.move_line_ids.ids))

          await firstValueFrom(this.odooEm.create<StockMoveLine>(new StockMoveLine(), {
            move_id: m.id,
            product_id: m.product_id.id,
            qty_done: m.product_uom_qty,
            picking_id: ps[0].id,
            location_dest_id: 8,
            
            result_package_id: p.id
          }))
        }
      }

}
