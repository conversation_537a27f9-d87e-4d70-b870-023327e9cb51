<div class="card shadow-sm mb-4">
  <!-- Card header -->
  <div class="card-header px-3 d-flex align-items-center justify-content-between" style="height: 48px">
    <h5 class="card-title mb-0">Lavorazioni e progettazione</h5>
    <div>
      <button class="btn btn-link" title="Espandi" (click)="toggleComponent()">
        <i class="fa-solid fa-arrows-up-down fa-lg"></i>
      </button>
    </div>
  </div>

  <!-- Component content (shown when expanded) -->
  <div *ngIf="showComponent" class="d-flex flex-column">
    <!-- Status message -->
    <div *ngIf="statusMessage" class="p-2 text-primary">{{statusMessage}}</div>

    <!-- Time grouping selector -->
    <div *ngIf="allHours.length > 0" class="p-3 border-bottom">
      <div class="d-flex justify-content-end">
        <div class="btn-group" role="group">
          <ng-container *ngFor="let option of timeGroupingOptions">
            <input type="radio" class="btn-check" [id]="'timeGroup' + option.value" 
                  [value]="option.value" [(ngModel)]="timeGroupingMode" 
                  (change)="changeTimeGrouping(option.value)">
            <label class="btn btn-outline-muted" [for]="'timeGroup' + option.value">
              {{option.label}}
            </label>
          </ng-container>
        </div>
      </div>
    </div>

    <!-- Table view for each main group -->
    <div *ngFor="let group of mainGroups" class="border mb-3">
      <!-- Main group header -->
      <div class="border-bottom bg-light d-flex flex-row align-items-center py-2" 
           (click)="toggleGroupExpansion(group)" style="cursor: pointer">
        <i *ngIf="!group.isOpen" class="px-3 fa-solid fa-caret-right" title="Espandi categoria"></i>
        <i *ngIf="group.isOpen" class="px-3 fa-solid fa-caret-down" title="Comprimi categoria"></i>
        
        <div class="fs-6 fw-bold">{{group.groupName}}</div>
        
        <div class="ms-auto me-3 d-flex align-items-center">
          <span class="me-3">Ore: <strong>{{formatHours(group.totalHours)}}</strong></span>
        </div>
      </div>

      <!-- Table for the group -->
      <div *ngIf="group.isOpen" class="table-responsive">
        <table class="table table-bordered table-hover mb-0">
          <!-- Table header with periods -->
          <thead class="table-light">
            <tr>
              <th class="sticky-column bg-light" style="min-width: 200px;">Dipendente</th>
              <th *ngFor="let period of group.periods" class="text-center" style="min-width: 100px;">
                {{period.label}}
              </th>
              <th class="text-center bg-light" style="min-width: 100px;">Totale</th>
            </tr>
          </thead>
          <!-- Table body with employees and their hours -->
          <tbody>
            <tr *ngFor="let employee of group.employeeRows">
              <td class="sticky-column bg-white">{{employee.name}}</td>
              <td *ngFor="let period of group.periods" class="text-center">
                <ng-container *ngIf="employee.periodHours[period.key]; else emptyCell">
                  {{formatHours(employee.periodHours[period.key])}}
                </ng-container>
                <ng-template #emptyCell>-</ng-template>
              </td>
              <td class="text-center fw-bold bg-light">
                {{formatHours(employee.totalHours)}}
              </td>
            </tr>
            <!-- Total row -->
            <tr class="table-active fw-bold">
              <td class="sticky-column">Totale</td>
              <td *ngFor="let period of group.periods" class="text-center">
                {{formatHours(period.totalHours)}}
              </td>
              <td class="text-center bg-light">
                {{formatHours(group.totalHours)}}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty state -->
    <div *ngIf="allHours.length === 0 && !statusMessage" class="p-4 text-center text-muted">
      <p>Nessun dato disponibile per questa commessa.</p>
    </div>

    <!-- Footer with total -->
    <div *ngIf="allHours.length > 0" class="border-top p-3 d-flex justify-content-end">
      <div class="d-flex align-items-center">
        <span class="fw-bold me-3">TOTALE:</span>
        <span class="me-3">Ore: <strong>{{formatHours(allTotal)}}</strong></span>
      </div>
    </div>
  </div>
</div>