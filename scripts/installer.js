// source: https://github.com/netlify-labs/all-the-functions/blob/master/scripts/installer.js
// thread: https://community.netlify.com/t/functions-and-node-modules/2323/8

const path = require('path')
const fs = require('fs')
const cp = require("child_process")
const globby = require("globby")



function installDeps(functionDir, cb) {
  cp.exec("npm i", { cwd: functionDir }, cb)
}

(async () => {

  const findJSFiles = ['package.json', '*/package.json', '!node_modules', '!**/node_modules']
  const directory = path.join(__dirname, '..', 'functions')
  const foldersWithDeps = await globby(findJSFiles, { cwd: directory })
  const folders = foldersWithDeps.map(fnFolder => {
    return fnFolder.substring(0, fnFolder.indexOf("package.json"))
  }).map((folder) => {
    installDeps(path.join(__dirname, '..', 'functions', folder), () => {
      console.log(`${folder} dependencies installed`)
    })
  })
})()
