import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { GapiService } from 'src/app/shared/services/g-api.service';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

// Configuration for contact drive folders
export const CONTACT_DRIVE_CFG = {
  template_folder_id: "1CF9GPjc0UnTnCXJu2nIdDkuDcj9_1zKr",
  output_folder_id: "1ARJjnRMr1eNwQrM6hRgEmsFn9CLXhuCL"
}

@Component({
    selector: 'app-contact-drive',
    template: `
      <!-- Create folder button - muted color indicates no folder exists -->
      <button *ngIf="!hasDriveFolder" 
              class="btn" 
              (click)="createContactFolder()" 
              [disabled]="isCreating">
          <i class="fa fa-folder fa-2x" 
             [class.text-muted]="!isCreating"
             [class.text-warning]="isCreating"></i>
      </button>

      <!-- Access existing folder button - primary color indicates folder exists -->
      <a *ngIf="hasDriveFolder" 
         target="_blank" 
         class="btn" 
         [href]="getDriveLinkFromRef(contact.ref)">
          <i class="fa fa-folder fa-2x text-primary"></i>
      </a>
    `,
    standalone: false
})
export class ContactDriveComponent implements OnInit {
  
  @Input() contact: any; // res.partner object

  
  isCreating = false;
  hasDriveFolder = false;

  constructor(
    private gapiService: GapiService,
    private odooEm: OdooEntityManager
  ) { }

  ngOnInit() {
    // Check if contact already has a drive folder reference
    this.hasDriveFolder = this.contact?.ref && this.contact.ref.includes('drive.google.com');
  }

  async createContactFolder() {
    if (this.isCreating || this.hasDriveFolder) return;
    
    this.isCreating = true;
    
    try {
      // Generate folder name based on contact info
      const folderName = this.generateContactFolderName();
      
      // Clone the template folder to the output location
      const folderId = await this.gapiService.cloneFolder(
        { id: CONTACT_DRIVE_CFG.template_folder_id },
        { id: CONTACT_DRIVE_CFG.output_folder_id },
        false, // not merge
        folderName,
        null // no subject needed for simple case
      );

      // Generate the drive link
      const driveLink = this.getDriveLinkFromId(folderId);
      
      // Update the contact's ref field with the drive link
      await firstValueFrom(this.odooEm.update(this.contact, { 
        ref: driveLink 
      }));

      // Update local state
      this.contact.ref = driveLink;
      this.hasDriveFolder = true;

      
    } catch (error) {
      console.error('Error creating contact folder:', error);
      alert('Errore durante la creazione della cartella: ' + error.message);
    } finally {
      this.isCreating = false;
    }
  }

  private generateContactFolderName(): string {
    let folderName = this.contact.name || 'Contatto Senza Nome';
    
    // Add additional info if available
    if (this.contact.city) {
      folderName += ` - ${this.contact.city}`;
    }
    
    // Add customer reference or VAT if available
    if (this.contact.vat) {
      folderName += ` - ${this.contact.vat}`;
    } else if (this.contact.customer_code) {
      folderName += ` - ${this.contact.customer_code}`;
    }
    
    return folderName;
  }

  private getDriveLinkFromId(folderId: string): string {
    return `https://drive.google.com/drive/u/0/folders/${folderId}`;
  }

  private getDriveLinkFromRef(ref: string): string {
    // If ref is already a full link, return it
    if (ref && ref.includes('drive.google.com')) {
      return ref;
    }
    // Otherwise assume it's just an ID and construct the link
    return this.getDriveLinkFromId(ref);
  }
}