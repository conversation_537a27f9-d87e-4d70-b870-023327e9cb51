<nav class="navbar text-white navbar-dark fixed-top px-1">
    <button class="btn btn-link text-white mr2-auto" routerLink=".." >
        <i class="fas fa-chevron-left"></i>      
    </button>
    <h1 class="ms-auto me-auto">Arrivo ordine {{purchase?.name}} </h1>
    <p>&nbsp;</p>
    <bar-loader [loading]="loading"></bar-loader>
  </nav>
  
    <p class="ms-3" style="margin-top:78px;">Per <a target="_blank" class="text-dark" href="/contact/{{purchase?._partner_id[0].id}}">{{purchase?._partner_id[0].name}}</a></p>
    <hr>
    <br>
    
    <div class="d-flex justify-content-center align-items-center mb-2 px-3">
      <h2 class="me-auto mb-0"><PERSON>li scaricati</h2>
      <a class="btn btn-outline-dark" data-test-id="addBarcodeButton" (click)="addBarcode()"> <i class="fas fa-plus "></i> Nuovo</a>
    </div>
      
    <div class="list-group mb-3">
        <a *ngFor="let b of purchase._barcode_ids" class="list-group-item list-group-item-action rounded-0 d-flex align-items-center" > 
                <span class="me-auto">{{b.barcode}}</span>
                <span class="badge text-muted">{{b.create_date}}</span>
                <small class="me-2 badge badge-info text-muted">{{b.create_uid[1]}}</small>
                <!--<span *ngIf="!p.shipment_date" class="badge badge-success">Da spedire</span>  -->
        </a>
    </div>
    <app-barcode-reader-dynamsoft  *ngIf="showBarcodeScanner" #barcodePicking (onBarCodeSuccess)="onBarCodeSuccess($event)"></app-barcode-reader-dynamsoft>
  