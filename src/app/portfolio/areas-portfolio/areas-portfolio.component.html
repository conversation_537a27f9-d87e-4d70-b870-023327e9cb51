<!-- ================== Improved Filter Controls with Legend ================== -->
<div class="d-flex align-items-center flex-wrap gap-3 py-3 px-2 border-bottom">
  <!-- Filter Sector -->
  <div class="d-flex align-items-center">
    <label for="sectorFilter" class="form-label mb-0 me-2 fw-semibold">Settore:</label>
    <select id="sectorFilter" class="form-select form-select-sm" style="min-width: 120px" [(ngModel)]="selectedSector"
      (ngModelChange)="onFilterChange()">
      <option [ngValue]="null">All</option>
      <option *ngFor="let s of sectorOptions" [ngValue]="s">{{ s }}</option>
    </select>
  </div>

  <!-- Filter Responsible -->
  <div class="d-flex align-items-center">
    <label for="userFilter" class="form-label mb-0 me-2 fw-semibold">Responsabile::</label>
    <div class="d-flex align-items-center">
      <select id="userFilter" class="form-select form-select-sm" style="min-width: 120px" [(ngModel)]="selectedUser"
        (ngModelChange)="onFilterChange()">
        <option [ngValue]="null">All</option>
        <option *ngFor="let u of userOptions" [ngValue]="u">{{ u }}</option>
      </select>
      <!-- Confirmation icon if selected user matches current user -->
      <div *ngIf="user.name == selectedUser" class="text-success ms-2">
        <i class="fa-solid fa-check"></i>
      </div>
    </div>
  </div>

  <!-- Visual separator -->
  <div class="vr mx-1" style="height: 28px;"></div>

  <!-- Edit Mode Button -->
  <div>
    <button type="button" class="btn btn-outline-primary btn-sm d-flex align-items-center" 
      (click)="toggleEdit()" [disabled]="user.name != selectedUser">
      <i class="fa fa-file-invoice me-2"></i>
      {{ editMode ? 'Esci Modifica' : 'Modalità Modifica' }}
    </button>
  </div>
  
  <!-- Legend Dropdown -->
  <div>
    <button class="btn btn-sm btn-outline-secondary d-flex align-items-center" type="button" id="legendDropdown"
      data-bs-toggle="dropdown" aria-expanded="false">
      <i class="fa-solid fa-circle-info me-2"></i> Legenda
    </button>
    <div class="dropdown-menu p-3 shadow-sm" style="min-width: 350px; max-width: 600px;">
      <h6 class="dropdown-header border-bottom pb-2 mb-2 text-primary">Colori Fatture</h6>
      <div class="mb-2 d-flex align-items-center">
        <div class="color-indicator bg-success me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
        <span class="text-success fw-bold">10.000</span>
        <span class="ms-2">fattura emessa e registrata</span>
      </div>
      <div class="mb-2 d-flex align-items-center">
        <div class="color-indicator bg-warning me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
        <span class="text-warning fw-bold">10.000</span>
        <span class="ms-2">fattura in bozza</span>
      </div>
      <div class="mb-2 d-flex align-items-center">
        <div class="color-indicator bg-danger me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
        <span class="text-danger fw-bold">10.000</span>
        <span class="ms-2">residuo non inserito a programma</span>
      </div>
      
      <h6 class="dropdown-header border-bottom pb-2 mb-2 mt-3 text-primary">Funzionalità</h6>
      <div class="mb-2 d-flex align-items-center">
        <i class="fa-solid fa-file-invoice text-secondary me-2"></i>
        <span class="fw-semibold">Modalità Modifica:</span>
        <span class="ms-1">attiva/disattiva modifica importi</span>
      </div>
      <div class="mb-2 d-flex align-items-center">
        <i class="fa-solid fa-info-circle text-secondary me-2"></i>
        <span class="fw-semibold">Info Residuo:</span>
        <span class="ms-1">mostra dettagli sul residuo</span>
      </div>
      <div class="mb-2 d-flex align-items-center">
        <div class="d-flex align-items-center me-2">
          <i class="fa-solid fa-chevron-right text-secondary"></i>
          <span class="mx-1">/</span>
          <i class="fa-solid fa-chevron-down text-secondary"></i>
        </div>
        <span class="fw-semibold">espande/comprime settori</span>
      </div>
    </div>
  </div>
</div>

<!-- ================ Main Table ================ -->
<p-table [value]="portfolioLeads" sortMode="multiple" [customSort]="true" (sortFunction)="customSort($event)"
  scrollable="true" scrollDirection="horizontal" [resizableColumns]="true" showGridlines stripedRows
  tableStyleClass="p-datatable-sm compact-table" [autoLayout]="true">

  <!-- HEADER -->
  <ng-template pTemplate="header">
    <tr class="p-datatable-header small">
      <th  pFrozenColumn pSortableColumn="lead.tracking_code">Codice<p-sortIcon
          field="lead.tracking_code"></p-sortIcon></th>
      <th  pFrozenColumn pSortableColumn="lead.partner_id.name" class="bg-white">Cliente<p-sortIcon
          field="lead.partner_id.name"></p-sortIcon></th>
      <th  pFrozenColumn pSortableColumn="lead.city">Descrizione<p-sortIcon
          field="lead.city"></p-sortIcon></th>
      <th  pFrozenColumn pSortableColumn="lead.user_id.name">Responsabile<p-sortIcon
          field="lead.user_id.name"></p-sortIcon></th>
      <th  pFrozenColumn pSortableColumn="totalContracts">Contratti<p-sortIcon
          field="totalContracts"></p-sortIcon>
      </th>
      <th  pFrozenColumn pSortableColumn="totalResidual">Residuo<p-sortIcon
          field="totalResidual"></p-sortIcon></th>
      <ng-container *ngFor="let yr of years">
        <ng-container *ngFor="let mn of months; let mi = index">
          <th [ngClass]="getMonthlyBgClass(yr, mi + 1)" class="text-center">
            {{ mn }}<br />{{ yr }}
          </th>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>

  <!-- BODY: group-headers now include totals -->
  <ng-template pTemplate="body" let-rowData let-ri="rowIndex">
    <!-- GROUP HEADER when area changes -->
    <ng-container *ngIf="ri === 0 || portfolioLeads[ri-1].lead.area !== rowData.lead.area">
      <tr class="p-rowgroup-header text-dark fw-bold " [ngClass]="getAreaBadgeClass(rowData.lead.area)">
        <!-- 1) Sector + toggle -->
        <td pFrozenColumn [ngClass]="getAreaBadgeClass(rowData.lead.area)">
          <button type="button" class="btn btn-link btn-sm p-0 me-2" (click)="toggleGroup(rowData.lead.area)">
            <i [ngClass]="
                expandedGroups[rowData.lead.area]
                  ? 'fa fa-chevron-down'
                  : 'fa fa-chevron-right'
              "></i>
          </button>
          {{ getArea(rowData.lead.area) }}
        </td>


        <!-- 4) Name column shows count -->
        <td pFrozenColumn [ngClass]="getAreaBadgeClass(rowData.lead.area)">
          {{ getGroupCount(rowData.lead.area) }} commesse
        </td>
        

        <!-- 3) Code column (blank) -->
        <td pFrozenColumn [ngClass]="getAreaBadgeClass(rowData.lead.area)"></td>


        <!-- 5 & 6) City, Resp columns blank -->
        <td pFrozenColumn [ngClass]="getAreaBadgeClass(rowData.lead.area)"></td>


        <!-- 7) Contracts sum -->
        <td pFrozenColumn [ngClass]="getAreaBadgeClass(rowData.lead.area)">
          {{ getGroupSum(rowData.lead.area, 'totalContracts') | number:'1.0-0' }}
        </td>

        <!-- 8) Residual sum -->
        <td pFrozenColumn [ngClass]="getAreaBadgeClass(rowData.lead.area)">
          {{ getGroupSum(rowData.lead.area, 'totalResidual') | number:'1.0-0' }}
         <br>
          (
          {{ getGroupSum(rowData.lead.area, 'totalResidual') / getGroupSum(rowData.lead.area, 'totalContracts') |
          percent:'1.0-0' }}
          )
        </td>

        <!-- 9+) Monthly sums -->
        <ng-container *ngFor="let yr of years">
          <ng-container *ngFor="let mn of months; let mi = index">
            <td [ngClass]="getAreaBadgeClass(rowData.lead.area)">
              {{ getGroupMonthlySum(rowData.lead.area, yr, mi+1) | number:'1.0-0' }}
            </td>
          </ng-container>
        </ng-container>
      </tr>
    </ng-container>

    <!-- DATA ROWS: only if expanded -->
    <tr *ngIf="expandedGroups[rowData.lead.area]">
      <!-- First 8 frozen columns -->
      <td pFrozenColumn>{{ rowData.lead.tracking_code }}</td>
      <td pFrozenColumn class="text-wrap">{{ rowData.lead.partner_id.name }}</td>
      <td pFrozenColumn class="text-wrap">{{ rowData.lead.city }} - {{ rowData.lead.name }}  </td>
      <!-- take only the iniitals of the user -->
      <td pFrozenColumn>{{ rowData.lead.user_id.name  }}</td>
      <td pFrozenColumn>{{ rowData.totalContracts | number:'1.0-0' }}</td>
      <td pFrozenColumn>
        {{ rowData.totalResidual | number:'1.0-0'}}
       <br>
        (
        {{ rowData.totalResidual / rowData.totalContracts | percent:'1.0-0' }}
        )
      </td>
      
      <!-- Monthly data cells -->
      <ng-container *ngFor="let yr of years; let yi = index">
        <ng-container *ngFor="let _ of months; let mi = index">
          <!-- Monthly data cells with onBlur handling -->
<td class="position-relative" [ngClass]="{
  'text-success': getMonthlyType(rowData, yr, mi+1) === 'posted',
  'text-warning': getMonthlyType(rowData, yr, mi+1) === 'draft',
  'text-danger': getMonthlyType(rowData, yr, mi+1) === 'residual'
}">
<div class="d-flex align-items-center justify-content-between">
  <!-- Cell value with in-place edit for draft/residual/empty cells -->
  <ng-container *ngIf="getMonthlyType(rowData, yr, mi+1) === 'posted'; else editableCell">
    <!-- For posted cells - read-only display -->
    <span>{{ getMonthlyAmount(rowData, yr, mi+1) | number:'1.0-0' }}</span>
  </ng-container>
  
  <ng-template #editableCell>
    <!-- For draft/residual/empty cells - editable when in edit mode -->
    <ng-container *ngIf="editMode; else readOnlyValue">
      <input type="number" 
             class="form-control form-control-sm" 
             style="width: 75px; height: 25px;" 
             [ngModel]="getMonthlyAmount(rowData, yr, mi+1)" 
             (blur)="updateCellValue(rowData, yr, mi+1, $event.target.value)"
             (click)="$event.stopPropagation()"
             (keydown.enter)="$event.target.blur()">
    </ng-container>
    
    <ng-template #readOnlyValue>
      <!-- Non-edit mode display for draft/residual -->
      <span *ngIf="getMonthlyAmount(rowData, yr, mi+1) > 0">
        {{ getMonthlyAmount(rowData, yr, mi+1) | number:'1.0-0' }}
      </span>
    </ng-template>
  </ng-template>

  <!-- Info button only for residual cells -->
  <button *ngIf="getMonthlyType(rowData, yr, mi+1) === 'residual' && !editMode" 
          type="button" 
          class="btn btn-link btn-sm p-0 ms-1"
          (click)="showResidualInfo(rowData, yr, mi+1, $event)" 
          title="Visualizza dettagli residuo">
    <i class="fa fa-info-circle small"></i>
  </button>
</div>
</td>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>

  <!-- Footer with totals -->
  <ng-template pTemplate="footer">
    <tr class="p-datatable-footer text-white fw-bold bg-primary border-top border-5">
      <td pFrozenColumn class="text-white fw-bold bg-primary"> Totale</td>

      <td pFrozenColumn class="text-white fw-bold bg-primary"> {{this.portfolioLeads.length}} commesse </td>
      <td pFrozenColumn class="text-white fw-bold bg-primary"></td>
      <td pFrozenColumn class="text-white fw-bold bg-primary"></td>
      <td pFrozenColumn class="text-white fw-bold bg-primary">
        {{ getTotalSum('totalContracts') | number:'1.0-0' }}
      </td>
      <td pFrozenColumn class="text-white fw-bold bg-primary">
        {{ getTotalSum('totalResidual') | number:'1.0-0' }}
        <br />
        (
        {{ getTotalSum('totalResidual') / getTotalSum('totalContracts') | percent:'1.0-0' }}
        )
      </td>
      <ng-container *ngFor="let yr of years">
        <ng-container *ngFor="let mn of months; let mi = index">
          <td class="text-white fw-bold bg-primary">
            {{ getTotalMonthlySum(yr, mi+1) | number:'1.0-0' }}
          </td>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>

  <ng-template pTemplate="emptymessage">
    <tr>
      <td [attr.colspan]="8 + years.length * months.length">
        No leads found.
      </td>
    </tr>
  </ng-template>
</p-table>