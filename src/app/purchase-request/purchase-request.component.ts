import { Component, Input, OnInit, AfterViewInit, ElementRef, OnDestroy } from '@angular/core';
import { SaleOrder } from '../models/sale-order.model';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { Project, Task } from '../models/project.model';
import * as bootstrap from 'bootstrap';
import { firstValueFrom } from 'rxjs';
import { Lead } from '../models/crm.lead.model';
import { CrmLeadPart } from '../models/crm.lead.part.model';

@Component({
    selector: 'app-purchase-request',
    templateUrl: './purchase-request.component.html',
    standalone: false
})
export class PurchaseRequestComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() saleOrder: SaleOrder;
  @Input() lead?: Lead;
  @Input() part?: CrmLeadPart;

  purchaseProject: Project;
  existingTask: Task | null = null;
  formModal: any;
  loading = false;
  
  // Form data
  description: string = '';
  
  private modalElement: HTMLElement;
  private modalInitialized = false;

  constructor(
    private odooEm: OdooEntityManager,
    private elRef: ElementRef
  ) {}
  
  async ngOnInit() {
    console.log('Purchase request component initialized', this.saleOrder?.name);
    if (!this.saleOrder) {
      console.error('No sale order provided to purchase-request component');
      return;
    }

    try {
      // Load the "Richieste di acquisto" project with id = 9
      const projects = await firstValueFrom(this.odooEm.search<Project>(new Project(), [['id', '=', 9]]));
      if (!projects || projects.length === 0) {
        console.error('Purchase project not found');
        return;
      }
      this.purchaseProject = projects[0];
      
      // Only check for existing task if we have a sale order name
      if (this.saleOrder?.name) {
        await this.checkForExistingTask();
      }
    } catch (error) {
      console.error('Error in purchase-request ngOnInit:', error);
    }
  }

  /**
   * Checks for an existing purchase task linked to this sale order
   */
  async checkForExistingTask() {
    try {
      if (!this.saleOrder?.name) {
        console.log('Cannot check for existing task: no sale order name');
        return;
      }
      
      const tasks = await firstValueFrom(
        this.odooEm.search<Task>(new Task(), [['origin', '=', this.saleOrder.name]])
      );
      
      if (tasks && tasks.length > 0) {
        this.existingTask = tasks[0];
        console.log('Existing task found:', this.existingTask);
      } else {
        console.log('No existing task found for sale order:', this.saleOrder.name);
      }
    } catch (error) {
      console.error('Error checking for existing task:', error);
    }
  }

  hasCommitmentDate(): boolean {
    return !!this.saleOrder && !!this.saleOrder.commitment_date;
  }

  ngAfterViewInit() {
    try {
      // Get the modal element
      this.modalElement = this.elRef.nativeElement.querySelector('.modal');
      
      // Return if modal element isn't found
      if (!this.modalElement) {
        console.error('Modal element not found in the template');
        return;
      }
      
      // Move it to body
      document.body.appendChild(this.modalElement);
      
      // Initialize Bootstrap modal
      this.formModal = new bootstrap.Modal(this.modalElement, {
        backdrop: 'static',  // Prevents closing when clicking outside
        keyboard: false      // Prevents closing with keyboard
      });
      
      this.modalInitialized = true;
      
      // Add event listener for when modal is fully hidden
      this.modalElement.addEventListener('hidden.bs.modal', () => {
        // Clean up modal if task was created successfully
        if (this.loading === false) {
          this.formModal.dispose();
          if (this.modalElement.parentNode) {
            document.body.removeChild(this.modalElement);
          }
        }
      });
    } catch (error) {
      console.error('Error in ngAfterViewInit:', error);
    }
  }

  openModal() {
    if (this.modalInitialized && this.formModal) {
      this.formModal.show();
    } else {
      console.error('Cannot open modal - not initialized');
    }
  }

  closeModal() {
    if (this.modalInitialized && this.formModal) {
      this.formModal.hide();
    } else {
      console.error('Cannot close modal - not initialized');
    }
  }

  async createPurchaseTask() {
    if (!this.hasCommitmentDate()) {
      alert('Imposta prima una data di consegna');
      return;
    }
    
    this.loading = true;
    console.log('Creating purchase task for sale order:', this.saleOrder?.name);

    try {
      //if no lead, try and fetch it from sale order
      if (!this.lead && this.saleOrder.opportunity_id?.id) {
        console.log('Fetching lead from sale order opportunity_id:', this.saleOrder.opportunity_id.id);
        await firstValueFrom(this.odooEm.resolveSingle<Lead>(new Lead(), this.saleOrder.opportunity_id));
        this.lead = this.saleOrder.opportunity_id.value;
      }

      // Build task name with conditional parts
      let taskName = '';
      
      // Add tracking code and sale order name
      if (this.lead?.tracking_code) {
        taskName += `${this.lead.tracking_code} - `;
      }
      taskName += this.saleOrder.name;
      
      // Add lead description if available
      if (this.lead?.name) {
        taskName += ` | ${this.lead.name}`;
      }
      
      // Add part name if available
      if (this.part?.name) {
        taskName += ` (${this.part.name})`;
      }
      
      // Add ga_title if available
      if (this.saleOrder.ga_title) {
        taskName += ` | ${this.saleOrder.ga_title}`;
      }

      // Format description with HTML
      let formattedDescription = '';
      
      // Add user's description if present
      if (this.description) {
        formattedDescription += `<div>${this.description}</div>`;
      }
      
      // Add spacer
      formattedDescription += '<br><br><br>';
      
      // Add clickable link to current page
      formattedDescription += `<div><a href="${window.location.href}" target="_blank">${window.location.href}</a></div>`;

      //fetch user that is assigned to the sale order 
      let creatorId = this.saleOrder.user_id?.id;
      
      // Ensure we have a valid creator ID
      if (!creatorId) {
        console.warn('No creator ID found, using default');
        creatorId = 1; // Default user ID
      }
    
      const taskData = {
        name: taskName,
        project_id: this.purchaseProject.id,
        origin: this.saleOrder.name,
        partner_id: this.saleOrder.partner_id.id,
        date_deadline: this.saleOrder.commitment_date, // Use commitment date from sale order
        description: formattedDescription,
        stage_id: 73,
        user_ids: [[6, 0, [18, creatorId]]] // Assign to creator of sale order and user 18
      };

      console.log('Creating task with data:', taskData);

      // Create the task using the data object
      const createdTask = await firstValueFrom(
        this.odooEm.create<Task>(new Task(), taskData)
      );

      console.log('Task created successfully:', createdTask);

      // Hide modal before opening new window
      if (this.formModal) {
        this.formModal.hide();
        // Small delay to ensure modal is hidden
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Open the task in a new window
      window.open(`https://o3.galimberti.eu/web#id=${createdTask.id}&cids=1&menu_id=422&action=635&active_id=7&model=project.task&view_type=form`, '_blank');
      
      // Store the created task reference
      this.existingTask = createdTask;

      // Cleanup
      this.cleanupModal();

    } catch (error) {
      console.error('Error creating purchase task:', error);
      alert('Errore durante la creazione della richiesta di acquisto');
    } finally {
      this.loading = false;
    }
  }

  /**
   * Helper method to clean up the modal
   */
  private cleanupModal() {
    if (this.modalElement && this.modalElement.parentNode && this.formModal) {
      try {
        this.formModal.dispose();
        document.body.removeChild(this.modalElement);
        this.modalInitialized = false;
      } catch (error) {
        console.error('Error cleaning up modal:', error);
      }
    }
  }

  ngOnDestroy() {
    this.cleanupModal();
  }
}