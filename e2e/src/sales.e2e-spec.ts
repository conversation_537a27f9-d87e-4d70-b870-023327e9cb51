import { SalesTest } from './sales';
import { CommonTest } from './common';


describe('sales test', () => {
  let test: SalesTest;
  let commonTest: CommonTest

  beforeEach(() => {
    test = new SalesTest();
    commonTest = new CommonTest()
  })

  it('login', () => {
    commonTest.navigateToLogin()
  })

  it('open contact', () => {
    commonTest.navigateToContact()
  })

  it('open detail user giulio', () => {
    commonTest.openDeailContact()
  })

  it('new sales order', () => {
    test.newSalesOrder()
  })

  it('read values of sales product 0113', () => {
    var object = { 
      "1": "160",
      "2": "0",
      "3": "0",
      "4": "101",
      "5" : "101",
      "6" : "101",
      "7" : "101",
      "11" : "0.21",
      "12" : "0",
      // "13" : " ",
      "14" : "21.21"
    }
    commonTest.testProduct("0113", true, object)
  })

  // fail on Total value
  // it('read values of sales product 0018', () => {
  //   var object = { 
  //     "1": "3000",
  //     "2": "0",
  //     "3": "0",
  //     "4": "6",
  //     "5" : "16",
  //     "6" : "18",
  //     "7" : "6",
  //     "11" : "4.21", // ?
  //     "12" : "0", // 10
  //     // "13" :  "0"
  //     "14" : "12.63" // "75.82"
  //   }
  //   page.testProduct("0018",true,  object)
  // })

  // fail on Total value 2.016 - fotocopie 2.02 
  // it('read values of sales product 0037', () => {
  //   var object = { 
  //     "1": "0",
  //     "2": "0",
  //     "3": "0",

  //     "4": "2",
  //     "5" : "2",
  //     "6" : "2",
  //     "7" : "2",

  //     "11" : "1.12",
  //     "12" : "10",
  //     // "13" :  "0"
  //     "14" : "2.02" 
  //   }
  //   page.testProduct("0037", true, object)
  // })

  // afterEach(async () => {
  //   // Assert that there are no errors emitted from the browser
  //   const logs = await browser.manage().logs().get(logging.Type.BROWSER);
  //   expect(logs).not.toContain(jasmine.objectContaining({
  //     level: logging.Level.SEVERE,
  //   }))
  // })
})
