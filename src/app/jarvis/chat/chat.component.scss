.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 800px;
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 1.2rem;
  }

  .header-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;

    .model-select {
      padding: 0.25rem 0.5rem;
      border: none;
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      font-size: 0.9rem;

      option {
        background: #333;
        color: white;
      }
    }
  }
}

.api-config {
  padding: 2rem;
  background: white;
  margin: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .config-card {
    text-align: center;

    h4 {
      color: #333;
      margin-bottom: 1rem;
    }

    .input-group {
      display: flex;
      gap: 0.5rem;
      margin: 1rem 0;

      .api-input {
        flex: 1;
        padding: 0.75rem;
        border: 2px solid #e1e5e9;
        border-radius: 6px;
        font-size: 1rem;

        &:focus {
          outline: none;
          border-color: #667eea;
        }
      }
    }

    .help-text {
      color: #666;
      font-style: italic;
    }
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .message {
    display: flex;
    gap: 0.75rem;
    max-width: 80%;

    &.user-message {
      align-self: flex-end;
      flex-direction: row-reverse;

      .message-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
    }

    &.ai-message {
      align-self: flex-start;

      .message-content {
        background: white;
        color: #333;
        border: 1px solid #e1e5e9;
      }
    }

    &.loading .message-content {
      background: #f0f0f0;
      color: #666;
    }

    .message-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #e1e5e9;
      font-size: 1.2rem;
      flex-shrink: 0;
    }

    .message-content {
      border-radius: 18px;
      padding: 0.75rem 1rem;
      position: relative;
      word-wrap: break-word;

      .message-text {
        line-height: 1.4;
        white-space: pre-wrap;
      }

      .message-time {
        font-size: 0.75rem;
        opacity: 0.7;
        margin-top: 0.25rem;
      }
    }
  }
}

.input-area {
  padding: 1rem;
  background: white;
  border-top: 1px solid #e1e5e9;

  .input-container {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;

    .message-input {
      flex: 1;
      padding: 0.75rem;
      border: 2px solid #e1e5e9;
      border-radius: 20px;
      resize: none;
      font-family: inherit;
      font-size: 1rem;
      line-height: 1.4;
      max-height: 120px;

      &:focus {
        outline: none;
        border-color: #667eea;
      }

      &:disabled {
        background: #f5f5f5;
        cursor: not-allowed;
      }
    }

    .send-button {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      border: none;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
      transition: transform 0.2s;

      &:hover:not(:disabled) {
        transform: scale(1.05);
      }

      &:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
      }
    }
  }

  .input-help {
    margin-top: 0.5rem;
    text-align: center;

    small {
      color: #666;
      font-size: 0.8rem;
    }
  }
}

.system-prompt-config {
  padding: 1rem;
  background: #f8f9fa;
  border-top: 1px solid #e1e5e9;

  details {
    summary {
      cursor: pointer;
      font-weight: 500;
      color: #667eea;
      padding: 0.5rem 0;
      user-select: none;

      &:hover {
        color: #764ba2;
      }
    }

    .system-prompt-input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e1e5e9;
      border-radius: 6px;
      font-family: inherit;
      font-size: 0.9rem;
      resize: vertical;
      margin-top: 0.5rem;

      &:focus {
        outline: none;
        border-color: #667eea;
      }
    }
  }
}

// Buttons
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;

  &.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  }

  &.btn-outline {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }
}

// Loading spinner animation
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Scrollbar styling
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// Responsive design
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    border-radius: 0;
  }

  .chat-header {
    .header-controls {
      flex-direction: column;
      gap: 0.25rem;
    }
  }

  .message {
    max-width: 90% !important;
  }

  .api-config {
    margin: 0.5rem;
    padding: 1rem;

    .input-group {
      flex-direction: column;
    }
  }
}