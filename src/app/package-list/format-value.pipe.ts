import { <PERSON>pe, PipeTransform } from '@angular/core';
import { DatePipe, DecimalPipe } from '@angular/common';

@Pipe({
  name: 'formatValue',
  standalone: true
})
export class FormatValuePipe implements PipeTransform {
  private datePipe: DatePipe;
  private decimalPipe: DecimalPipe;

  constructor() {
    this.datePipe = new DatePipe('it-IT');
    this.decimalPipe = new DecimalPipe('it-IT');
  }

  transform(value: any, type: string, ...args: any[]): any {
    if (value === null || value === undefined || value === '') {
      return '';
    }
  
    switch (type) {
      case 'number':
        // For regular numbers, use up to 2 decimal places
        return this.decimalPipe.transform(value, '1.0-2');
      
      case 'currency':
        // For currency, always use 2 decimal places (no currency symbol)
        return this.decimalPipe.transform(value, '1.2-2');
      
      case 'date': //this is already formatted
        return value;
  
      case 'dimension':
        // First convert value to string to ensure includes() will work
        if (typeof value !== 'string') {
          // If it's a number, just format it
          if (typeof value === 'number') {
            return this.decimalPipe.transform(value, '1.0-2');
          }
          // Otherwise, try to safely convert to string
          value = String(value);
        }
        
        // Now check if the string contains a hyphen
        if (value.includes('-')) {
          const [min, max] = value.split('-').map(v => parseFloat(v.trim()));
          return `${this.decimalPipe.transform(min, '1.0-2')} - ${this.decimalPipe.transform(max, '1.0-2')}`;
        } else {
          // If no hyphen, try to parse as number and format
          const num = parseFloat(value);
          return !isNaN(num) ? this.decimalPipe.transform(num, '1.0-2') : value;
        }
      
      case 'text':
      default:
        // For text or unknown types, return as is
        return value;
    }
  }
}