{"netlify": {"id": "845b9c44-9afc-4308-97df-68ff48aa8aa9", "site_id": "845b9c44-9afc-4308-97df-68ff48aa8aa9", "plan": "nf_team_dev", "plan_data": {"title": "Netlify Team Free", "asset_acceleration": true, "form_processing": true, "cdn_propagation": "partial", "build_gc_exchange": "buildbot-gc", "build_node_pool": "buildbot-external-ssd", "build_cluster": "buildbot-3", "domain_aliases": true, "secure_site": false, "prerendering": true, "proxying": true, "ssl": "custom", "rate_cents": 0, "yearly_rate_cents": 0, "cdn_network": "free_cdn_network", "ipv6_domain": "cdn.makerloop.com", "branch_deploy": true, "managed_dns": true, "geo_ip": true, "split_testing": true, "id": "nf_team_dev"}, "ssl_plan": null, "premium": false, "claimed": true, "name": "happy-hamilton-b9dd5d", "custom_domain": null, "domain_aliases": [], "password": null, "notification_email": null, "url": "http://happy-hamilton-b9dd5d.netlify.com", "admin_url": "https://app.netlify.com/sites/happy-hamilton-b9dd5d", "deploy_id": "5cc0bc2a0075b8f23c318b39", "build_id": "", "deploy_url": "http://5cc0bc2a0075b8f23c318b39.happy-hamilton-b9dd5d.netlify.com", "state": "current", "screenshot_url": null, "created_at": "2019-04-24T10:50:12.183Z", "updated_at": "2019-04-24T19:42:36.332Z", "user_id": "5a05c4380b79b74b51e9690d", "error_message": null, "ssl": false, "ssl_url": "https://happy-hamilton-b9dd5d.netlify.com", "force_ssl": null, "ssl_status": null, "max_domain_aliases": 100, "build_settings": {}, "processing_settings": {"css": {"bundle": true, "minify": true}, "js": {"bundle": true, "minify": true}, "images": {"optimize": true}, "html": {"pretty_urls": true}, "skip": true}, "prerender": null, "prerender_headers": null, "deploy_hook": null, "published_deploy": {"id": "5cc0bc2a0075b8f23c318b39", "site_id": "845b9c44-9afc-4308-97df-68ff48aa8aa9", "build_id": null, "state": "ready", "name": "happy-hamilton-b9dd5d", "url": "http://happy-hamilton-b9dd5d.netlify.com", "ssl_url": "https://happy-hamilton-b9dd5d.netlify.com", "admin_url": "https://app.netlify.com/sites/happy-hamilton-b9dd5d", "deploy_url": "http://5cc0bc2a0075b8f23c318b39.happy-hamilton-b9dd5d.netlify.com", "deploy_ssl_url": "https://5cc0bc2a0075b8f23c318b39--happy-hamilton-b9dd5d.netlify.com", "created_at": "2019-04-24T19:42:34.455Z", "updated_at": "2019-04-24T19:42:36.336Z", "user_id": "5a05c4380b79b74b51e9690d", "error_message": null, "required": [], "required_functions": [], "commit_ref": null, "review_id": null, "branch": null, "commit_url": null, "skipped": null, "locked": null, "log_access_attributes": {"type": "firebase", "url": "https://netlify.firebaseio.com/deploys/5cc0bc2a0075b8f23c318b39/log"}, "title": null, "review_url": null, "published_at": "2019-04-24T19:42:36.179Z", "context": "production", "deploy_time": 1, "available_functions": [], "summary": {"status": "ready", "messages": [{"type": "info", "title": "2 new files uploaded", "description": "2 generated pages changed.", "details": "New pages include:\n- prototypes/search.html\n- prototypes/browse.html\n"}, {"type": "info", "title": "No redirect rules processed", "description": "This deploy did not include any redirect rules. [Learn more about redirects](https://www.netlify.com/docs/redirects/).", "details": ""}, {"type": "info", "title": "No header rules processed", "description": "This deploy did not include any header rules. [Learn more about headers](https://www.netlify.com/docs/headers-and-basic-auth/).", "details": ""}, {"type": "info", "title": "All linked resources are secure", "description": "Congratulations! No insecure mixed content found in your files.", "details": null}]}, "screenshot_url": null, "site_capabilities": {"title": "Netlify Team Free", "asset_acceleration": true, "form_processing": true, "cdn_propagation": "partial", "build_gc_exchange": "buildbot-gc", "build_node_pool": "buildbot-external-ssd", "build_cluster": "buildbot-3", "domain_aliases": true, "secure_site": false, "prerendering": true, "proxying": true, "ssl": "custom", "rate_cents": 0, "yearly_rate_cents": 0, "cdn_network": "free_cdn_network", "ipv6_domain": "cdn.makerloop.com", "branch_deploy": true, "managed_dns": true, "geo_ip": true, "split_testing": true, "id": "nf_team_dev"}, "committer": null, "skipped_log": null}, "managed_dns": true, "jwt_secret": null, "jwt_roles_path": "app_metadata.authorization.roles", "account_slug": "info-rexi8gq", "account_name": "info-rexi8gq's team", "account_type": "personal", "capabilities": {"title": "Netlify Team Free", "asset_acceleration": true, "form_processing": true, "cdn_propagation": "partial", "build_gc_exchange": "buildbot-gc", "build_node_pool": "buildbot-external-ssd", "build_cluster": "buildbot-3", "domain_aliases": true, "secure_site": false, "prerendering": true, "proxying": true, "ssl": "custom", "rate_cents": 0, "yearly_rate_cents": 0, "cdn_network": "free_cdn_network", "ipv6_domain": "cdn.makerloop.com", "branch_deploy": true, "managed_dns": true, "geo_ip": true, "split_testing": true, "id": "nf_team_dev"}, "active_subscription_ids": [], "external_contributors_enabled": false, "paid_individual_site_subscription": false, "dns_zone_id": null, "identity_instance_id": null, "use_functions": null, "parent_user_id": null, "automatic_tls_provisioning": null, "disabled": null, "lifecycle_state": "active", "id_domain": "845b9c44-9afc-4308-97df-68ff48aa8aa9.netlify.com", "use_lm": null, "build_image": "xenial", "has_analytics_data": true, "analytics_enabled": false, "automatic_tls_provisioning_expired": false}}