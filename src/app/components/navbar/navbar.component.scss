// christmas animationnn
.cursor-pointer {
    cursor: pointer;
  }
  
  .snowfall-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    pointer-events: none;
    z-index: 1000;
    overflow: hidden;
  }
  
  .snowflake {
    position: absolute;
    top: -20px;
    animation: falling linear 5s forwards;
    color: lightblue;
    
    i {
      filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.8));
    }
  }
  
  @keyframes falling {
    0% {
      transform: translateY(0) rotate(0deg);
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    100% {
      transform: translateY(100vh) rotate(360deg);
      opacity: 0;
    }
  }