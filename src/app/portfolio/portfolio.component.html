<!-- app.component.html (or wherever this sits) -->
<app-navbar 
  [loading]="loading" 
  backroute=".." 
  class="w-100 ">
  <a class="navbar-brand"><span>Portafoglio Ordini</span></a>
</app-navbar>

<!-- Wrap everything so it fills the viewport -->
<div class="container-fluid full-vh p-0">
  <!-- Tabs header -->
  <ul class="nav nav-tabs px-3 pt-3">
    <li class="nav-item">
      <a 
        class="nav-link" 
        [class.active]="selectedTab==='settori'"
        (click)="selectTab('settori')">
        Portafoglio Attuale
      </a>
    </li>
    <li class="nav-item">
      <a 
        class="nav-link" 
        [class.active]="selectedTab==='vendite'"
        (click)="selectTab('vendite')">
        Offerte Inviate
      </a>
    </li>
    <!-- <li class="nav-item">
      <a 
        class="nav-link" 
        [class.active]="selectedTab==='storico'"
        (click)="selectTab('storico')">
        Storico
      </a>
    </li> -->
  </ul>

  <!-- Scrollable content area at full height-->
<div class="container-fluid h-100 overflow-auto">
    <ng-container [ngSwitch]="selectedTab">
      <app-areas-portfolio *ngSwitchCase="'settori'"></app-areas-portfolio>
      <app-portfolio-sales *ngSwitchCase="'vendite'"></app-portfolio-sales>
      <!-- <app-portfolio-storico *ngSwitchCase="'storico'"></app-portfolio-storico> -->
    </ng-container>
  </div>
</div>