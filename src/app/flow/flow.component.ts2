import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { OdoorpcService } from '../shared/services/odoorpc.service';
import { ActionOdooSearch, ActionPlot } from './prompts.action.search';
import { Flow } from '../models/flow-flow.model';
import { FlowService } from './services/flow.service';

@Component({
  selector: 'app-flow',
  templateUrl: './flow.component.html'
})
export class FlowComponent implements OnInit {

  
  actions:Action[] = []
  queryResult = '';
  public static k = ''
  loading = false
  model = 'prodotti'
  domain = 'ordinati per nome'
  results: any[];
  lastQuery: string;
  prompts= [{
      model : "fascicoli",
      domain: "creati dopo il 2 maggio 2023"
    },
    {
      model: "prodotti",
      domain: "della categoria ferramenta"
    },
    {
      model: "prodotti",
      domain: "disponibili a magazzino della categoria velux"
    },
    {
      model: "prodotti",
      domain: "con un codice a barre e creati dopo il primo maggio 2023"
    },
    {
      model : "contatti",
      domain: "nella città di milano"
    },
    {
      model  : "trasferimenti",
      domain : "con ubicazione di destinazione Giacenza/E"
    },
    {
      model: "righe degli oridini di vendita",
      domain: "con un product_id della categoria Velux, dei soli ordini del 2023 creati da paganini"
    }
    
  
  ]
  pr: any;
  parsed: any;
  flows: Flow[];
  selectedFlow: Flow;

  constructor(
    private http: HttpClient,
    private flowService: FlowService,
    private odoorpc: OdoorpcService
  ) {}

  async ngOnInit(): Promise<void> {

    FlowComponent.k = localStorage.getItem('aik')

    if (typeof(localStorage.getItem("lastFlow")) !== "undefined" && localStorage.getItem("lastFlow") ) {
      this.restore()
    }

    this.refreshFlow()
  }


  async refreshFlow(){
    this.flows = await this.flowService.list()

    console.log("REFRESHED ", this.flows )
  }

  getPrevious(a:Action) {
    var i = this.actions.indexOf(a)
    if (i > 0)
      return this.actions[i -1]
    return null
  }

  newActionSearch() {
    this.actions.push(new ActionOdooSearch(this.http, this.odoorpc))
  }

  insertPrompt(p:{model:string,domain:any}) {
    this.model = p.model;
    this.domain = p.domain;
  }
  

  _restore(as:any){
    console.log("pre_restore",  typeof(as))
    JSON.parse(as).forEach(a => {
      if (a.type == 'search') {
        var x = new ActionOdooSearch(this.http, this.odoorpc)
        // x.results = a.results
        x.params[0] = a.params[0]
        x.params[1] = a.params[1]
        x.parsed = a.parsed
        this.actions.push(x)
      } else if (a.type == 'plot') {
        var p = new ActionPlot()
        this.actions.push(p)
      }
    });
  }

  async restore() {
    var f = await this.flowService.get( localStorage.getItem('lastFlow'))
    if(f && f.actions)
      this._restore(JSON.parse(f.actions))
    this.selectedFlow = f
  }

  handleFlowSelected(f: Flow){

    this.selectedFlow = f
    this.actions = []
    if(f && f.actions)
    var as = JSON.parse(f.actions)

    if(as)
    this._restore( as)
  }

  
  delete(a:Action) {
    this.actions = this.actions.filter(x => x != a)
  }


  addPlot() {
    this.actions.push(new ActionPlot())
  }
  
  async persist() {

    var j = JSON.stringify(this.actions,function (key, value) {
      var ret = false
      if (key =='domain')
        ret = false

        
      if( key == 'type' || key == 'parsed' || key=='id' || key=='results' || key=='name' || key=='display_name' || key == 'value' || key == 'params' || key== 'instan2ce' || value instanceof Array ||value instanceof ActionParam ||  typeof value === 'boolean' ||typeof value === 'number' ||  typeof value === 'string' || value instanceof ActionOdooSearch|| value instanceof ActionPlot ||  value.id  || value.value) {
        ret = value;
      }
      // if (typeof myVar === 'string' || myVar instanceof String)

      return ret
    })
    localStorage.setItem('actions',j)


    if (typeof(localStorage.getItem("lastFlow")) !== "undefined" && localStorage.getItem("lastFlow") ) {

      var f:Flow  = await this.flowService.get(localStorage.getItem("lastFlow"))
      await this.flowService.save(f, j)
      await this.refreshFlow()
    }


   
  }

  persistkey(event) {
    localStorage.setItem('aik',event.target.value)
  }

}
