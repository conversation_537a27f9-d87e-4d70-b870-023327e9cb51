.calendar-wrapper {
  display: flex;
  height: 100vh;
  background-color: white;
}

.split-view {
  display: flex;
  height: calc(100vh - 60px);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.calendar-section {
  flex: 1;
  min-height: 0;
  padding: 0.8rem;
}

/* Side Panel - Reduced width */
.side-panel {
  width: 230px;
  height: 100%;
  padding: 0.8rem;
  border-right: 1px solid #e5e7eb;
  background-color: white;
  overflow-y: auto;
}

.side-panel h5 {
  font-size: 0.8rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  margin-top: 0.7rem;
}

/* Year selector - more compact */
.year-selector {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-bottom: 0.7rem;
}

.year-selector select {
  flex: 1;
  padding: 0.3rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background-color: white;
  font-size: 0.8rem;
}

.year-selector select:focus {
  outline: none;
  border-color: #7d5bd1;
  box-shadow: 0 0 0 1px #7d5bd1;
}

/* Month grid - tighter layout */
.month-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.3rem;
  margin-bottom: 0.7rem;
}

.month-grid button {
  padding: 0.3rem 0.2rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  background-color: white;
  color: #374151;
  font-size: 0.75rem;
  transition: all 0.2s;
}

.month-grid button:hover {
  background-color: #f9fafb;
}

.month-grid button.active {
  background-color: #7d5bd1;
  color: white;
  border-color: #7d5bd1;
}

/* Filters section - tighter spacing */
.filters-section .filter-group {
  margin-bottom: 0.7rem;
}

.filters-section .filter-group label {
  display: block;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.3rem;
}

.filters-section .checkbox-list {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.filters-section .form-check {
  margin: 0;
  padding-left: 1.5rem;
}

.filters-section .form-check input {
  margin-left: -1.5rem;
}

.filters-section .form-check label {
  font-weight: normal;
  margin: 0;
  font-size: 0.75rem;
}

/* Calendar Main Area */
.calendar-main {
  flex: 1;
  padding: 0.8rem;
  background-color: #f9fafb;
}

/* Calendar Header - reduced size */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  padding: 0 0.3rem;
}

.calendar-header .nav-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.calendar-header h2 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #111827;
}

.calendar-header .nav-buttons {
  display: flex;
  gap: 0.3rem;
}

.calendar-header .nav-buttons button {
  padding: 0.3rem;
  border: 1px solid #e5e7eb;
  background-color: white;
  color: #374151;
  border-radius: 0.25rem;
}

.calendar-header .nav-buttons button:hover {
  background-color: #f9fafb;
}

.calendar-header .today-button {
  padding: 0.3rem 0.7rem;
  background-color: #7d5bd1;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.calendar-header .today-button:hover {
  background-color: #7d5bd1;
}

/* Calendar Grid - more compact */
.calendar-grid {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.4rem;
}

.calendar-grid .weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.calendar-grid .weekday {
  padding: 0.4rem;
  text-align: center;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
}

.calendar-grid .days {
  display: grid;
  grid-template-columns: repeat(7, minmax(110px, 1fr));
  grid-auto-rows: minmax(80px, auto);
  position: relative;
}

.calendar-grid .day {
  padding: 0.4rem;
  border-right: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  background-color: white;
  min-width: 0;
}

.calendar-grid .day:nth-child(7n) {
  border-right: none;
}

.calendar-grid .day.other-month {
  background-color: #f9fafb;
}

.calendar-grid .day.other-month .day-number {
  color: #9ca3af;
}

.calendar-grid .day.today .day-number {
  background-color: #7d5bd1;
  color: white;
}

.calendar-grid .day-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.3rem;
  height: 1.3rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.3rem;
}

/* Activity items in calendar days */
.calendar-grid .activities {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  position: relative;
  max-height: calc(100% - 1.6rem);
  overflow-y: auto;
}

.calendar-grid .activity {
  padding: 0.2rem 0.3rem;
  border-radius: 0.2rem;
  font-size: 0.7rem;
  cursor: pointer;
  position: relative;
}

.calendar-grid .activity .activity-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  font-weight: 500;
}

.calendar-grid .activity .activity-description {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.65rem;
}

.calendar-grid .activity.overdue {
  background-color: #fee2e2;
  color: #dc2626;
}

.calendar-grid .activity.today {
  background-color: #fef3c7;
  color: #d97706;
}

.calendar-grid .activity.planned {
  background-color: #dbeafe;
  color: #2563eb;
}

.calendar-grid .activity:hover {
  opacity: 0.9;
}

/* Activity Popup - Overlay style */
.activity-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-popup-content {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.popup-body {
  padding: 1rem;
  flex: 1;
}

.popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
}

.creator-info, .assigned-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.activity-full-details {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 0.5rem;
}

.activity-full-details h6 {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.activity-full-details p {
  margin: 0;
  font-size: 0.9rem;
  color: #374151;
  word-break: break-word;
}

.calendar-grid .activity.open {

  background-color: rgba(59, 130, 246, 0.2);
}

/* Activities List Section */
.activities-list-section {
  background-color: white;
  border-top: 1px solid #e5e7eb;
  max-height: 250px;
}

.activities-list-section .list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.8rem;
  background-color: #f9fafb;
}

.activities-list-section .list-header h5 {
  font-size: 0.8rem;
  margin: 0;
}

.activities-list-section .header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activities-list-section .input-group {
  width: 180px;
}

.activities-list-section .list-content {
  overflow-y: auto;
}

.activities-list-section table {
  margin-bottom: 0;
  font-size: 0.75rem;
}

.activities-list-section th {
  background-color: #f9fafb;
  cursor: pointer;
  user-select: none;
  padding: 0.4rem;
  font-size: 0.7rem;
}

.activities-list-section th:hover {
  background-color: #f3f4f6;
}

.activities-list-section th i {
  margin-left: 0.3rem;
}

.activities-list-section td {
  padding: 0.3rem 0.4rem;
  vertical-align: middle;
}

.activities-list-section .avatar-sm {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
}

.cursor-pointer {
  cursor: pointer;
}

/* Responsive design */
@media (max-width: 1024px) {
  .side-panel {
    width: 200px;
  }
  
  .calendar-grid .days {
    grid-template-columns: repeat(7, minmax(90px, 1fr));
  }
}

@media (max-width: 768px) {
  .split-view {
    flex-direction: column;
  }
  
  .side-panel {
    width: 100%;
    height: auto;
    max-height: 250px;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .month-grid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .filters-section {
    display: none;
  }
  
  .side-panel.filters-expanded .filters-section {
    display: block;
  }
  
  .filters-toggle {
    display: block;
    text-align: center;
    padding: 0.3rem;
    background-color: #f3f4f6;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    cursor: pointer;
    margin-top: 0.5rem;
  }
}

/* Toggle for collapsible sidebar */
.side-panel-toggle {
  display: none;
}

@media (max-width: 640px) {
  .calendar-grid .days {
    grid-template-columns: repeat(7, minmax(70px, 1fr));
    grid-auto-rows: minmax(60px, auto);
  }
  
  .side-panel-toggle {
    display: block;
    position: fixed;
    bottom: 10px;
    right: 10px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #7d5bd1;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .side-panel.collapsed {
    display: none;
  }
}