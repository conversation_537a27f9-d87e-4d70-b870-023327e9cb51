import { Component, OnInit, HostListener } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { Router } from '@angular/router';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import { HrAttendance } from '../models/hr-attendance.model';
import { PRINT_ATTENDANCE_CFG } from '../models/deal';
import { GapiService } from '../shared/services/g-api.service';
import { User } from '../models/user.model';
import { AnalyticAccountLine } from '../models/analytic-account.model';


@Component({
    selector: 'app-home',
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.scss'],
    standalone: false
})
export class HomeComponent implements OnInit {
  socket: any;

  quoteEnabled: boolean = false;
  keySequence: string = '';

  lastFourMonths: { value: string, label: string }[] = [];
  showPresence: boolean = false;
  loading: boolean = false;
  userId: number;
  user: User;
  

  constructor(private router: Router, 
    public odooEm: OdooEntityManager,
    private gapiService: GapiService
  
  ) {
    this.generateLastFourMonths();
  }

  generateLastFourMonths() {
    const months = ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 
                   'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'];
    const today = new Date();
    this.lastFourMonths = [];
    
    for (let i = 0; i < 4; i++) {
      const d = new Date(today.getFullYear(), today.getMonth() - i, 1);
      this.lastFourMonths.push({
        value: `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`,
        label: `${months[d.getMonth()]} ${d.getFullYear()}`
      });
    }
  }


  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    this.keySequence += event.key;
    
    // Keep only last 4 characters
    if (this.keySequence.length > 4) {
      this.keySequence = this.keySequence.slice(-4);
    }

    if (this.keySequence === '7331') {
      this.quoteEnabled = !this.quoteEnabled;
      this.keySequence = ''; // Reset after match
    }
  }


    async ngOnInit() {
    await this.loadUserInfo();
    console.log('connected user:', this.user,' so we show presence:', this.showPresence);
    //fetch all analytic account lines
    let analyticAccountLines = await firstValueFrom(this.odooEm.search<AnalyticAccountLine>(new AnalyticAccountLine()));  
    console.log('analyticAccountLines:', analyticAccountLines);
  }

//print presenze
async printAttendances(month: { value: string, label: string }) {
  this.loading = true;
  
  const [year, monthNum] = month.value.split('-');
  const startDate = `${year}-${monthNum}-01`;
  const endDate = `${year}-${monthNum}-${new Date(parseInt(year), parseInt(monthNum), 0).getDate()}`;

  // Fetch attendances for the selected month
  const attendances = await firstValueFrom(
    this.odooEm.search<HrAttendance>(
      new HrAttendance(),
      [
        ['check_in', '>=', startDate],
        ['check_in', '<=', endDate]
      ],
      null,
      null,
      'check_in DESC'
    )
  );

  // Prepare data for printing
  const header = ["Dipendente", "Ingresso", "Uscita", "Ore lavorate"];
  const Data = [header];

  attendances.forEach(att => {
    Data.push([
      att.employee_id?.name || '',
      att.check_in ? new Date(att.check_in).toLocaleString() : '',
      att.check_out ? new Date(att.check_out).toLocaleString() : '',
      att.worked_hours?.toString() || ''
    ]);
  });

  // Print using Google Sheets
  const sheetId = await this.gapiService.printGenericSingleSheet(
    PRINT_ATTENDANCE_CFG.template_sheet_id,
    PRINT_ATTENDANCE_CFG.spool_folder_id,
    Data
  );

  window.open('https://docs.google.com/spreadsheets/d/' + sheetId, '_blank');
  this.loading = false;
}

async loadUserInfo() {
  const result: any = await this.odooEm.odoorpcService.getSessionInfo();
    this.userId = result.result.user_id[0];
    //fetch all userse
    let usersFound = await firstValueFrom(this.odooEm.search<User>(new User(), [['id', '=', this.userId]]));
    this.user = usersFound[0]
//we show presence only for users with id: 8,2,13,68,61
    this.showPresence = [8, 2, 13, 68, 61].includes(this.userId);
  
}




}
