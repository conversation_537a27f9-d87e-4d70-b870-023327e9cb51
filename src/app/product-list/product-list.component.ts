// Fixing ProductListComponent to properly display attribute columns

import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';
import { ProductTemplate } from '../models/product.template.model';
import { ProductAttributeValue } from '../models/product.attribute.value';
import { ProductTemplateAttributeLine } from '../models/product.template.attribute.line';

@Component({
    selector: 'app-product-list',
    templateUrl: './product-list.component.html',
    styleUrl: './product-list.component.scss',
    standalone: false
})
export class ProductListComponent implements OnInit {
  @Input() product: ProductTemplate;
  @Input() existingData: any; // Input for existing data
  @Output() variantsConfirmed = new EventEmitter<{variant: any, quantity: number}[]>();

  rows: any[] = [{}];
  attributes: any[] = [];
  loading: boolean = false;
  showPieces: boolean = false;  

  // Sorting
  sortField: string = '';
  sortOrder: number = 1;

  // Store available attribute values for each attribute
  attributeValues: Map<string, string[]> = new Map();

  constructor() { }

  ngOnInit() {
    console.log('[DEBUG] Product-List Init - Product:', this.product , 'Existing Data:', this.existingData);
    this.initializeComponent();
  }

  
  private initializeComponent() {
    if (!this.product) {
      console.error('[DEBUG] No product template provided');
      return;
    }
  
    // Grab attributes from the product template
    if (this.product?.attribute_line_ids?.values) {
      this.attributes = this.product.attribute_line_ids.values;
      // Optionally load attribute value arrays from value_ids for dropdowns
      this.loadAttributeValues();
    } else {
      console.error('[DEBUG] Product template has no attribute_line_ids.values');
    }
    
    // Initialize rows from existing data if available
    if (this.existingData && this.existingData.outPacks && this.existingData.outPacks.length > 0) {
      this.populateRowsFromExistingData(this.existingData.outPacks);
    } else {
      this.addRow(); // Start with an initial empty row if no data exists
    }

    console.log ('[DEBUG] Product-List Init - Attributes:',this.product, this.attributes, 'Rows:', this.rows);
  }

  // Method to populate rows from existing data
  populateRowsFromExistingData(outPacks: any[]) {
    this.rows = []; // Clear existing rows

    for (const pack of outPacks) {
      const row: any = {
        pieces: pack.pzQuantity || 0,
        quantity: pack.sel_qty || 0,
        variant: pack._product_id?.value,
        variantExists: true,
        variantChecked: true
      };

      console.log('[DEBUG] Creating row from pack:', pack);

      // Set dimensional attributes
      if (pack.lunghezza) row['Lunghezza'] = pack.lunghezza;
      if (pack.larghezza) row['Larghezza'] = pack.larghezza;
      if (pack.altezza) row['Altezza'] = pack.altezza;

      // Set other attributes from product template attribute values
      if (pack._product_id?.value?.product_template_attribute_value_ids?.values) {
        const ptavs = pack._product_id.value.product_template_attribute_value_ids.values;

        for (const ptav of ptavs) {
          const attrName = ptav.attribute_id?.name;
          if (attrName && !this.isDimensionalAttribute(attrName)) {
            row[attrName] = ptav.name;
          }
        }
      }

      this.rows.push(row);
    }

    // Make sure we have at least one row
    if (this.rows.length === 0) {
      this.addRow();
    }
    
    // Add an empty row at the end for new entries
    this.addRow();
  }

  loadAttributeValues() {
    if (!this.attributes || this.attributes.length === 0) {
      console.log('[DEBUG] No attributes to load values for');
      return;
    }
  
    console.log('[DEBUG] Starting to load attribute values for', this.attributes.length, 'attributes');
  
    // Clear existing values
    this.attributeValues.clear();
    console.log('[DEBUG] Attributes before loading values:', this.attributes);
  
    // For each attribute, store its available values
    this.attributes.forEach((attributeLine: ProductTemplateAttributeLine) => {
      const attributeName = attributeLine.attribute_id.name;
      console.log('[DEBUG] Processing attribute:', attributeName);
  
      // Store the available values for this attribute as simple strings
      if (attributeLine.value_ids && attributeLine.value_ids.values) {
        const stringValues = attributeLine.value_ids.values.map(value => value.name);
        this.attributeValues.set(attributeName, stringValues);
        console.log('[DEBUG] Stored values for attribute:', attributeName, stringValues);
      } else {
        console.log('[DEBUG] No values found for attribute:', attributeName);
      }
    });
  
    console.log('[DEBUG] Final available attribute values map:', this.attributeValues);
  }

  calculatePieceUoM(row: any): number {
    if (!row) return 0;
    const length = parseFloat(row['Lunghezza']) || 0;
    const width = parseFloat(row['Larghezza']) || 0;
    const height = parseFloat(row['Altezza']) || 0;

    return (length * width * height) / 1000000000; // Convert mm³ to m³
  }

  calculateQuantity(row: any) {
    if (!this.showPieces) return;
    
    const pieceUoM = this.calculatePieceUoM(row);
    const pieces = parseFloat(row.pieces) || 0;
    row.quantity = pieceUoM * pieces;
  }

  onDimensionalValueChange(row: any) {
    this.calculateQuantity(row);
  }

  onValueChange(row: any) {
    // When a non-dimensional attribute value changes
    console.log('[DEBUG] Attribute value changed. Row data:', row);
  }

  isDimensionalAttribute(name: string): boolean {
    return name === 'Lunghezza' ||
           name === 'Larghezza' ||
           name === 'Altezza';
  }

  onKeyDown(event: KeyboardEvent, rowIndex: number, attr: any) {
    // Navigation logic for keyboard events
    let nextId: string;

    switch(event.key) {
      case 'ArrowDown':
        nextId = `cell-${rowIndex + 1}-${attr.id}`;
        if (rowIndex === this.rows.length - 1) {
          this.addRow();
        }
        break;
      case 'ArrowUp':
        if (rowIndex > 0) {
          nextId = `cell-${rowIndex - 1}-${attr.id}`;
        }
        break;
      case 'Enter':
        // If we're on the last row, add a new one
        if (rowIndex === this.rows.length - 1) {
          this.addRow();
        }
        // Move to the next row, same column
        nextId = `cell-${rowIndex + 1}-${attr.id}`;
        break;
      case 'ArrowRight':
        const nextAttrIndex = this.attributes.indexOf(attr) + 1;
        if (nextAttrIndex < this.attributes.length) {
          nextId = `cell-${rowIndex}-${this.attributes[nextAttrIndex].id}`;
        }
        break;
      case 'ArrowLeft':
        const prevAttrIndex = this.attributes.indexOf(attr) - 1;
        if (prevAttrIndex >= 0) {
          nextId = `cell-${rowIndex}-${this.attributes[prevAttrIndex].id}`;
        }
        break;
      case 'Tab':
        // Let the default tab behavior work
        return;
    }

    if (nextId) {
      event.preventDefault();
      setTimeout(() => document.getElementById(nextId)?.focus());
    }
  }

  addRow() {
    // Get the last row
    const lastRow = this.rows.length > 0 ? this.rows[this.rows.length - 1] : {};

    // Create a new row with data from the last row
    const newRow: any = {};
    newRow.pieces = lastRow.pieces || 0;

    // Copy attribute values from the previous row
    this.attributes.forEach(attr => {
      const attrName = attr.attribute_id.name;
      if (lastRow[attrName]) {
        newRow[attrName] = lastRow[attrName];
      }
    });

    this.rows.push(newRow);

    // Calculate quantity if dimensional attributes are present
    if (this.showPieces && newRow['Lunghezza'] && newRow['Larghezza'] && newRow['Altezza']) {
      this.calculateQuantity(newRow);
    }

    return newRow;
  }

  isRowComplete(row: any): boolean {
    if (!row.pieces) return false;
    
    // If we have dimensional attributes, check if they're all filled
    if (this.showPieces) {
      if (!row['Lunghezza'] || !row['Larghezza'] || !row['Altezza']) {
        return false;
      }
    }
    
    // For non-dimensional attributes, we'll be more lenient 
    return true;
  }

  hasCompletedRows() {
    return this.rows.some(row => this.isRowComplete(row));
  }
  
  confirmVariants() {
    console.log('[DEBUG] Starting confirmVariants');
    this.loading = true;
    const result = [];

    try {
      // Filter out any incomplete rows and the last empty row
      const completedRows = this.rows.filter(row => this.isRowComplete(row));
      console.log('[DEBUG] Found', completedRows.length, 'completed rows');

      for (const row of completedRows) {
        // For existing variants, use the variant data we already have
        if (row.variant) {
          console.log('[DEBUG] Using existing variant:', row.variant);
          result.push({
            variant: row.variant,
            quantity: this.showPieces ? row.quantity : row.pieces
          });
          continue;
        }

        // For new variants, create an object with all necessary data
        console.log('[DEBUG] Creating new variant from row:', row);
        const variant: any = {
          id: Date.now() + Math.floor(Math.random() * 1000), // Generate a temporary unique ID
          name: this.generateVariantName(row)
        };
        
        // Add dimensional attributes
        if (row['Lunghezza']) variant.lunghezza = parseFloat(row['Lunghezza']);
        if (row['Larghezza']) variant.larghezza = parseFloat(row['Larghezza']);
        if (row['Altezza']) variant.altezza = parseFloat(row['Altezza']);

        // Add other attribute values as strings
        const attributeValues = [];
        console.log('[DEBUG] Adding attribute values to variant');
        this.attributes.forEach(attr => {
          const attrName = attr.attribute_id.name;
          if (!this.isDimensionalAttribute(attrName) && row[attrName]) {
            // We're using simple string values now
            console.log(`[DEBUG] Adding attribute ${attrName} = ${row[attrName]}`);
            attributeValues.push({
              attribute_id: {
                id: attr.attribute_id.id,
                name: attr.attribute_id.name
              },
              name: row[attrName] // This is already a string
            });
          }
        });
        console.log('[DEBUG] Final attribute values:', attributeValues);

        // Create a minimal structure similar to what we'd get from the API
        variant._product_id = { value: variant };
        variant.product_template_attribute_value_ids = { values: attributeValues };
        console.log('[DEBUG] Final variant structure:', variant);

        result.push({
          variant: variant,
          quantity: this.showPieces ? row.quantity : row.pieces
        });
        console.log('[DEBUG] Added variant to result with quantity:', this.showPieces ? row.quantity : row.pieces);
      }
    } finally {
      this.loading = false;
    }

    if (result.length > 0) {
      console.log('[DEBUG] Emitting', result.length, 'confirmed variants:', result);
      this.variantsConfirmed.emit(result);
    } else {
      console.log('[DEBUG] No variants to emit');
    }
  }

  // Generate a name for a new variant based on its attributes
  private generateVariantName(row: any): string {
    let name = this.product.name || 'Variante';
    
    // Add dimensions to the name if present
    if (row['Lunghezza'] && row['Larghezza'] && row['Altezza']) {
      name += ` (${row['Lunghezza']}x${row['Larghezza']}x${row['Altezza']} mm)`;
    }
    
    // Add other attributes
    const otherAttrs = this.attributes
      .filter(attr => !this.isDimensionalAttribute(attr.attribute_id.name))
      .map(attr => row[attr.attribute_id.name])
      .filter(val => val);
      
    if (otherAttrs.length > 0) {
      name += ` - ${otherAttrs.join(', ')}`;
    }
    
    return name;
  }

  getAttributeValues(attributeName: string): string[] {
    // Given an attribute name, search for all the possible values of that attribute
    const values = this.attributeValues.get(attributeName);
    return values || [];
  }

  // Determine the colspan for the empty message
  getColspan(): number {
    let span = 2; // Start with # and Pieces columns
    span += this.attributes.length; // Add one column per attribute
    if (this.showPieces) span += 2; // Add Piece UoM and Quantity columns if showing pieces
    return span;
  }

  // For table sorting
  sort(field: string) {
    if (this.sortField === field) {
      this.sortOrder = this.sortOrder * -1;
    } else {
      this.sortField = field;
      this.sortOrder = 1;
    }
    
    // Sort the rows based on the selected field and order
    this.rows.sort((a, b) => {
      let valueA = a[field];
      let valueB = b[field];
      
      // Handle numeric values
      if (typeof valueA === 'string' && !isNaN(Number(valueA))) {
        valueA = Number(valueA);
      }
      if (typeof valueB === 'string' && !isNaN(Number(valueB))) {
        valueB = Number(valueB);
      }
      
      // Handle missing values
      if (valueA === undefined || valueA === null) return this.sortOrder;
      if (valueB === undefined || valueB === null) return -this.sortOrder;
      
      // Compare the values
      if (valueA < valueB) return -1 * this.sortOrder;
      if (valueA > valueB) return 1 * this.sortOrder;
      return 0;
    });
    
    console.log(`[DEBUG] Sorted by ${field} in ${this.sortOrder === 1 ? 'ascending' : 'descending'} order`);
  }
}