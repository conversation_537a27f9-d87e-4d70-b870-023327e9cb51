/* Table Layout and Structure */
.table-responsive {
  overflow-x: auto;
  height: calc(100vh - 230px); /* Adjust based on the height of your top elements */
  overflow-y: auto;
  position: relative;
}

table.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* Border Styling - Even lighter regular borders */
.table th, 
.table td {
  border: 1px solid #eeeeee;  /* Much lighter border */
  white-space: nowrap;
}

/* Day columns - fixed width */
.table th:not(.sticky-column),
.table td:not(.sticky-column) {
  width: 60px;
  min-width: 60px;

}

/* Thinner headers */
.table th {
  padding-top: 0.4rem !important;
  padding-bottom: 0.4rem !important;
  font-weight: 500;
}

/* Sticky Columns Configuration */
.sticky-column {
  position: sticky;
  z-index: 1;
  border-right: 1px solid #eeeeee;
}

/* First sticky column */
.sticky-col-1 {
  left: 0;
  width: 500px;
  min-width: 500px;
  max-width: 500px;
}

/* Second sticky column with strong right border */
.sticky-col-2 {
  left: 500px;
  width: 80px;
  min-width: 80px;
 
  border-right: 6px solid  #eeeeee !important;

}

/* Higher z-index for column headers to ensure they're always on top */
thead .sticky-column {
  z-index: 3;
}

/* Weekend and Sunday Styling */
.bg-light-weekend {
  background-color: rgba(0, 0, 0, 0.02) !important;
}

.sunday {
  border-right: 2px solid #000 !important;  /* Prominent black right border for Sundays */
  background-color: rgba(0, 0, 0, 0.03) !important;
}

/* Section Background Colors */
// tr.bg-secondary .sticky-column {
//   background-color: #9780d2 !important;
// }

// tr.bg-success .sticky-column {
//   background-color: #55b46b !important;
// }

// tr.bg-light .sticky-column {
//   background-color: #f8f9fa !important;
// }

/* Hover Effects - Fixed for all cells */
tr:hover:not(.bg-light):not(.bg-success):not(.bg-secondary) td {
  background-color: rgba(0, 0, 0, 0.03);
}

tr:hover:not(.bg-light):not(.bg-success):not(.bg-secondary) td.sticky-column {
  background-color: rgba(0, 0, 0, 0.05);
}

tr:hover:not(.bg-light):not(.bg-success):not(.bg-secondary) td.sunday {
  background-color: rgba(0, 0, 0, 0.06) !important;
}

/* Form Control Styling */
.form-control {
  transition: all 0.15s ease-in-out;
  
  &:focus {
    box-shadow: none;
    background-color: rgba(0, 123, 255, 0.1);
  }
}

/* Compact number inputs in cells */
input[type="number"].form-control {
  width: 40px !important;
  min-width: 40px;
  padding: 0.15rem 0.3rem;
  text-align: center;
}

.sunday .form-control {
  background-color: rgba(0, 0, 0, 0.01);
}

/* Section Header Styling */
.section-header td {
  padding: 8px !important;
  border-bottom: none;
  border-top: 1px solid #dee2e6 !important;
}

.subsection-header td {
  padding: 6px !important;
  border-bottom: none;
  border-top: 1px solid #dee2e6 !important;
}

/* Section Header Borders */
.section-header td,
.subsection-header td {
  border-left: none;
  border-right: none;
}

/* White Background for Data Rows */
.sticky-column.bg-white {
  background-color: white !important;
}

/* Badge Styling */
.badge {
  padding: 0.25rem 0.5rem;
  font-weight: 500;
}

/* Table row hover - ensure it works even with nested elements */
.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

/* Day header text formatting */
th:not(.sticky-column) {
  font-size: 0.85rem;
}


//no snippets input
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

table.table thead {
  position: sticky;
  top: 0;
  z-index: 20;
  background-color: #f8f9fa;
}

/* Enhanced styling for the daily totals row */
table.table tbody tr:first-child {
  position: sticky;
  top: 58px;
  z-index: 19;
  background-color: #f3f3f3;
  border-bottom: 2px solid #dee2e6 !important;  /* Add a more visible border */
  box-shadow: 0 3px 5px rgba(0,0,0,0.05);
}


/* Make the total numbers larger and more visible */


/* Add subtle highlight to the totals */
table.table tbody tr:first-child td {
  background-color: #f3f3f3 !important;
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}

/* Ensure the sticky columns stay visible when scrolling horizontally */
.sticky-column {
  position: sticky;
  z-index: 10;
}

/* First sticky column */
.sticky-col-1 {
  left: 0;
  z-index: 15;
}

/* Second sticky column */
.sticky-col-2 {
  left: 500px;
  z-index: 15;
}

/* Increase z-index for sticky columns in header */
thead .sticky-column {
  z-index: 25;
}

/* Increase z-index for sticky columns in the totals row */
tbody tr:first-child .sticky-column {
  z-index: 20;
}

/* Ensure all cells in the total row have proper background */
tbody tr:first-child td {
  background-color: #f8f9fa;
}

/* Add shadow to fixed elements for visual separation */
table.table thead::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -5px;
  height: 5px;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0));
  pointer-events: none;
}

tbody tr:first-child::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -5px;
  height: 5px;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0));
  pointer-events: none;
}

/* Ensure section headers have proper background colors */
// tr.bg-secondary .sticky-column {
//   background-color: #9780d2 !important;
// }

// tr.bg-success .sticky-column {
//   background-color: #55b46b !important;
// }

/* Ensure white background for data cells */
.sticky-column.bg-white {
  background-color: white !important;
}

/* Add to your CSS file */
// tr.bg-info .sticky-column {
//   background-color: #e59885 !important;
// }

/* Add this to ensure ALL cells in the OFF header row get the background color */
// tr.bg-info td {
//   background-color: #e59885 !important;
// }