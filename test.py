def odoo_domain_to_sql(domain):
    """
    Converte un dominio Odoo in una query SQL per il modello stock_quant,
    ricavando automaticamente il nome della tabella e della colonna
    splittando su eventuale punto.
    
    Esempio di dominio:
    [
        ["product_tmpl_id.name", "ilike", "vit%"],
        ["product_id.qty_available", ">", 0],
        ["location_id.usage", "=", "internal"],
        ["product_id.active", "=", True]
    ]
    
    Per ogni condizione:
     - Se il campo contiene un punto, la parte prima del punto è considerata
       il nome della tabella, e la parte dopo il punto il nome della colonna.
     - Se non contiene il punto, si assume che il campo appartenga a stock_quant.
     
    Per ogni tabella trovata (diversa da stock_quant) viene aggiunta una join:
      JOIN <tabella> ON stock_quant.<tabella> = <tabella>.id
    
    Ritorna la query SQL completa.
    """
    base_table = "stock_quant"
    # Dizionario per evitare join ripetuti (chiave = nome tabella)
    joins = {}
    conditions = []

    for field, operator, value in domain:
        # Se il campo contiene un punto, split in tabella e colonna
        if '.' in field:
            table, column = field.split('.', 1)
            sql_field = f"{table}.{column}"
            # Aggiungo il join per la tabella se non è già presente
            if table not in joins:
                joins[table] = f"JOIN {table} ON {base_table}.{table} = {table}.id"
        else:
            # Campo del modello principale
            sql_field = f"{base_table}.{field}"
        
        # Gestione dell'operatore "ilike" (utilizzo ILIKE in SQL per la case-insensitive)
        op = "ILIKE" if operator.lower() == "ilike" else operator
        
        # Conversione del valore in stringa SQL
        if isinstance(value, bool):
            sql_value = "TRUE" if value else "FALSE"
        elif isinstance(value, (int, float)):
            sql_value = str(value)
        else:
            sql_value = f"'{value}'"
        
        conditions.append(f"{sql_field} {op} {sql_value}")

    # Costruzione della query finale
    sql = f"SELECT {base_table}.* FROM {base_table} "
    if joins:
        sql += " ".join(joins.values()) + " "
    if conditions:
        sql += "WHERE " + " AND ".join(conditions)
    
    return sql

# Esempio di utilizzo:
domain = [
    ["product_tmpl_id.name", "ilike", "vit%"],
    ["product_id.qty_available", ">", 0],
    ["location_id.usage", "=", "internal"],
    ["product_id.active", "=", True]
]

query_sql = odoo_domain_to_sql(domain)
print(query_sql)
