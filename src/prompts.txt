Model: Default (GPT-3.5)

<EMAIL>
voglio che leggi qst USE [galimberti2]
GO
/****** Object:  Table [dbo].[clie]    Script Date: 21/03/23 13:58:38 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[clie](
	[clie_cd_clie] [int] IDENTITY(19934,1) NOT NULL,
	[clie_de_rags] [varchar](100) NULL,
	[clie_de_addr] [varchar](255) NULL,
	[clie_de_cap] [varchar](50) NULL,
	[clie_de_city] [varchar](50) NULL,
	[clie_de_prov] [varchar](3) NULL,
	[clie_de_paes] [varchar](50) NULL,
	[clie_cd_agen] [int] NULL,
	[clie_do_agen] [smallint] NULL,
	[clie_fl_fidoprop] [int] NULL,
	[clie_do_annotel] [smallint] NULL,
	[clie_do_annovis] [smallint] NULL,
	[clie_do_consgml] [smallint] NULL,
	[clie_do_conspml] [smallint] NULL,
	[clie_do_consgm3] [smallint] NULL,
	[clie_do_conspm3] [smallint] NULL,
	[clie_do_fpag] [smallint] NULL,
	[clie_de_nopa] [varchar](255) NULL,
	[clie_nu_dipe] [smallint] NULL,
	[clie_do_atti] [smallint] NULL,
	[clie_de_forn1] [varchar](50) NULL,
	[clie_nu_vaso] [smallint] NULL,
	[clie_nu_vaes] [smallint] NULL,
	[clie_nu_vapr] [smallint] NULL,
	[clie_nu_vaga] [smallint] NULL,
	[clie_nu_vacm] [smallint] NULL,
	[clie_nu_vacc] [smallint] NULL,
	[clie_nu_prso] [smallint] NULL,
	[clie_me_memo] [text] NULL,
	[clie_de_noin] [varchar](255) NULL,
	[clie_cd_pers] [int] NULL,
	[clie_do_divi] [int] NULL,
	[clie_do_pers] [smallint] NULL,
	[clie_da_crea] [datetime] NULL,
	[clie_ce_type] [char](1) NULL,
	[clie_ce_ammi] [varchar](10) NULL,
	[clie_do_annofax] [smallint] NULL,
	[clie_do_consleg] [smallint] NULL,
	[clie_nu_vanu] [smallint] NULL,
	[clie_nu_pres] [smallint] NULL,
	[clie_nu_prpr] [smallint] NULL,
	[clie_nu_prga] [smallint] NULL,
	[clie_nu_prcm] [smallint] NULL,
	[clie_nu_prcc] [smallint] NULL,
	[clie_nu_prnu] [smallint] NULL,
	[clie_ce_clfo] [char](1) NULL,
	[clie_do_atts] [smallint] NULL,
	[clie_de_forn] [varchar](255) NULL,
	[clie_fl_fido] [int] NULL,
	[clie_de_agen] [varchar](255) NULL,
	[clie_de_nick] [varchar](100) NULL,
	[clie_do_scar] [smallint] NULL,
	[clie_me_notescarico] [text] NULL,
	[clie_de_sett] [varchar](30) NULL,
	[clie_de_contrpart] [varchar](60) NULL,
	[clie_de_destrags] [varchar](100) NULL,
	[clie_de_destaddr] [varchar](50) NULL,
	[clie_de_destcap] [varchar](50) NULL,
	[clie_de_destcity] [varchar](50) NULL,
	[clie_de_destprov] [varchar](2) NULL,
	[clie_do_acce] [smallint] NULL,
	[clie_nu_chkiso] [bit] NULL,
	[clie_de_partiva] [varchar](50) NULL,
	[clie_de_codfisc] [varchar](50) NULL,
	[clie_do_scpv] [int] NULL,
	[clie_de_username] [varchar](50) NULL,
	[clie_de_password] [varchar](50) NULL,
	[clie_da_scadfido] [datetime] NULL,
	[clie_nu_broc] [bit] NULL,
 CONSTRAINT [pk_clie] PRIMARY KEY NONCLUSTERED 
(
	[clie_cd_clie] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE [dbo].[clie] ADD  CONSTRAINT [DF_clie_clie_ce_type_1__11]  DEFAULT ('I') FOR [clie_ce_type]
GO
ALTER TABLE [dbo].[clie]  WITH NOCHECK ADD  CONSTRAINT [FK_clie_pers] FOREIGN KEY([clie_cd_pers])
REFERENCES [dbo].[pers] ([pers_cd_pers])
GO
ALTER TABLE [dbo].[clie] CHECK CONSTRAINT [FK_clie_pers]
GO
Questo è uno script SQL che crea una tabella "clie" nel database "galimberti2". Ecco la struttura della tabella:

Nome della tabella: clie
Colonne:

clie_cd_clie [int] (chiave primaria)
clie_de_rags varchar
clie_de_addr varchar
clie_de_cap varchar
clie_de_city varchar
clie_de_prov varchar
clie_de_paes varchar
clie_cd_agen [int]
clie_do_agen [smallint]
clie_fl_fidoprop [int]
clie_do_annotel [smallint]
clie_do_annovis [smallint]
clie_do_consgml [smallint]



<EMAIL>
sai farmi una query che estragga tutti i clienti che iniziano con "ALBE" ?
Sì, posso aiutarti a creare una query SQL che estrae tutti i clienti il cui nome inizia con "ALBE". Ecco la query:

sql
Copy code
SELECT * FROM clie
WHERE clie_de_rags LIKE 'ALBE%'
Questa query seleziona tutte le righe dalla tabella "clie" in cui il valore della colonna "clie_de_rags" (ragione sociale) inizia con "ALBE".




<EMAIL>
puoi creare un query che estragga tuttii clienti nella provinvia di milano ?
Certo, ecco una query SQL che estrae tutti i clienti nella provincia di Milano:

sql
Copy code
SELECT * FROM clie
WHERE clie_de_prov = 'MI'
Questa query seleziona tutte le righe dalla tabella "clie" in cui il valore della colonna "clie_de_prov" (provincia) è uguale a 'MI', che rappresenta la provincia di Milano.




/****** Object:  Table [dbo].[fasc]    Script Date: 21/03/23 14:29:27 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[fasc](
	[fasc_cd_fasc] [int] IDENTITY(27205,1) NOT NULL,
	[fasc_cd_stat] [int] NULL,
	[fasc_cd_clie] [int] NOT NULL,
	[fasc_cd_comm] [int] NULL,
	[fasc_da_crea] [datetime] NULL,
	[fasc_de_fasc] [varchar](100) NULL,
	[fasc_cd_pers] [int] NULL,
	[fasc_do_persresp] [int] NULL,
	[fasc_me_note] [text] NULL,
	[fasc_nu_gene] [bit] NULL,
	[fasc_de_appeprog] [varchar](100) NULL,
	[fasc_me_appecopr] [text] NULL,
	[fasc_me_appecodi] [text] NULL,
	[fasc_de_appeclfi] [varchar](100) NULL,
	[fasc_me_appecofi] [text] NULL,
	[fasc_de_appeindi] [varchar](100) NULL,
	[fasc_de_appecica] [varchar](100) NULL,
	[fasc_da_appeinla] [datetime] NULL,
	[fasc_do_motrappe] [int] NULL,
	[fasc_me_appenoco] [varchar](8000) NULL,
	[fasc_do_acceappe] [int] NULL,
	[fasc_nu_appeunlo] [bit] NULL,
	[fasc_do_scar] [int] NULL,
	[fasc_do_ltraappe] [int] NULL,
	[fasc_nu_appeposa] [bit] NULL,
	[fasc_de_appeaser] [varchar](200) NULL,
	[fasc_me_appenote] [text] NULL,
	[fasc_me_appeprom] [text] NULL,
	[fasc_cd_nome] [int] NULL,
	[fasc_me_appenoda] [text] NULL,
	[fasc_me_nopa] [varchar](8000) NULL,
	[fasc_da_appedain] [datetime] NULL,
	[fasc_do_spca] [int] NULL,
	[fasc_cd_mast] [int] NULL,
	[fasc_cd_faru] [int] NULL,
	[fasc_cd_ccfa] [int] NULL,
	[fasc_fl_totale] [float] NULL,
	[fasc_do_fpag] [int] NULL,
	[fasc_do_divi] [int] NULL,
	[fasc_nu_iva] [int] NULL,
	[fasc_nu_chkivaagev] [bit] NULL,
	[fasc_de_nhigh] [varchar](200) NULL,
 CONSTRAINT [PK__fasc__72910220] PRIMARY KEY CLUSTERED 
(
	[fasc_cd_fasc] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[orcl]    Script Date: 21/03/23 14:29:27 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[orcl](
	[orcl_cd_orcl] [int] IDENTITY(1409,2) NOT NULL,
	[orcl_cd_stat] [int] NULL,
	[orcl_cd_fasc] [int] NOT NULL,
	[orcl_cd_pers] [int] NULL,
	[orcl_do_divi] [int] NULL,
	[orcl_do_persvend] [int] NULL,
	[orcl_da_ordi] [datetime] NULL,
	[orcl_do_fpag] [int] NULL,
	[orcl_me_nopa] [text] NULL,
	[orcl_de_indc] [varchar](100) NULL,
	[orcl_de_cico] [varchar](100) NULL,
	[orcl_fl_impo] [float] NULL,
	[orcl_fl_acco] [float] NULL,
	[orcl_me_note] [text] NULL,
	[orcl_fl_orel] [float] NULL,
	[orcl_cd_prev] [int] NULL,
	[orcl_de_desc] [varchar](200) NULL,
	[orcl_cd_clie] [int] NULL,
	[orcl_fl_totale] [float] NULL,
	[orcl_fl_forfait] [float] NULL,
	[orcl_nu_prim] [bit] NULL,
	[orcl_de_achi] [varchar](50) NULL,
	[orcl_me_noin] [text] NULL,
	[orcl_do_acce] [smallint] NULL,
	[orcl_do_scar] [smallint] NULL,
	[orcl_cd_ccoc] [int] NULL,
 CONSTRAINT [PK__orcl__04AFB25B] PRIMARY KEY CLUSTERED 
(
	[orcl_cd_orcl] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO


/****** Object:  Table [dbo].[orfo]    Script Date: 21/03/23 14:29:27 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[orfo](
	[orfo_cd_orfo] [int] IDENTITY(1410,2) NOT NULL,
	[orfo_cd_stat] [int] NULL,
	[orfo_cd_fasc] [int] NOT NULL,
	[orfo_cd_pers] [int] NULL,
	[orfo_do_divi] [int] NULL,
	[orfo_do_pers] [int] NULL,
	[orfo_da_orfo] [datetime] NOT NULL,
	[orfo_da_cons] [datetime] NULL,
	[orfo_do_moco] [int] NULL,
	[orfo_nu_arri] [int] NULL,
	[orfo_me_note] [text] NULL,
	[orfo_nu_conf] [bit] NULL,
	[orfo_do_fpag] [int] NULL,
	[orfo_cd_forn] [int] NULL,
	[orfo_fl_totale] [float] NULL,
	[orfo_fl_forfait] [float] NULL,
	[orfo_cd_achi] [int] NULL,
	[orfo_nu_verif] [bit] NULL,
	[orfo_do_resa] [int] NULL,
 CONSTRAINT [PK__orfo__7755B73D] PRIMARY KEY CLUSTERED 
(
	[orfo_cd_orfo] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE [dbo].[fasc]  WITH NOCHECK ADD  CONSTRAINT [FK__fasc__fasc_cd_cl__756D6ECB] FOREIGN KEY([fasc_cd_clie])
REFERENCES [dbo].[clie] ([clie_cd_clie])
GO
ALTER TABLE [dbo].[fasc] CHECK CONSTRAINT [FK__fasc__fasc_cd_cl__756D6ECB]
GO
ALTER TABLE [dbo].[fasc]  WITH NOCHECK ADD  CONSTRAINT [FK__fasc__fasc_cd_di__73852659] FOREIGN KEY([fasc_cd_comm])
REFERENCES [dbo].[clie] ([clie_cd_clie])
GO
ALTER TABLE [dbo].[fasc] CHECK CONSTRAINT [FK__fasc__fasc_cd_di__73852659]
GO
ALTER TABLE [dbo].[fasc]  WITH NOCHECK ADD  CONSTRAINT [FK__fasc__fasc_cd_no__4959E263] FOREIGN KEY([fasc_cd_nome])
REFERENCES [dbo].[nome] ([nome_cd_nome])
GO
ALTER TABLE [dbo].[fasc] CHECK CONSTRAINT [FK__fasc__fasc_cd_no__4959E263]
GO
ALTER TABLE [dbo].[fasc]  WITH NOCHECK ADD  CONSTRAINT [FK__fasc__fasc_cd_st__74794A92] FOREIGN KEY([fasc_cd_stat])
REFERENCES [dbo].[stat] ([stat_cd_stat])
GO
ALTER TABLE [dbo].[fasc] CHECK CONSTRAINT [FK__fasc__fasc_cd_st__74794A92]
GO
ALTER TABLE [dbo].[fasc]  WITH NOCHECK ADD  CONSTRAINT [FK_fasc_pers] FOREIGN KEY([fasc_cd_pers])
REFERENCES [dbo].[pers] ([pers_cd_pers])
GO
ALTER TABLE [dbo].[fasc] CHECK CONSTRAINT [FK_fasc_pers]
GO
ALTER TABLE [dbo].[orcl]  WITH NOCHECK ADD  CONSTRAINT [FK__orcl__orcl_cd_cl__789EE131] FOREIGN KEY([orcl_cd_clie])
REFERENCES [dbo].[clie] ([clie_cd_clie])
GO
ALTER TABLE [dbo].[orcl] CHECK CONSTRAINT [FK__orcl__orcl_cd_cl__789EE131]
GO
ALTER TABLE [dbo].[orcl]  WITH NOCHECK ADD  CONSTRAINT [FK__orcl__orcl_cd_fa__078C1F06] FOREIGN KEY([orcl_cd_fasc])
REFERENCES [dbo].[fasc] ([fasc_cd_fasc])
GO
ALTER TABLE [dbo].[orcl] CHECK CONSTRAINT [FK__orcl__orcl_cd_fa__078C1F06]
GO
ALTER TABLE [dbo].[orcl]  WITH NOCHECK ADD  CONSTRAINT [FK__orcl__orcl_cd_pr__54CB950F] FOREIGN KEY([orcl_cd_prev])
REFERENCES [dbo].[prev] ([prev_cd_prev])
GO
ALTER TABLE [dbo].[orcl] CHECK CONSTRAINT [FK__orcl__orcl_cd_pr__54CB950F]
GO
ALTER TABLE [dbo].[orcl]  WITH NOCHECK ADD  CONSTRAINT [FK__orcl__orcl_cd_st__05A3D694] FOREIGN KEY([orcl_cd_stat])
REFERENCES [dbo].[stat] ([stat_cd_stat])
GO
ALTER TABLE [dbo].[orcl] CHECK CONSTRAINT [FK__orcl__orcl_cd_st__05A3D694]
GO
ALTER TABLE [dbo].[orcl]  WITH NOCHECK ADD  CONSTRAINT [FK_orcl_pers] FOREIGN KEY([orcl_cd_pers])
REFERENCES [dbo].[pers] ([pers_cd_pers])
GO
ALTER TABLE [dbo].[orcl] CHECK CONSTRAINT [FK_orcl_pers]
GO
ALTER TABLE [dbo].[orfo]  WITH NOCHECK ADD  CONSTRAINT [FK__orfo__orfo_cd_fa__793DFFAF] FOREIGN KEY([orfo_cd_fasc])
REFERENCES [dbo].[fasc] ([fasc_cd_fasc])
GO
ALTER TABLE [dbo].[orfo] CHECK CONSTRAINT [FK__orfo__orfo_cd_fa__793DFFAF]
GO
ALTER TABLE [dbo].[orfo]  WITH NOCHECK ADD  CONSTRAINT [FK__orfo__orfo_cd_fo__60C757A0] FOREIGN KEY([orfo_cd_forn])
REFERENCES [dbo].[clie] ([clie_cd_clie])
GO
ALTER TABLE [dbo].[orfo] CHECK CONSTRAINT [FK__orfo__orfo_cd_fo__60C757A0]
GO
ALTER TABLE [dbo].[orfo]  WITH NOCHECK ADD  CONSTRAINT [FK__orfo__orfo_cd_st__7849DB76] FOREIGN KEY([orfo_cd_stat])
REFERENCES [dbo].[stat] ([stat_cd_stat])
GO
ALTER TABLE [dbo].[orfo] CHECK CONSTRAINT [FK__orfo__orfo_cd_st__7849DB76]
GO
ALTER TABLE [dbo].[orfo]  WITH NOCHECK ADD  CONSTRAINT [FK_orfo_pers] FOREIGN KEY([orfo_cd_pers])
REFERENCES [dbo].[pers] ([pers_cd_pers])
GO
ALTER TABLE [dbo].[orfo] CHECK CONSTRAINT [FK_orfo_pers]
GO







orcl sta per ordini clienti

orfo sta per ordine fornitore

USE [galimberti2]
GO
/****** Object:  Table [dbo].[rioc]    Script Date: 03/21/2023 14:45:32 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[rioc](
	[rioc_cd_rioc] [int] IDENTITY(2729,1) NOT NULL,
	[rioc_cd_bloc] [int] NOT NULL,
	[rioc_cd_riof] [int] NULL,
	[rioc_nu_prog] [int] NULL,
	[rioc_cd_move] [int] NULL,
	[rioc_de_desc] [varchar](400) NULL,
	[rioc_de_lung] [varchar](100) NULL,
	[rioc_de_larg] [varchar](100) NULL,
	[rioc_de_spes] [varchar](100) NULL,
	[rioc_cd_umve] [int] NOT NULL,
	[rioc_fl_quri] [float] NULL,
	[rioc_fl_prezlist] [float] NULL,
	[rioc_fl_prezin] [float] NULL,
	[rioc_fl_sconto] [float] NULL,
	[rioc_fl_prez] [float] NULL,
	[rioc_nu_mate] [bit] NULL,
	[rioc_fl_cost] [float] NULL,
	[rioc_me_note] [text] NULL,
	[rioc_fl_quas] [float] NULL,
	[rioc_fl_diam] [float] NULL,
	[rioc_fl_peso] [float] NULL,
	[rioc_fl_quasumor] [float] NULL,
	[rioc_de_notaprod] [varchar](200) NULL,
	[rioc_cd_ripr] [int] NULL,
	[rioc_fl_qtalorda] [float] NULL,
	[rioc_fl_qtanetta] [float] NULL,
	[rioc_fl_qupren] [float] NULL,
	[rioc_fl_totale] [float] NULL,
	[rioc_de_prda] [varchar](100) NULL,
	[rioc_fl_qtok] [float] NULL,
	[rioc_fl_totcorif] [float] NULL,
	[rioc_fl_qtamanc] [float] NULL,
	[rioc_do_mont] [int] NULL,
	[rioc_de_pezz] [varchar](100) NULL,
	[rioc_fl_codif]  AS (case when (isnull([rioc_fl_totcorif],0) = 0) then 100 else (abs((([rioc_fl_cost] - [rioc_fl_totcorif]) / [rioc_fl_totcorif])) * 100) end),
	[rioc_fl_costman] [float] NULL,
 CONSTRAINT [PK__rioc__762C88DA] PRIMARY KEY CLUSTERED 
(
	[rioc_cd_rioc] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_PADDING OFF
GO
/****** Object:  Table [dbo].[riof]    Script Date: 03/21/2023 14:45:32 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[riof](
	[riof_cd_riof] [int] IDENTITY(2789,1) NOT NULL,
	[riof_cd_orfo] [int] NOT NULL,
	[riof_nu_prog] [int] NULL,
	[riof_cd_move] [int] NULL,
	[riof_de_arfo] [varchar](300) NULL,
	[riof_de_desc] [varchar](400) NULL,
	[riof_cd_umor] [int] NOT NULL,
	[riof_de_lung] [varchar](100) NULL,
	[riof_de_larg] [varchar](100) NULL,
	[riof_de_spes] [varchar](100) NULL,
	[riof_nu_mate] [bit] NULL,
	[riof_fl_quan] [float] NULL,
	[riof_fl_qtar] [float] NULL,
	[riof_fl_prezlist] [float] NULL,
	[riof_fl_sconto] [float] NULL,
	[riof_fl_prez] [float] NULL,
	[riof_fl_prezumve] [float] NULL,
	[riof_fl_prezeff] [float] NULL,
	[riof_fl_prezeffumve] [float] NULL,
	[riof_fl_totale] [float] NULL,
	[riof_fl_coag] [float] NULL,
	[riof_me_note] [text] NULL,
	[riof_de_notaprod] [varchar](200) NULL,
	[riof_fl_qtor] [float] NULL,
	[riof_de_pezz] [varchar](100) NULL,
	[riof_de_umor] [varchar](20) NULL,
	[riof_do_valu] [int] NULL,
	[riof_fl_camb] [float] NULL,
	[riof_fl_convum] [float] NULL,
	[riof_de_prper] [varchar](200) NULL,
 CONSTRAINT [PK__riof__7EF6D905] PRIMARY KEY CLUSTERED 
(
	[riof_cd_riof] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_PADDING OFF
GO
/****** Object:  ForeignKey [FK__rioc__rioc_cd_bl__7814D14C]    Script Date: 03/21/2023 14:45:32 ******/
ALTER TABLE [dbo].[rioc]  WITH NOCHECK ADD  CONSTRAINT [FK__rioc__rioc_cd_bl__7814D14C] FOREIGN KEY([rioc_cd_bloc])
REFERENCES [dbo].[bloc] ([bloc_cd_bloc])
ON UPDATE CASCADE
GO
ALTER TABLE [dbo].[rioc] CHECK CONSTRAINT [FK__rioc__rioc_cd_bl__7814D14C]
GO
/****** Object:  ForeignKey [FK__rioc__rioc_cd_mo__79FD19BE]    Script Date: 03/21/2023 14:45:32 ******/
ALTER TABLE [dbo].[rioc]  WITH NOCHECK ADD  CONSTRAINT [FK__rioc__rioc_cd_mo__79FD19BE] FOREIGN KEY([rioc_cd_move])
REFERENCES [dbo].[move] ([move_cd_move])
GO
ALTER TABLE [dbo].[rioc] CHECK CONSTRAINT [FK__rioc__rioc_cd_mo__79FD19BE]
GO
/****** Object:  ForeignKey [FK__rioc__rioc_cd_ri__3FF073BA]    Script Date: 03/21/2023 14:45:32 ******/
ALTER TABLE [dbo].[rioc]  WITH NOCHECK ADD  CONSTRAINT [FK__rioc__rioc_cd_ri__3FF073BA] FOREIGN KEY([rioc_cd_ripr])
REFERENCES [dbo].[ripr] ([ripr_cd_ripr])
GO
ALTER TABLE [dbo].[rioc] CHECK CONSTRAINT [FK__rioc__rioc_cd_ri__3FF073BA]
GO
/****** Object:  ForeignKey [FK__rioc__rioc_cd_ri__7720AD13]    Script Date: 03/21/2023 14:45:32 ******/
ALTER TABLE [dbo].[rioc]  WITH NOCHECK ADD  CONSTRAINT [FK__rioc__rioc_cd_ri__7720AD13] FOREIGN KEY([rioc_cd_riof])
REFERENCES [dbo].[riof] ([riof_cd_riof])
GO
ALTER TABLE [dbo].[rioc] CHECK CONSTRAINT [FK__rioc__rioc_cd_ri__7720AD13]
GO
/****** Object:  ForeignKey [FK__rioc__rioc_cd_un__7908F585]    Script Date: 03/21/2023 14:45:32 ******/
ALTER TABLE [dbo].[rioc]  WITH NOCHECK ADD  CONSTRAINT [FK__rioc__rioc_cd_un__7908F585] FOREIGN KEY([rioc_cd_umve])
REFERENCES [dbo].[umve] ([umve_cd_umve])
GO
ALTER TABLE [dbo].[rioc] CHECK CONSTRAINT [FK__rioc__rioc_cd_un__7908F585]
GO
/****** Object:  ForeignKey [FK_riof_orfo]    Script Date: 03/21/2023 14:45:32 ******/
ALTER TABLE [dbo].[riof]  WITH NOCHECK ADD  CONSTRAINT [FK_riof_orfo] FOREIGN KEY([riof_cd_orfo])
REFERENCES [dbo].[orfo] ([orfo_cd_orfo])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[riof] CHECK CONSTRAINT [FK_riof_orfo]
GO
le righe dell ordine cliente sono nella tabella rioc
rioc sono le righe degli ordini cliente
riof invece sono le righe degli ordini fornitori
la tabella degli ordini fornitori e' la tabella orfo




<EMAIL>
sai scrivere una query che calcoli il totale in euro di tutte le righe degli ordini clienti del 2022 ?

Per calcolare il totale in euro di tutte le righe degli ordini clienti del 2022, puoi utilizzare la seguente query:

sql
Copy code
SELECT SUM(rioc.fl_prez * rioc.fl_quri) AS TotaleEuro
FROM rioc
WHERE YEAR(rioc.dt_data) = 2022
Questa query seleziona tutte le righe degli ordini clienti del 2022 e calcola il totale in euro moltiplicando il prezzo per la quantità di ogni riga e sommando i risultati. Il risultato viene visualizzato nella colonna "TotaleEuro".




<EMAIL>
sai scrivere una query che calcoli il totale in euro di tutte le righe degli ordini clienti del 2022 per il prodotto con codice articolo 2018 ?
Sì, ecco una possibile query per calcolare il totale in euro di tutte le righe degli ordini clienti del 2022 per il prodotto con codice articolo 2018:

sql
Copy code
SELECT SUM(rioc.fl_prez * rioc.fl_quri) AS totale_euro
FROM rioc
INNER JOIN orcl ON rioc.rioc_cd_bloc = orcl.orcl_cd_bloc
WHERE orcl_dt_data BETWEEN '2022-01-01' AND '2022-12-31'
AND rioc_nu_mate = 1
AND rioc_cd_move = 1
AND rioc_cd_umve = 1
AND rioc.fl_totale = 0
AND rioc.fl_prez <> 0
AND rioc.fl_quri <> 0
AND rioc.fl_prezlist <> 0
AND rioc.fl_prez * rioc.fl_quri = rioc.fl_prezlist * rioc.fl_quri
AND rioc_cd_mate = 2018
Questa query seleziona le righe dalla tabella rioc per cui:

rioc_nu_mate è pari a 1, indicando che si tratta di un'ordine di materiale (cioè un ordine cliente);
rioc_cd_move è pari a 1, indicando che si tratta di una riga di tipo "materiale";
rioc_cd_umve è pari a 1, indicando che l'unità di misura del prezzo è l'euro;
fl_totale è pari a 0, indicando che la riga non ha già un prezzo totale calcolato;
fl_prez, fl_quri e fl_prezlist sono diversi da 0, per evitare di considerare righe senza prezzo o quantità specificati;
fl_prez * fl_quri è uguale a fl_prezlist * fl_quri, per assicurarsi che il prezzo specificato sia consistente con il totale calcolato;
rioc_cd_mate è pari a 2018, che è il codice articolo del prodotto per cui vogliamo calcolare il totale.
Inoltre, la query effettua un join con la tabella orcl per selezionare solo le righe degli ordini effettuati nel 2022, e utilizza la funzione SUM() per calcolare il totale in euro di tutte le righe selezionate.


Regenerate response

New chat
History is temporarily unavailable. We're working to restore this feature as soon as possible.
Light mode
My account
Updates & FAQ
Log out



orcl_cd_ccoc e' il codice dell ordine cliente

SELECT *
FROM riof
WHERE riof_cd_riof = 58474;















crea un query sql che mi mostri il totale delle quantità comprate di prodotti con il nome che contiene "%INDUS" nell ordine fornitore 58474





dopo aver letto tutto scrivi solo la parola "LETTO"




-------------

seleziona le righe degli ordini fornitori che contengono un prodotto che sia chiama %trava%

seleziona le righe dell ordine fornitore con riferimento 58474
SELECT * FROM riof WHERE riof_cd_orfo = 58474;



crea un query sql che mi mostri il totale delle quantità comprate nell ordine fornitore 58474
SELECT SUM(riof_fl_quan) AS TotaleQuantita
FROM riof
WHERE riof_cd_orfo = 58474;


crea un query sql che mi mostri il totale delle quantità comprate di prodotti con il nome che contiene "%INDUS" nell ordine fornitore 58474
SELECT SUM(riof.riof_fl_quan) AS TotaleQuantita
FROM dbo.riof
JOIN dbo.prodotti ON riof.prodotti_id = prodotti.id
WHERE riof.riof_cd_orfo = 58474 AND prodotti.nome LIKE '%INDUS%';




crea un query sql che mi mostri il totale delle quantità di prodotto nell'ordine fornitore  58474  il cui nome  contiene "%INDUS" 



crea un query sql che mi dia 
il totale delle quantità dei metri cubi 
di tutte le righe degli ordini fornitore del 2022
in cui il nome del fornitore inizia con "NORI"











Contratti
-------

Crea un nuovo modello odoo chiamato crm.lead.part

