<div class="chat-container">
  <!-- Header -->
  <div class="chat-header">
    <h3>🤖 AI Assistant</h3>
    <div class="header-controls">
      <select [(ngModel)]="selectedModel" class="model-select" [disabled]="isLoading">
        <option *ngFor="let model of availableModels" [value]="model.value">
          {{ model.label }}
        </option>
      </select>
      <button (click)="clearChat()" class="btn btn-outline" [disabled]="isLoading">
        🗑️ Pulisci
      </button>
    </div>
  </div>

  <!-- API Key Configuration (shown when not configured) -->
  <div *ngIf="!isConfigured" class="api-config">
    <div class="config-card">
      <h4>⚙️ Configurazione</h4>
      <p>Inserisci la tua API Key di OpenAI per iniziare:</p>
      <div class="input-group">
        <input
          type="password"
          [(ngModel)]="apiKey"
          placeholder="sk-..."
          class="api-input"
          (keypress)="$event.key === 'Enter' && configureApiKey()"
        >
        <button (click)="configureApiKey()" class="btn btn-primary" [disabled]="!apiKey.trim()">
          Configura
        </button>
      </div>
      <small class="help-text">
        La tua API Key viene utilizzata solo localmente e non viene memorizzata.
      </small>
    </div>
  </div>

  <!-- Messages Container -->
  <div class="messages-container" #messagesContainer>
    <div *ngFor="let message of messages" class="message"
         [ngClass]="{'user-message': message.isUser, 'ai-message': !message.isUser, 'loading': message.isLoading}">

      <div class="message-avatar">
        <span *ngIf="message.isUser">👤</span>
        <span *ngIf="!message.isUser && !message.isLoading">🤖</span>
        <span *ngIf="message.isLoading" class="loading-spinner">⏳</span>
      </div>

      <div class="message-content">
        <div class="message-text">{{ message.content }}</div>
        <div class="message-time">
          {{ message.timestamp | date:'HH:mm' }}
        </div>
      </div>
    </div>
  </div>

  <!-- Input Area -->
  <div class="input-area" *ngIf="isConfigured">
    <div class="input-container">
      <textarea
        [(ngModel)]="currentMessage"
        placeholder="Scrivi il tuo messaggio..."
        class="message-input"
        rows="1"
        [disabled]="isLoading"
        (keypress)="onKeyPress($event)"
        #messageInput>
      </textarea>
      <button
        (click)="sendMessage()"
        class="send-button"
        [disabled]="!currentMessage.trim() || isLoading">
        <span *ngIf="!isLoading">📤</span>
        <span *ngIf="isLoading" class="loading-spinner">⏳</span>
      </button>
    </div>
    <div class="input-help">
      <small>Premi Invio per inviare, Shift+Invio per andare a capo</small>
    </div>
  </div>

  <!-- System Prompt Configuration -->
  <div class="system-prompt-config" *ngIf="isConfigured">
    <details>
      <summary>⚙️ Prompt di Sistema</summary>
      <textarea
        [(ngModel)]="systemPrompt"
        placeholder="Inserisci il prompt di sistema..."
        class="system-prompt-input"
        rows="3">
      </textarea>
    </details>
  </div>
</div>
