import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OpenAICompletionRequest {
  model: string;
  messages: OpenAIMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stream?: boolean;
}

export interface OpenAICompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: OpenAIMessage;
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface OpenAIEmbeddingRequest {
  input: string | string[];
  model: string;
  encoding_format?: 'float' | 'base64';
}

export interface OpenAIEmbeddingResponse {
  object: string;
  data: {
    object: string;
    embedding: number[];
    index: number;
  }[];
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class L33tService {
  private apiKey: string = '';
  private baseUrl: string = 'https://api.openai.com/v1';

  constructor(private http: HttpClient) { }

  /**
   * Imposta la chiave API di OpenAI
   */
  setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  /**
   * Imposta l'URL base per le API (utile per proxy o endpoint personalizzati)
   */
  setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
  }

  /**
   * Crea gli headers HTTP per le richieste OpenAI
   */
  private createHeaders(): HttpHeaders {
    if (!this.apiKey) {
      throw new Error('API Key non impostata. Usa setApiKey() per configurarla.');
    }

    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`
    });
  }

  /**
   * Gestisce gli errori delle richieste HTTP
   */
  private handleError(error: any): Observable<never> {
    console.error('Errore OpenAI API:', error);

    let errorMessage = 'Errore sconosciuto';

    if (error.error?.error?.message) {
      errorMessage = error.error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }

  /**
   * Crea una chat completion usando l'API OpenAI
   */
  createChatCompletion(request: OpenAICompletionRequest): Observable<OpenAICompletionResponse> {
    const headers = this.createHeaders();
    const url = `${this.baseUrl}/chat/completions`;

    return this.http.post<OpenAICompletionResponse>(url, request, { headers })
      .pipe(
        catchError(this.handleError.bind(this))
      );
  }

  /**
   * Metodo semplificato per inviare un messaggio e ricevere una risposta
   */
  sendMessage(
    message: string,
    model: string = 'gpt-3.5-turbo',
    systemPrompt?: string,
    options?: Partial<OpenAICompletionRequest>
  ): Observable<string> {
    const messages: OpenAIMessage[] = [];

    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }

    messages.push({ role: 'user', content: message });

    const request: OpenAICompletionRequest = {
      model,
      messages,
      max_tokens: 1000,
      temperature: 0.7,
      ...options
    };

    return this.createChatCompletion(request).pipe(
      map(response => response.choices[0]?.message?.content || '')
    );
  }

  /**
   * Crea embeddings per il testo fornito
   */
  createEmbeddings(request: OpenAIEmbeddingRequest): Observable<OpenAIEmbeddingResponse> {
    const headers = this.createHeaders();
    const url = `${this.baseUrl}/embeddings`;

    return this.http.post<OpenAIEmbeddingResponse>(url, request, { headers })
      .pipe(
        catchError(this.handleError.bind(this))
      );
  }

  /**
   * Metodo semplificato per ottenere embeddings di un testo
   */
  getTextEmbedding(
    text: string,
    model: string = 'text-embedding-ada-002'
  ): Observable<number[]> {
    const request: OpenAIEmbeddingRequest = {
      input: text,
      model
    };

    return this.createEmbeddings(request).pipe(
      map(response => response.data[0]?.embedding || [])
    );
  }

  /**
   * Crea una conversazione con più messaggi
   */
  createConversation(
    messages: OpenAIMessage[],
    model: string = 'gpt-3.5-turbo',
    options?: Partial<OpenAICompletionRequest>
  ): Observable<string> {
    const request: OpenAICompletionRequest = {
      model,
      messages,
      max_tokens: 1000,
      temperature: 0.7,
      ...options
    };

    return this.createChatCompletion(request).pipe(
      map(response => response.choices[0]?.message?.content || '')
    );
  }

  /**
   * Verifica se l'API Key è configurata
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Ottiene informazioni sui modelli disponibili
   */
  getModels(): Observable<any> {
    const headers = this.createHeaders();
    const url = `${this.baseUrl}/models`;

    return this.http.get(url, { headers })
      .pipe(
        catchError(this.handleError.bind(this))
      );
  }
}
