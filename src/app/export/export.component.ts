import { Component, OnInit } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { Lead } from '../models/crm.lead.model';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';

@Component({
    selector: 'app-export',
    templateUrl: './export.component.html',
    styleUrls: ['./export.component.scss'],
    standalone: false
})
export class ExportComponent implements OnInit {
  lists: Lead[];

  constructor(private odooEm:OdooEntityManager) { }

  async ngOnInit(): Promise<void> {

    this.lists = await firstValueFrom(this.odooEm.search<Lead>(new Lead()))

    
  }

}
