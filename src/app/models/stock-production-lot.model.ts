import { OdooModel } from './odoo-model.model';
import { OdooSerializableInterface } from '../shared/interfaces/odoo-serializable-interface';
import { OdooRelationship } from './odoo-relationship.model';
import { Product } from './product.model';


export class StockProductionLot extends OdooModel implements OdooSerializableInterface<StockProductionLot> {
  public readonly ODOO_MODEL = 'stock.production.lot';

  name:string = ""
  product_id:OdooRelationship<Product> = new OdooRelationship<Product>();
  
  create() {
    return new StockProductionLot();
  }
}
