import { OdooModel } from './odoo-model.model';
import { OdooSerializableInterface } from '../shared/interfaces/odoo-serializable-interface';
import { SaleOrder } from './sale-order.model';
import { OdooMultiRelationship } from './odoo-multi-relationship.model';
import { OdooRelationship } from './odoo-relationship.model';
import { StockQuantPackage } from './stock-quant-package';
import { Product } from './product.model';
import { StockLocation } from './stock-location';
import { StockProductionLot } from './stock-production-lot.model';
import { StockMove } from './stock-move';
import { StockPickingType } from './stock-picking-type.model';
import { StockRule } from './stock-rule';
import { UomUom } from './uom-uom.model';
import { ProductPackaging } from './product.packaging.model';
import { StockPicking } from './stock-picking';
import { ProductTemplate } from './product.template.model';
import { User } from './user.model';


export class StockMoveLine extends OdooModel implements OdooSerializableInterface<StockMoveLine> {
  public readonly ODOO_MODEL = 'stock.move.line';

  id: number;
  lot_id:OdooRelationship<StockProductionLot> = new OdooRelationship<StockProductionLot>()
  location_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>()
  location_dest_id:OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>()
  product_id:OdooRelationship<Product> = new OdooRelationship<Product>()
  package_id:OdooRelationship<StockQuantPackage> = new OdooRelationship()
  result_package_id:OdooRelationship<StockQuantPackage> = new OdooRelationship()
  qty_done:number = 0;
  _pz_done:number = 0;
  _qty_done:number = 0; // temp used by production-ext
  lot_name:string =""
  origin:string = ""
  picking_id:OdooRelationship<StockPicking> = new OdooRelationship<StockPicking>()
  picking_type_id:OdooRelationship<StockPickingType> = new OdooRelationship<StockPickingType>()
  move_id:OdooRelationship<StockMove> = new OdooRelationship<StockMove>()
  state:string = ""
  product_uom_id:OdooRelationship<UomUom> = new OdooRelationship<UomUom>()
  _checked:boolean = false
  _justChanged:boolean = false
  reserved_uom_qty:number = 0
  _PackErrorText:string = "" 
  _available_qty:number = 0
  product_packaging_id:OdooRelationship<ProductPackaging> = new OdooRelationship<ProductPackaging>()
  _package_qty:number = 0
  date:string = ""
  create_uid:OdooRelationship<User> = new OdooRelationship<User>()
  write_date:string = ""
  picking_code:string = ""
  


  constructor(id?: number) {
    super(id);
    this.id = id;
    // this.name = name
    // this.product_uom_qty = product_uom_qty
    // this.product_uom_qty_po = product_uom_qty_po
    // this.righe_from = new OdooRelationship<NaPacchiProduct>()
    // this.unit = unit;
    // this.packages = packages;
    // this.barcodes = barcodes;
  }

  create(): StockMoveLine {
    return new StockMoveLine();
  }
}
