crea un query sql che mi dia 
il totale delle quantità dei metri cubi 
di tutte le righe degli ordini fornitore del 2022
in cui il nome del fornitore inizia con "NORI"

dato questo

/****** Object:  Table [dbo].[clie]    Script Date: 21/03/23 13:58:38 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[clie](
	[clie_cd_clie] [int] IDENTITY(19934,1) NOT NULL,
	[clie_de_rags] [varchar](100) NULL,
	[clie_de_addr] [varchar](255) NULL,
	[clie_de_cap] [varchar](50) NULL,
	[clie_de_city] [varchar](50) NULL,
	[clie_de_prov] [varchar](3) NULL,
	[clie_de_paes] [varchar](50) NULL,
	[clie_cd_agen] [int] NULL,
	[clie_do_agen] [smallint] NULL,
	[clie_fl_fidoprop] [int] NULL,
	[clie_do_annotel] [smallint] NULL,
	[clie_do_annovis] [smallint] NULL,
	[clie_do_consgml] [smallint] NULL,
	[clie_do_conspml] [smallint] NULL,
	[clie_do_consgm3] [smallint] NULL,
	[clie_do_conspm3] [smallint] NULL,
	[clie_do_fpag] [smallint] NULL,
	[clie_de_nopa] [varchar](255) NULL,
	[clie_nu_dipe] [smallint] NULL,
	[clie_do_atti] [smallint] NULL,
	[clie_de_forn1] [varchar](50) NULL,
	[clie_nu_vaso] [smallint] NULL,
	[clie_nu_vaes] [smallint] NULL,
	[clie_nu_vapr] [smallint] NULL,
	[clie_nu_vaga] [smallint] NULL,
	[clie_nu_vacm] [smallint] NULL,
	[clie_nu_vacc] [smallint] NULL,
	[clie_nu_prso] [smallint] NULL,
	[clie_me_memo] [text] NULL,
	[clie_de_noin] [varchar](255) NULL,
	[clie_cd_pers] [int] NULL,
	[clie_do_divi] [int] NULL,
	[clie_do_pers] [smallint] NULL,
	[clie_da_crea] [datetime] NULL,
	[clie_ce_type] [char](1) NULL,
	[clie_ce_ammi] [varchar](10) NULL,
	[clie_do_annofax] [smallint] NULL,
	[clie_do_consleg] [smallint] NULL,
	[clie_nu_vanu] [smallint] NULL,
	[clie_nu_pres] [smallint] NULL,
	[clie_nu_prpr] [smallint] NULL,
	[clie_nu_prga] [smallint] NULL,
	[clie_nu_prcm] [smallint] NULL,
	[clie_nu_prcc] [smallint] NULL,
	[clie_nu_prnu] [smallint] NULL,
	[clie_ce_clfo] [char](1) NULL,
	[clie_do_atts] [smallint] NULL,
	[clie_de_forn] [varchar](255) NULL,
	[clie_fl_fido] [int] NULL,
	[clie_de_agen] [varchar](255) NULL,
	[clie_de_nick] [varchar](100) NULL,
	[clie_do_scar] [smallint] NULL,
	[clie_me_notescarico] [text] NULL,
	[clie_de_sett] [varchar](30) NULL,
	[clie_de_contrpart] [varchar](60) NULL,
	[clie_de_destrags] [varchar](100) NULL,
	[clie_de_destaddr] [varchar](50) NULL,
	[clie_de_destcap] [varchar](50) NULL,
	[clie_de_destcity] [varchar](50) NULL,
	[clie_de_destprov] [varchar](2) NULL,
	[clie_do_acce] [smallint] NULL,
	[clie_nu_chkiso] [bit] NULL,
	[clie_de_partiva] [varchar](50) NULL,
	[clie_de_codfisc] [varchar](50) NULL,
	[clie_do_scpv] [int] NULL,
	[clie_de_username] [varchar](50) NULL,
	[clie_de_password] [varchar](50) NULL,
	[clie_da_scadfido] [datetime] NULL,
	[clie_nu_broc] [bit] NULL,
 CONSTRAINT [pk_clie] PRIMARY KEY NONCLUSTERED 
(
	[clie_cd_clie] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]





/****** Object:  Table [dbo].[orfo]    Script Date: 21/03/23 14:29:27 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[orfo](
	[orfo_cd_orfo] [int] IDENTITY(1410,2) NOT NULL,
	[orfo_cd_stat] [int] NULL,
	[orfo_cd_fasc] [int] NOT NULL,
	[orfo_cd_pers] [int] NULL,
	[orfo_do_divi] [int] NULL,
	[orfo_do_pers] [int] NULL,
	[orfo_da_orfo] [datetime] NOT NULL,
	[orfo_da_cons] [datetime] NULL,
	[orfo_do_moco] [int] NULL,
	[orfo_nu_arri] [int] NULL,
	[orfo_me_note] [text] NULL,
	[orfo_nu_conf] [bit] NULL,
	[orfo_do_fpag] [int] NULL,
	[orfo_cd_forn] [int] NULL,
	[orfo_fl_totale] [float] NULL,
	[orfo_fl_forfait] [float] NULL,
	[orfo_cd_achi] [int] NULL,
	[orfo_nu_verif] [bit] NULL,
	[orfo_do_resa] [int] NULL,
 CONSTRAINT [PK__orfo__7755B73D] PRIMARY KEY CLUSTERED 
(
	[orfo_cd_orfo] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO








/****** Object:  Table [dbo].[riof]    Script Date: 03/21/2023 14:45:32 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[riof](
	[riof_cd_riof] [int] IDENTITY(2789,1) NOT NULL,
	[riof_cd_orfo] [int] NOT NULL,
	[riof_nu_prog] [int] NULL,
	[riof_cd_move] [int] NULL,
	[riof_de_arfo] [varchar](300) NULL,
	[riof_de_desc] [varchar](400) NULL,
	[riof_cd_umor] [int] NOT NULL,
	[riof_de_lung] [varchar](100) NULL,
	[riof_de_larg] [varchar](100) NULL,
	[riof_de_spes] [varchar](100) NULL,
	[riof_nu_mate] [bit] NULL,
	[riof_fl_quan] [float] NULL,
	[riof_fl_qtar] [float] NULL,
	[riof_fl_prezlist] [float] NULL,
	[riof_fl_sconto] [float] NULL,
	[riof_fl_prez] [float] NULL,
	[riof_fl_prezumve] [float] NULL,
	[riof_fl_prezeff] [float] NULL,
	[riof_fl_prezeffumve] [float] NULL,
	[riof_fl_totale] [float] NULL,
	[riof_fl_coag] [float] NULL,
	[riof_me_note] [text] NULL,
	[riof_de_notaprod] [varchar](200) NULL,
	[riof_fl_qtor] [float] NULL,
	[riof_de_pezz] [varchar](100) NULL,
	[riof_de_umor] [varchar](20) NULL,
	[riof_do_valu] [int] NULL,
	[riof_fl_camb] [float] NULL,
	[riof_fl_convum] [float] NULL,
	[riof_de_prper] [varchar](200) NULL,
 CONSTRAINT [PK__riof__7EF6D905] PRIMARY KEY CLUSTERED 
(
	[riof_cd_riof] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]


riof  sono le righe degli ordini fornitori
la tabella degli ordini fornitori e' la tabella orfo
la colonna "riof_cd_orfo" nella tabella "riof" rappresenta il riferimento dell'ordine fornitore
la colonna "riof_de_desc" nella tabella "riof" rappresenta il nome o la descrizione del prodotto
riof_fl_quan e' la quantità di prodotto richiesta in ogni riga di ogni ordine fornitore
le quantità dei prodotti comprati negli ordini fornitori puo' essere espressa in metri cubi
la quanità dei prodotti comprati espressa in metri cubi si chiama "riof_fl_quan" non "riof_fl_quan_mc"
nella tabella dei fornitori la colonna "clie_de_rags" rappresenta il nome del fornitore
orfo_da_orfo raprresenta la data di creazione dell'ordine fornitore