import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Subject, firstValueFrom } from 'rxjs';
import { MailActivity } from 'src/app/models/mail.message';
import { SaleOrder } from 'src/app/models/sale-order.model';
import { User } from 'src/app/models/user.model';
import { OdooEntityManager } from 'src/app/shared/services/odoo-entity-manager.service';

@Component({
    selector: 'app-activity-scheduler',
    templateUrl: './activity-scheduler.component.html',
    styleUrls: ['./activity-scheduler.component.scss'],
    standalone: false
})
export class ActivitySchedulerComponent implements OnInit, AfterViewInit, OnChanges {

  @Output() loading: EventEmitter<boolean> = new EventEmitter();
  @Input() sale: SaleOrder;
  activity: MailActivity;
  date_deadline: Date;
  userId: number;
  user: User;

  constructor(private odooEm: OdooEntityManager) { }
 
  async ngOnInit(): Promise<void> {
    // not needed
      // const result: any = await this.odooEm.odoorpcService.getSessionInfo();
      //   this.userId = result.result.user_id[0];
      //   //fetch all userse
      //   let usersFound = await firstValueFrom(this.odooEm.search<User>(new User(), [['id', '=', this.userId]]));
      //   this.user = usersFound[0]
    this.findActivity();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['sale']) {
      this.findActivity();
    }
  }

  ngAfterViewInit(): void {
    this.findActivity();
  }
  
  private findActivity(): void {
    // Check if sale and activity_ids are defined
    if (!this.sale || !this.sale.activity_ids) {
      return;
    }

    // Force resolve activities if they're not loaded yet
    if (!this.sale.activity_ids.values || this.sale.activity_ids.values.length === 0) {
      if (this.sale.activity_ids.ids && this.sale.activity_ids.ids.length > 0) {
        firstValueFrom(this.odooEm.resolve(this.sale.activity_ids)).then(() => {
          this.updateActivity();
          console.log("Activities resolved:", this.sale.activity_ids.values);
        });
      }
    } else {
      this.updateActivity();
    }
  }

  private updateActivity(): void {
    if (this.sale.activity_ids.values?.length > 0) {
      const foundActivity = this.sale.activity_ids.values.find(x => x.activity_type_id.id == 4);
      if (foundActivity) {
        this.activity = foundActivity;
        console.log("Activity with type_id=4 found:", this.activity);
      }
    }
  }

  async rescheduleActivity(date: Date) {
    this.loading.next(true);
    // Convert input date to Date object
    const inputDate = new Date(date);
    // Get today's date and set hours to 0 for date comparison only
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset the time portion
    // Compare timestamps of both dates
    if (inputDate.getTime() < today.getTime()) {
      this.loading.next(false);
      // Use Alert to show error message
      alert('Non è possibile preparare l\'ordine prima di oggi');
      return;
    }
    await this.odooEm.check(firstValueFrom(this.odooEm.update(this.activity, {date_deadline: date.toString()})));
    this.activity.date_deadline = date.toString();
    this.loading.next(false);
  }

  async unscheduleActivity(s: SaleOrder) {
    if (!this.activity) {
      console.warn("Attempted to unschedule activity, but no activity is found");
      this.loading.next(false);
      return;
    }
    
    this.loading.next(true);
    await this.odooEm.check(this.odooEm.call2("mail.activity", "unlink", [[this.activity.id]]));
    this.activity = null;
    this.loading.next(false);
  }

  async scheduleActivity(s: SaleOrder, date: Date) {
    this.loading.next(true);

    // If sale notes are empty send a confirmation message
    if (!s.note) {
      if (!confirm('Non ci sono istruzioni di consegna (peso pacchi). Continuare lo stesso? Potrai inserirle successivamente')) {
        this.loading.next(false);
        return;
      }
    }

    // Convert input date to Date object
    const inputDate = new Date(date);
    // Get today's date and set hours to 0 for date comparison only
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset the time portion
    // Compare timestamps of both dates
    if (inputDate.getTime() < today.getTime()) {
      this.loading.next(false);
      // Use Alert to show error message
      alert('Non è possibile preparare l\'ordine prima di oggi');
      return;
    }

    try {
      const newActivity = await firstValueFrom(
        this.odooEm.create<MailActivity>(new MailActivity(), {
          res_model_id: 502,
          res_id: s.id,
          res_model: "sale.order",
          activity_type_id: 4,
          date_deadline: date.toString(),
          user_id: 104
        })
      );
      
      this.activity = newActivity;
      console.log("New activity created:", this.activity);
    } catch (error) {
      console.error("Error creating activity:", error);
      alert('Errore durante la creazione dell\'attività');
    } finally {
      this.loading.next(false);
    }
  }
}