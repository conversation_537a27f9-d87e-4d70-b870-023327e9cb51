{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"gali-erp2": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"prefix": "app", "style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.png", "src/assets", "src/_redirects", "src/manifest.json"], "styles": ["src/styles/app.scss"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/popper.js/dist/popper.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js", "node_modules/trello/main.js"], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "src/main.ts"}, "configurations": {"e2e": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}]}, "production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}], "serviceWorker": "ngsw-config.json"}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "gali-erp2:build", "ssl": true}, "configurations": {"production": {"buildTarget": "gali-erp2:build:production"}, "e2e": {"buildTarget": "gali-erp2:build:e2e"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "gali-erp2:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles/app.scss"], "scripts": [], "assets": ["src/favicon.png", "src/assets", "src/_redirects", "src/manifest.json"]}}, "deploy": {"builder": "@angular/fire:deploy", "options": {"version": 2, "browserTarget": "gali-erp2:build:production"}}}}, "gali-erp2-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js"}, "configurations": {"production": {"devServerTarget": "gali-erp2:serve:production"}}}}}}, "cli": {"analytics": "330888e5-873b-4789-8bb8-b53dbcc200e6"}}