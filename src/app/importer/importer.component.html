<div class="container mt-4">
  <div class="card">
    <div class="card-header">
      <h4>CSV Importer</h4>
    </div>
    <div class="card-body">
      <div class="mb-3">
        <label for="csvFile" class="form-label">Select CSV File</label>
        <input type="file" class="form-control" id="csvFile" accept=".csv" (change)="onFileSelected($event)">
      </div>
      

      <div>
        <button class="btn btn-primary" (click)="importProductTemplateAttributeValues()">import product template attribute values</button>
        <!-- <button class="btn btn-primary" (click)="importAttributes()">import attributi</button>
        <button class="btn btn-primary" (click)="importAttributeValues()">import attribute values</button> -->
        <button class="btn btn-primary" (click)="importVariantsAndPurchase()">import variants and purch lines</button> 
        <!-- <button class="btn btn-primary" (click)="importSales()">import sales</button> -->
         <button class="btn btn-primary" (click)="fillPicking()">Fill picking</button>
      </div>

      <div *ngIf="data.length > 0" class="mt-4">
        <h5>Imported Data</h5>
        <p-table [value]="data" [columns]="columns" [resizableColumns]="true" 
                 styleClass="p-datatable-sm" [tableStyle]="{'min-width': '50rem'}">
          <ng-template pTemplate="header" let-columns>
            <tr>
              <th *ngFor="let col of columns" [pSortableColumn]="col.field" pResizableColumn>
                {{col.header}}
                <p-sortIcon [field]="col.field"></p-sortIcon>
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-rowData let-columns="columns">
            <tr>
              <td *ngFor="let col of columns">
                {{rowData[col.field]}}
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>

      <div *ngIf="logMessages.length > 0" class="mt-4">
        <h5>Log</h5>
        <div class="log-container p-3 bg-light border rounded">
          <div *ngFor="let message of logMessages" class="log-message">
            {{message}}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
