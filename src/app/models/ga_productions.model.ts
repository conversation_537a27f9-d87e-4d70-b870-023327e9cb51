import { ProductTemplate } from '../models/product.template.model';
import { StockMove } from '../models/stock-move';
import { UomUom } from '../models/uom-uom.model';
import { OdooMultiRelationship } from './odoo-multi-relationship.model';
import { OdooRelationship } from './odoo-relationship.model';
import { Product } from './product.model';
import { ProductPackaging } from './product.packaging.model';


export interface ProductionGroupedMove {
  // Template Information
  product_tmpl_id: OdooRelationship<ProductTemplate>;
  uom_id: OdooRelationship<UomUom>;
  
  // Moves and Quantities
  moves: GroupLineMove[];
  total_quantity: number;
  
  // Cost Information
  cost_share: number;      // Percentage of total production cost
  total_cost: number;      // Total cost for this group
  _tempTotalCost?: number; // For editing
  unit_cost: number;       // Cost per unit
  
  // Status and Type
  is_expanded: boolean;    // UI state for expanded view
  is_component: boolean;   // Whether this is a component of the production or the output
  
  }


export interface GroupLineMove  {
    //we basically have to add total cost and unit cost for the move (cost% is already in the move)
    // we also add packagings and packaging quantities

    stockMove?: StockMove;
    _productTemplate: OdooRelationship<ProductTemplate>;
    _product: OdooRelationship<Product>
    _uom: OdooRelationship<UomUom>;
    _costShare: number;
    _quantity: number;

    is_main_product: boolean; // Whether this is the main production output
    

    _totalCost: number;
    _unitCost: number;

    // Packaging Information
    packaging_qty: number;

 
}


