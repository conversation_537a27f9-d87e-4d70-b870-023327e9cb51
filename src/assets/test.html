<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Formatter</title>
</head>

<body>
    <h2>Enter Raw Client Data:</h2>
    <textarea id="rawData" rows="10" cols="50"></textarea><br><br>
    <button onclick="formatData()">Format Data</button>

    <h2>Formatted Data:</h2>
    <table border="1" id="dataTable">
        <thead>
            <tr>
                <th>Client Code</th>
                <th>Client Name</th>
                <th>Street</th>
                <th>ZIP</th>
                <th>City</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>

    <script>
        function formatData() {
            const data = document.getElementById('rawData').value.split('\n');
            const tableBody = document.getElementById('dataTable').getElementsByTagName('tbody')[0];
            
            // Clear existing rows
            tableBody.innerHTML = '';

            for (let i = 0; i < data.length; i++) {
                if (data[i].startsWith('C0')) {
                    const clientCode = data[i];
                    const clientName = data[++i];
                    const street = (i + 1 < data.length && !data[i + 1].startsWith('C0')) ? data[++i] : '';
                    const zip = (i + 1 < data.length && !isNaN(data[i + 1])) ? data[++i] : '';
                    const city = (zip && i + 1 < data.length && !data[i + 1].startsWith('C0')) ? data[++i] : '';

                    const row = tableBody.insertRow();
                    row.insertCell(0).textContent = clientCode;
                    row.insertCell(1).textContent = clientName;
                    row.insertCell(2).textContent = street;
                    row.insertCell(3).textContent = zip;
                    row.insertCell(4).textContent = city;
                }
            }
        }
    </script>
</body>

</html>
