import { ChangeDetectorRef, Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import {  ProductWithOnlyVariants } from '../models/product.model';
import { OdooEntityManager } from '../shared/services/odoo-entity-manager.service';
import {  firstValueFrom } from 'rxjs';
import { StockQuant } from '../models/stock-quant';
import { ODOO_IDS } from '../models/deal';
import { StockQuantPackage } from '../models/stock-quant-package';
import { IrAttachment } from '../models/mail.message';

@Component({
    selector: 'app-packs-photos',
    templateUrl: './packs-photos.component.html',
    styles: [`
    .dropdown-menu {
      width: auto;
      min-width: 200px;
    }
    .dropdown-menu.expanded {
      width: min(600px, calc(100vw - 100px));
    }
  `],
    standalone: false
})

export class PacksPhotosComponent implements OnInit, OnChanges {

  @Input() from: 'product' | 'package'; 
  @Input() singleQuant?: StockQuant;  //if we are showing only one package (ex package list) we pass the quant
  @Input() productWithQuants?: ProductWithOnlyVariants;

   attachments: boolean = false;
   messages: boolean = false;
   loaded: boolean = false;

  public QuantsToShow: StockQuant[] = [];

  constructor(
    private odooEm: OdooEntityManager, 
    private cdr: ChangeDetectorRef //needed cause order inventory passes data asynchronously
  ) {}

  async ngOnInit() {
    await this.initializeComponent();
  }

  async ngOnChanges(changes: SimpleChanges) {
    // Check if singleQuant input has changed (for package mode)
    if (changes['singleQuant'] && !changes['singleQuant'].firstChange) {
      console.log('singleQuant changed, reinitializing component:', changes['singleQuant'].currentValue);
      await this.initializeComponent();
    }
    
    // Check if productWithQuants input has changed (for product mode)
    if (changes['productWithQuants'] && !changes['productWithQuants'].firstChange) {
      console.log('productWithQuants changed, reinitializing component:', changes['productWithQuants'].currentValue);
      await this.initializeComponent();
    }
  }

  private async initializeComponent() {
    // Reset state
    this.loaded = false;
    this.attachments = false;
    this.messages = false;
    this.QuantsToShow = [];

    await this.getValidPackages();

    if (this.from === 'package' && this.singleQuant?.package_id?.value) {
      await this.onClickPack(this.singleQuant.package_id.value, new Event('click'));
    }

    // Force change detection to update the template
    this.cdr.detectChanges();
  }

  async getValidPackages() { 
    console.log('getValidPackages called with:', this.from, this.singleQuant);
    
    if (this.from === 'package') { 
      if (this.singleQuant?.package_id?.id) { 
        this.singleQuant._productQuantity = this.singleQuant.available_quantity;
        this.QuantsToShow.push(this.singleQuant);
        console.log('Added single quant to show:', this.singleQuant);
        console.log('Package ID details:', this.singleQuant.package_id);
      } else {
        console.error('Single quant missing package_id:', this.singleQuant);
        console.error('Package ID value:', this.singleQuant?.package_id);
      }
    }
    else {
      if (this.from === 'product') { 
        this.productWithQuants.stock_quant_ids.values?.forEach((quant: StockQuant) => {
          if (quant.package_id.id 
            && quant.available_quantity > 0
            && !ODOO_IDS.exclude_packs_location_ids.includes(quant.location_id.id)
            && quant.product_id.id === this.productWithQuants.id) {
            quant._productQuantity = quant.available_quantity;
            
            this.QuantsToShow.push(quant);
          }
        });
      }
    }
    this.loaded = true; 
  }

  getNumberOfPacks() {
    let q = 0;
    q = this.QuantsToShow.filter(pack => pack.package_id.id).length;
    return q
  }

  // New method to get total number of photos across all packages
  getTotalPhotosCount(): number {
    if (this.from !== 'product') return 0;
    
    return this.QuantsToShow.reduce((total, quant) => {
      return total + (quant.package_id.value?.message_attachment_count || 0);
    }, 0);
  }

  // New method to get total number of messages across all packages
  getTotalMessagesCount(): number {
    if (this.from !== 'product') return 0;
    
    return this.QuantsToShow.reduce((total, quant) => {
      return total + (quant.package_id.value?.message_ids?.ids?.length || 0);
    }, 0);
  }

  // Helper method to check if there are any photos across all packages
  hasAnyPhotos(): boolean {
    return this.getTotalPhotosCount() > 0;
  }

  // Helper method to check if there are any messages across all packages
  hasAnyMessages(): boolean {
    return this.getTotalMessagesCount() > 0;
  }

  async onClickPack(pack: StockQuantPackage, event: Event) {
    // Prevent the main dropdown from closing
    event.preventDefault();
    event.stopPropagation();
    
    // Reset previous state
    pack._attachments = [];
    this.attachments = false;
    this.messages = false;
    
    //find attachments (photos)
    if (pack.message_attachment_count > 0) {
      pack._attachments = await firstValueFrom(
        this.odooEm.search<IrAttachment>(
          new IrAttachment(), 
          [
            ['res_id', '=', pack.id], 
            ['res_model', '=', 'stock.quant.package']
          ]
        )
      );
      this.attachments = true;
    }
      
    //set messages to true if there are messages
    if (pack.message_ids && pack.message_ids.ids.length > 0) {
      await firstValueFrom(this.odooEm.resolve(pack.message_ids));
      this.messages = true;
      if (this.messages) {
        console.log("MESSAGES: ", pack);
      }
    }

    // Force change detection after loading new data
    this.cdr.detectChanges();
  }
}