<app-navbar [loading]="loading" backroute=".." [leadId]="lead?.id" class="sticky-top" queryParamsHandling="preserve">
    <!-- Wrap everything in a div that takes full width -->
    <div class="d-flex justify-content-between align-items-center w-100">
        <!-- Left side -->
        <a class="navbar-brand">
            <span>&nbsp;<b>Commesse / {{lead?.tracking_code}} - {{lead?.partner_id.name}}</b></span>
        </a>
        <!-- Right side -->
        <div class="dropdown">
            <button class="btn btn-link text-dark" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fa fa-bars text-white"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" target="_blank"
                        href="//o3.galimberti.eu/web#id={{lead?.id}}&cids=1&menu_id=331&action=504&model=crm.lead&view_type=form">Apri
                        in Odoo</a></li>
                <li><a class="dropdown-item" target="_blank" (click)="delete()">Elimina</a></li>
            </ul>
        </div>
    </div>
</app-navbar>

<!-- Stage Navigation and print message-->
<div>
    <nav class="bg-light border-bottom p-3">
        <div class="d-flex align-items-center justify-content-between">
            <!-- Stages on the left -->
            <div class="d-flex align-items-center gap-3">
                <ng-container *ngFor="let s of stages; let i = index">
                    <div class="d-flex align-items-center">
                        <a class="text-decoration-none" (click)="toStatus(s)"
                            [ngClass]="{'badge bg-primary': s.id == lead?.stage_id?.id, 'text-muted': s.id != lead?.stage_id?.id}">
                            {{s.name}}
                        </a>
                        <i class="fa-solid fa-chevron-right ms-3 text-muted" *ngIf="i < stages.length - 1"></i>
                    </div>
                </ng-container>
            </div>
        </div>
    </nav>
</div>

<div class="container-fluid p-4 h-100">
    <div class="row g-4 h-100">

        <!-- Left Column -->
        <div class="col-md-4 h-100" style="max-width: 35%;" *ngIf="canShowLeft">
            <div class="card shadow-sm h-100">
                <div class="card-header px-3 d-flex align-items-center justify-content-between" style="height: 48px">
                    <h5 class="card-title mb-0">Informazioni e attività</h5>
                </div>

                <!-- Tab Navigation -->
                <ul class="nav nav-tabs px-3 pt-2">
                    <li class="nav-item">
                        <a class="nav-link" [class.active]="activeTab === 'info'" (click)="activeTab = 'info'">
                            Informazioni
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" [class.active]="activeTab === 'activities'"
                            (click)="activeTab = 'activities'">
                            Note e scadenze
                        </a>
                    </li>
                </ul>

                <div class="card-body d-flex flex-column p-2 mx-1"
                    style="height: calc(100vh - 250px); overflow-y: auto;">
                    <!-- Information Tab Content -->
                    <div *ngIf="activeTab === 'info'" style="overflow-x: hidden;">
                        <!-- Title Field -->
                        <div class="mb-4 mt-2">
                            <div class="d-flex align-items-center  mb-2">
                                <label class="form-label text-muted mb-0 me-3 ">Descrizione</label>
                                <button class="btn text-secondary me-2 p-0 text-end " type="button" *ngIf="getStage().sequence > 0" title="Modifica descrizione"
                                    (click)="toggleEditName()">
                                    <i [class]="editName ? 'fa-solid fa-save' : 'fa-solid fa-pen'"></i>
                                </button>
                                <button class="btn text-secondary me-1 p-0" type="button" title="Copia info per mail"
                                    (click)="copyLeadData()">
                                    <i
                                        [class]="copied ? 'fa-solid fa-envelope-circle-check' : 'fa-solid fa-envelope'"></i>
                                </button>
                            </div>
                            <ng-container *ngIf="getStage().sequence > 0 && !editName">
                                <div class="ms-3">
                                    <h5 class="mb-0">{{lead.tracking_code}} - {{lead.name}}</h5>
                                </div>
                            </ng-container>
                            <div class="ms-3" *ngIf="getStage().sequence == 0 || editName">
                                <input class="form-control" placeholder="*Inserisci descrizione commessa*"
                                    [(ngModel)]="lead.name" [ngClass]="{'is-invalid': !lead.name}"
                                    (change)="updateDealToOdoo('name')">
                                <div class="invalid-feedback">
                                    Descrizione commessa richiesta
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <label class="form-label text-muted mb-0 me-3">Cliente</label>
                                <div class="dropdown">
                                    <button class="btn text-secondary p-0 me-1" type="button" title="Modifica cliente"
                                        data-bs-toggle="dropdown">
                                        <i class="fa-solid fa-user-pen"></i>
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-right"
                                        style="min-width: 300px;z-index:9999999">
                                        <form class="px-3" style="min-width: 440px;">
                                            <app-contact [mode]="'embedded'" class="embedded"
                                                (onSelect)="onContact($event)">
                                            </app-contact>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="ms-3">
                                <div class="d-flex align-items-center">
                                    <h5 class="mb-0">{{lead.partner_id.name}}</h5>
                                    <button class="btn text-primary p-0 ms-2" type="button" title="Visualizza cliente"
                                        (click)="openContact(lead.partner_id.id)">
                                        <i class="fa-solid fa-external-link-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                       <!-- Address with visual validation and auto-fill button -->
<div class="mb-3 me-2">
    <label class="form-label text-muted mb-2">Indirizzo</label>
    <div class="input-group ms-3">
        <span class="input-group-text bg-light">
            <i class="fa-solid fa-map-marker-alt"></i>
        </span>
        <input #placesRef="ngx-places" ngx-google-places-autocomplete class="form-control "
            [(ngModel)]="lead.street"
            [ngClass]="{'border-danger': !lead.street || lead.street === 'false' || lead.street === 'undefined' || lead.street === false}"
            (onAddressChange)="onAddressChange($event)"
            placeholder="Inserisci l'indirizzo della commessa">
        <button class="btn btn-outline-secondary me-2" type="button" 
                title="Auto-riempi con indirizzo Lomagna"
                (click)="autoFillLomagnaAddress()">
            <i class="fa-solid fa-home"></i>
        </button>
        <div class="invalid-feedback d-block"
            *ngIf="!lead.street || lead.street === 'false' || lead.street === 'undefined' || lead.street === false">
            Indirizzo commessa richiesto
        </div>
    </div>
</div>
                        <div class="border-bottom mb-2"></div>

                        <!-- Improved Three Column Layout for Area, Responsabile, and Expected Revenue -->
                        <div class="mb-3">
                            <div class="row">
                                <!-- Area Column -->
                                <div class="col-md-4">
                                    <label class="form-label text-muted mb-2">Fatturazione</label>
                                    <div class="d-flex">
                                        <button
                                            class="btn btn-sm text-start w-100 d-flex justify-content-between align-items-center"
                                            [ngClass]="getAreaBadgeClass(lead.area)" data-bs-toggle="dropdown"
                                            aria-expanded="false">
                                            <span>{{lead.area}}</span>
                                            <i class="fa-solid fa-chevron-down"></i>
                                        </button>
                                        <ul class="dropdown-menu w-100">
                                            <li
                                                *ngFor="let area of ['Tetti', 'Case', 'Facciate e Decking', 'Aziendale', 'Pavimenti', 'Massello']">
                                                <a class="dropdown-item d-flex align-items-center"
                                                    (click)="assignArea(area)" [class.active]="area === lead.area">
                                                    <i class="fa-solid fa-check me-2" *ngIf="area === lead.area"></i>
                                                    {{area}}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Responsabile Column -->
                                <div class="col-md-4">
                                    <label class="form-label text-muted mb-2">Responsabile</label>
                                    <div class="d-flex">
                                        <button
                                            class="btn btn-sm btn-primary text-white text-start w-100 d-flex justify-content-between align-items-center"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                            <span>{{lead.user_id.name}}</span>
                                            <i class="fa-solid fa-chevron-down"></i>
                                        </button>
                                        <ul class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;">
                                            <li *ngFor="let user of users">
                                                <a class="dropdown-item d-flex align-items-center"
                                                    (click)="assignUser(user)"
                                                    [class.active]="user.id === lead.user_id.id">
                                                    <i class="fa-solid fa-check me-2"
                                                        *ngIf="user.id === lead.user_id.id"></i>
                                                    {{user.name}}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Expected Revenue Column -->
                                <div class="col-md-4">
                                    <label class="form-label text-muted mb-2">Valore atteso €</label>
                                    <div class="input-group input-group-sm">
                                        <button class="btn btn-outline-secondary" type="button"
                                            (click)="SyncExpectedRevenue()" title="Sincronizza con contratti">
                                            <i class="fa-solid fa-rotate"></i>
                                        </button>
                                        <input type="number" class="form-control" [ngModel]="lead.expected_revenue"
                                            [ngClass]="{'border-danger' : !lead.expected_revenue || lead.expected_revenue == '0'}"
                                            (ngModelChange)="tempRevenue = $event"
                                            (blur)="updateExpectedRevenue(tempRevenue)"
                                            [ngModelOptions]="{updateOn: 'blur'}" placeholder="0">
                                        <span class="input-group-text">€</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Improved Followers Section with Smaller Badges -->
                        <div class="mb-3">
                            <label class="form-label text-muted mb-2">Segue la commessa:</label>
                            <div class="d-flex flex-wrap gap-1 align-items-center">
                                <div *ngFor="let follower of lead.message_follower_ids.values"
                                    class="badge bg-info text-dark d-flex align-items-center py-1 px-2" 
                                    style="font-size: 0.75rem;">
                                    <span class="text-truncate" style="max-width: 110px;">{{follower.display_name}}</span>
                                    <button class="btn btn-link btn-sm p-0 ms-1" (click)="removeFollower(follower)"
                                            title="Rimuovi follower" style="font-size: 0.75rem;">
                                        <i class="fa-solid fa-times"></i>
                                    </button>
                                </div>

                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary p-1" 
                                            type="button"
                                            data-bs-toggle="dropdown" 
                                            aria-expanded="false" 
                                            title="Aggiungi follower"
                                        >
                                        <i class="fa-solid fa-plus px-1"></i>
                                    </button>
                                    <ul class="dropdown-menu" style="max-height: 200px; overflow-y: auto;">
                                        <li *ngFor="let possibleFollower of followers">
                                            <a class="dropdown-item d-flex align-items-center"
                                                [class.active]="isFollower(possibleFollower)"
                                                (click)="addFollower(possibleFollower)">
                                                <i class="fa-solid fa-check me-2" *ngIf="isFollower(possibleFollower)"></i>
                                                {{possibleFollower.name}}
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="border-bottom mb-2"></div>

                        <!-- Delivery Type and Installation Data with Stacked Posa Inputs  -->
                        <div class="mb-3">
                            <label class="form-label text-muted mb-2"
                            [ngClass]="{'bg-danger bg-opacity-25 p-1 rounded': !hasTag('Fornitura') && !hasTag('Fornitura e posa')}"    
                            >Modalità fornitura</label>
                            <div class="row">
                                <!-- Delivery Type Column -->
                                <div class="col-md-7">
                                    <div class="d-flex gap-2 "
                                       
                                    >
                                        <button class="btn btn-sm flex-grow-1" (click)="toggleTags('Fornitura')"
                                            [ngClass]="hasTag('Fornitura') ? 'btn-secondary' : 'btn-outline-muted'">
                                            <i class="fa-solid fa-truck-arrow-right me-1"></i>Solo fornitura
                                        </button>
                                        <button class="btn btn-sm flex-grow-1" (click)="toggleTags('Fornitura e posa')"
                                            [ngClass]="hasTag('Fornitura e posa') ? 'btn-secondary' : 'btn-outline-muted'">
                                            <i class="fa-solid fa-user-helmet-safety me-1"></i>Fornitura e posa
                                        </button>
                                    </div>
                                </div>

                                <!-- date_deadline after fornitura e posa-->
                                <div class="col-md-5" *ngIf="hasTag('Fornitura')||hasTag('Fornitura e posa')" >
                                    <div class="input-group input-group-sm flex-wrap">
                                        <span class="input-group-text bg-light">Consegna</span>
                                        <input type="date" class="form-control"
                                            [ngModel]="lead.date_deadline"
                                            (ngModelChange)="updateDateDeadline($event)"
                                            [ngClass]="{'border-danger': lead.date_deadline === false  }"                                                
                                            placeholder="Consegna">
                                    </div>
                                </div>

                                <!-- Installation Data Column (stacked)  DEACTIVATED AS OF 10/03/2025 -->
                                <!-- <div class="col-md-6" *ngIf="hasTag('Fornitura e posa')">
                                    <div class="d-flex flex-column gap-2"> -->
                                        <!-- Installation Start Row -->
                                        <!-- <div class="input-group input-group-sm">
                                            <span class="input-group-text bg-light" style="width: 60px;">Inizio</span>
                                            <input type="date" class="form-control"
                                                [ngModel]="posaStartActivity?.date_deadline"
                                                (ngModelChange)="updatePosa($event, posaDuration)"
                                                placeholder="Inizio posa">
                                        </div> -->

                                        <!-- Installation Duration Row -->
                                        <!-- <div class="input-group input-group-sm">
                                            <span class="input-group-text bg-light" style="width: 60px;">Durata</span>
                                            <input type="number" class="form-control" [ngModel]="posaDuration"
                                                (ngModelChange)="tempDuration = $event"
                                                (blur)="updatePosa(posaStartActivity?.date_deadline, tempDuration)"
                                                placeholder="0" [ngModelOptions]="{updateOn: 'blur'}">
                                            <span class="input-group-text">giorni</span>
                                        </div> -->
                                    <!-- </div> -->
                                <!-- </div> -->
                            </div>
                        </div>

                        <!-- Certification Section - Compact Layout -->
                        <div class="mb-3 ">
                            <label class="form-label text-muted mb-2 "
                            [ngClass]="{'bg-danger bg-opacity-25 p-1 rounded': !hasTag('No certificazione') && !hasTag('100% PEFC') && !hasTag('Massiccio strutturale') && !hasTag('FSC')}"
                            >Certificazioni</label>
                            <div class="d-flex flex-wrap gap-2 "
                       
                           >
                                <button class="btn btn-sm" (click)="toggleTags('No certificazione')"
                                    [ngClass]="hasTag('No certificazione') ? 'btn-secondary' : 'btn-outline-muted'">
                                    <i class="fa-solid fa-times-circle me-1"></i>No certificazione
                                </button>
                                <button class="btn btn-sm" (click)="toggleTags('100% PEFC')"
                                    [ngClass]="hasTag('100% PEFC') ? 'btn-secondary' : 'btn-outline-muted'">
                                    <i class="fa-solid fa-leaf me-1"></i>100% PEFC
                                </button>
                                <button class="btn btn-sm" (click)="toggleTags('FSC')"
                                [ngClass]="hasTag('FSC') ? 'btn-secondary' : 'btn-outline-muted'">
                                <i class="fa-solid fa-leaf me-1"></i>FSC
                            </button>
                                <button class="btn btn-sm" (click)="toggleTags('Massiccio strutturale')"
                                    [ngClass]="hasTag('Massiccio strutturale') ? 'btn-secondary' : 'btn-outline-muted'">
                                    <i class="fa-solid fa-badge-check me-1"></i>Massiccio strutturale
                                </button>
                            </div>
                        </div>

                        <div class="border-bottom mb-2"></div>

                    <!-- Notes Field for lead.description with HTML support -->
                    <!-- <div class="mb-4">
                        <div class="d-flex align-items-center justify-content-between mb-2">
                        <label class="form-label text-muted mb-0">Note</label>
                        </div>
                        <div class="ms-3">
                        <textarea 
                            class="form-control" 
                            style="resize: none; min-height: 100px;" 
                            [(ngModel)]="lead.description" 
                            placeholder="Inserisci note aggiuntive..."
                            [ngModelOptions]="{updateOn: 'blur'}"
                            (ngModelChange)="updateNotes($event)">
                        </textarea>
                        </div>
                    </div> -->
                    </div>
                    <!-- Activities Tab Content this is simply hidden so i can load it only once at the beginning -->
                    <div *ngIf="activeTab == 'activities'" class="h-100 ">
                        <app-message-widget [id]="lead.id" [action]="819">
                        </app-message-widget>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-md-8" style="max-width: 65%; height: calc(100vh - 160px); overflow-y: auto;"
            *ngIf="canShowRight">
            <!-- Drive & Trello -->
            <div class="card shadow-sm mb-4" *ngIf="shouldShowSection('driveAndTrello')">

                <div class="card-header px-3 d-flex align-items-center justify-content-between" style="height: 48px">
                    <h5 class="card-title mb-0">Drive & Trello</h5>
                </div>

                <div class="card-body p-0">
                    <div class="d-flex border-bottom">
                        @if(lead.name && lead.street && lead.partner_id.name) {
                        <!-- Preventivo -->
                        <div class="d-flex flex-column align-items-center border-end py-3 flex-fill">
                            <app-connect-to type="Preventivo" [deal]="lead" [driveSource]="cfg?.src"
                                [driveTarget]="cfg?.dst" [driveFolders]="filterDriveFolders('Preventivo')"
                                [trelloCards]="filterTrelloCards('Preventivo')"
                                [trelloListId]="cfg?.preventivo_trello_list"
                                [trelloBoardId]="cfg?.preventivo_trello_board" [connectable]="lead" [pos]="'top'"
                                (onConnect)="loadDeal()">
                            </app-connect-to>
                            <small class="mt-2">Preventivo</small>
                        </div>

                        <!-- Progetto -->
                        <div class="d-flex flex-column align-items-center border-end py-3 flex-fill">
                            <app-connect-to type="Progetto" [deal]="lead" [connectable]="lead"
                                [driveSource]="cfg?.project_src" driveIsMerge="true"
                                [driveTarget]="filterDriveFolders('Preventivo')[0]?.value"
                                [driveFolders]="filterDriveFolders('Progetto')"
                                [trelloCards]="filterTrelloCards('Progetto')" [trelloListId]="cfg?.project_trello_list"
                                [trelloBoardId]="cfg?.project_trello_board" [trelloTemplateCard]="cfg?.project_trello"
                                [skipAttachments]="true" [pos]="'bottom'" (onConnect)="loadDeal()"
                                (loading)="loading = true">
                            </app-connect-to>
                            <small class="mt-2">Progetto</small>
                        </div>



                        <!-- POS -->
                        <div class="d-flex flex-column align-items-center py-3 flex-fill">
                            <app-connect-to type="POS" [deal]="lead" [driveSource]="cfg?.pos_src"
                                [driveTarget]="cfg?.pos_dst" [driveLinkIn]="filterDriveFolders('Preventivo')[0]?.value"
                                [driveLinkTitle]="cfg.pos_link_name" [driveFolders]="filterDriveFolders('POS')"
                                [trelloCards]="filterTrelloCards('POS')" [trelloListId]="cfg?.pos_trello_list"
                                [trelloBoardId]="cfg?.pos_trello_board" [trelloTemplateCard]="cfg?.pos_template_card"
                                [connectable]="lead" (onConnect)="loadDeal()" (loading)="loading = true">
                            </app-connect-to>
                            <small class="mt-2">POS</small>
                        </div>
                        }
                        @else {
                        <div class="d-flex flex-column align-items-center py-3 flex-fill">
                            <small class="text-muted text-center">Inserisci indirizzo e descrizione commessa per
                                creare Drive e Trello</small>
                        </div>
                        }

                    </div>
                </div>
            </div>


            <!-- Preventivi -->
            <div class="card shadow-sm mb-4" *ngIf="shouldShowSection('preventivi')">
                <div class="card-header px-3 d-flex align-items-center justify-content-between" style="height: 48px">
                    <h5 class="card-title mb-0">Preventivi su lista</h5>
                    <button class="btn btn-link ms-auto " title="Espandi" (click)="toggleResolvePart(offersPart)"
                        [disabled]="offersPart?.sale_order_ids.ids.length == 0">
                        <i class="fa-solid fa-arrows-up-down fa-lg"></i>
                    </button>

                    <!-- nuovo preventivo -->
                    <div class="dropdown">
                        <button class="btn btn-link text-white" type="button" (click)="attachNewOffer()"
                            title="Aggiungi nuovo preventivo"
                            [disabled]="offersPart && !offersPart?.sale_order_ids.values && !offersPart?.sale_order_ids.ids.length == 0 ">
                            <i class="fa fa-plus text-primary fa-lg"></i>
                        </button>
                    </div>
                </div>

                <!-- righe preventivi -->

                <div *ngIf="offersPart && offersPart?.sale_order_ids.values" class="border" cdkDropList
                    id='part-{{offersPart.id}}' [cdkDropListConnectedTo]="partIds" [cdkDropListData]="offersPart"
                    (cdkDropListDropped)="drop($event)">


                    <div *ngFor="let s of offersPart?.sale_order_ids.values" cdkDrag [cdkDragData]="s">

                        <div class="border-bottom d-flex flex-row align-items-center py-2">
                            <i class="fa fa-bars fa-large mx-3"></i>



                            <a [routerLink]="['/leads', lead.id , 'sale' , s.id]" queryParamsHandling="preserve"
                                class="ps-3 text-nowrap">
                                <p class="mb-0 pb-0">{{s.name}}</p>
                            </a>

                            <input class="ms-3 flex-fill text-muted form-control bg-transparent border-0 px-1"
                                [ngModel]="s.ga_title" (ngModelChange)="renameSale(s, $event)"
                                [ngModelOptions]="{'updateOn':'blur'}">

                            <div class="ps-2 pe-3 ms-auto">
                                <div class="d-flex align-items-center justify-content-end text-nowrap"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Component: too big to do here -->
            <app-billing *ngIf="shouldShowSection('billing')" [lead]="lead" (loading)="loading = $event">
            </app-billing>

            <!-- Production Section -->
            <div class="card shadow-sm mb-4" *ngIf="shouldShowSection('production')">
                <div class="card-header px-3 d-flex align-items-center justify-content-between" style="height: 48px">
                    <h5 class="card-title mb-0">Ordini e lista materiali</h5>
                    <div class="dropdown ms-auto">
                        <button class="btn btn-link " type="button" data-bs-toggle="dropdown" aria-haspopup="true"
                            aria-expanded="false" title="Importa sotto-commesse da template">
                            <i class="fa fa-file-import fa-lg"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li *ngFor="let i of importableLeads">
                                <a class="dropdown-item" target="_blank" (click)="importLead(i)">{{i.name}}</a>
                            </li>
                        </ul>
                    </div>

                    <button class="btn btn-link " title="Espandi" (click)="expandAll()" [disabled]="parts.length==0">
                        <i class="fa-solid fa-arrows-up-down fa-lg"></i>
                    </button>

                    <!-- nuovo gruppo -->
                    <div class="dropdown">
                        <button class="btn btn-link text-white" type="button" (click)="attachNewPart()"
                            title="Aggiungi sotto-commessa">
                            <i class="fa fa-plus text-primary fa-lg"></i>
                        </button>
                    </div>

                </div>

                <div class="d-flex flex-column">

                    <div *ngFor="let p of parts | sortBy:'name'" class="border" cdkDropList id='part-{{p.id}}'
                        [cdkDropListConnectedTo]="partIds" [cdkDropListData]="p" (cdkDropListDropped)="drop($event)">
                        <div class="border-bottom bg-light  d-flex flex-row align-items-center py-2">
                            <!-- <i class="fa fa-bars fa-large ms-3" cdkDragHandle ></i> -->

                            <i *ngIf="!p.sale_order_ids.values" class="px-3 fa-solid fa-caret-right"
                                title="Espandi sotto-commessa" (click)="toggleResolvePart(p)"></i>
                            <i *ngIf="p.sale_order_ids.values" class="px-3 fa-solid fa-caret-down"
                                title="Comprimi sotto-commessa" (click)="toggleResolvePart(p)"></i>
                                <!-- input for sottocommessa name (red highlight if empty) -->

                            <input class="form-control border-0 " [ngModel]="p.name"
                                placeholder="*Inserisci nome sotto-commessa*" [ngModelOptions]="{'updateOn':'blur'}"
                                (ngModelChange)="updatePart(p, 'name', $event)"
                                [ngClass]="{
                                    'bg-danger bg-opacity-25 rounded': !p.name,
                                    'bg-transparent': p.name
                                  }">

                            <button class="btn btn-link text-white me-1" type="button" (click)="attachNewSale(p)"
                                title="Nuovo ordine">
                                <i class="fa fa-plus text-primary"></i>
                            </button>
                            <button class="btn btn-link text-white me-1" type="button" (click)="deletePart(p)"
                                title="Rimuovi sotto-commessa">
                                <i class="fa-solid fa-trash text-primary"></i>
                            </button>
                        </div>

                        <ng-container *ngIf="p.sale_order_ids.values">

                            <div *ngFor="let s of p.sale_order_ids.values  | sortBy:'ga_order'" cdkDrag
                                [cdkDragData]="s">

                                <div class="border-bottom d-flex flex-row align-items-center py-2 ">

                                    <i class="fa fa-bars fa-large mx-3"></i>

                                    <a [routerLink]="['/leads', lead.id , 'sale' , s.id]" queryParamsHandling="preserve"
                                        class=" text-nowrap pe-2" target="_blank">
                                        <p class="mb-0 pb-0">{{s.name}}</p>
                                    </a>

                                    <!-- function could be used to get the output string and colours insted of here                                          -->

                                    <span title="Confermato" class="badge bg-primary  "
                                        *ngIf="s._delivery_state =='Confermato'">{{s._delivery_state}}&nbsp;</span>
                                    <span title="Spedito interamente" class="badge  bg-success  "
                                        *ngIf="s._delivery_state =='Spedito interamente'">{{s._delivery_state}}&nbsp;</span>
                                    <span title="Confermato - acquisti arrivati" class="badge  bg-primary "
                                        *ngIf="s._delivery_state =='Confermato - acquisti arrivati'">{{s._delivery_state}}&nbsp;</span>
                                    <span title="Preparato - non spedito" class="badge  bg-warning bg-opacity-50 text-dark"
                                        *ngIf="s._delivery_state =='Preparato - non spedito'">{{s._delivery_state}}&nbsp;</span>
                                    <span title="Spedito parzialmente" class="badge  bg-warning bg-opacity-75  text-dark "
                                        *ngIf="s._delivery_state =='Spedito parzialmente'">{{s._delivery_state}}&nbsp;</span>
                                    <span title="Bozza" class="badge bg-dark "
                                        *ngIf="s._delivery_state =='Bozza'">{{s._delivery_state}}&nbsp;</span>
                                    <span title="Annullato" class="badge bg-light text-dark"
                                        *ngIf="s._delivery_state =='Annullato'">{{s._delivery_state}}&nbsp;</span>
                                    <span title="Da bollare" class="badge bg-warning  text-dark"
                                        *ngIf="s._delivery_state =='Da bollare'">{{s._delivery_state}}&nbsp;</span>
                                    



                                    <input class="ms-3 flex-fill text-muted form-control border-0 px-1"
                                        [ngModel]="s.ga_title" (ngModelChange)="renameSale(s, $event)"
                                        placeholder="*Inserisci descrizione ordine*"
                                        [ngModelOptions]="{'updateOn':'blur'}"
                                        [ngClass]="{
                                            'bg-danger bg-opacity-10 rounded': !s.ga_title,
                                            'bg-transparent': s.ga_title
                                          }">

                                    <div class="ps-2 pe-3 ms-auto">

                                        <!-- <span *ngIf="isFirstSale(s)" class="text-danger">PRIMANOTA</span><br> -->
                                        <div class="d-flex align-items-center justify-content-end text-nowrap">


                                            <app-activity-scheduler
                                                *ngIf="(s.state == 'sale' && s.delivery_status != 'full' && s.delivery_status)"
                                                class="" [sale]="s"
                                                (loading)="loading = $event"></app-activity-scheduler>

                                            <span *ngIf="(s.state != 'sale')" class="text-muted me-2">
                                                <button class="btn btn-sm bg-muted text-white" type="button"
                                                    [routerLink]="['/leads', lead.id , 'sale' , s.id]">
                                                    Conferma
                                                </button>
                                            </span>

                                            <!--delivery date button -->
                                            <div class="dropdown me-2">
                                                <button class="btn btn-sm dropdown-toggle"
                                                    [ngClass]="s.commitment_date ? 'btn-secondary text-white' : 'bg-muted text-white'"
                                                    data-bs-auto-close="outside" type="button" data-bs-toggle="dropdown"
                                                    [disabled]="s.delivery_status == 'full' || s.state == 'cancel'">
                                                    <i class="fa-solid fa-calendar-days"></i>
                                                    {{ s.commitment_date ? ('Consegna ' + (s.commitment_date |
                                                    date:'dd/MM/yy')) : 'Consegna' }}
                                                </button>

                                                <div class="dropdown-menu px-3 py-3">
                                                    <div class="mb-3">Consegna prevista</div>
                                                    <input class="form-control mb-3" type="date"
                                                        [ngModel]="s.commitment_date ? (s.commitment_date | date:'yyyy-MM-dd') : ''"
                                                        (change)="updateCommitmentDate(s, $event.target.value)">
                                                </div>
                                            </div>


                                            <app-connect-to class="d-block-inline" [connectable]="s" type="Produzione"
                                                [deal]="lead" [driveFolders]="filterProductionDriveFolders(s)"
                                                [driveSource]="cfg?.produzione_src" [driveTarget]="cfg?.produzione_dst"
                                                [driveLinkIn]="filterDriveFolders('Preventivo')[0]?.value"
                                                [driveLinkTitle]="cfg.produzione_link_name + ' > ' + s.name + ' ' + (s?.ga_title ? s.ga_title : '')"
                                                [driveAdditionalTitle]="' > ' + s.name + ' ' + (s?.ga_title ? s?.ga_title : '')"
                                                [trelloCards]="filterProductionTrelloFolders(s)"
                                                [trelloListId]="cfg?.produzione_trello"
                                                [trelloBoardId]="cfg?.produzione_trello_board"
                                                [trelloDateLabel]="'Spedizione'" (onConnect)="loadDeal()">
                                            </app-connect-to>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </div>
            </div>

            <app-hours-on-lead [lead]="lead" (loading)="loading = $event" *ngIf="shouldShowSection('hours')">
            </app-hours-on-lead> 


            <app-cost-check-embedded [lead]="lead" (loading)="loading = $event" *ngIf="shouldShowSection('costCheck')">
            </app-cost-check-embedded>


        </div>
    </div>
</div>

<app-reminder-popup 
  *ngIf="showReminderPopup" 
  [lead]="lead"
  (close)="handleReminderPopup($event)">
</app-reminder-popup>

<!-- Lost Reason Popup -->
<div *ngIf="showLostReason" class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" 
     style="background-color: rgba(0,0,0,0.5); z-index: 1050;">
  <div class="card shadow" style="width: 500px; max-width: 90%;">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0">Motivo della perdita</h5>
      <button type="button" class="btn-close" (click)="showLostReason = false"></button>
    </div>
    <div class="card-body">
      <div class="mb-3">
        <label for="lostReasonSelect" class="form-label">Motivo della perdita</label>
        <select class="form-select" id="lostReasonSelect" [(ngModel)]="selectedLostReasonId">
          <option [ngValue]="null" selected>Seleziona un motivo</option>
          <option *ngFor="let reason of lostReasons" [ngValue]="reason.id">{{reason.name}}</option>
        </select>
      </div>
      <div class="mb-3">
        <label for="lostFeedback" class="form-label">Cosa è successo? (facoltativo)</label>
        <textarea class="form-control" id="lostFeedback" rows="3" [(ngModel)]="lostFeedback"></textarea>
      </div>
    </div>
    <div class="card-footer d-flex justify-content-end">
      <button type="button" class="btn btn-secondary me-2" (click)="showLostReason = false">Annulla</button>
      <button type="button" class="btn btn-primary" (click)="submitLostReason()">Invia</button>
    </div>
  </div>
</div>