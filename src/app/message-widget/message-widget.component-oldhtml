<style>
  #mainParent {
    height: 90vh;
    padding: 5px;
  }

  @media (max-width: 768px) {
    #mainParent {
      width: 300px;
    }
  }

  .gray {
    color: #6c757d
  }

  .alignLeft {
    left: 10px !important;
    right: auto !important;
  }



  textarea.form-control {
    resize: none;
    transition: height 0.2s ease;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: inherit;
  }

  .message-text {
    font-family: inherit;
    /* Keep the same font as the rest of the app */
    white-space: pre-wrap;
    /* Preserve line breaks and wrap text */
    margin: 0;
    /* Remove default pre margins */
    background: none;
    /* Remove default pre background */
    border: none;
    /* Remove default pre border */
    padding: 0;
    /* Remove default pre padding */
    overflow-wrap: break-word;
    /* Break long words if needed */
  }

  .message-content {
    width: 100%;
  }

  .message-content {
    white-space: pre-wrap;
    word-wrap: break-word;
  }


  .bg-gray {
    background-color: #eee;
  }

  .bg-green {
    background-color: #d4edda;
  }

  .w-messages {
    display: block;
  }

  .w-messages>div {
    max-width: 70%;
    position: relative;
    margin-bottom: 20px;
  }

  .message-container {
    position: relative;
  }

  .message-container:hover .reaction-button {
    display: block !important;
  }

  .reaction-button {
    padding: 4px 8px;
    color: #6c757d;
  }

  .reaction-button:hover {
    color: #495057;
  }

  .message-bubble {
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    padding-bottom: 1rem;
    /* Space for reactions */
  }


  .reaction-emoji {
    font-size: 1.2rem;
    cursor: pointer;
    transform: translateY(50%);
  }

  .system-message {
    font-size: 0.9rem;
    color: #666;
  }


  .reactions .badge {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    background-color: rgba(0, 0, 0, 0.05);
    color: #666;
  }

  .reactions .badge:hover {
    background-color: rgba(0, 0, 0, 0.08);
  }

  .w-messages .smiles {
    position: relative;
    bottom: -20px;
  }

  .smiles2 {
    bottom: 55px;
    right: 20px;
  }

  .reaction-picker {
    margin-left: 40px;
    margin-right: 40px;
    z-index: 1000;
  }

  .reaction-pick-btn {
    font-size: 1.2rem;
    line-height: 1;
    text-decoration: none;
  }

  .reaction-pick-btn:hover {
    transform: scale(1.2);
    transition: transform 0.2s;
  }

  .hover-show {
    transition: opacity 0.2s ease;
  }

  .message-content {
    word-break: break-word;
  }

  .attachment-item:hover a {
    color: #0d6efd;
  }
</style>

<div id="mainParent" class="d-flex flex-column justify-content-between" (click)="$event.stopPropagation()">
  <!-- Input area -->
  <div class="message-input-area">
    <div class="d-flex mt-2 message-input-container">
      <!-- Notification toggle button -->
      <button class="btn px-1 " (click)="hideNotifications = !hideNotifications"
        [title]="hideNotifications ? 'Mostra anche dettagli' : 'Mostra solo commenti'"
        [class.text-primary]="hideNotifications" [class.text-muted]="!hideNotifications">
        <i class="fa-solid fa-comment"></i>
      </button>

      <!-- File upload button -->
      <button class="btn px-1" id="attachmentButton" (click)="$event.stopPropagation()">
        <label for="fileInput" role="button" class="mb-0 cursor-pointer">
          <i class="fa-solid fa-plus gray fa-lg"></i>
        </label>
        <input type="file" id="fileInput" class="d-none" (change)="uploadFile($event)" multiple accept="*/*" #fileInput>
      </button>

      <!-- Show upload progress if needed -->
      <div *ngIf="isUploading" class="upload-progress spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">Uploading...</span>

      </div>

      <!-- Show selected files -->
      <div *ngIf="selectedFiles.length > 0" class="mt-2 selected-files">
        <div class="list-group list-group-flush">
          <div *ngFor="let file of selectedFiles"
            class="list-group-item list-group-item-action d-flex align-items-center">
            <i class="fa fa-paperclip me-2"></i>
            <span>{{ file.name }}</span>
          </div>
        </div>
      </div>

      <div class="position-relative w-100 me-2">
        <!-- Textarea input -->
        <textarea #messageInput class="form-control rounded-3" [(ngModel)]="message" (keyup)="onKeyup($event)"
          (input)="autoResize($event.target)" placeholder="Scrivi un messaggio" aria-label="Message input" rows="1"
          style="min-height: 38px; max-height: 200px; overflow-y: auto;">
        </textarea>

        <!-- Suggestions Dropdown -->
        <div *ngIf="showSuggestions" class="position-absolute top-100 start-0 w-100 mt-1 shadow-sm overflow-hidden">
          <div class="list-group" style="max-height: 200px; overflow-y: auto; z-index:4000">
            <button *ngFor="let suggestion of suggestions; let i = index" type="button"
              class="list-group-item list-group-item-action border-0 py-2"
              [class.active]="i === selectedSuggestionIndex"
              (click)="$event.stopPropagation(); selectSuggestion(suggestion)"
              (mouseover)="selectedSuggestionIndex = i">
              {{ suggestion.name }}
            </button>
          </div>
        </div>


      </div>

      <button *ngIf="message && message.trim() !== ''" class="btn btn-link me-1"
        (click)="$event.stopPropagation(); confirmMessage()" aria-label="Send message">
        <i class="fad fa-2x fa-arrow-circle-right text-primary"></i>
      </button>
    </div>

    <!-- Scrollable Content for Messages -->
    <div #scrollableContent class="scrollable-dropdown-content mt-3">
      <ng-container *ngFor="let group of getFilteredGroups()">
        <!-- Date header -->
        <div class="d-flex justify-content-center mb-2">
          <div class="bg-gray text-dark p-2 rounded-5 px-3">
            {{ group.dateFromNow }}
          </div>
        </div>

        <!-- Messages -->
        <div *ngFor="let message of filterMessages(group.messages)" class="message-container mb-3">
          <div class="d-flex" [ngClass]="{
          'justify-content-end': message.author_id.id === userId, 
          'justify-content-start': message.author_id.id !== userId        
           }">


            <!-- Delete message button (for my messages) - Now on the left -->
            <button *ngIf="message.author_id.id === userId && message.message_type === 'comment'"
              class="btn btn-link icon-trash  reaction-button  d-none position-relative hover-show "
              (click)="deleteMessage(message)">
              <i class="fal fa-trash"></i>
            </button>

            <!-- Message Bubble -->
            <div class="message-bubble rounded-3 p-3 text-dark"
              [class.bg-opacity-10]="message.message_type === 'notification'"
              [class.bg-opacity-25]="message.message_type !== 'notification'" [ngClass]="{
                  'bg-primary text-end': message.author_id.id === userId,
                  'bg-secondary': message.author_id.id !== userId
                }" style="max-width: 80%;">

              <!-- Author and Time -->
              <div class="d-flex justify-content-between align-items-center mb-2">
                <div class="d-flex">
                  <i class="fa-solid mx-1 text-muted" [ngClass]="getMessageTypeIcon(message.message_type)"></i>
                  <span class="small text-muted mx-1">{{ message.author_id.name }}</span>
                </div>
                <span class="small text-muted mx-1">{{ message.dateHour }}</span>
              </div>
              <!-- Message Content -->
              <div class="message-content">
                <!-- Message Text GIULIO: QUI PRIMA ERA <pre [innerHTML]="getMessageBody(message)" class="message-text"></pre>
                 HO TOLTO PERCHE' CREAVA UNA SFILZA DI WARNING GIALLE E RALLENTAVA -->
                <div class="message-text"> {{getMessageBody(message) }}</div>

                <!-- Attachments -->
                <div *ngIf="message.attachment_ids?.values?.length > 0">
                  <div *ngFor="let attachment of message.attachment_ids.values">
                    <!-- Image Attachments -->
                    <div *ngIf="isImage(attachment)" class="d-inline-block">
                      <!-- GIULIO:      here i need to paste the url for the image. REMOVED cause it created a bunch of errors in the log -->
                      <img [src]="" [alt]="attachment.name"
                        class="rounded mw-100"
                        [style]="'max-height: ' + attachment.image_height + 'px; max-width: ' + attachment.image_width + 'px'">
                    </div>

                    <!-- Non-Image Attachments -->
                    <div *ngIf="!isImage(attachment)" class="d-flex align-items-center p-2 rounded-2 border">
                      <i class="fa-solid fa-paperclip me-2 text-muted"></i>
                      <a [href]="'/api/web/content/ir.attachment/' + attachment.id + '/datas?download=true'"
                        class="text-decoration-none" download>
                        {{ attachment.name }}
                      </a>
                    </div>
                  </div>
                </div>


                <!-- Reactions -->
                <div *ngIf="message.reaction_ids?.values?.length > 0"
                  class="reactions position-absolute bottom-0 end-0 translate-middle-y">
                  <div class="d-flex gap-1">
                    <span *ngFor="let reaction of message.reaction_ids.values" class="reaction-emoji"
                      [title]="reaction.partner_id.name">
                      {{ reaction.content }}
                    </span>
                  </div>
                </div>

                <!-- Reaction Button (only for other people's messages) -->
                <button *ngIf="message.author_id.id != userId"
                  class="btn btn-link reaction-button d-none position-absolute" (click)="toggleReactions(message.id)"
                  style="right: -30px; top: 0;">
                  <i class="fal fa-smile"></i>
                </button>
              </div>

              <!-- Reaction Picker -->
              <div *ngIf="currentReactionMessageId === message.id"
                class="reaction-picker position-absolute start-100 top-0 ms-2">
                <div class="bg-white shadow-sm rounded p-1 d-flex gap-1">
                  <button *ngFor="let emoji of ['😀', '😍', '👍', '⭐', '😲']" type="button"
                    class="btn btn-link p-1 reaction-pick-btn" (click)="setReaction($event, message, emoji)">
                    {{ emoji }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>


      </ng-container>
    </div>



    <!-- Selected files -->
    <div *ngIf="selectedFiles.length > 0" class="mt-2 selected-files-container">
      <ul class="list-group list-group-flush">
        <li *ngFor="let file of selectedFiles"
          class="list-group-item list-group-item-action list-group-item-success py-1">
          <small>{{ file.name }}</small>
        </li>
      </ul>
    </div>
  </div>