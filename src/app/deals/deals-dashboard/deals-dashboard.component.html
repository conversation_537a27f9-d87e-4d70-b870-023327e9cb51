<app-navbar [loading]="loading" backroute=".." class="w-100">
  <div class="d-flex justify-content-between align-items-center w-100">
    <a class="navbar-brand">
      <span>Commesse</span>&nbsp;
    </a>
    <div class="dropdown">
      <button class="btn btn-primary text-white dropdown-toggle" type="button" data-bs-toggle="dropdown"
        aria-expanded="false">
        Nuovo
      </button>
      <div class="dropdown-menu dropdown-menu-end p-3" style="min-width: 300px;">
        <form>
          <div class="mb-3">
            <label for="sectorSelect" class="form-label fw-bold">Settore</label>
            <select id="sectorSelect" name="area" class="form-select" [(ngModel)]="newDeal.area"
              (change)="filter.next(newDeal.area)">
              <option value="" disabled selected>Seleziona un settore</option>
              <option *ngFor="let a of areas" [ngValue]="a.name">
                {{ a.name }}
              </option>
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label fw-bold">Contatto</label>
            <app-contact-picker2 [mode]="'embedded'" class="embedded overflow-hidden border-0"
              (onSelect)="onContact($event)"></app-contact-picker2>
          </div>
          <div class="d-grid gap-2">
            <button [disabled]="!newDeal.partner_id.id" class="btn btn-primary" (click)="onCreate()">
              Crea nuova commessa
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</app-navbar>

<!-- Filters Section - With Toggle Button -->
<div class="bg-light px-3 py-3 w-100">
  <div class="container-fluid">
    <div class="row g-3">
      <!-- First Row: View and Action Tools - Always visible -->
      <div class="col-12 border-bottom pb-2">
        <div class="d-flex flex-wrap justify-content-end align-items-center gap-4">
          <!-- View Controls Group -->
          <div class="filter-group d-flex align-items-center" *ngIf="filtersVisible">
            <span class="text-muted me-2">Visualizzazione:</span>
            <div class="btn-group">
              <button class="btn btn-sm text-nowrap"
                [ngClass]="!isTableView ? 'btn-primary text-white' : 'btn-outline-muted'" (click)="toggleView()">
                <i class="fa-solid fa-grip-vertical px-1"></i> Liste
              </button>
              <button class="btn btn-sm text-nowrap"
                [ngClass]="isTableView ? 'btn-primary text-white' : 'btn-outline-muted'" (click)="toggleView()">
                <i class="fa-solid fa-table-list"></i> Tabella
              </button>
              <button class="btn btn-sm text-nowrap btn-outline-secondary" (click)="toggleOpen()">
                <i class="fa-solid fa-eye me-1"></i>Confermate
              </button>
            </div>
          </div>

          <!-- Sort Controls -->
          <div class="btn-group d-flex align-items-center " *ngIf="filtersVisible">
            <span class="text-muted me-2">Ordina:</span>
            <select class="form-select form-select-sm w-auto text-nowrap" [(ngModel)]="sortField" (change)="refresh()">
              <option value="tracking_code">Codice</option>
              <option value="partner_name">Cliente</option>
              <option value="expected_revenue">Importo</option>
              <option value="stage_id">Fase</option>
              <option value="write_date">Data modifica</option>
              <option value="ga_sent_date">Data invio</option>
              <option value="ga_approved_date">Data conferma</option>
              <option value="ga_last_activity_date">Ultima attività</option>
              <option value="activity_date_deadline">Prossima attività</option>
              <option value="date_deadline">Consegna prevista</option>
            </select>
            <button class="btn btn-outline-secondary btn-sm text-nowrap" (click)="toggleSortDirection()">
              <i class="fa-light" [ngClass]="sortAscending ? 'fa-arrow-up-a-z' : 'fa-arrow-down-z-a'"></i>
            </button>
          </div>

          <!-- Actions Group -->
          <div class="filter-group d-flex align-items-center">
            <span class="text-muted me-2">Azioni:</span>
            <div class="btn-group">
              <button class="btn btn-sm btn-outline-muted text-nowrap" *ngIf="filtersVisible" (click)="resetFilters()">
                <i class="fa-solid fa-refresh me-1"></i>Reset
              </button>
              <button class="btn btn-sm btn-outline-secondary text-nowrap" *ngIf="filtersVisible"  (click)="saveFilters()">
                <i class="fa-solid fa-bookmark me-1"></i>Salva
              </button>
              <button class="btn btn-sm btn-outline-secondary text-nowrap" *ngIf="filtersVisible"  (click)="printView()">
                <i class="fa-solid fa-file-export me-1"></i>Esporta
              </button>
              <button class="btn btn-sm btn-outline-secondary text-nowrap"  (click)="toggleFiltersVisible()">
                <i class="fa-solid" [ngClass]="filtersVisible ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                {{ filtersVisible ? 'Nascondi filtri' : 'Mostra filtri' }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- All Filters row - Only visible when NOT in table view AND filters are visible -->
      <div class="col-12" *ngIf="!isTableView && filtersVisible">
        <div class="row g-2">
          <!-- Sector Filter -->
          <div class="col-md-1">
            <label class="text-muted small mb-1">Settore</label>
            <div class="dropdown w-100">
              <button class="btn btn-sm dropdown-toggle w-100 text-start"
                [ngClass]="!selectedAreas || selectedAreas.length === 0 ? 'btn-outline-muted' : 'btn-primary text-white'"
                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                Settori ({{ selectedAreas?.length || 0 }})
              </button>
              <ul class="dropdown-menu">
                <li>
                  <div class="dropdown-item">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" id="area-all"
                        [checked]="!selectedAreas || selectedAreas.length === 0" (change)="toggleAllAreas()">
                      <label class="form-check-label" for="area-all">Tutti</label>
                    </div>
                  </div>
                </li>
                <li *ngFor="let area of areas">
                  <div class="dropdown-item">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" [id]="'area-' + area.name"
                        [checked]="isAreaSelected(area.name)" (change)="toggleAreaFilter(area.name)">
                      <label class="form-check-label" [for]="'area-' + area.name">
                        <span class="badge" [ngClass]="getAreaBadgeClass(area.name)">{{ area.name }}</span>
                      </label>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>

          <!-- Stage Filter -->
          <div class="col-md-1">
            <label class="text-muted small mb-1">Fase</label>
            <div class="dropdown" id="stageFilterDropdown" (hidden.bs.dropdown)="refresh()">
              <button class="btn btn-sm dropdown-toggle w-100 text-start"
                [ngClass]="selectedStages.size === stages.length ? 'btn-outline-muted' : 'btn-primary text-white'"
                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                ({{ selectedStages.size }})
              </button>
              <ul class="dropdown-menu p-2" style="min-width: 200px;">
                <!-- Select/Deselect All Button -->
                <li class="mb-2">
                  <button class="btn btn-sm w-100" 
                          [ngClass]="selectedStages.size === stages.length ? 'btn-outline-secondary' : 'btn-outline-primary'"
                          (click)="toggleAllStages(); $event.stopPropagation()">
                    {{ selectedStages.size === stages.length ? 'Deseleziona tutto' : 'Seleziona tutto' }}
                  </button>
                </li>
                
                <!-- Stage Buttons -->
                <li *ngFor="let stage of stages" class="mb-1">
                  <button class="btn btn-sm w-100" 
                          [ngClass]="isStageSelected(stage.id) ? 'btn-primary text-white' : 'btn-outline-muted'"
                          (click)="updateStageFilter(stage.id); $event.stopPropagation()">
                    {{ stage.name }}
                  </button>
                </li>
              </ul>
            </div>
          </div>

          <!-- Search -->
          <div class="col-md-2">
            <label class="text-muted small mb-1">Cerca</label>
            <div class="input-group input-group-sm">
              <span class="input-group-text"><i class="fa-solid fa-search"></i></span>
              <input type="text" class="form-control" placeholder="Cliente, commessa..."
                [ngModel]="searchDealsInput"  (ngModelChange)="onSearchChange($event)" #searchInput />
            </div>
          </div>

          <!-- Activity Date -->
          <div class="col-md-2">
            <label class="text-muted small mb-1">Ultima attività</label>
            <div class="input-group input-group-sm">
              <input type="date" class="form-control form-control-sm" [(ngModel)]="doneBefore" (change)="refresh()" />
              <button class="btn btn-sm" [ngClass]="withActivity ? 'btn-primary text-white' : 'btn-outline-secondary'"
                (click)="toggleActivity()">
                <i class="fa-solid fa-calendar-days me-1"></i>Con scadenza
              </button>
            </div>
          </div>

          <!-- Participant Filter -->
          <div class="col-md-3">
            <label class="text-muted small mb-1">Partecipanti</label>
            <div class="input-group input-group-sm">
              <select class="form-select form-select-sm" [(ngModel)]="selectedUser" (change)="refresh()">
                <option [ngValue]="null">Segue</option>
                <option *ngFor="let follower of followers" [ngValue]="follower">
                  {{ follower.name }}
                </option>
              </select>
              <button *ngIf="userId" class="btn btn-sm"
                [ngClass]="selectedUser?.id === userId ? 'btn-primary text-white' : 'btn-outline-secondary'"
                (click)="myDealsPart()" title="Commesse in cui partecipo">
                <i class="fa-solid fa-user-tag"></i>
              </button>
              <select class="form-select form-select-sm" [(ngModel)]="selectedResponsable" (change)="refresh()">
                <option [ngValue]="null">Responsabile</option>
                <option *ngFor="let follower of followers" [ngValue]="follower">
                  {{ follower.name }}
                </option>
              </select>
              <button *ngIf="userId" class="btn btn-sm"
                [ngClass]="selectedResponsable?.id === userId ? 'btn-primary text-white' : 'btn-outline-secondary'"
                (click)="myDealsResp()" title="Solo le mie commesse">
                <i class="fa-solid fa-user-check"></i>
              </button>
            </div>
          </div>

          <!-- Date Range Filter -->
          <div class="col-md-3">
            <label class="text-muted small mb-1">Periodo (data invio)</label>
            <div class="d-flex">
              <div class="input-group input-group-sm">
                <span class="input-group-text">Da</span>
                <input type="date" class="form-control form-control-sm" [(ngModel)]="startDate" (change)="refresh()" />
              </div>
              <div class="input-group input-group-sm">
                <span class="input-group-text">A</span>
                <input type="date" class="form-control form-control-sm" [(ngModel)]="endDate" (change)="refresh()" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Table View with Filters in Headers -->
<div *ngIf="isTableView" class="vh-100 d-flex flex-column pt-1 bg-light">
  <div class="table-responsive flex-grow-1">
    <div style="height: calc(100vh - 120px);" class="overflow-auto">
      <table class="table table-hover table-sm table-bordered align-middle">
        <thead class="bg-light position-sticky top-0" style="z-index: 4;">
          <!-- Header Row with Column Titles -->
          <tr>
            <!-- Sticky header columns -->
            <th style="left: 0; min-width: 120px;">
              <div class="d-flex flex-column">
                <div>Settore</div>
                <div class="dropdown mt-1">
                  <button class="btn btn-sm dropdown-toggle w-100 text-start"
                    [ngClass]="!selectedAreas || selectedAreas.length === 0 ? 'btn-outline-muted' : 'btn-primary text-white'"
                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    ({{ selectedAreas?.length || 0 }})
                  </button>
                  <ul class="dropdown-menu">
                    <li>
                      <div class="dropdown-item">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="area-all-table"
                            [checked]="!selectedAreas || selectedAreas.length === 0" (change)="toggleAllAreas()">
                          <label class="form-check-label" for="area-all-table">Tutti</label>
                        </div>
                      </div>
                    </li>
                    <li *ngFor="let area of areas">
                      <div class="dropdown-item">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" [id]="'area-table-' + area.name"
                            [checked]="isAreaSelected(area.name)" (change)="toggleAreaFilter(area.name)">
                          <label class="form-check-label" [for]="'area-table-' + area.name">
                            <span class="badge" [ngClass]="getAreaBadgeClass(area.name)">{{ area.name }}</span>
                          </label>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </th>
            <th colspan="3" class="text-center" style="min-width: 360px;">
              <div class="d-flex flex-column">
                <div>Commessa</div>
                <div class="input-group input-group-sm mt-1">
                  <span class="input-group-text"><i class="fa-solid fa-search"></i></span>
                  <input type="text" class="form-control" placeholder="Cliente, commessa..."
                    [(ngModel)]="searchDealsInput" 
                    
                    [ngModel]="searchDealsInput"  (ngModelChange)="onSearchChange($event)" />

                    
                </div>
              </div>
            </th>
            <th>
              <div>Città</div>
            </th>
            <th>
              <div class="d-flex flex-column">
                <div>Fase</div>
                <div class="dropdown" id="stageFilterDropdown" (hidden.bs.dropdown)="refresh()">
                  <button class="btn btn-sm dropdown-toggle w-100 text-start"
                    [ngClass]="selectedStages.size === stages.length ? 'btn-outline-muted' : 'btn-primary text-white'"
                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    ({{ selectedStages.size }})
                  </button>
                  <ul class="dropdown-menu p-2" style="min-width: 200px;">
                    <!-- Select/Deselect All Button -->
                    <li class="mb-2">
                      <button class="btn btn-sm w-100" 
                              [ngClass]="selectedStages.size === stages.length ? 'btn-outline-secondary' : 'btn-outline-primary'"
                              (click)="toggleAllStages(); $event.stopPropagation()">
                        {{ selectedStages.size === stages.length ? 'Deseleziona tutto' : 'Seleziona tutto' }}
                      </button>
                    </li>
                    
                    <!-- Stage Buttons -->
                    <li *ngFor="let stage of stages" class="mb-1">
                      <button class="btn btn-sm w-100" 
                              [ngClass]="isStageSelected(stage.id) ? 'btn-primary text-white' : 'btn-outline-muted'"
                              (click)="updateStageFilter(stage.id); $event.stopPropagation()">
                        {{ stage.name }}
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </th>
            <th>
              <div class="d-flex flex-column">
                <div>Partecipanti</div>
                <div class="input-group input-group-sm mt-1 flex-nowrap">
                  <select class="form-select form-select-sm" [(ngModel)]="selectedUser" (change)="refresh()">
                    <option [ngValue]="null">Tutti</option>
                    <option *ngFor="let follower of followers" [ngValue]="follower">
                      {{ follower.name }}
                    </option>
                  </select>
                  <button *ngIf="userId" class="btn btn-sm"
                [ngClass]="selectedUser?.id === userId ? 'btn-primary text-white' : 'btn-outline-secondary'"
                (click)="myDealsPart()" title="Solo le mie commesse">
                <i class="fa-solid fa-user-check"></i>
              </button>
                </div>
              </div>
            </th>
            <th>
              <div class="d-flex flex-column">
                <div>Responsabile</div>
                <div class="input-group input-group-sm mt-1 flex-nowrap">
                  <select class="form-select form-select-sm" [(ngModel)]="selectedResponsable" (change)="refresh()">
                    <option [ngValue]="null">Tutti</option>
                    <option *ngFor="let follower of followers" [ngValue]="follower">
                      {{ follower.name }}
                    </option>
                  </select>
                  <button *ngIf="userId" class="btn btn-sm"
                [ngClass]="selectedResponsable?.id === userId ? 'btn-primary text-white' : 'btn-outline-secondary'"
                (click)="myDealsResp()" title="Solo le mie commesse">
                <i class="fa-solid fa-user-check"></i>
              </button>
                </div>
              </div>
            </th>
            <th>
              <div class=" text-center d-flex flex-column">
                <div>Inviato il</div>
                <div class="input-group input-group-sm mt-1">
                  <input type="date" class="form-control form-control-sm" [(ngModel)]="startDate"
                    (change)="refresh()" />
                </div>
                <div class="input-group input-group-sm mt-1">
                  <input type="date" class="form-control form-control-sm" [(ngModel)]="endDate" (change)="refresh()" />
                </div>
              </div>
            </th>
            <th>
              <div class=" text-center d-flex flex-column">
                <div>Confermato il</div>
                <div class="input-group input-group-sm mt-1">
                  <input type="date" class="form-control form-control-sm" [(ngModel)]="approvedStartDate"
                    (change)="refresh()" />
                </div>
                <div class="input-group input-group-sm mt-1">
                  <input type="date" class="form-control form-control-sm" [(ngModel)]="approvedEndDate" 
                    (change)="refresh()" />
                </div>
              </div>
            </th>
            <th>
              <div  class=" text-center">Consegna</div>
            </th>
            <th>
              <div class=" text-center">Fatto il</div>
            </th>
            <th>
              <div class="d-flex text-center flex-column">
                <div>Da fare</div>
                <div class=" text-center mt-1">
                  <button class="btn btn-sm w-100"
                    [ngClass]="withActivity ? 'btn-primary text-white' : 'btn-outline-secondary'"
                    (click)="toggleActivity()">
                    <i class="fa-solid fa-calendar-days me-1"></i>Scadenze
                  </button>
                </div>
              </div>
            </th>
            <th class="text-center ">Val atteso</th>
            <th class="text-center " *ngIf="showInvoices">Contratti
            </th>
            <th class="text-center" class="text-center" *ngIf="showInvoices">Fatturato</th>
            <th class="text-center" *ngIf="showInvoices">Residuo</th>
          </tr>

          <!-- Grand total row -->
          <tr *ngIf="showInvoices">
            <th></th>
            <th colspan="3">{{megaBigCount}} commesse</th>
            <th colspan="9"></th>
            <th class="text-center">
              {{ totalExpected | currency:'EUR':'symbol':'1.0-0' }}
            </th>
            <th class="text-center">
              {{ totalContract | currency:'EUR':'symbol':'1.0-0' }}
            </th>
            <th class="text-center">
              {{ totalInvoiced | currency:'EUR':'symbol':'1.0-0' }}
              <div class="progress text-center" style="min-width: 60px;">
                <div class="progress-bar bg-success" role="progressbar"
                  [style.width.%]="(totalInvoiced/totalContract) * 100" aria-valuenow="25" aria-valuemin="0"
                  aria-valuemax="100">
                  {{ totalInvoiced/totalContract | percent:'1.0-0' }}
                </div>
              </div>
            </th>
            <th class="text-center">
              {{ totalLeftToInvoice | currency:'EUR':'symbol':'1.0-0' }}
            </th>
          </tr>
        </thead>

        <!-- Table body -->
        <tbody *ngIf="cardsLoaded" class="bg-white">
          <ng-container *ngFor="let card of cards">
            <tr *ngIf="isStageSelected(card.stage_id.id)" style="cursor: pointer;">
              <!-- Sticky body columns -->
              <td class="bg-white position-sticky" style="z-index: 1">
                <span class="badge" [ngClass]="getAreaBadgeClass(card.area)">
                  {{ card.area==="Facciate e Decking"? "Facciate" : card.area }}
                </span>
              </td>
              <td class="bg-white position-sticky" style="z-index: 1">
                <a [routerLink]="['/leads', card.id]" [queryParams]="queryParameters">
                  {{ card.tracking_code }}
                </a>
              </td>
              <td class="bg-white position-sticky text-wrap" style=" z-index: 1">
                <a [routerLink]="['/leads', card.id]" [queryParams]="queryParameters">
                  {{ card.partner_id.name | uppercase }}
                </a>
              </td>
              <td class="bg-white position-sticky text-wrap" style=" z-index: 1">
                <a [routerLink]="['/leads', card.id]" [queryParams]="queryParameters">
                  {{ card.name }}
                </a>
              </td>
              <td class="text-wrap">{{ card.city }}</td>
              <td>{{ getStageName(card) }}</td>
              <td>
                <div class="d-flex flex-wrap gap-1">
                  <span *ngFor="let follower of card.message_follower_ids?.values" 
                        class="badge d-flex align-items-center text-uppercase"
                        [ngClass]="follower?.name === card.user_id.name ? 'bg-primary text-white' : 'bg-light text-dark'"
                        [title]="follower?.name">
                    {{ getInitials(follower.name) }}
                  </span>
                </div>
              </td>
              <td>
                <span>
                  {{ card.user_id.name }}
                </span>
              </td>
              <td>{{ card.ga_sent_date ?  (card.ga_sent_date | date:'dd/MM/yy') : '' }}</td>
              <td>{{ card.ga_approved_date? (card.ga_approved_date | date:'dd/MM/yy') : '' }}</td>
              <td>{{ card.date_deadline? (card.date_deadline | date:'dd/MM/yy') : '' }}</td>
              <td>{{ card.ga_last_activity_date? (card.ga_last_activity_date | date:'dd/MM/yy') : '' }}</td>
              <td class="text-wrap">{{card.activity_type_id?.name? card.activity_type_id?.name : ''}}
                {{ card.activity_date_deadline? (card.activity_date_deadline | date:'dd/MM/yyyy') : '' }}</td>
              <td class="text-center">
                {{ card.expected_revenue | currency:'EUR':'symbol':'1.0-0' }}
              </td>
              <td class="text-center" *ngIf="showInvoices" [ngClass]="(!card._contractsExist && card._contractTotal>0) ? 'text-danger' : ''">
                <span>
                  {{ card._contractTotal>0 ?  (card._contractTotal | currency:'EUR':'symbol':'1.0-0') : '' }}
                </span>
              </td>
              <td class="text-center" *ngIf="showInvoices">
                <span>
                  {{ card._invoicedTotal>0 ?   (card._invoicedTotal | currency:'EUR':'symbol':'1.0-0') : '' }}
                </span>

                <div class="progress text-end" style="min-width: 60px;" *ngIf="card._invoicedTotal>0">
                  <div class="progress-bar bg-success" role="progressbar"
                    [style.width.%]="(card._invoicedTotal/card._contractTotal) * 100" aria-valuenow="25"
                    aria-valuemin="0" aria-valuemax="100">
                    {{ card._invoicedTotal/card._contractTotal | percent:'1.0-0' }}
                  </div>
                </div>
              </td>
              <td class="text-center">
                {{ card._leftToInvoice>0 ? (card._leftToInvoice | currency:'EUR':'symbol':'1.0-0') : '' }}
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Cards View - Original Implementation - Unchanged -->
<div class="w-100 trello-like" *ngIf="!isTableView">
  <ng-container *ngFor="let stage of stages">
    <ul *ngIf="isStageSelected(stage.id)">
      <!-- Fixed Header with stage name and badge for total-->
      <li>
        <div class="d-flex justify-content-between align-items-start">
          <h3 class="mb-0">{{ stage.name }}</h3>
          <div class="text-end flex-nowrap">
            <span class="badge bg-muted text-white me-1">{{ stage._number_of_leads }}</span>
            <span class="badge bg-light text-secondary">{{ stage._total_revenue |currency:'EUR':'symbol':'1.0-0'
              }}</span>
          </div>
        </div>
      </li>

      <!-- Scrollable Cards Container -->
      <div cdkDropList>
        <!-- droplist needed for styling -->
        <li *ngFor="let card of filterCards(stage.id)" cdkDrag [cdkDragDisabled]="true">
          <!-- Anchor around the card -->
          <a [routerLink]="['/leads', card.id]" [queryParams]="queryParameters" class="text-decoration-none">
            <!-- Card Content -->
            <div class="d-flex flex-column mx-1">
              <!-- First row -->
              <div class="d-flex justify-content-between align-items-center mb-1 mt-1">
                <h6 class="mb-0">{{ card.tracking_code }} - {{ card.partner_id.name | uppercase }}</h6>
                <!-- Bell icon with activities counter -->
                <div class="position-relative" *ngIf="card._activityCount > 0"
                  [ngClass]="card._isOverdue ? 'text-danger' : 'text-muted'">
                  <i class="fa-solid fa-lg fa-bell"></i>
                  <span class="badge text-dark position-absolute top-0 start-100 translate-middle">
                    {{ card._activityCount }}
                  </span>
                </div>
              </div>

              <!-- Second row -->
              <div class="d-flex justify-content-between align-items-center">
                <p class="mb-0 text-muted">{{ card.name }} - {{ card.city }}</p>
              </div>

              <!-- add a divider line -->
              <hr class="my-2">

              <!-- Third row -->
              <div class="d-flex justify-content-between align-items-center mb-3">
                <span class="badge" [ngClass]="getAreaBadgeClass(card.area)">
                  {{ card.area }}
                </span>
                <div class="badge bg-warning text-dark" *ngIf="card.expected_revenue">
                  {{ card.expected_revenue | currency:'EUR':'symbol':'1.0-0' }}
                </div>
              </div>

              <!-- Fourth row - Followers -->
              <div class="d-flex flex-wrap gap-1" *ngIf="card.message_follower_ids.ids.length>0">
                <span *ngFor="let follower of card.message_follower_ids?.values" class="badge d-flex align-items-center"
                  [ngClass]="follower?.name === card.user_id.name ? 'bg-primary text-white' : 'bg-light text-dark'">
                  {{ follower?.name }}
                </span>
              </div>
            </div>
          </a>
        </li>
      </div>
    </ul>
  </ng-container>
</div>

<router-outlet></router-outlet>