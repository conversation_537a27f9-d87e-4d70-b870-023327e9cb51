<!-- Link for existing task -->
<a *ngIf="existingTask"
   class="btn btn-sm btn-warning text-white me-2"
   [href]="'https://o3.galimberti.eu/web#id=' + existingTask.id + '&cids=1&menu_id=422&action=635&active_id=7&model=project.task&view_type=form'"
   target="_blank">
    <i class="fa fa-shopping-cart"></i>
    RDA {{existingTask.date_deadline | date:'dd/MM/yyyy'}}
</a>

<!-- Button for creating new task - Disabled if no commitment date -->
<button *ngIf="!existingTask" 
        class="btn btn-sm dropdown-toggle me-2" 
        [ngClass]="{'bg-muted text-white': !hasCommitmentDate(), 'bg-muted text-white': hasCommitmentDate()}"
        [disabled]="!hasCommitmentDate()"
        data-bs-toggle="dropdown"
        [title]="!hasCommitmentDate() ? 'Imposta prima una data di consegna' : 'Crea richiesta di acquisto'">
    <i class="fa fa-shopping-cart"></i>
    Crea RDA
</button>

<div *ngIf="!existingTask" class="dropdown-menu p-3" style="min-width: 300px;">
    <div class="mb-3">
        <label class="form-label">Data richiesta:</label>
        <div class="form-control-plaintext">{{saleOrder?.commitment_date | date:'dd/MM/yyyy'}}</div>
    </div>
    <div class="mb-3">
        <label class="form-label">Note aggiuntive</label>
        <textarea class="form-control" 
                  rows="3"
                  [(ngModel)]="description"></textarea>
    </div>
    <button class="btn btn-primary text-white w-100" 
            [disabled]="loading"
            (click)="createPurchaseTask()">
        <i class="fa fa-spinner fa-spin" *ngIf="loading"></i>
        {{loading ? 'Creazione...' : 'Conferma'}}
    </button>
</div>

<!-- Modal for task creation -->
<div class="modal fade" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Creazione RDA</h5>
                <button type="button" class="btn-close" (click)="closeModal()"></button>
            </div>
            <div class="modal-body">
                <p *ngIf="loading" class="text-center">
                    <i class="fa fa-spinner fa-spin"></i> Creazione richiesta in corso...
                </p>
            </div>
        </div>
    </div>
</div>