import { OdooSerializableInterface } from "../shared/interfaces/odoo-serializable-interface";
import { OdooModel } from "./odoo-model.model";
import { OdooMultiRelationship } from "./odoo-multi-relationship.model";
import { User } from "./user.model";

export class ResGroup extends OdooModel implements OdooSerializableInterface<ResGroup> {
    public readonly ODOO_MODEL = 'res.groups';

    id: number;
    name: string = "";
    users:OdooMultiRelationship<User> = new OdooMultiRelationship<User>(User)
    comment: string = "";

  
  
    constructor(id?: number,) {
      super(id);
      this.id = id;
    }
  
    create(): ResGroup {
      return new ResGroup();
    }
  }