.component-barcode-scanner{
    width:100%;
    height:100%;
    // min-width:640px;
    // min-height:480px;
    background:black;
    position:relative;
    resize:both;
}
.dbrScanner-bg-loading{animation:1s linear infinite dbrScanner-rotate;width:40%;height:40%;position:absolute;margin:auto;left:0;top:0;right:0;bottom:0;fill:#aaa;}
.dbrScanner-bg-camera{width:40%;height:40%;position:absolute;margin:auto;left:0;top:0;right:0;bottom:0;fill:#aaa;}
.dbrScanner-video{width:100%;height:100%;position:absolute;left:0;top:0;}
.dbrScanner-cvs-drawarea2{width:100%;height:100%;position:absolute;left:0;top:0;}

.dbrScanner-scanlight{width:100%;height:3%;position:absolute;animation:3s infinite dbrScanner-scanlight;border-radius:50%;box-shadow:0px 0px 2vw 1px #00e5ff;background:#fff;}
.dbrScanner-sel-camera{margin:0 auto;position:absolute;left:0;top:0;}
.dbrScanner-sel-resolution{position:absolute;left:0;top:20px;}
// @keyframes dbrScanner-rotate{from{transform:rotate(0turn);}to{transform:rotate(1turn);}}
// @keyframes dbrScanner-scanlight{from{top:0;}to{top:97%;}}

.dbrScanner-cvs-scanarea{
    width:100%;
    height:100%;
    position:absolute;
    left:0;
    top:0;
}