import { OdooModel } from './odoo-model.model';
import { OdooSerializableInterface } from '../shared/interfaces/odoo-serializable-interface';
import { OdooMultiRelationship } from './odoo-multi-relationship.model';
import { OdooRelationship } from './odoo-relationship.model';
import { Currency, MailMessage, IrAttachment } from './mail.message';
import { MrpProduction, MrpWorkcenter } from './mrp-production';
import { Partner } from './partner';
import { Project } from './project.model';
import { User } from './user.model';
import { SaleOrderLine } from './sale-order-line.model';
import { SaleOrder } from './sale-order.model';
import { HrEmployee } from './hr-employee.model';
import { Lead } from './crm.lead.model';

export class AnalyticAccount extends OdooModel implements OdooSerializableInterface<AnalyticAccount> {
  public readonly ODOO_MODEL = 'account.analytic.account';
  
  id: number = 0;
  __last_update: string = '';
  active: boolean = true;
  balance: number = 0;
  code: string = '';
  color: number = 0;
  create_date: string = '';
  create_uid: OdooRelationship<User> = new OdooRelationship<User>();
  credit: number = 0;
  currency_id: OdooRelationship<Currency> = new OdooRelationship<Currency>();
  debit: number = 0;
  display_name: string = '';
  has_message: boolean = false;
  invoice_count: number = 0;
  line_ids: OdooMultiRelationship<AnalyticAccountLine> = new OdooMultiRelationship<AnalyticAccountLine>(AnalyticAccountLine);
  name: string = '';
  partner_id: OdooRelationship<Partner> = new OdooRelationship<Partner>();
  plan_id: OdooRelationship<AnalyticPlan> = new OdooRelationship<AnalyticPlan>();
  production_count: number = 0;
  production_ids: OdooMultiRelationship<MrpProduction> = new OdooMultiRelationship<MrpProduction>(MrpProduction);
  project_count: number = 0;
  project_ids: OdooMultiRelationship<Project> = new OdooMultiRelationship<Project>(Project);
  purchase_order_count: number = 0;
  root_plan_id: OdooRelationship<AnalyticPlan> = new OdooRelationship<AnalyticPlan>();
  vendor_bill_count: number = 0;
  workcenter_ids: OdooMultiRelationship<MrpWorkcenter> = new OdooMultiRelationship<MrpWorkcenter>(MrpWorkcenter);
  workorder_count: number = 0;
  write_date: string = '';
  write_uid: OdooRelationship<User> = new OdooRelationship<User>();


  _forEdit: boolean = false;
  _is_confirmed: boolean = false;
  _connected_lead: Lead
  
  constructor(id?: number) {
    super(id);
  }

  create(): AnalyticAccount {
    return new AnalyticAccount();
  }
}

export class AnalyticPlan extends OdooModel implements OdooSerializableInterface<AnalyticPlan> {
    public readonly ODOO_MODEL = 'account.analytic.plan';
    
    id: number = 0;
    __last_update: string = '';
    
    // Counts and statistics
    account_count: number = 0;
    all_account_count: number = 0;
    children_count: number = 0;
    
    // Relations
    account_ids: OdooMultiRelationship<AnalyticAccount> = new OdooMultiRelationship<AnalyticAccount>(AnalyticAccount);
    children_ids: OdooMultiRelationship<AnalyticPlan> = new OdooMultiRelationship<AnalyticPlan>(AnalyticPlan);
    parent_id: OdooRelationship<AnalyticPlan> = new OdooRelationship<AnalyticPlan>();
    
    // Basic fields
    color: number = 0;
    complete_name: string = '';
    default_applicability: string = '';
    description: string = '';
    display_name: string = '';
    name: string = '';
    parent_path: string = '';
    
    // Metadata
    create_date: string = '';
    create_uid: OdooRelationship<User> = new OdooRelationship<User>();
    write_date: string = '';
    write_uid: OdooRelationship<User> = new OdooRelationship<User>();
    
    constructor(id?: number) {
      super(id);
    }
  
    create(): AnalyticPlan {
      return new AnalyticPlan();
    }
  }

  export class AnalyticAccountLine extends OdooModel implements OdooSerializableInterface<AnalyticAccountLine> {
    public readonly ODOO_MODEL = 'account.analytic.line';
    
    // Basic identification fields
    id: number = 0;
    __last_update: string = '';
    name: string = '';
    ref: string = '';
    code: string = '';
    display_name: string = '';
    
    // Main analytic fields
    account_id: OdooRelationship<AnalyticAccount> = new OdooRelationship<AnalyticAccount>();
    amount: number = 0;
    date: string = '';
    unit_amount: number = 0;
    plan_id: OdooRelationship<AnalyticPlan> = new OdooRelationship<AnalyticPlan>();
    category: string = '';
    
    // Partners and users
    partner_id: OdooRelationship<Partner> = new OdooRelationship<Partner>();
    user_id: OdooRelationship<User> = new OdooRelationship<User>();
    employee_id: OdooRelationship<HrEmployee> = new OdooRelationship<HrEmployee>();
    
    // Project related
    project_id: OdooRelationship<Project> = new OdooRelationship<Project>();
    task_id: OdooRelationship<Task> = new OdooRelationship<Task>();
    
    // Sales related
    order_id: OdooRelationship<SaleOrder> = new OdooRelationship<SaleOrder>();
    so_line: OdooRelationship<SaleOrderLine> = new OdooRelationship<SaleOrderLine>();
    is_so_line_edited: boolean = false;
    
    // Metadata fields
    create_date: string = '';
    create_uid: OdooRelationship<User> = new OdooRelationship<User>();
    write_date: string = '';
    write_uid: OdooRelationship<User> = new OdooRelationship<User>();
    
    constructor(id?: number) {
      super(id);
    }
  
    create(): AnalyticAccountLine {
      return new AnalyticAccountLine();
    }
  }
  


  
