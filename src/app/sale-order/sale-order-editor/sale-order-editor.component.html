<!-- Main navbar -->
<app-navbar [loading]="loading" backroute="../.." queryParamsHandling="preserve">
  <div class="d-flex align-items-center justify-content-between w-100">
    <!-- Left side - Brand and customer info -->
    <div class="d-flex align-items-center">
      <a class="navbar-brand text-nowrap mb-0">
        <span>
          {{ opportunity ? opportunity.tracking_code : relatedSales[0]?.partner_id.name }}
        </span>
        <span *ngIf="part">
          - {{ part?.name }}
        </span>
        <span *ngIf="opportunity && relatedSales.length > 0">
          - {{ relatedSales[0]?.partner_id.name }}
        </span>
      </a>
      <!-- Edit customer button -->
      <div class="dropdown ms-1" *ngIf="relatedSales.length > 0 && !loading && !opportunity">
        <button class="btn btn-sm btn-outline-primary py-0 px-1" type="button"
          title="Modifica cliente (solo ordini in bozza)" data-bs-toggle="dropdown" (click)="checkEditCustomer($event)"
          [ngClass]="{'disabled': relatedSales[0].state == 'sale' || relatedSales[0].state == 'done'}">
          <i class="fa-solid fa-user-pen"></i>
        </button>
        <div class="dropdown-menu dropdown-menu-start" style="min-width: 300px; z-index: 9999999;">
          <form class="px-3" style="min-width: 440px;">
            <app-contact *ngIf="openDropdown" [mode]="'embedded'" class="embedded"
              (onSelect)="updateCustomer(relatedSales[0], $event)">
            </app-contact>
          </form>
        </div>
      </div>
    </div>

    <!-- Right side - Actions and tools -->
    <div class="d-flex align-items-center gap-1">
      <!-- Add tools group -->
      <div class="d-flex align-items-center border-end pe-2">
        <span class="text-white small me-1">Aggiungi:</span>

        <div class="d-flex btn-group btn-group-sm gap-1" id="toolList" cdkDropList [cdkDropListConnectedTo]="saleIds">
          <button class="btn btn-light py-1" title="Mostra inventario" (click)="toggleInventory()">
            <i class="fa-regular  fa-layer-plus" [class.text-primary]="showInventory"></i>
            <span class="d-none d-md-inline ms-1">Prodotti</span>
          </button>
          <!-- <button class="btn btn-light py-1" title="Mostra inventario" (click)="togglePacks(); packagePanel.toggle($event)">
            <i class="fa-regular fa-cubes" [class.text-primary]="showPacks"></i>
            <span class="d-none d-md-inline ms-1">Pacchi</span>
          </button> -->

          <button class="btn btn-light py-1" title="Aggiungi nota descrittiva" cdkDrag [cdkDragData]="getNewNote()"
            (click)="onAddNotes(getNewNote())">
            <i class="fa-regular fa-file-pen"></i>
            <span class="d-none d-md-inline ms-1">Nota</span>
          </button>

          <button class="btn btn-light py-1" title="Aggiungi sezione" cdkDrag [cdkDragData]="getNewSection()"
            (click)="onAddNotes(getNewSection())">
            <i class=" fa-regular fa-file-dashed-line"></i>
            <span class="d-none d-md-inline ms-1">Sezione</span>
          </button>
        </div>


        <button class="btn btn-sm btn-primary ms-2" title="Crea nuovo ordine in questa sotto-commessa" type="button"
          (click)="createSaleFromRows()">
          <i class="fa fa-plus"></i>
          Ordine
        </button>
      </div>


      <!-- Display filters group -->
      <div class="d-flex align-items-center">
        <span class="text-white small me-1">Mostra:</span>
        <div class="btn-group btn-group-sm gap-1">
          <button class="btn py-1" [ngClass]="showDeliveries ? 'btn-primary' : 'btn-light'" (click)="toggleDeliveries()"
            title="Consegne">
            <i class="fa fa-regular fa-truck-arrow-right"></i>
            <span class="d-none d-md-inline ms-1">Consegne</span>
          </button>
          <button class="btn py-1" [ngClass]="showCosts ? 'btn-primary' : 'btn-light'" (click)="flagCosts()"
            title="Costi">
            <i class="fa-regular fa-circle-euro"></i>
            <span class="d-none d-md-inline ms-1">Costi</span>
          </button>
        </div>
      </div>
    </div>
  </div>


</app-navbar>

<!-- Inventory component (only shown when toggled) -->
<div *ngIf="loadedInventory && showInventory && relatedSales.length > 0" style="z-index: 100000">
  <app-order-inventory #orderInventory [sales]="relatedSales" [saleIds]="saleIds" [saleEditor]="thisIs" [from]="sale"
    (loading)="loading = $event" (toggleInventory)="showInventory = !showInventory" class="flex-grow-1">
  </app-order-inventory>
</div>
<!-- packlist component -->
<p-overlayPanel 
  #packagePanel 
  [showCloseIcon]="true" 
  [dismissable]="true" 
  [style]="{width: '70vw', maxWidth: '100vw', right: '20px', left: 'auto'}">
  <app-package-list 
    *ngIf="showPacks"
    [embedded]="true" 
    (emitQuants)="handleSelectedQuants($event)">
  </app-package-list>
</p-overlayPanel>

<!-- Main table container -->
<div class="container-fluid p-0 h-100 overflow-x-scroll overflow-y-scroll" *ngIf="relatedSales.length > 0">
  <!-- Loop through each sale order -->
  <ng-container *ngFor="let s of relatedSales; trackBy: identify; let i = index">

    <!-- SALE ORDER CARD -->
    <div class="card mb-0 mt-1" [ngClass]="{'border-primary': activeSale?.name == s.name}">
      <!-- Card header with main order info -->
      <div class="card-header py-1 px-2 border-0"
        [ngClass]="{'bg-primary bg-opacity-25': activeSale?.name == s.name, 'bg-info bg-opacity-50': activeSale?.name != s.name}">
        <div class="d-flex align-items-center justify-content-between w-100">
          <!-- Left side - Order name and ID -->
          <div class="d-flex align-items-center">
            <button class="btn btn-link text-decoration-none p-0 text-dark text-nowrap" (click)="toggleSale(s)">
              <i *ngIf="!s._open" class="fa-solid fa-caret-right"></i>
              <i *ngIf="s._open" class="fa-solid fa-caret-down"></i>

            </button>
            <span class="ms-3 fw-bold">{{s.name}}</span>

            <div *ngIf="s._delivery_state" class="mx-2">
              <span [title]="getDeliveryBadge(s._delivery_state).title" class="badge"
                [ngClass]="getDeliveryBadge(s._delivery_state).class">
                {{s._delivery_state}}
              </span>
            </div>
          </div>

          <div class="flex-grow-1 mx-2">
            <input [ngModel]="s.ga_title" (ngModelChange)="updateOrder(s,$event, 'ga_title')"
              [ngModelOptions]="{'updateOn':'blur'}" type="text" placeholder="*inserisci titolo ordine*"
              class="form-control-plaintext w-100">
          </div>

          <!-- Right side - Action buttons -->
          <div class="d-flex align-items-center gap-2">

            <!-- Purchase request button if needed -->
            <ng-container *ngIf="showTools">

              <app-purchase-request [saleOrder]="s" [lead]="opportunity" [part]="part">
              </app-purchase-request>

              <app-activity-scheduler *ngIf="(s.state == 'sale' && s.delivery_status != 'full')" [sale]="s"
                (loading)="loading = $event">
              </app-activity-scheduler>
            </ng-container>
            
            <!-- Commitment date -->
            <div class="dropdown">
              <button class="btn btn-sm" [ngClass]="s.commitment_date ? 'btn-info text-white' : 'btn-outline-secondary'"
                data-bs-auto-close="outside" type="button" data-bs-toggle="dropdown"
                [disabled]="s.delivery_status == 'full' || s.state == 'cancel'">
                <i class="fa-solid fa-calendar-days me-1"></i>
                {{ s.commitment_date ? (s.commitment_date | date:'dd/MM/yyyy') : 'Consegna' }}
              </button>

              <div class="dropdown-menu px-3 py-3">
                <div class="mb-3">Consegna prevista</div>
                <input class="form-control mb-3" type="date"
                  [ngModel]="s.commitment_date ? (s.commitment_date | date:'yyyy-MM-dd') : ''"
                  (change)="updateCommitmentDate(s, $event.target.value)">
              </div>
            </div>

            <!-- Message Button -->
            <!-- <button class="btn btn-sm" 
                   [ngClass]="(openMessage && openSaleId == s.id) ? 'btn-primary' : 'btn-outline-muted'"
                   (click)="toggleMessage(s)">
              <i class="fa-solid fa-comment me-1"></i>

            </button> -->

            <!-- Tools dropdown -->
            <div class="dropdown">
              <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                aria-expanded="false">
                <i class="fa fa-gear me-1"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-end">
                <!-- Order state actions con pulsanti centrati -->
                <li *ngIf="s.state == 'draft'" class="text-center px-2">
                  <button class="py-1 btn btn-small btn-outline-primary w-100" type="button" (click)="confirm(s)">
                    <i class="fa fa-check text-success me-2"></i> Conferma
                  </button>
                </li>

                <li *ngIf="s.state == 'sale'" class="text-center px-2">
                  <button class="py-1 btn btn-small btn-outline-primary w-100" type="button" (click)="cancel(s)">
                    <i class="fa fa-times text-danger me-2"></i> Annulla
                  </button>
                </li>

                <li *ngIf="s.state == 'cancel'" class="text-center px-2">
                  <button class="py-1 btn btn-small btn-outline-primary w-100" type="button" (click)="draft(s)">
                    <i class="fa fa-pencil text-primary me-2"></i> Imposta a bozza
                  </button>
                </li>

                <li>
                  <hr class="dropdown-divider my-1">
                </li>


                <!-- Document actions -->
                <li>
                  <a class="dropdown-item py-1" target="_blank" (click)="print(s)">
                    <i class="fa fa-print text-secondary me-2"></i> Stampa
                  </a>
                </li>

                <li>
                  <a class="dropdown-item py-1" target="_blank"
                    href="//o3.galimberti.eu/web#id={{s.id}}&cids=1&menu_id=178&action=296&model=sale.order&view_type=form">
                    <i class="fa fa-external-link text-secondary me-2"></i> Apri in Odoo
                  </a>
                </li>

                <li>
                  <a class="dropdown-item py-1" target="_blank" [routerLink]="['/pickings']"
                    [queryParams]="{search: s.name}" target="_blank">
                    <i class="fa fa-truck text-secondary me-2"></i> Vedi Trasferimenti
                  </a>
                </li>

                <li>
                  <a class="dropdown-item py-1" target="_blank"
                    (click)="copy('https://m3.galimberti.eu/leads/' + s.opportunity_id.id + '/sale/' + s.id)">
                    <i class="fa fa-link text-secondary me-2"></i> Copia link
                  </a>
                </li>

                <li>
                  <hr class="dropdown-divider my-1">
                </li>

                <!-- Advanced actions -->
                <li>
                  <a class="dropdown-item py-1 text-danger" target="_blank" (click)="delete(s)">
                    <i class="fa fa-trash me-2"></i> Elimina
                  </a>
                </li>

                <li>
                  <a class="dropdown-item py-1" (click)="copySale(s)">
                    <i class="fa fa-copy text-secondary me-2"></i> Crea una copia
                  </a>
                </li>

                <li>
                  <hr class="dropdown-divider my-1">
                </li>

                <!-- Order information -->
                <li class="dropdown-item py-1 disabled small">
                  <i class="fa fa-calendar text-muted me-2"></i> Creato il {{s.date_order | date}} <br>
                  <i class="fa fa-user text-muted me-2"></i> di {{s.user_id.name}}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Order settings row (only when open) -->
      <div *ngIf="s._open" class="card-body bg-white border-bottom border-2 p-2">
        <div class="d-flex flex-nowrap gap-1 overflow-auto">

          <!-- Pricelist selection -->
          <div class="input-group input-group-sm">
            <select class="form-select form-select-sm" *ngIf="s.pricelist_id.id" [(ngModel)]="s.pricelist_id.id"
              title="Prezzi non disponibili per il listino a corpo. Seleziona un listino per attivare i prezzi."
              (ngModelChange)="updateOrder(s, $event, 'pricelist_id', true)">
              <option *ngFor="let pl of pricelists" [ngValue]="pl.id">{{pl.name}}</option>
            </select>
            <span class="input-group-text py-0 px-1 bg-white">
              <i class="fa-solid fa-tags"></i>
            </span>
          </div>

          <!-- Payment terms -->
          <div class="input-group input-group-sm" *ngIf="hasPricelist(s)">
            <select class="form-select form-select-sm" [ngModel]="s.payment_term_id.id"
              (ngModelChange)="updateOrder(s, $event, 'payment_term_id', true)" title="Modalità di pagamento"
              placeholder="*Seleziona pagamento*">
              <option *ngFor="let pl of paymentTerms" [value]="pl.id">{{pl.name}}</option>
            </select>
            <span class="input-group-text py-0 px-1 bg-white">
              <i class="text-muted fa fa-credit-card"></i>
            </span>
          </div>

          <!-- Delivery terms -->
          <div class="input-group input-group-sm" *ngIf="hasPricelist(s)">
            <select class="form-select form-select-sm" [ngModel]="s.incoterm?.id"
              (ngModelChange)="updateOrder(s,$event, 'incoterm', true)" title="Condizioni di vendita"
              placeholde="*Seleziona condizioni consegna*">
              <option *ngFor="let pl of incoTerms" [value]="pl.id">{{pl.name}}</option>
            </select>
            <span class="input-group-text py-0 px-1 bg-white">
              <i class="fa fa-truck-fast text-muted"></i>
            </span>
          </div>

          <!-- Client reference -->
          <div class="input-group input-group-sm">
            <input type="text" class="form-control form-control-sm" placeholder="Rif. cliente"
              [ngModel]="s.client_order_ref ? s.client_order_ref : ''"
              (ngModelChange)="updateOrder(s, $event, 'client_order_ref')" [ngModelOptions]="{'updateOn':'blur'}"
              title="Riferimento cliente">
            <span class="input-group-text py-0 px-1 bg-white">
              <i class="fa fa-hashtag text-muted"></i>
            </span>
          </div>

          <!-- Delivery address -->
          <div class="input-group input-group-sm">
            <input type="text" class="form-control form-control-sm" placeholder="Indirizzo di consegna"
              [ngModel]="s.ga_address ? s.ga_address : ''" (ngModelChange)="updateOrder(s, $event, 'ga_address')"
              [ngModelOptions]="{'updateOn':'blur'}" title="Indirizzo di consegna">
            <span class="input-group-text py-0 px-1 bg-white">
              <i class="fa fa-location-pin text-muted"></i>
            </span>
          </div>

          <!-- Sales user -->
          <div class="input-group input-group-sm">
            <select class="form-select form-select-sm" [ngModel]="s.user_id.id"
              (ngModelChange)="updateOrder(s,$event, 'user_id', true)" title="Utente di vendita">
              <option *ngFor="let pl of users" [value]="pl.id">{{pl.name}}</option>
            </select>
            <span class="input-group-text py-0 px-1 bg-white">
              <i class="fa fa-user text-muted"></i>
            </span>
          </div>
        </div>
      </div>

      <!-- Order table (only when open) -->
      <div *ngIf="s._open" class="table-responsive">
        <table id="order-{{s.id}}" class="table table-bordered table-hover table-sm mb-0">
          <!-- Table header -->
          <thead class=" bg-light py-2 align-middle">

            <tr class="bg-light ">
              <th style="width: 30px;">
                <input *ngIf="s._open" type="checkbox" class="me-2 text-end form-check-input"
                  style="min-width: 16px; min-height: 16px;" (ngModelChange)="selectOrder($event, s)"
                  [(ngModel)]="s._checked" name="checksale">

              </th>
              <!-- <th style="width: 50px;" class="ps-2">ID</th> -->
              <th>
                <div class="d-flex align-items-center ">
                  <span class="me-3">Descrizione</span>
                  <button class="btn btn-sm btn-outline-secondary  py-0 px-1 " 
                          (click)="sortByName(s)" 
                          title="Ordina alfabeticamente A-Z"
                          [disabled]="loading">
                    <i class="fa-sharp fa-regular fa-arrow-down-a-z me-1"></i>
                    <span class="d-none d-lg-inline">Ordina</span>
                  </button>
                </div>
              </th>
              <th>Percorso</th>
              <th class="text-center">Qtà</th>
              <th colspan="2" class="text-center">Descr</th>
              <th class="text-center">
                Stato
                <span class="dropdown">
                  <button class="btn btn-sm btn-outline-secondary py-0 px-1" title="Legenda icone"
                    data-bs-toggle="dropdown">
                    <i class="fa-solid fa-circle-info"></i>
                  </button>
                  <div class="dropdown-menu dropdown-menu-end overflow-auto p-2"
                    style="min-width: 300px; max-height: 400px; z-index: 3000000">
                    <h6 class="dropdown-header py-1">Legenda</h6>

                    <!-- Purchases -->
                    <div class="mb-2">
                      <div class="text-muted mb-1 small">
                        <i class="fa fa-cart-circle-check me-1"></i>
                        La riga ha un acquisto collegato:
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-cart-circle-check text-success me-1"></i>
                        Acquisto arrivato
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-cart-circle-check text-warning me-1"></i>
                        Acquisto parzialmente arrivato
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-cart-circle-check text-danger me-1"></i>
                        Acquisto non arrivato
                      </div>
                    </div>

                    <div class="dropdown-divider my-1"></div>

                    <!-- Productions -->
                    <div class="mb-2">
                      <div class="text-muted mb-1 small">
                        <i class="fa fa-hammer me-1"></i>
                        La riga ha una produzione collegata:
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-hammer text-success me-1"></i>
                        Produzione completata
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-hammer text-warning me-1"></i>
                        Produzione in corso
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-hammer text-danger me-1"></i>
                        Produzione non iniziata
                      </div>
                    </div>

                    <div class="dropdown-divider my-1"></div>

                    <!-- Inventory -->
                    <div class="mb-2">
                      <div class="text-muted mb-1 small">
                        <i class="fa fa-warehouse me-1"></i>
                        <i class="fa fa-lock me-1"></i>
                        Disponibilità e prenotazioni
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-warehouse text-success me-1"></i>
                        Non riservato, disponibile
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-warehouse text-warning me-1"></i>
                        Non riservato, disponibile parzialmente
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-lock text-success me-1"></i>
                        Riservato completamente
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-lock text-warning me-1"></i>
                        Riservato parzialmente
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-warehouse text-danger me-1"></i>
                        Non disponibile
                      </div>
                    </div>

                    <div class="dropdown-divider my-1"></div>

                    <!-- Deliveries -->
                    <div class="mb-2">
                      <div class="text-muted mb-1 small">
                        <i class="fa fa-truck-fast me-1"></i>
                        Stato consegna
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-truck-fast text-success me-1"></i>
                        Consegnato completamente
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-truck-fast text-warning me-1"></i>
                        Parzialmente consegnato o restituito
                      </div>
                      <div class="ms-2 mb-1 small">
                        <i class="fa fa-truck-fast text-danger me-1"></i>
                        Non consegnato
                      </div>
                    </div>
                  </div>
                </span>
              </th>
              <th class="text-center" *ngIf="showDeliveries">
                Collo/i
                <button class="btn btn-sm btn-outline-secondary py-0 px-1" title="Copia dati pacco"
                  (click)="copyPackageData()">
                  <i class="fa-solid fa-copy"></i>
                </button>
              </th>
              <th class="text-center" >Consegnati</th>
              <th class="text-center">La</th>
              <th class="text-center">Alt</th>
              <th class="text-center">Lu</th>
              <th class="text-center">€/UdM</th>
              <th class="text-center">Sc %</th>
              <th class="text-center">Tot €</th>
              <th *ngIf="showCosts" class="text-center">Costo €</th>
            </tr>
          </thead>

          <!-- Loading indicator -->
          <tr *ngIf="s._open && !s.order_line.values">
            <td colspan="16">
              <div class="p-2 text-center">
                <i class="text-primary me-1 fa fa-spinner fa-spin"></i> Caricamento in corso...
              </div>
            </td>
          </tr>


          <!-- Order lines -->
          <tbody id='saleList-{{s.id}}' cdkDropList [cdkDropListData]="s" [cdkDropListConnectedTo]="saleIds"
            (cdkDropListDropped)="drop($event)">
            <tr *ngIf="certificationMessage && s._open" class="bg-danger bg-opacity-10 ">
              <td colspan="16" class="text-danger">
                <div [innerHTML]="certificationMessage"></div>
              </td>
            </tr>
            <tr *ngIf="!s.order_line || s.order_line.values?.length == 0" class="bg-light">
              <td colspan="16" class="text-muted text-center py-3">
                <i class="fa fa-info-circle me-2"></i>Nota vuota - trascina un prodotto per iniziare
              </td>
            </tr>
            <ng-container *ngFor="let line of s.order_line.values; trackBy: identify">
              <tr cdkDrag [cdkDragData]="line" class="mw-100" id="tr{{line.id}}">
                <td cdkDragHandle class="align-middle">
                  <div class="d-flex align-items-center">
                    <i class="fa fa-bars text-muted me-2"></i>
                    <div class="form-check mb-0">
                      <input type="checkbox" class="form-check-input" (mouseup)="selectLine($event, line, s)"
                        [(ngModel)]="line._checked" (ngModelChange)="updateOrderCheckbox(s)" name="x">
                    </div>
                    <button *ngIf="line._checked" class="btn btn-sm btn-link text-danger p-0 ms-1"
                      (click)="deleteLine(s)" title="Elimina">
                      <i class="fa fa-trash"></i>
                    </button>
                  </div>
                </td>

                <ng-container *ngIf="!line.display_type && s" [ngTemplateOutlet]="lineProductTpl"
                  [ngTemplateOutletContext]="{line: line, order: s}">
                </ng-container>

                <ng-container *ngIf="line.display_type == 'line_section'" [ngTemplateOutlet]="lineSectionTpl"
                  [ngTemplateOutletContext]="{line: line, order: s}">
                </ng-container>

                <ng-container *ngIf="line.display_type == 'line_note'" [ngTemplateOutlet]="lineNoteTpl"
                  [ngTemplateOutletContext]="{line: line, order: s}">
                </ng-container>
              </tr>
            </ng-container>
          </tbody>

          <!-- Notes section -->
          <tbody *ngIf="s._open && (!s.order_line || s.order_line.values)">
            <tr>
              <td colspan="16" class="p-0">
                <div class="row m-0 p-2 bg-light">
                  <!-- Left side - Package weight options -->
                  <div class="col-md-2 p-2">
                    <h6 class="fw-bold mb-2">Istruzioni di imballaggio</h6>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" [checked]="hasWeightLimit(s, 6)"
                        (change)="toggleWeightLimit(s, 6, $event)" id="weight-limit-6-{{s.id}}">
                      <label class="form-check-label" for="weight-limit-6-{{s.id}}">
                        Pacchi massimo 6 q.li
                      </label>
                    </div>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" [checked]="hasWeightLimit(s, 9)"
                        (change)="toggleWeightLimit(s, 9, $event)" id="weight-limit-9-{{s.id}}">
                      <label class="form-check-label" for="weight-limit-9-{{s.id}}">
                        Pacchi massimo 9 q.li
                      </label>
                    </div>
                  </div>
                  <!-- Right side - Editable notes textarea -->
                  <div class="col-md-10 p-2">
                    <h6 class="fw-bold mb-2">Altre informazioni</h6>
                    <div class="mb-1">
                      <textarea class="form-control bg-secondary bg-opacity-10 " [ngModel]="getUserVisibleNotes(s)"
                        (ngModelChange)="updateUserNotes(s, $event)" [ngModelOptions]="{ updateOn: 'blur'}"
                        placeholder="Aggiungi note per trasferimenti e produzione"></textarea>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>

          <!-- Footer with totals -->
          <tfoot *ngIf="s._open" class="bg-light">
            <tr>
              <td colspan="16" class="py-2">
                <div class="d-flex justify-content-between align-items-center px-3">
                  <div class="text-muted">
                    {{getInfoMultiple(s)}}
                  </div>
                  <div class="fw-bold" *ngIf="hasPricelist(s)">
                    Totale <span>{{s.amount_untaxed | number : '1.0-2':'it-IT' }}
                      €</span>
                  </div>
                  <div class="text-muted small fst-italic fw-bold" *ngIf="!hasPricelist(s)">
                    Seleziona un listino per visualizzare i prezzi
                  </div>
                </div>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  </ng-container>



  <!-- --------------end of tables. here templates and other elementss -->

  <!-- Off-canvas Message Panel TEMPORARELY DISABLED W EDO NOT USE IT-->
  <div class="offcanvas offcanvas-end" style="width: 550px;" tabindex="-1" id="messageOffcanvas"
    [ngClass]="{'show': openMessage}" [ngStyle]="{'visibility': openMessage ? 'visible' : 'hidden'}">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">Messaggi e allegati {{openSale?.name}}</h5>
      <button type="button" class="btn-close text-reset" (click)="toggleMessage(null)"></button>
    </div>
    <div class="offcanvas-body overflow-hidden">
      <app-message-widget *ngIf="openMessage" [id]="openSale.id" [action]="850"></app-message-widget>
    </div>
  </div>
</div>

<app-sale-print *ngIf="isPrinting" class="shadow" style="overflow: scroll;" (onDone)="isPrinting = false"
  [orderLines]="orderLinesToPrint" [order]="activeSale"></app-sale-print>

<ng-template #lineProductTpl let-line="line" let-order="order">
  <!-- Product Name (not editable) -->
  <td class="position-relative text-start align-middle p-2 "*ngIf="line.product_id"
    [ngClass]="{' text-info fst-italic': line.product_uom_qty == 0}">
    <a class="position-absolute top-0 end-0 mt-1 me-1 text-primary" title="apri in Odoo"
      href="https://o3.galimberti.eu/web?debug=1#id={{line.product_id.id}}&cids=1&menu_id=223&action=393&model=product.product&view_type=form"
      target="_blank" style="font-size: 0.68rem;">
      <i class="fa fa-magnifying-glass ms-1"></i>
    </a>
    <!-- Product display name (non-editable) -->
    <div class="mx-1  text-wrap ">
      {{line.product_id.value?.display_name?.replaceAll(', -','')}}
    </div>

    <!-- Editable custom description - only show if product has tag id 63 -->
    <div class="ms-1" *ngIf="line.product_id.value?.product_tag_ids?.ids?.includes(63)">
    <span class="gap-3 fw-italic d-flex align-items-center ">
      Descrizione aggiuntiva:
      <input type="text" class="form-control-plaintext border-0 bg-secondary bg-opacity-10 text-muted fw-italic p-1"
       [ngModel]="line.name"
        (ngModelChange)="updateLine(order, line, 'name', $event)" [ngModelOptions]="{'updateOn':'blur'}"
        [disabled]="loading" placeholder="+ aggiungi descrizione personalizzata..."
        title="Descrizione personalizzata per questa riga">
        </span>
    </div>
  </td>

  <!-- Route -->
  <td class="text-start align-middle py-1 px-2">
    <div class="dropdown position-static flex-grow-1">
      <button [disabled]="loading || (order.state === 'sale' && line.product_uom_qty !== 0)"
        class="btn w-100 p-0 btn-link dropdown-toggle small p-1" [ngClass]="{
                'bg-secondary bg-opacity-10 ': order.state === 'draft' || (order.state === 'sale' && line.product_uom_qty === 0)
              }" type="button" data-bs-toggle="dropdown" aria-expanded="false"
        (mousedown)="updateSelectableRoutes(order, line)">
        {{(line.route_id?.name?.split('-')[0]) || line.route_id?.name}}
      </button>
      <ul class="dropdown-menu  shadow" style="position: absolute; z-index: 1050;">
        <li *ngFor="let r of selectableRoutes">
          <button class="dropdown-item p-3 text-dark border-bottom" 
                  (click)="updateLineRoute(order, line, r)">
            {{r.name}}
          </button>
        </li>
      </ul>
    </div>
  </td>
  <!-- Quantity: editable only if sale in draft or if confirmed and route is set -->
  <td class="text-nowrap align-middle py-1 px-2 ">
    <div class="d-flex align-items-center justify-content-between ">
      <input-number [disabled]="loading || (order.state === 'sale' && !line.route_id.id && line.product_uom_qty === 0)"
        class="border-rounded text-end me-1 p-1" style="width: 80px;" [ngClass]="{
                      'bg-secondary bg-opacity-10  ': (order.state === 'draft' || (order.state === 'sale' && line.route_id.id))
                    }" [ngModel]="line.product_uom_qty | number : '1.0-5':'en-EN'"
        (ngModelChange)="updateLine(order, line, 'product_uom_qty', $event)">
      </input-number>
      <span class="small text-end"  >{{line.product_id.value?.uom_id?.name}}</span>
    </div>
  </td>

  <!-- Package Quantity -->
  <td class="align-middle py-1 px-2">
    <div class="d-flex align-items-center justify-content-end"
      *ngIf="line.product_id.value?.packaging_ids?.ids?.length">
      <input-number [disabled]="loading || (order.state === 'sale' && !line.route_id)" class="me-2 border-rounded p-1"
        [ngClass]="{
                      'bg-secondary bg-opacity-10 ': order.state === 'draft' || (order.state === 'sale' && line.route_id.id)
                    }" [hidden]="!line.product_id.value?.packaging_ids?.ids?.length"
        [ngModel]="line.product_packaging_qty | number : '1.0-5':'en-EN'"
        (ngModelChange)="updateLine(order, line, 'product_packaging_qty', $event)">
      </input-number>

      <!-- Package Selector (always editable) -->
      <div class="dropdown position-static">
        <button class="btn w-100 p-0 btn-link dropdown-toggle small bg-secondary  bg-opacity-10  p-1" type="button"
          *ngIf="line.product_id.value?.packaging_ids?.ids?.length" data-bs-toggle="dropdown" aria-expanded="false"
          (mousedown)="updateSelectablePackaging(line)">
          {{ line.product_packaging_id.name }}
        </button>
        <ul class="dropdown-menu" style="position: absolute; z-index: 1050;">
          <li *ngFor="let r of selectablePackagings">
            <button class="dropdown-item py-1" (click)="updateLinePackage(order, line, r)">
              {{r.name}}
              <span class="text-muted">({{r.qty}} {{line.product_id.value?.uom_id?.name}})</span>
            </button>
          </li>
        </ul>
      </div>
    </div>
  </td>

  <!-- Descriptive Quantity (not editable) -->
  <td class="align-middle text-nowrap border-start border-0 py-1 px-2">
    <div *ngIf="line.product_id.value?.packaging_ids?.values?.length > 1">
      {{getDescriptive(line)}}
    </div>
  </td>

  <!-- disponibilità - Dropdown migliorato -->
  <td class="align-middle text-center py-1 px-2">

    <!-- <p-button  type="button" (click)=" op.toggle($event) ;resolveLine(line)">
        <ng-container *ngFor="let i of getIconsForLine(line,order)">
          <i [ngClass]="i + ' me-1'"></i>
        </ng-container>
    </p-button>
    <p-overlayPanel #op [style]="{width: '400px'}">
      
    </p-overlayPanel> -->
    
    
    <div class="dropdown position-static text-center ms-1">
      <button class="btn btn-link p-0" type="button" data-bs-toggle="dropdown" aria-expanded="false"
        (click)="resolveLine(line)">
        <ng-container *ngFor="let i of getIconsForLine(line,order)">
          <i [ngClass]="i + ' me-1'"></i>
        </ng-container>
      </button>

      <!-- Dropdown menu con posizione migliorata -->
      <div class="dropdown-menu p-2" style="position: absolute; z-index: 1050; max-height: none; overflow: visible;">
        <div *ngIf="!line._resolved || !order._resolved" class="text-center p-1">
          <i class="fa fa-spinner fa-spin text-primary me-1"></i> Caricamento...
        </div>


        <!-- <div *ngIf="line._connected_productions?.length > 0" class="mt-2">

          <div class="card">
            <div class="card-header">
              <span class="card-title "> Produzione collegata</span>
            </div>
            <div class="card-body">
              <div *ngFor="let p of line._connected_productions" class="mb-1">
                <button class="btn btn-primary text-white w-100" (click)="onClickProduction(p)">
                  {{p.name}}
                  <i class="fa-solid fa-arrow-up-right-from-square ms-1"></i>
                </button>
              </div>
            </div>
          </div>
        </div> -->
        <!-- <div class="mb-1 small fw-bold">Produzione collegata</div>
          <div *ngFor="let p of line._connected_productions" class="mb-1">
            <button class="btn btn-outline-primary w-100 py-0" (click)="onClickProduction(p)">
              {{p.name}}
              <i class="fa-solid fa-arrow-up-right-from-square ms-1"></i>
            </button>
          </div>
        </div> -->


        <div class="mb-2 mt-1" *ngIf="line._resolved && order._resolved ">
                  <app-transfers-table 
                    [saleOrder]="order" 
                    [orderLine]="line" 
                    [productions]="line._connected_productions"
                    [purchases] = "line._purchase_line_values"
                    (loading)="loading = $event"
                  >
                  </app-transfers-table>
        </div>
        <!-- next section only if not service  -->
        <div class="table-responsive mt-2 border-top border-primary" *ngIf="line.product_id.value?.type != 'service'">
          <table class="table table-sm table-hover mb-0" >
  <!-- Intestazione: prima cella vuota, poi le UOM -->
  <thead class="sticky-top ">
    <tr>
      <th colspan="3">Disponibilità di {{line.product_id?.value?.display_name.replaceAll(", -","") }}</th>
    </tr>
    <tr>
      <th class="text-center bg-light"></th>
      <!-- Colonna per l’unità di misura base -->
      <th class="text-center bg-light">
        {{ line.product_id.value?.uom_id?.name }}
      </th>
      <!-- Colonna per l’imballo (se presente) -->
      <th class="text-center bg-light"  *ngIf="line.product_packaging_id " >
        {{ line.product_packaging_id.name }}
      </th>
    </tr>
  </thead>
  <tbody>
    <!-- Riga Stock -->
    <tr>
      <th class="text-end" >Stock</th>
      <td class="text-center ">
        {{ line.product_id.value?.qty_available || 0 | number:'1.0-2':'it-IT' }}
      </td>
      <td *ngIf="line.product_packaging_id" class="text-center text-muted">
        {{ getPackagingQuantity(line.product_id.value?.qty_available, line) }}
      </td>
    </tr>
    <!-- Riga Liberi (Free) -->
    <tr>
      <th   class="text-end"  >Liberi</th>
      <td class="text-center">
        {{ getFree(line, order) | number:'1.0-2':'it-IT' }}
      </td>
      <td *ngIf="line.product_packaging_id" class="text-center text-muted">
        {{ getPackagingQuantity(getFree(line, order), line) }}
      </td>
    </tr>
    <!-- Riga Arrivi (solo se ci sono linee di acquisto) -->
    <tr *ngIf="line._purchaselineids?.length > 0 && order.state=='sale'">
      <th  class="text-end"  >Arrivi</th>
      <td class="text-center">
        {{ line._quantity_arrived | number:'1.0-2':'it-IT' }}
      </td>
      <td *ngIf="line.product_packaging_id" class="text-center text-muted">
        {{ getPackagingQuantity(line._quantity_arrived, line) }}
      </td>
    </tr>
  </tbody>
</table>
          <!-- Costi più compatti -->
           <br>
          <div class="d-flex w-100 justify-content-between mb-1 "
            *ngIf="order.state == 'sale' || order.state == 'draft'">
            <span>Costo di acquisto</span>
            <span class="fw-bold text-end">{{ (line._line_cost_fetched_data?.cost | number : '1.0-2':'it-IT') + " €/" +
              (line.product_id.value?.uom_id?.name) }} 
({{line._line_cost_fetched_data?.origin }})</span>
          </div>

          <!-- Acquisti e produzioni più compatti -->
          <!-- <div *ngIf="line._purchase_line_values?.length > 0" class="mt-2">
            <div class="mb-1 small fw-bold">Acquisti collegati:</div>
            <div *ngFor="let p of line._purchase_line_values" class="mb-1">
              <button class="btn btn-sm btn-outline-primary w-100 py-0" (click)="onClickPurchase(p)">
                {{p?.order_id.name}}
                <br>
                {{p?.partner_id.name}}
                <i class="fa-solid fa-arrow-up-right-from-square ms-1"></i>
              </button>
      
                <div class="d-flex w-100 justify-content-between mb-1 small mt-1"
                *ngIf="(order.state == 'sale' || order.state == 'draft') && order._resolvedProcurement && line._resolved  ">
                  <span>                    Consegna:                  </span>  
                  <span class="fw-bold"> 
                  {{getArrivedDate (line, order) | date:'dd/MM/yyyy'}}      
                  </span>      
                </div>       
            </div>           
          </div> -->
        </div>
      </div>
    </div>
  </td>

  <!-- Package column più compatto -->
  <td class="text-center align-middle  py-1 px-2" *ngIf="showDeliveries">
    <ng-container *ngIf="line._package_info && line._package_info.total_packages > 0">
      <!-- Show directly if 3 or fewer packages -->
      <ng-container *ngIf="line._package_info.total_packages <= 3">
        <div *ngFor="let pkg of line._package_info.package_details" class="mb-0 small">
          <span class="">{{ pkg.name ? pkg.name : 'Senza collo' }} </span>

          <span *ngIf="getDescriptiveEm(line.product_id.value, pkg.quantity)" class="ms-1  small">
            {{ getDescriptiveEm(line.product_id.value, pkg.quantity) }}
          </span>
        </div>
      </ng-container>

      <!-- Dropdown più compatto per più pacchi -->
      <ng-container *ngIf="line._package_info.total_packages > 3">
        <div class="dropdown position-static">
          <button class="btn btn-sm btn-outline-primary py-0 dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <span>{{ line._package_info.total_packages }}</span>
          </button>
          <ul class="dropdown-menu p-0" style="position: absolute; z-index: 1050;">
            <li *ngFor="let pkg of line._package_info.package_details" class="p-1 border-bottom small">
              <span class="fw-bold">{{ pkg.name ? pkg.name : 'Senza collo' }} </span>
              <span *ngIf="getDescriptiveEm(line.product_id.value, pkg.quantity)" class="ms-1 text-primary small">
                {{ getDescriptiveEm(line.product_id.value, pkg.quantity) }}
              </span>
            </li>
          </ul>
        </div>
      </ng-container>
    </ng-container>
  </td>

  <!-- consegnati -->
  <td class="align-middle text-center small py-1 px-2">
    <div *ngIf="order.state == 'sale' && line.product_uom_qty > 0 && line.product_id.value?.detailed_type !== 'service'"
      [ngClass]="getDeliveryClass(line)">
      {{line.qty_delivered | number : '1.0-2':'it-IT'}} / {{line.product_uom_qty | number : '1.0-2':'it-IT'}}
      {{line.product_id.value?.uom_id?.name}}
    </div>
  </td>

<!-- Measures -->
<td class="text-end align-middle border-start-2 py-1 px-2">
  <input-number data-print-col class="p-1" [ngClass]="{
'bg-secondary bg-opacity-10 ': isWidthEditable(line) && order.state === 'draft'
}" [disabled]="!isWidthEditable(line) || order.state === 'sale' || loading"
    (ngModelChange)="updateVariant3(order, line, $event, 'Larghezza')"
    [ngModel]="getVariantAttribute(line, 'Larghezza')?.name">
  </input-number>
</td>

<td class="text-end align-middle py-1 px-2 ">
  <input-number data-print-col class="p-1" [ngClass]="{
'bg-secondary bg-opacity-10 ': isHeightEditable(line) && order.state === 'draft'
}" [disabled]="!isHeightEditable(line) || order.state === 'sale' || loading"
    (ngModelChange)="updateVariant3(order, line, $event, 'Altezza')"
    [ngModel]="getVariantAttribute(line, 'Altezza')?.name">
  </input-number>
</td>

<td class="text-end align-middle  border-end-2 py-1 px-2">
  <input-number data-print-col class="p-1" [disabled]="!isLengthEditable(line) || loading || order.state === 'sale'" [ngClass]="{
              'bg-secondary  bg-opacity-10 ': isLengthEditable(line) && order.state === 'draft'
            }" (ngModelChange)="updateVariant3(order, line, $event, 'Lunghezza')"
    [ngModel]="getVariantAttribute(line, 'Lunghezza')?.name">
  </input-number>
</td>

  <!-- Price -->
  <td class="text-end align-middle  py-1 px-2">
    <input-number [disabled]="loading" *ngIf="hasPricelist(order)" class="p-1" [ngClass]="{
                  'bg-secondary  bg-opacity-10 ': hasPricelist(order)
                }" [ngModel]="line.price_unit" (ngModelChange)="updateLine(order, line, 'price_unit', $event)">
    </input-number>
  </td>

  <!-- Discount -->
  <td class="text-end align-middle  py-1 px-2">
    <input-number [disabled]="loading" *ngIf="hasPricelist(order)" class="p-1" [ngClass]="{
                  'bg-secondary bg-opacity-10 p-1' : hasPricelist(order)
                }" [ngModel]="line.discount" (ngModelChange)="updateLine(order, line, 'discount', $event)">
    </input-number>
  </td>

  <!-- Subtotal -->
  <td class="text-end align-middle py-1 px-2">
    <ng-container *ngIf="hasPricelist(order)">
      {{ line.price_subtotal | number : '1.0-2':'it-IT' }}
    </ng-container>
  </td>

  <!-- Costs (not editable) -->
  <td class="text-end align-middle text-muted fst-italic  py-1 px-2" *ngIf="showCosts">
    <span *ngIf="!costsLoaded">
      <i class="fa fa-spinner fa-spin"></i>
    </span>
    <span *ngIf="costsLoaded" class="small">
      {{ (line._line_cost_fetched_data?.cost * line.product_uom_qty | number : '1.0-2':'it-IT')}}
    </span>
  </td>
</ng-template>

<!-- Template per le sezioni - sfondo primary e grassetto -->
<ng-template #lineSectionTpl let-line="line" let-order="order">
  <td colspan="16" class="bg-primary bg-opacity-10  p-1">
    <input class="form-control-plaintext fw-bold border-0 ps-2 py-1" [ngModelOptions]="{'updateOn':'blur'}"
      [ngModel]="line.name" (ngModelChange)="updateLine(order, line, 'name',$event)">
  </td>
</ng-template>

<!-- Template per le note - rientrate, in italico e colore più chiaro -->
<ng-template #lineNoteTpl let-line="line" let-order="order">
  <td colspan="16">
    <input class="form-control-plaintext text-muted fst-italic w-100 border-0 py-1" style="padding-left: 4rem;"
      [ngModel]="line.name" [ngModelOptions]="{'updateOn':'blur'}"
      (ngModelChange)="updateLine(order, line, 'name',$event)">
  </td>
</ng-template>
<router-outlet></router-outlet>