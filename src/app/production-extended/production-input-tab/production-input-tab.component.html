<div class="d-flex align-items-center mb-3 p-3">

  <!-- Remove the loading spinner -->
  <!-- <div *ngIf="loading" class="position-relative d-flex justify-content-center">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div> -->

  <!-- <button class="btn btn-sm btn-light ms-auto" title="Aggiorna" (click)="loadMoveLines()">
      <i class="fa fa-sync-alt"></i>
  </button> -->


  <!-- <button class="btn btn-dark btn-sm ms-2" (click)="onTogglePacklist()" title="Aggiungi nuovo pacco di input">
    <i class="fa fa-plus me-2"></i>Aggiungi Componenti
  </button> -->
  
  <p-button class="ms-auto" (onClick)="onTogglePacklist()" icon="fa fa-plus" label="Aggiungi componenti" />
</div>


<!-- Grouped components cards -->
<ng-container *ngFor="let group of groupedByTemplateInput">
  <div class="card mb-3 mx-3" *ngIf="group.moveLines.length > 0">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="me-auto mb-0">{{group.templateName}}</h5>
       <input style="width: 30em;"  pInputText class="ms-auto form-control form-control-sm" 
          (input)="dt.filterGlobal($event.target.value, 'contains')"
          [ngModel]="dt.filters['global']? dt.filters['global'].value : ''"
          placeholder="Cerca per pacco o quantità" />
    </div>
    <div class="card-body p-0">
      <p-table #dt [value]="group.moveLines" [scrollable]="true" [resizableColumns]="true" [reorderableColumns]="true"
        [columns]="selectedColumns" 
        [columnResizeMode]="'expand'" 
        [showGridlines]="true"
        [tableStyle]="{'min-width':'50rem'}" 
        [globalFilterFields]="['package_id.name', '_qty_done']"
        (onEditComplete)="onEditComplete($event, line)" 
        [sortMode]="'multiple'"
        [metaKeySelection]="metaKey"

      >
     
        <!-- styleClass="p-datatable-sm" -->

        <ng-template pTemplate="header">
          <tr>
            <!-- <th>Prodotto</th> -->
            <th class="bg-light" 
              pReorderableColumn 
              pResizableColumn 
              pSortableColumn="package_id.name" 
              pFrozenColumn>Pacco<p-sortIcon field="package_id.name" /></th>
            <th class="bg-light" 
            pReorderableColumn
            pSortableColumn="_qty_done" 
            pFrozenColumn>
              Prelevata
              <p-sortIcon field="_qty_done" />
          </th>
            <th 
              *ngIf="production?.state != 'done'" pReorderableColumn pResizableColumn pSortableColumn>
              Disponibile
            </th>
            <!-- <th>Ubicazione</th> -->
            <th pReorderableColumn pResizableColumn pSortableColumn>€/um</th>
            <th pReorderableColumn pResizableColumn pSortableColumn>€ sel</th>
            <!-- Dynamic attribute columns -->
            <ng-container *ngIf="group.template && group.template.attribute_line_ids?.values">
              <th pReorderableColumn pResizableColumn pSortableColumn class="text-no-wrap"
                *ngFor="let attr of getAttributeColumns(group.template)">
                {{attr.header}}
              </th>
            </ng-container>
            <th>Azioni</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-line let-rowIndex="rowIndex">

          <tr>
            <!-- <td style="min-width: 200px;">
              <div class="d-flex flex-column">
                <div class="d-flex align-items-center">
                  <span class="text-truncate">{{formatProductName(line.product_id?.value?.display_name || line.product_id?.name)}}</span>
                  <a class="ms-2 text-primary" 
                    href="https://o3.galimberti.eu/web?debug=1#id={{line.product_id?.id}}&model=product.product&view_type=form" 
                    target="_blank"
                    title="Visualizza prodotto">
                    <i class="fa fa-external-link-alt"></i>
                  </a>
                </div>
              </div>
            </td> -->
            <td class="bg-light" pFrozenColumn>
              <span *ngIf="line.package_id?.name" class="badge bg-dark fs-6">{{line.package_id?.name}}</span>
              <span *ngIf="!line.package_id?.name" class="text-muted">-</span>
            </td>
            <td class="bg-light" pFrozenColumn [pEditableColumn]="line" pEditableColumnField="_qty_done">
              <div class="d-flex align-items-center justify-content-end"
              [ngClass] = "{'text-primary': line._qty_done }">
                <p-cellEditor>
                  <ng-template pTemplate="input" #input>
                    <input pInputText type="number" class="form-control form-control-sm" style="width: 80px;"
                      [(ngModel)]="line._qty_done">
                  </ng-template>
                  <ng-template pTemplate="output" #output>
                    {{ line['_qty_done'] | number:'1.0-2'}}
                  </ng-template>
                </p-cellEditor>
                <span class="ms-1">
                  Pz
                  <!-- {{line.product_id?.value?.uom_id?.name}} -->
                </span>
                <span class="ms-2 text-muted">
                  ({{line.qty_done | number:'1.0-2'}} {{line.product_id?.value?.uom_id?.name}})
                </span>
              </div>
            </td>

            <!-- <td>{{line.location_id?.name || '-'}}</td> -->
            <!-- qty available in quant -->
            <td *ngIf="production?.state != 'done'">
              <button *ngIf="line._available_qty <= 0" class="btn btn-sm btn-light text-dark"
                (click)="getAllDoneQuantities(line)">
                <i class="fa-regular fa-arrow-right "></i>
              </button>
              <button *ngIf="line._available_qty > 0" class="btn btn-sm btn-primary text-white"
                (click)="getAllAvailableQuantities(line)">
                <i class="fa-regular fa-arrow-left "></i>
              </button>
              {{line._available_qty | number:'1.0-0'}}
              <span class="ms-1">
                Pz
                <!-- {{line.product_id?.value?.uom_id?.name}} -->
              </span>

              <span class="ms-2 text-muted">

                ({{ getAvailableQty (line) - line.qty_done | number:'1.0-2'}} {{line.product_id?.value?.uom_id?.name}})
              </span>
            </td>
            <td>
              {{line.product_id?.value?.standard_price | currency:'EUR':'symbol':'1.2-2'}}</td>
            <td>{{ line.product_id?.value?.standard_price * line.qty_done | currency:'EUR':'symbol':'1.2-2'}}</td>

            <!-- Dynamic attribute values -->
            <ng-container *ngIf="group.template && group.template.attribute_line_ids?.values">
              <td *ngFor="let attr of getAttributeColumns(group.template)">
                {{getAttributeValue(line, attr.header)}}
              </td>
            </ng-container>

            <td>
              <button class="btn btn-sm btn-danger" (click)="deleteMoveLine(line)" [disabled]="loading"
              [disabled] = "production?.state == 'done'">
                <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
        </ng-template>
        <!-- footer with totals for each group -->
        <ng-template pTemplate="footer">
          <tr>
            <td class="fw-bold bg-light">Totale</td>
            <td class="fw-bold text-end bg-light"> {{group.totalQty | number:'1.0-2'}} {{group.uom}}</td>
            <td class="fw-bold text-end bg-light" *ngIf="production?.state != 'done'  "></td>
            <td class="fw-bold text-end bg-light"> {{group.averageCost | currency:'EUR':'symbol':'1.2-2'}} </td>
            <td class="fw-bold text-end bg-light"> {{group.totalValue | currency:'EUR':'symbol':'1.2-2'}} </td>
            <td class="fw-bold text-end bg-light"   *ngFor="let attr of getAttributeColumns(group.template)"></td>
            <td class="fw-bold text-end bg-light"></td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>

</ng-container>
<!-- Statistiche dei componenti -->
<div class="d-flex justify-content-between align-items-center p-3 bg-light border-top">
  <div class="text-muted small">
    <span *ngIf="getMoveLines().length > 0">
      Totale {{getMoveLines().length}} componenti
    </span>
  </div>
  <div class="text-muted small">
    <span *ngIf="getMoveLines().length > 0">
      Costo totale {{getTotalCost() | currency:'EUR':'symbol':'1.2-2'}}
    </span>
  </div>

  <div class="d-flex align-items-center gap-2">

  </div>
</div>

<!-- Modal for Input Pack List -->
<p-dialog #inputdialog appendTo="body" (onShow)="inputdialog.maximize()" header="Selezione Componenti" [modal]="true"
  [(visible)]="openPacklist" [style]="{ width: '70vw' }" [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
  [maximizable]="true">
  <app-package-list *ngIf="openPacklist" [embedded]="true" (emitQuants)="handleSelectedQuants($event)">
  </app-package-list>
</p-dialog>