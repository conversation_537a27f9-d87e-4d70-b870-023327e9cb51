#!/bin/bash

# =========================================================
# Odoo PostgreSQL Database Backup Script
# =========================================================
# Configuration Parameters - Modify these as needed
# =========================================================

# PostgreSQL connection details
PG_USER="odoo"                   # PostgreSQL username
PG_PASSWORD="Evonet2023"      # PostgreSQL password
PG_DATABASE="o16"               # PostgreSQL database name
PG_HOST="localhost"              # PostgreSQL host 
PG_PORT="5432"                   # PostgreSQL port

# Backup settings
BACKUP_DIR="/var/backups/odoo"   # Local backup directory
BACKUP_DAYS=7                    # Number of days to keep backups

# SMB share details
SMB_SHARE="//*************/robot/"      # SMB share address
SMB_MOUNT="/mnt/backup"          # Local mount point for SMB
SMB_USER="robot"           # SMB username
SMB_PASSWORD="Robot2024"   # SMB password
SMB_DOMAIN="WORKGROUP"           # SMB domain/workgroup
SMB_BACKUP_DIR="odoo"    # Directory on SMB share

# =========================================================
# Script logic - No need to modify below this line
# =========================================================

# Create timestamp for the backup file
TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")
BACKUP_FILENAME="odoo_backup_${PG_DATABASE}_${TIMESTAMP}.sql.gz"

# Ensure local backup directory exists
mkdir -p $BACKUP_DIR

# Log file
LOG_FILE="$BACKUP_DIR/backup_log.txt"

# Function to log messages
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

log_message "Starting Odoo PostgreSQL backup process"

# Create the database backup
log_message "Creating database backup for $PG_DATABASE"
export PGPASSWORD=$PG_PASSWORD
pg_dump -h $PG_HOST -p $PG_PORT -U $PG_USER $PG_DATABASE | gzip > "$BACKUP_DIR/$BACKUP_FILENAME"
backup_result=$?
unset PGPASSWORD

if [ $backup_result -ne 0 ]; then
    log_message "ERROR: Database backup failed with error code $backup_result"
    exit 1
fi

log_message "Database backup completed successfully: $BACKUP_FILENAME"

# Check backup file size
BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILENAME" | cut -f1)
log_message "Backup size: $BACKUP_SIZE"

# Mount SMB share
log_message "Mounting SMB share $SMB_SHARE"
mkdir -p $SMB_MOUNT

# Check if already mounted
if grep -qs "$SMB_MOUNT" /proc/mounts; then
    log_message "SMB share already mounted"
else
    mount -t cifs $SMB_SHARE $SMB_MOUNT -o username=$SMB_USER,password=$SMB_PASSWORD,domain=$SMB_DOMAIN,vers=3.0
    mount_result=$?
    if [ $mount_result -ne 0 ]; then
        log_message "ERROR: Failed to mount SMB share with error code $mount_result"
        log_message "Keeping local backup only"
    else
        log_message "SMB share mounted successfully"
    fi
fi

# Copy backup to SMB share if mounted
if grep -qs "$SMB_MOUNT" /proc/mounts; then
    log_message "Copying backup to SMB share"
    
    # Create backup directory on SMB share if it doesn't exist
    mkdir -p "$SMB_MOUNT/$SMB_BACKUP_DIR"
    
    # Copy the backup file
    cp "$BACKUP_DIR/$BACKUP_FILENAME" "$SMB_MOUNT/$SMB_BACKUP_DIR/"
    copy_result=$?
    
    if [ $copy_result -ne 0 ]; then
        log_message "ERROR: Failed to copy backup to SMB share with error code $copy_result"
    else
        log_message "Backup successfully copied to SMB share"
    fi
    
    # Remove old backups from SMB share
    log_message "Removing backups older than $BACKUP_DAYS days from SMB share"
    find "$SMB_MOUNT/$SMB_BACKUP_DIR" -name "odoo_backup_*.sql.gz" -type f -mtime +$BACKUP_DAYS -delete
    
    # Unmount SMB share
    log_message "Unmounting SMB share"
    umount $SMB_MOUNT
else
    log_message "SMB share not mounted, skipping remote backup"
fi

# Remove old local backups
log_message "Removing local backups older than $BACKUP_DAYS days"
find "$BACKUP_DIR" -name "odoo_backup_*.sql.gz" -type f -mtime +$BACKUP_DAYS -delete

log_message "Backup process completed"
