
<div class="p-3 h-100 align-items-center justify-content-center w-100 d-flex" *ngIf="errorDraft">
  <h4 class="mb-0 d-flex">
    <span class="text-danger">Non è possibile modificare i tempi di lavoro su una produzione in stato bozza</span>
  </h4>
</div>

<div class="p-3 h-100" *ngIf="!errorDraft">
  <h4 class="mb-0 d-flex justify-content-between align-items-center">
    
    <!-- Status badges -->
    <!-- <div class="d-flex gap-2 flex-wrap">
      <span *ngIf="getActiveWorkordersCount() > 0" class="badge bg-primary">
        <i class="fa fa-stopwatch fa-spin me-1"></i> In corso: {{ getActiveWorkordersCount() }}
      </span>
      <span class="badge bg-success">
        <i class="fa fa-check-circle me-1"></i> Completate: {{ getTotalCompletedWorkordersCount() }} / {{ workorders.length }}
      </span>
      <span >
        {{showNewRow}}
      </span>
      <span class="badge bg-info">
        <i class="fa fa-clock me-1"></i> Tempo totale: {{ getTotalWorkordersTime() }}
      </span>
    </div> -->
  </h4>

  <!-- Loading indicator -->
 

    <!-- Quick Action Buttons - Simple version -->
  <div>
    <div class="d-flex gap-2 flex-wrap">
      <p-button class="ms-auto" (click)="addInternalWorkorder('Lavorazione interna')" label="Lavorazione interna" icon="fa fa-warehouse">
      </p-button>
      <p-button (click)="addInternalWorkorder('Lavorazione esterna')" label="Lavorazione esterna" icon="fa fa-plus">
      </p-button>
      <!-- <button class="btn btn-warning" (click)="addPredefinedWorkorder('oliatura')" [disabled]="loading" *ngIf="selectedSector == 'Pavimenti'">
        <i class="fa fa-tint me-2"></i>
        Crea Oliatura
      </button>

      <button class="btn btn-info" (click)="addPredefinedWorkorder('verniciatura')" [disabled]="loading" *ngIf="selectedSector == 'Pavimenti'">
        <i class="fa fa-paint-brush me-2"></i>
        Crea Verniciatura
      </button>

      <button class="btn btn-primary" (click)="addPredefinedWorkorder('imballaggio')" [disabled]="loading" *ngIf="selectedSector == 'Pavimenti'">
        <i class="fa fa-cubes me-2"></i>
        Crea Imballaggio
      </button>

      <button class="btn btn-secondary" (click)="toggleNewRow()">
        <i class="fa fa-cogs me-2"></i>
        Crea Operazione Personalizzata
      </button> -->
    </div>
  </div>

  <p-table [value]="workorders">
    <ng-template pTemplate="header">
      <tr>
        <th>Operazione</th>
        <th>Centro di Lavoro</th>
        <!-- <th>Stato</th> -->
        <th>Tempo</th>
        <th>Costo/h</th>
        <!-- <th>Azioni</th> -->
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-workorder let-i="rowIndex">
      <tr>
        <td>{{workorder.name}}</td>
        <td>{{workorder.workcenter_id.name}}</td>
        <!-- <td>{{workorder.state}}</td> -->
        <td><i  class="fa fa-clock me-1"></i>{{workorder.duration}} min</td>
        <td>
          <input class="form-control" [ngModel]="workorder.costs_hour">
        </td>
        <!-- <td>
        </td> -->
      </tr>
    </ng-template>
  </p-table>

  <hr>
  <!-- Workorders Table -->
  <div class="table-container d-none" *ngIf="!loading" >
    <p-table [value]="workorders" styleClass="p-datatable-sm table-hover workorders-table" [tableStyle]="{'min-width': '50rem'}">
      <ng-template pTemplate="header">
        <tr>
          <th>Operazione</th>
          <th class="d-flex align-items-center">Centro di Lavoro
            <span class="ms-2">
              <select class="form-select form-select-sm" [(ngModel)]="selectedSector" (ngModelChange)="onSectorChange()">
                <option *ngFor="let sector of sectors" [ngValue]="sector">
                  {{sector}}
                </option>
              </select>
            </span>
          </th>
          <th>Stato</th>
          <th>Tempo</th>
          <th>Azioni</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-workorder let-i="rowIndex">
        <tr [ngClass]="{'table-primary': workorder.state === 'progress', 'table-success': workorder.state === 'done', 'table-active': workorder.is_user_working}">
          <td class="operation-cell">
            <!-- Editable name in draft state -->
            <div *ngIf="isProductionDraft(); else readOnlyName">
              <input type="text" class="form-control form-control-sm" [(ngModel)]="workorder.name"
                (blur)="onOperationNameBlur(workorder)" placeholder="Nome operazione">
            </div>
            <ng-template #readOnlyName>
              <div class="d-flex align-items-center">
                <span class="fw-medium">{{workorder.name}}</span>
                <!-- Show spinner only when user is actively working -->
                <span *ngIf="workorder.is_user_working" class="ms-2 spinner-grow spinner-grow-sm text-primary"
                  role="status" aria-hidden="true"></span>
              </div>
            </ng-template>
          </td>

          <td >
            <!-- Editable workcenter in draft state -->
            <div *ngIf="isProductionDraft(); else readOnlyWorkcenter">
              <select class="form-select form-select-sm" [(ngModel)]="workorder.workcenter_id.id"
                (ngModelChange)="onWorkcenterSelected(workorder, $event)">
                <option [ngValue]="null">Seleziona centro...</option>
                <option *ngFor="let workcenter of availableWorkcenters" [ngValue]="workcenter.id">
                  {{workcenter.name}}
                </option>
              </select>
            </div>
            <ng-template #readOnlyWorkcenter>
              <div class="d-flex align-items-center">
                <i class="fa fa-tools me-2 text-secondary"></i>
                <span>{{workorder.workcenter_id.name || 'Non assegnato'}}</span>
              </div>
            </ng-template>
          </td>

          <td >
            <div class="d-flex align-items-center text-nowrap">
              <span class="status-badge" [ngClass]="getWorkorderStatusClass(workorder.state)">
                <i class="fa me-1" [ngClass]="getWorkorderStatusIcon(workorder)"></i>
                {{ getWorkorderStatusText(workorder) }}
              </span>
            </div>
          </td>

          <!-- Time and time actions -->
          <td  >
            <div class="d-flex align-items-center time-controls">
              <!-- ADD HOURS BUTTON -->
              <button class="btn btn-primary btn-sm me-2 add-hours-btn" type="button" 
                *ngIf="workorder.state != 'done'" 
                (click)="showAddHoursOverlay($event, addHoursPanel, workorder)"
                title="Aggiungi ore di lavoro">
                <i class="fa fa-plus"></i>
                Aggiungi Ore
              </button>

              <!-- OVERLAY PANEL for ADD HOURS -->
              <p-overlayPanel #addHoursPanel [dismissable]="true" [appendTo]="'body'" styleClass="add-hours-overlay">
                <div class="p-3">
                  <h6 class="mb-3 text-primary">
                    <i class="fa fa-clock me-2"></i>Aggiungi Ore di Lavoro
                  </h6>
                  <div class="mb-3">
                    <div class="input-group">
                      <input type="number" class="form-control" placeholder="0.0" step="0.25" min="0" max="24"
                        [(ngModel)]="addHoursInput[workorder.id]" (keyup.enter)="confirmAddHours(workorder)">
                      <span class="input-group-text">ore</span>
                    </div>
                    <!-- worker selector - -->
                     <select class="form-select form-select-sm mt-2" [(ngModel)]="selectedWorker">
                      <option *ngFor="let worker of workers" [ngValue]="worker">
                        {{worker.name}}
                      </option>
                    </select>
                  </div>
                  <div class="d-flex justify-content-between gap-2">
                    <button type="button" class="btn btn-outline-secondary" (click)="addHoursPanel.hide()">
                      <i class="fa fa-times me-1"></i> Annulla
                    </button>
                    <button type="button" class="btn btn-success"
                      [disabled]="!addHoursInput[workorder.id] || addHoursInput[workorder.id] <= 0"
                      (click)="confirmAddHours(workorder); addHoursPanel.hide();">
                      <i class="fa fa-check me-1"></i> Conferma
                    </button>
                  </div>
                </div>
              </p-overlayPanel>

              <!-- Timer progress display -->
              <div class="time-display" [ngClass]="{'time-active': workorder.is_user_working}"
                [attr.title]="'Durata totale: ' + formatDuration(workorder.duration)">
                <i class="fa fa-clock me-1"></i>
                {{formatDuration(workorder.duration) || '00:00:00'}}
              </div>
            </div>
          </td>

          <!-- Workorder actions -->
          <td >
            <div class="d-flex gap-2 align-items-center">
              
              <!-- Delete button -->
              <button class="btn btn-danger btn-sm action-btn" (click)="deleteWorkorder(workorder)"
                [disabled]="workorder.time_ids.ids.length > 0" 
                [title]="(workorder.time_ids && workorder.time_ids.values && workorder.time_ids.values.length > 0) ? 
                         'Non è possibile eliminare ordini con tempi registrati' : 'Elimina operazione'">
                <i class="fa fa-trash"></i>
              </button>

              <!-- Details dropdown -->
              <button type="button" class="btn btn-outline-secondary btn-sm action-btn"
                (click)="showDetailsOverlay($event, opDetails, workorder)" 
                [disabled]="production.state == 'draft'"
                title="Dettagli tempi di lavoro">
                <i class="fa-solid fa-bars"></i>
              </button>

              <!-- OverlayPanel for DETAILS -->
              <p-overlayPanel #opDetails [appendTo]="'body'" styleClass="details-overlay" [style]="{width: '800px'}">
                <div class="p-4">
                  <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="m-0 text-primary">
                      <i class="fa fa-clock me-2"></i>Registri di tempo: {{workorder.name}}
                    </h5>
                    <button type="button" class="btn-close" (click)="opDetails.hide()"></button>
                  </div>
                  
                  <!-- Time records table -->
                  <p-table 
                    *ngIf="workorder.time_ids && workorder.time_ids.values && workorder.time_ids.values.length > 0"
                    [value]="workorder.time_ids.values" 
                    styleClass="p-datatable-sm time-records-table"
                    [tableStyle]="{'width': '100%'}">
                    
                    <ng-template pTemplate="header">
                      <tr>
                        <th>Utente</th>
                        <th>Durata</th>
                        <th>Data inizio</th>
                        <th>Data fine</th>
                        <th style="width: 60px;">Azioni</th>
                      </tr>
                    </ng-template>
                    
                    <ng-template pTemplate="body" let-timeRecord>
                      <tr>
                        <td>{{ timeRecord.user_id?.name || 'N/A' }}</td>
                        <td class="time-record-duration">{{ formatDuration(timeRecord.duration) }}</td>
                        <td>{{ timeRecord.date_start | date:'dd/MM/yyyy HH:mm' }}</td>
                        <td>{{ timeRecord.date_end | date:'dd/MM/yyyy HH:mm' }}</td>
                        <td>
                          <button 
                            type="button" 
                            class="btn btn-danger btn-sm" 
                            (click)="deleteTimeRecord(timeRecord, workorder)"
                            title="Elimina registro">
                            <i class="fa fa-trash"></i>
                          </button>
                        </td>
                      </tr>
                    </ng-template>
                  </p-table>

                  <!-- Empty state -->
                  <div *ngIf="!workorder.time_ids || !workorder.time_ids.values || workorder.time_ids.values.length === 0" 
                       class="text-center p-4">
                    <i class="fa fa-clock text-muted mb-3" style="font-size: 2rem;"></i>
                    <p class="text-muted mb-0">Nessun registro disponibile</p>
                  </div>

                  <!-- Summary footer -->
                  <div *ngIf="workorder.time_ids && workorder.time_ids.values && workorder.time_ids.values.length > 0" 
                       class="mt-3 pt-3 border-top bg-light rounded p-3">
                    <div class="d-flex justify-content-between align-items-center">
                      <span class="text-muted">
                        <strong>{{ workorder.time_ids.values.length }}</strong> registri totali
                      </span>
                      <span class="badge bg-primary fs-6">
                        <i class="fa fa-clock me-1"></i>{{ formatDuration(workorder.duration) }}
                      </span>
                    </div>
                  </div>
                </div>
              </p-overlayPanel>

              <!-- Complete button -->
              <!-- <button class="btn btn-success btn-sm action-btn" *ngIf="workorder.state != 'done'"
                [disabled]="workorder.state == 'done' || workorder.is_user_working || production.state == 'draft'"
                (click)="completeWorkorder(workorder)" title="Completa operazione">
                <i class="fa fa-check"></i>
              </button> -->
            </div>
          </td>
        </tr>

      </ng-template>

      <!-- Empty message template -->
    <ng-template pTemplate="emptymessage">
      <tr>
        <td [attr.colspan]="isProductionDraft() ? 4 : 5" class="text-center p-5">
          <div class="empty-state">
            <i class="fa fa-clipboard-list text-muted mb-2" style="font-size: 3rem;"></i>
            <h6 class="text-muted">Nessun ordine di lavoro disponibile</h6>
            <p class="text-muted small mb-0">Utilizza i pulsanti qui sotto per creare nuove operazioni</p>
          </div>
        </td>
      </tr>
    </ng-template>
</p-table>

  <!-- Separate table structure when no workorders exist and showing new row -->
   <!-- Separate table structure when no workorders exist and showing new row -->
   <div *ngIf="workorders.length === 0 && showNewRow" class="p-datatable p-component p-datatable-sm table-hover workorders-table" style="min-width: 50rem;">
    <div class="p-datatable-wrapper">
      <table class="p-datatable-table">
        <tbody class="p-datatable-tbody">
          <tr class="bg-light border-top">
            <td class="operation-cell" style="width: 200px;">
              <input type="text" class="form-control" placeholder="Nome operazione" [(ngModel)]="newWorkorderName">
            </td>
            <td style="width: 180px;">
              <select class="form-select" [(ngModel)]="newWorkorderWorkcenter">
                <option [ngValue]="null">Seleziona centro...</option>
                <option *ngFor="let workcenter of availableWorkcenters" [ngValue]="workcenter">
                  {{workcenter.name}}
                </option>
              </select>
            </td>
            <td style="width: 120px;">
              <span class="badge bg-secondary">
                <i class="fa fa-plus-circle me-1"></i>
                Nuovo
              </span>
            </td>
            <td *ngIf="!isProductionDraft()"  style="width: 180px;"></td>
            <td style="width: 200px;">
              <div class="d-flex gap-2">
                <button class="btn btn-success" [disabled]="!newWorkorderName || !newWorkorderWorkcenter || loading"
                  (click)="createWorkorder()" title="Conferma operazione">
                  <i class="fa fa-check me-1"></i>
                  Conferma
                </button>
                <button class="btn btn-outline-secondary" (click)="toggleNewRow()" title="Annulla">
                  <i class="fa fa-times me-1"></i>
                  Annulla
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>


</div>

<style>

.badge-lg {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 6px;
}

/* Time display */
.time-display {
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  transition: all 0.3s ease;
  min-width: 100px;
  text-align: center;
  font-family: 'Courier New', monospace;
  white-space: nowrap;
  border: 1px solid #dee2e6;
}

.time-active {
  background: linear-gradient(145deg, #fff3cd, #ffeaa7);
  color: #664d03;
  border-color: #ffc107;
  animation: pulsate 2s infinite;
}

.time-controls {
  white-space: nowrap;
  flex-wrap: nowrap;
}

/* Overlay panels */
.add-hours-overlay .p-overlaypanel-content {
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.details-overlay .p-overlaypanel-content {
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.time-records-table {
  border-radius: 8px;
  overflow: hidden;
}

.time-record-duration {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #495057;
}

/* Disabled state */
.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Animations */
@keyframes pulsate {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Responsive design */
@media (max-width: 768px) {
  .badge-lg {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }
  
  .quick-action-btn {
    height: 100px;
  }
  
  .sector-selection {
    min-width: 150px;
  }
  
  h4.mb-3 {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem !important;
  }
}

@media (max-width: 576px) {
  .time-display {
    font-size: 0.8rem;
    padding: 0.375rem 0.5rem;
    min-width: 80px;
  }
  
  .action-btn, .add-hours-btn {
    min-width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }
}
</style>