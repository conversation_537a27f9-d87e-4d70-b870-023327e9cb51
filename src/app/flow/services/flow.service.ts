import { Injectable, TemplateRef } from '@angular/core';
import { Action, Flow } from '../../models/flow-flow.model';
import { OdooEntityManager } from '../../shared/services/odoo-entity-manager.service';

@Injectable({ providedIn: 'root' })
export class FlowService {
    flows: Flow[] = [];

    constructor(
        private odooEM: OdooEntityManager
    ) {
    }


    
    async save(flow: Flow, actions: any) {

        try {
            await this.odooEM.update(flow, {
                "actions": JSON.stringify( actions )
            }).toPromise()
            // flow.actions = JSON.parse(flow.actions)
        } catch (error) {
            console.log("eror ", error,actions )
        }
   
    }

    async list(): Promise<Flow[]> {
        return await this.odooEM.search<Flow>(new Flow(), []).toPromise()
    }

    async get(id) : Promise<Flow> {
        var xx = await this.odooEM.search<Flow>(new Flow(),[["id","=",Number(id)]]).toPromise()
        return xx[0]
    }

    async remove(id) {
        await this.odooEM.delete(new Flow(), [id]).toPromise()

    }


}
