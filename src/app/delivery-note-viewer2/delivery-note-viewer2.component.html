<app-navbar [loading]="loading" backroute="..">
  <a class="navbar-brand me-auto"> <PERSON> boll<PERSON> </a>
</app-navbar>

<div class="container-fluid mt-3">
  

  <div
    *ngIf="showMultipleOpportunityAlert"
    class="alert alert-warning d-flex align-items-center mb-3"
    role="alert"
  >
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>Attenzione:</strong> Hai selezionato ordini di diverse commesse!
  </div>
  <!-- <div
    *ngIf="PEFCmoved"
    class="alert alert-danger mt-3"
    role="alert"
  >
    Attenzione: Nelle vendite selezionate è presente materiale PEFC!!!
  </div> -->
  <div
    *ngIf="certificationAlert"
    class="alert alert-danger d-flex align-items-center mb-3"
    role="alert"
  >
    <i class="fas fa-certificate me-2"></i>
    {{certificationAlert}}
  </div>
  <div
    *ngIf="PEFCAlert"
    class="alert alert-danger d-flex align-items-center mb-3"
    role="alert"
  >
    <i class="fas fa-leaf me-2"></i>
    {{PEFCAlert}}
  </div>

  <div class="row mt-3">
    <div class="col-md-6 pe-3">
      <div class="border rounded">
        <div class="bg-light px-3 py-2 rounded-top">
          <div class="row align-items-center g-3">
            <div class="col-lg-6">
              <h5 class="mb-0 text-nowrap">Selezione ordini</h5>
            </div>
            <div class="col-lg-3">
              <div class="input-group">
                <span class="input-group-text bg-white border-end-0">
                  <i class="fas fa-search text-muted"></i>
                </span>
                <input
                  type="text"
                  class="form-control border-start-0"
                  [(ngModel)]="searchString"
                  (ngModelChange)="load()"
                  placeholder="Cerca ordini, clienti, riferimenti..."
                />
              </div>
            </div>
            <div class="col-lg-3">
              <div class="btn-group w-100" role="group" aria-label="View selection">
                <button
                  type="button"
                  class="btn btn-sm"
                  [class.btn-primary]="!filterListino"
                  [class.btn-outline-primary]="filterListino"
                  (click)="toggleFilterCommesse()"
                >
                  <i class="fas fa-shop me-1"></i>
                  Commesse
                </button>
                <button
                  type="button"
                  class="btn btn-sm"
                  [class.btn-primary]="filterListino"
                  [class.btn-outline-primary]="!filterListino"
                  (click)="toggleFilterListino()"
                >
                  <i class="fas fa-square-list me-1"></i>
                  A listino
                </button>
              </div>
            </div>
            <!-- <div class="col-lg-2">
              <div class="form-check form-switch d-flex align-items-center justify-content-center">
                <input
                  class="form-check-input me-2"
                  type="checkbox"
                  id="filterJustReady"
                  [(ngModel)]="filterJustReady"
                  (change)="load()"
                />
                <label class="form-check-label mb-0 text-nowrap" for="filterJustReady">
                  Solo pronti
                </label>
              </div>
            </div> -->
          </div>
        </div>
        <div style="max-height: calc(100vh - 145px); overflow-y: auto;">
          <ul class="list-group list-group-flush" *ngIf="!loading">
            <ng-container *ngIf="!filterListino">
              <table class="table table-hover">
                <thead class="bg-light">
                  <tr>
                    <th colspan="3"> <i class="fas fa-file-invoice me-1" ></i>Ordine</th>
                    <th></th>
                    <th><i class="fas fa-tag me-1"></i>Titolo</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container
                    *ngFor="let group of groupedPicks; let i = index"
                  >
                    <tr [class.border-top]="i > 0" class="bg-primary bg-opacity-25">
                      <td colspan="6">
                        <div class="form-check d-flex align-items-center">
                          <input
                            class="form-check-input me-2"
                            type="checkbox"
                            [id]="
                              group.opportunity
                                ? 'opportunity_' + group.opportunity.id
                                : 'no_opportunity'
                            "
                            [(ngModel)]="group.allSelected"
                            (change)="onOpportunitySelect(group)"
                          />
                          <div class="flex-grow-1">
                            <label
                              class="form-check-label fw-bold mb-0"
                              [for]="
                                group.opportunity
                                  ? 'opportunity_' + group.opportunity.id
                                  : 'no_opportunity'
                              "
                            >
                              <a
                                href="#"
                                (click)="
                                  $event.preventDefault();
                                  toggleLeadDropdown(group)
                                "
                                class="text-decoration-none text-dark d-inline-flex align-items-center"
                              >
                                <i
                                  class="fas me-2 text-secondary"
                                  [class.fa-caret-down]="group.showLeadDropdown"
                                  [class.fa-caret-right]="!group.showLeadDropdown"
                                ></i>
                                {{
                                  group.opportunity
                                    ? (group.opportunity.tracking_code || "N/A") +
                                      " - " +
                                      (group.opportunity.partner_id.name ||
                                        "N/A") +
                                      " - " +
                                      (group.opportunity.name || "N/A")
                                    : "No Opportunity"
                                }}
                              </a>
                              <span
                                class="badge ms-2"
                                [ngClass]="getLeadBadgeClass(group.opportunity)"
                              >
                                {{ group.opportunity?.area || "N/A" }}
                              </span>
                            </label>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr *ngIf="group.showLeadDropdown">
                      <td colspan="6">
                        <div class="p-3 bg-light">
                          <div class="row">
                            <div class="col-md-6">
                              <strong><i class="fas fa-user me-1 text-secondary"></i>Cliente:</strong>
                              {{
                                group.opportunity?.partner_id.value?.ga_arca
                              }}
                              - {{ group.opportunity?.partner_id.name }}
                            </div>
                            <div class="col-md-6">
                              <strong><i class="fas fa-map-marker-alt me-1 text-secondary"></i>Indirizzo:</strong>
                              {{ group.opportunity?.street }}
                            </div>
                          </div>
                          <div class="row mt-2">
                            <div class="col-md-4">
                              <strong><i class="fas fa-tags me-1 text-secondary"></i>Dettagli:</strong>
                              {{ getLeadTags(group.opportunity) }}
                            </div>
                            <div class="col-md-4">
                              <strong><i class="fas fa-user-tie me-1 text-secondary"></i>Responsabile:</strong>
                              {{ group.opportunity?.user_id.name }}
                            </div>
                            <div class="col-md-4" *ngIf="group.opportunity?.create_date">
                              <strong><i class="fas fa-calendar-plus me-1 text-secondary"></i>Data creazione:</strong>
                              {{ group.opportunity.create_date | date:'dd/MM/yyyy HH:mm':'it-IT' }}
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <ng-container *ngFor="let pick of group.picks">
                      <tr>
                        <td style="width: 40px;" class="ps-4">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            [id]="pick.id"
                            [(ngModel)]="pick._selected"
                            (change)="onPickingSelect(pick, group)"
                          />
                        </td>
                        <td style="width: 30px;">
                          <i
                            class="fa fa-circle"
                            [ngClass]="
                              pick.state === 'assigned'
                                ? 'text-success'
                                : 'text-warning'
                            "
                          ></i>
                        </td>
                        <td colspan="2">                       
                            {{ pick.sale_id?.value.name || "N/A" }}                                         
                        
                          <a [routerLink]="['/immediate-sale/s', pick.sale_id?.value?.id]"
                          target="_blank"
                          class="text-decoration-none">
                         <i class="fas fa-external-link-alt me-1 ms-4"></i>
                       </a>
                        </td>
                        <td colspan="3">
                          {{ pick.sale_id?.value.ga_title || "N/A" }}
                        </td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </tbody>
              </table>
            </ng-container>
            <ng-container *ngIf="filterListino">
              <table class="table table-hover">
                <thead class="bg-light">
                  <tr>
                    <th colspan="3"><i class="fas fa-file-invoice me-1"></i>Ordine</th>
                    <th></th>
                    <th><i class="fas fa-user me-1"></i>Cliente</th>
                    <th><i class="fas fa-tag me-1"></i>Titolo</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngFor="let pick of picks; let i = index">
                    <tr [class.border-top]="i > 0">
                      <td>
                        <input
                          class="form-check-input"
                          type="checkbox"
                          [id]="pick.id"
                          [(ngModel)]="pick._selected"
                          (change)="onPickingSelect(pick, null)"
                        />
                      </td>
                      <td>
                        <i
                          class="fa fa-circle"
                          [ngClass]="
                            pick.state === 'assigned'
                              ? 'text-success'
                              : 'text-warning'
                          "
                        ></i>
                      </td>
                      <td>
                        <a
                          href="#"
                          (click)="
                            $event.preventDefault(); toggleSaleDropdown(pick)
                          "
                          class="text-decoration-none text-dark d-inline-flex align-items-center"
                        >
                          <i
                            class="fas me-2 text-secondary"
                            [class.fa-caret-down]="pick._showSaleDropdown"
                            [class.fa-caret-right]="!pick._showSaleDropdown"
                          ></i>
                          {{ pick.sale_id?.value.name || "" }}
                        </a>                 
                      </td>
                      <td>
                        <a [routerLink]="['/immediate-sale/s', pick.sale_id?.value?.id]"
                        target="_blank"
                        class="text-decoration-none">
                       <i class="fas fa-external-link-alt me-1"></i>
                     </a>
                      </td>
                      <td>
                        {{ pick.sale_id?.value?.partner_id.name || "" }}
                      </td>
                      <td>
                        {{ pick.sale_id?.value?.ga_title || "" }}
                      </td>
                    </tr>
                    <tr *ngIf="pick._showSaleDropdown" class="bg-light">
                      <td colspan="7">
                        <div class="p-3">
                          <div class="row">                         
                            <div class="col-md-3">
                              <strong><i class="fas fa-map-marker-alt me-1 text-secondary"></i>Indirizzo:</strong> {{ pick.sale_id?.value.partner_shipping_id.value?.street || "" }}
                            </div>
                            <div class="col-md-3">
                              <strong><i class="fas fa-hashtag me-1 text-secondary"></i>Riferimento:</strong> {{ pick.sale_id?.value.client_order_ref || "" }}
                            </div>
                            <div class="col-md-3">
                              <strong><i class="fas fa-user-tie me-1 text-secondary"></i>Responsabile:</strong>
                              {{ pick.sale_id?.value.user_id.name }}
                            </div>
                            <div class="col-md-3">
                              <strong><i class="fas fa-credit-card me-1 text-secondary"></i>Pagamento:</strong>                        
                                {{ pick.sale_id?.value?.payment_term_id.name || "" }}
                            </div>
                          </div>
                          <div class="row mt-2" *ngIf="pick.sale_id?.value?.create_date">
                            <div class="col-md-12">
                              <strong><i class="fas fa-calendar-plus me-1 text-secondary"></i>Data creazione:</strong>
                              {{ pick.sale_id.value.create_date | date:'dd/MM/yyyy HH:mm':'it-IT' }}
                            </div>
                          </div>  
                        </div>
                      </td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </ng-container>
          </ul>
        </div>
      </div>
    </div>

    <div class="col-md-6 ps-3">
      <div class="border rounded">
        <div class="bg-light  px-3 py-2 rounded-top d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Materiale preparato</h5>
          <button
            class="btn btn-primary text-white"
            (click)="completePicks()"
            [disabled]="loading"
          >
            <span *ngIf="!loading">Bolla</span>
            <span
              *ngIf="loading"
              class="spinner-border spinner-border-sm"
              role="status"
              aria-hidden="true"
            ></span>
          </button>
        </div>
        <div style="max-height: calc(100vh - 145px); overflow-y: auto;">
          <div *ngIf="updatingMoves" class="text-center mb-3">
            <div class="fa fa-spinner fa-spin" role="status">
              <span class="visually-hidden">Updating...</span>
            </div>
          </div>
          <table class="table">
            <thead class="bg-light">
              <tr>
                <th><i class="fas fa-box me-1"></i>Prodotto</th>
                <th><i class="fas fa-weight me-1"></i>UdM</th>
                <th><i class="fas fa-chart-bar me-1"></i>Caricato / Richiesto</th>
                <th *ngIf="filterListino"><i class="fas fa-euro-sign me-1"></i>Prezzo</th>
                <th *ngIf="filterListino"><i class="fas fa-percentage me-1"></i>Sconto</th>
                <th *ngIf="filterListino"><i class="fas fa-calculator me-1"></i>Totale</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let group of groupedMoves">
                <tr
                  (click)="group.expanded = !group.expanded"
                  style="cursor: pointer;"
                  class="user-select-none fw-bold"
                >
                  <td class="fw-bold">
                    <i
                      class="fas me-2 text-secondary"
                      [class.fa-caret-down]="group.expanded"
                      [class.fa-caret-right]="!group.expanded"
                    ></i>
                    {{ group.category }}
                  </td>
                  <td>{{ group.uom }}</td>
                  <td
                    *ngIf="!filterListino"
                    [ngClass]="
                      getQuantityClass(
                        getTotalPrepared(group),
                        getTotalQuantity(group)
                      )
                    "
                  >
                    {{ getTotalPrepared(group) | number : "1.0-5" : "it-IT" }} /
                    {{ getTotalQuantity(group) | number : "1.0-5" : "it-IT" }}
                  </td>
                  <td *ngIf="filterListino" colspan="3"></td>
                  <td *ngIf="filterListino" class="text-end">
                    <strong
                      >{{
                        calculateGroupTotal(group) | number : "1.2-2" : "it-IT"
                      }}
                      €</strong
                    >
                  </td>
                </tr>
                <ng-container *ngIf="group.expanded">
                  <tr *ngFor="let move of group.moves">
                    <td class="ps-4">
                      <div class="form-check">
                        <input class="form-check-input me-2" type="checkbox" 
                          [(ngModel)]="move.selected" 
                          [disabled]=" move.quantity_done === 0"
                          (change)="onMoveSelectionChange(group)" >
                        {{ move.product_id[1] }}
                      </div>
                    </td>
                    <td>{{ move.product_uom.name }}</td>
                    <td
                      [ngClass]="
                        getQuantityClass(
                          move.quantity_done,
                          move.product_uom_qty
                        )
                      "
                    >
                      {{ move.quantity_done | number : "1.0-5" : "it-IT" }} /
                      {{ move.product_uom_qty | number : "1.0-5" : "it-IT" }}
                    </td>
                    <td *ngIf="filterListino">
                      {{ move.price_unit | number : "1.2-2" : "it-IT" }} €
                    </td>
                    <td *ngIf="filterListino">
                      {{ move.discount | number : "1.2-2" : "it-IT" }}%
                    </td>
                    <td *ngIf="filterListino" class="text-end">
                      {{ calculateTotal(move) | number : "1.2-2" : "it-IT" }} €
                    </td>
                  </tr>
                </ng-container>
              </ng-container>
            </tbody>
            <tfoot *ngIf="filterListino">
              <tr>
                <td colspan="5" class="text-end">
                  <strong>Totale Complessivo</strong>
                </td>
                <td class="text-end">
                  <strong
                    >{{
                      calculateGrandTotal() | number : "1.2-2" : "it-IT"
                    }}
                    €</strong
                  >
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>