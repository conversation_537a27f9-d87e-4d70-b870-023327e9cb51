import { Component, OnInit, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { L33tService } from '../../l33t.service';

export interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isLoading?: boolean;
}

@Component({
  selector: 'app-chat',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './chat.component.html',
  styleUrl: './chat.component.scss'
})
export class ChatComponent implements OnInit, AfterViewChecked {
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;

  messages: ChatMessage[] = [];
  currentMessage: string = '';
  isLoading: boolean = false;
  apiKey: string = '';
  isConfigured: boolean = false;
  selectedModel: string = 'gpt-3.5-turbo';
  systemPrompt: string = 'Sei un assistente AI utile e cordiale. Rispondi sempre in italiano.';

  availableModels = [
    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
    { value: 'gpt-4', label: 'GPT-4' },
    { value: 'gpt-4-turbo-preview', label: 'GPT-4 Turbo' }
  ];

  constructor(private l33tService: L33tService) {}

  ngOnInit() {
    this.isConfigured = this.l33tService.isConfigured();
    this.addWelcomeMessage();
  }

  ngAfterViewChecked() {
    this.scrollToBottom();
  }

  private addWelcomeMessage() {
    if (!this.isConfigured) {
      this.messages.push({
        id: this.generateId(),
        content: 'Benvenuto! Per iniziare, inserisci la tua API Key di OpenAI nelle impostazioni.',
        isUser: false,
        timestamp: new Date()
      });
    } else {
      this.messages.push({
        id: this.generateId(),
        content: 'Ciao! Sono il tuo assistente AI. Come posso aiutarti oggi?',
        isUser: false,
        timestamp: new Date()
      });
    }
  }

  configureApiKey() {
    if (this.apiKey.trim()) {
      this.l33tService.setApiKey(this.apiKey.trim());
      this.isConfigured = true;
      this.messages = [];
      this.addWelcomeMessage();
    }
  }

  sendMessage() {
    if (!this.currentMessage.trim() || this.isLoading || !this.isConfigured) {
      return;
    }

    // Aggiungi messaggio utente
    const userMessage: ChatMessage = {
      id: this.generateId(),
      content: this.currentMessage.trim(),
      isUser: true,
      timestamp: new Date()
    };
    this.messages.push(userMessage);

    // Aggiungi messaggio di caricamento
    const loadingMessage: ChatMessage = {
      id: this.generateId(),
      content: 'Sto pensando...',
      isUser: false,
      timestamp: new Date(),
      isLoading: true
    };
    this.messages.push(loadingMessage);

    const messageToSend = this.currentMessage.trim();
    this.currentMessage = '';
    this.isLoading = true;

    // Invia richiesta a OpenAI
    this.l33tService.sendMessage(
      messageToSend,
      this.selectedModel,
      this.systemPrompt
    ).subscribe({
      next: (response) => {
        // Rimuovi messaggio di caricamento
        this.messages = this.messages.filter(m => m.id !== loadingMessage.id);

        // Aggiungi risposta AI
        this.messages.push({
          id: this.generateId(),
          content: response,
          isUser: false,
          timestamp: new Date()
        });

        this.isLoading = false;
      },
      error: (error) => {
        // Rimuovi messaggio di caricamento
        this.messages = this.messages.filter(m => m.id !== loadingMessage.id);

        // Aggiungi messaggio di errore
        this.messages.push({
          id: this.generateId(),
          content: `Errore: ${error.message}`,
          isUser: false,
          timestamp: new Date()
        });

        this.isLoading = false;
      }
    });
  }

  clearChat() {
    this.messages = [];
    this.addWelcomeMessage();
  }

  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  private scrollToBottom(): void {
    try {
      if (this.messagesContainer) {
        this.messagesContainer.nativeElement.scrollTop =
          this.messagesContainer.nativeElement.scrollHeight;
      }
    } catch(err) {}
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 11);
  }
}
