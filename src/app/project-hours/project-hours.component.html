<!-- Navbar -->
<app-navbar [loading]="loading" backroute="..">
    <div class="d-flex justify-content-between align-items-center w-100">
        <a class="navbar-brand">
            <span> CFQL di {{ user.name || 'Caricamento utente...' }} {{user.id !== userData.id ? ' - <PERSON><PERSON> (sensibili)
                di ' + userData.name : ''}}</span>
        </a>
    </div>
</app-navbar>
<div class="container-fluid py-2">
    <!-- Compact Top Controls -->
    <div class="row g-2 mb-2">
        <!-- View Type Selector -->
        <div class="col-md-4">
            <div class="btn-group w-100" role="group">
                <input type="radio" class="btn-check" name="viewType" id="dayView" [(ngModel)]="viewType" value="day"
                    (change)="changeViewType('day')">
                <label class="btn btn-sm" for="dayView" [ngClass]="viewType === 'day' 
        ? 'btn-primary text-white' 
        : 'btn-outline-muted'">G<PERSON><PERSON>
                </label>
                <input type="radio" class="btn-check" name="viewType" id="weekView" [(ngModel)]="viewType" value="week"
                    (change)="changeViewType('week')">
                <label class="btn btn-sm" for="weekView" [ngClass]="viewType === 'week'
        ? 'btn-primary text-white'
        : 'btn-outline-muted'">Settimana
                </label>

                <input type="radio" class="btn-check" name="viewType" id="monthView" [(ngModel)]="viewType"
                    value="month" (change)="changeViewType('month')">
                <label class="btn btn-sm" for="monthView" [ngClass]="viewType === 'month' 
        ? 'btn-primary text-white'
        : 'btn-outline-muted'">Mese
                </label>
                <!-- <input type="radio" class="btn-check" name="viewType" id="yearView" [(ngModel)]="viewType" value="year"
                    (change)="changeViewType('year')">
                <label class="btn  btn-sm" for="yearView" [ngClass]="viewType === 'year'
        ? 'btn-primary text-white'
        : 'btn-outline-muted'">Anno
                </label> -->
            </div>
        </div>


        <!-- Period Selector -->
        <div class="col-md-5">
            <div class="input-group input-group-sm">
                <span class="input-group-text bg-light">{{ getViewTypeLabel() }}</span>
                <select class="form-select form-select-sm" [(ngModel)]="selectedPeriod" (change)="onPeriodChange()">
                    <option *ngFor="let period of periods" [value]="period.value">
                        {{ period.label }}
                    </option>
                </select>
                <button class="btn btn-outline-primary" (click)="loadPreviousPeriod()" [disabled]="viewType === 'year'">
                    <i class="fa fa-arrow-left"></i> Prec
                </button>
                <button class="btn btn-outline-primary" (click)="loadNextPeriod()" [disabled]="viewType === 'year'">
                    Succ <i class="fa fa-arrow-right"></i>
                </button>
            </div>
        </div>


        <div class="col-md-1" *ngIf="!(viewType==='day')">
            <button class="btn btn-sm w-100" (click)="editNotes = !editNotes"
                [ngClass]="editNotes ? 'btn-primary text-white' : 'btn-outline-secondary'">
                <i class="fa fa-file-pen"></i> Modifica note
            </button>
        </div>
        <!-- if superuser, can select another person -->
        <div class="col-md-2" *ngIf="canSeeEveryone">
            <div class="input-group input-group-sm">
                <span class="input-group-text bg-light">Utente</span>
                <select class="form-select form-select-sm" [ngModel]="userData" (ngModelChange)="changeUser($event)">
                    <option *ngFor="let u of allUsers" [ngValue]="u">
                        {{ u.name }}
                    </option>
                </select>
                <button *ngIf="user.id" class="btn btn-sm"
                    [ngClass]="userData?.id === user.id ? 'btn-primary text-white' : 'btn-outline-secondary'"
                    (click)="changeUser(user)" title="Solo le mie commesse">
                    <i class="fa-solid fa-user-check"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Visible Disclaimer -->
<div class="alert alert-danger text-center" *ngIf="user.id !== userData.id" role="alert">
    <!-- if user <> userData show another disclaumer -->
    <h3>Attenzione! Stai visualizzando i dati di {{ userData.name }}</h3>


</div>



<div class="table-responsive py-2">
    <table class="table table-hover table-sm">
        <thead>
            <tr>
                <th class="sticky-column sticky-col-1 bg-light">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary" type="button" id="legendDropdown"
                            data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa-solid fa-circle-info me-1"></i> Legenda
                        </button>
                        <div class="dropdown-menu p-3" style="min-width: 350px; max-width: 600px;">
                            <h6 class="dropdown-header border-bottom pb-2 mb-2">Ore registrate</h6>
                            <div class="mb-2">
                                <span class="text-primary fw-bold">hh</span> : ore registrate correttamente
                            </div>
                            <div class="mb-2">
                                <span class="bg-danger text-white px-1">hh</span> : manca descrizione attività
                            </div>
                            <div class="mb-2">
                                <span class="text-success ">≥6</span> / <span class="text-warning"><6</span> : ore giornaliere sufficienti/insufficienti
                            </div>
                            
                            <h6 class="dropdown-header border-bottom pb-2 mb-2 mt-3">Sezioni della Tabella</h6>
                            <div class="mb-2">
                                <span class="bg-danger bg-opacity-50  px-1">OFF</span> : ferie, festività e assenze
                            </div>
                            <div class="mb-2">
                                <span class="bg-secondary bg-opacity-50  px-1">EXTRA</span> : attività non legate a commesse specifiche
                            </div>
                            <div class="mb-2">
                                <span class="bg-success bg-opacity-50 px-1">COMMESSE</span> : attività e costo legati a commesse
                            </div>
                            <div class="mb-2">
                                <i class="fa-solid fa-check-circle text-success text-wrap"></i> Commesse in Corso: progetti attivi (da preventivo a confermato)
                            </div>
                            <div class="mb-2">
                                <i class="fa-solid fa-circle-exclamation text-warning"></i> Commesse Chiuse/Perse: progetti conclusi o cancellati
                            </div>
                            <h6 class="dropdown-header border-bottom pb-2 mb-2 mt-3">Funzionalità</h6>
                            <div class="mb-2">
                                <span class="text-secondary">
                                    <i class="fa-solid fa-file-pen"></i> Modifica note:
                                </span> aggiungi descrizione attività
                            </div>
                            <div class="mb-2">
                                <span class="text-success">filtra commesse: </span> ricerca tra le mie commesse
                            </div>
                            <div class="mb-2">
                                <span class="text-secondary"> Aggiungi all'elenco: </span> cerca e aggiunge commesse
                                all'elenco
                            </div>
                            <div class="mb-2">
                                <i class="fa-solid fa-chevron-up"></i> / <i class="fa-solid fa-chevron-down"></i>:
                                espande/comprime sezioni
                            </div>
                        </div>
                    </div>
                </th>

                <th class="sticky-column sticky-col-2 text-center bg-light">Tot</th>
                <th *ngFor="let day of periodDays" class="text-center bg-light" [class.sunday]="isSunday(day.date)">
                    {{ day.date | date:'EEE' }}<br>
                    {{ day.date | date:'dd/MM' }}
                </th>
                <th *ngIf="viewType === 'day'" class="text-center bg-light">Note</th>
            </tr>
        </thead>

        <tbody>
            <!-- Daily Totals Row -->
            <tr class="bg-light text-dark">
                <td class="sticky-column sticky-col-1 text-end bg-white">
                    <strong>TOTALE GIORNALIERO</strong>
                </td>
                <td class="sticky-column sticky-col-2 text-center bg-white">
                    <strong >{{ getTotalHours() | number: '1.0-0' }}</strong>
                </td>
                <td *ngFor="let day of periodDays" class="text-center align-middle p-0"
                    [class.sunday]="isSunday(day.date)">
                    <strong [ngClass]="getDayTotalHoursClass(day.date)">{{ getDayTotalHours(day.date) > 0 ? getDayTotalHours(day.date) : '' | number: '1.0-0' }}</strong>
                </td>
                <td *ngIf="viewType === 'day'"></td>
            </tr>

            <!-- OFF Tasks Section Header -->
            <tr *ngIf="offAccounts.length > 0" class="bg-danger bg-opacity-50 section-header">
                <td class="sticky-column sticky-col-1 bg-danger bg-opacity-50">
                    <div class="d-flex justify-content-between align-items-center">
                        <strong>
                            <i class="fa-solid fa-calendar-xmark text-white mx-2"></i>
                            OFF
                        </strong>
                        <button class="btn btn-sm btn-outline-light border-0"
                            (click)="offSectionCollapsed = !offSectionCollapsed">
                            <i class="fa" [ngClass]="offSectionCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
                        </button>
                    </div>
                </td>
                <td class="sticky-column sticky-col-2 text-center bg-danger bg-opacity-50">
                    <strong>{{ getTotalOffHours() > 0 ? getTotalOffHours() : '' | number: '1.0-0' }}</strong>
                </td>
                <td *ngFor="let day of periodDays" class="text-center align-middle p-0"
                    [class.sunday]="isSunday(day.date)">
                    <strong>{{ getDayTotalOffHours(day.date) > 0 ? getDayTotalOffHours(day.date) : '' | number: '1.0-0' }}</strong>
                </td>
                <td *ngIf="viewType === 'day'" class="bg-info"></td>
            </tr>

            <!-- OFF Tasks rows -->
            <ng-container *ngIf="!offSectionCollapsed && offAccounts.length > 0">
                <tr *ngFor="let task of offAccounts" [class.table-warning]="task._forEdit">
                    <td class="sticky-column sticky-col-1 text-nowrap bg-white">
                        <div class="d-flex justify-content-between text-wrap align-items-center">
                            {{ task.name.replace('P.OFF - ', '') }}
                        </div>
                    </td>
                    <td class="sticky-column sticky-col-2 text-center bg-white">
                        {{ getTaskTotalHours(task) > 0 ? getTaskTotalHours(task) : '' | number: '1.0-0' }}
                    </td>

                    <!-- Hours cells for OFF Tasks -->
                    <td *ngFor="let day of periodDays" class="text-center align-middle p-0"
                        [class.sunday]="isSunday(day.date)">
                        <div class="d-flex align-items-center justify-content-center position-relative">
                            <ng-container
                                *ngIf="getEntryForTaskAndDay(task, day.date) as entry; else newOffTaskEntryInput">
                                <input type="number"
                                    class="form-control form-control-sm border-0 text-center flex-grow-0 w-auto"
                                    [ngClass]="entry.unit_amount > 0 ? 'text-primary fw-bold' : ''"
                                    [ngModel]="entry.unit_amount"
                                    (blur)="updateTaskHours(task, day, $event.target.value, entry)" min="0" max="24"
                                    step="0.1">
                            </ng-container>

                            <ng-template #newOffTaskEntryInput>
                                <input type="number" class="form-control form-control-sm border-0 text-center w-auto"
                                    [ngModel]="null" (blur)="updateTaskHours(task, day, $event.target.value)" min="0"
                                    max="24" step="0.1" placeholder="">
                            </ng-template>
                        </div>
                    </td>
                    <!-- Notes column for day view -->
                    <td *ngIf="viewType === 'day'" class="text-center align-middle p-0">
                        <!-- No notes field needed for OFF tasks -->
                    </td>
                </tr>
            </ng-container>

            <!-- Extra Tasks Section Header -->
            <tr *ngIf="extraAccounts.length > 0" class="bg-secondary bg-opacity-50   section-header">
                <td class="sticky-column sticky-col-1 bg-secondary bg-opacity-50 ">
                    <div class="d-flex justify-content-between align-items-center">
                        <strong>
                            <i class="fa-solid fa-list-check text-white mx-2"></i>
                            EXTRA
                        </strong>
                        <button class="btn btn-sm btn-outline-light border-0"
                            (click)="extraSectionCollapsed = !extraSectionCollapsed">
                            <i class="fa" [ngClass]="extraSectionCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
                        </button>
                    </div>
                </td>
                <td class="sticky-column sticky-col-2 text-center bg-secondary bg-opacity-50 ">
                    <strong>{{ getTotalExtraHours() > 0 ? getTotalExtraHours() : '' | number: '1.0-0' }}</strong>
                </td>
                <td *ngFor="let day of periodDays" class="text-center align-middle p-0"
                    [class.sunday]="isSunday(day.date)">
                    <strong>{{ getDayTotalExtraHours(day.date) > 0 ? getDayTotalExtraHours(day.date) : '' | number: '1.0-0' }}</strong>
                </td>
                <td *ngIf="viewType === 'day'" class="bg-secondary bg-opacity-50 "></td>
            </tr>

            <!-- Extra Tasks rows -->
            <ng-container *ngIf="!extraSectionCollapsed && extraAccounts.length > 0">
                <tr *ngFor="let task of extraAccounts" [class.table-warning]="task._forEdit">
                    <td class="sticky-column sticky-col-1 text-nowrap bg-white">
                        <div class="d-flex justify-content-between text-wrap align-items-center">
                            {{ task.name.replace('P.EXT - ', '') }}
                        </div>
                    </td>
                    <td class="sticky-column sticky-col-2 text-center bg-white">
                        {{ getTaskTotalHours(task) > 0 ? getTaskTotalHours(task) : ''  | number: '1.0-0' }}
                    </td>

                    <!-- Hours cells for Extra Tasks -->
                    <td *ngFor="let day of periodDays" class="text-center align-middle p-0"
                        [class.sunday]="isSunday(day.date)">
                        <div class="d-flex align-items-center justify-content-center position-relative">
                            <ng-container
                                *ngIf="getEntryForTaskAndDay(task, day.date) as entry; else newTaskEntryInput">
                                <input type="number"
                                    class="form-control form-control-sm border-0 text-center flex-grow-0 w-auto"
                                    [ngClass]="entry.unit_amount > 0 ? ((entry.name === '/' && !watchingResponsableData) ? 'bg-danger text-white' : 'text-primary fw-bold') : ''"
                                    [ngModel]="entry.unit_amount"
                                    (blur)="updateTaskHours(task, day, $event.target.value, entry)" min="0" max="24"
                                    step="0.1">

                                <div class="dropdown ms-1 position-absolute end-0 ms-1" *ngIf="editNotes">
                                    <button class="btn btn-sm btn-link text-secondary p-0 px-1" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fa-solid fa-file-pen"></i>
                                    </button>
                                    <div class="dropdown-menu p-2" style="min-width: 250px;">
                                        <div class="mb-2">
                                            <label class="form-label small text-muted">Descrizione attività</label>
                                            <input type="text" class="form-control form-control-sm"
                                                [(ngModel)]="entry.name" (blur)="updateTaskLineName(task, day, entry)"
                                                placeholder="Inserisci descrizione">
                                        </div>
                                        <small class="text-muted d-block text-end mt-1">
                                            Premi Invio o clicca fuori per salvare
                                        </small>
                                    </div>
                                </div>
                            </ng-container>

                            <ng-template #newTaskEntryInput>
                                <input type="number" class="form-control form-control-sm border-0 text-center w-auto"
                                    [ngModel]="null" (blur)="updateTaskHours(task, day, $event.target.value)" min="0"
                                    max="24" step="0.1" placeholder="">
                            </ng-template>
                        </div>
                    </td>
                    <!-- Notes column for day view -->
                    <td *ngIf="viewType === 'day'" class="text-center align-middle p-0">
                        <div class="d-flex align-items-center justify-content-center position-relative">
                            <ng-container *ngIf="getEntryForTaskAndDay(task, singleDay) as entry">
                                <!-- Existing Entry with edit note on blur -->
                                <input type="text" class="form-control form-control-sm border-0"
                                    [(ngModel)]="entry.name" (blur)="updateTaskLineName(task, singleDay, entry)"
                                    placeholder="Inserisci descrizione">
                            </ng-container>
                        </div>
                    </td>
                </tr>
            </ng-container>

            <!-- Commesse Section Header -->
            <tr class="bg-success bg-opacity-50 section-header">
                <td class="sticky-column sticky-col-1 bg-success bg-opacity-50">
                    <div class="d-flex justify-content-between text-nowrap align-items-center">
                        <strong>
                            <i class="fa-solid fa-shop text-white mx-2"></i>
                            COMMESSE
                        </strong>
                        <!-- Filter input for accounts -->
                        <div class="input-group input-group-sm mx-4 my-1 ">
                            <input type="text" class="form-control form-control-sm" [(ngModel)]="accountFilter"
                                placeholder="filtra commesse..." (input)="filterAccounts()">
                        </div>

                        <button class="btn btn-sm btn-outline-light border-0"
                            (click)="commesseSectionCollapsed = !commesseSectionCollapsed">
                            <i class="fa"
                                [ngClass]="commesseSectionCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
                        </button>
                    </div>
                </td>
                <td class="sticky-column sticky-col-2 text-center align-middle bg-success bg-opacity-50">
                    <strong>{{ getTotaleCommesseHours() > 0 ? getTotaleCommesseHours() : '' | number: '1.0-0' }}</strong>
                </td>
                <td *ngFor="let day of periodDays" class="text-center align-middle p-0"
                    [class.sunday]="isSunday(day.date)">
                    <strong>{{ getDayCommesseTotalHours(day.date) > 0 ? getDayCommesseTotalHours(day.date) : '' | number: '1.0-0' }}</strong>
                </td>
                <td *ngIf="viewType === 'day'" class="bg-success bg-opacity-50"></td>
            </tr>

            <!-- Only show commesse content if not collapsed -->
            <ng-container *ngIf="!commesseSectionCollapsed">
                <!-- Search row -->
                <tr class="bg-light subsection-header">
                    <td colspan="{{ viewType === 'day' ? periodDays.length + 3 : periodDays.length + 5 }}"
                        class="sticky-column sticky-col-1 p-2">
                        <div class="input-group input-group-sm mb-2">
                            <button class="btn btn-outline-secondary btn-sm" (click)="addSelectedLead()"
                                [disabled]="!selectedLead || loading">
                                <i class="fa fa-plus"></i> Aggiungi all'elenco
                            </button>
                            <input type="text" class="form-control form-control-sm" [(ngModel)]="searchTerm"
                                (keyup)="searchLeads()" placeholder="Codice, nome o cliente...">
                            <button *ngIf="searchTerm" class="btn btn-outline-secondary btn-sm" (click)="clearSearch()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>

                        <!-- Inline search results -->
                        <div *ngIf="searchResults.length > 0">
                            <div class="list-group shadow-sm rounded">
                                <a *ngFor="let lead of searchResults"
                                    class="list-group-item list-group-item-action py-2" style="cursor: pointer;"
                                    (click)="selectLead(lead)">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>{{ lead.tracking_code }}</strong> - {{ lead.name }} {{ lead.city ? '
                                            - ' + lead.city : '' }}
                                        </div>
                                        <span class="text-muted">{{ lead.partner_id.name }}</span>
                                    </div>
                                </a>
                            </div>
                        </div>


                    </td>
                    <td class="sticky-column  sticky-col-2"> </td>
                    <td *ngFor="let day of periodDays" class="text-center" [class.sunday]="isSunday(day.date)">
                    </td>
                </tr>

                <!-- Confirmed accounts header -->
                <tr *ngIf="getConfirmedAccountsFiltered().length > 0" class="bg-light subsection-header">
                    <td class="sticky-column sticky-col-1">
                        <div class="d-flex justify-content-between align-items-center">
                            <strong>
                                <i class="fa-solid fa-check-circle text-success mx-2"></i>
                                Commesse in Corso
                            </strong>
                            <button class="btn btn-sm btn-outline-secondary border-0"
                                (click)="confirmedAccountsCollapsed = !confirmedAccountsCollapsed">
                                <i class="fa"
                                    [ngClass]="confirmedAccountsCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
                            </button>
                        </div>
                    </td>
                    <td class="sticky-column sticky-col-2"></td>
                    <td *ngFor="let day of periodDays" class="text-center" [class.sunday]="isSunday(day.date)">
                    </td>
                    <td *ngIf="viewType === 'day'"></td>
                </tr>

                <!-- Rows for confirmed accounts -->
                <ng-container *ngIf="!confirmedAccountsCollapsed">
                    <tr *ngFor="let account of getConfirmedAccountsFiltered()" [class.table-warning]="account._forEdit">
                        <td class="sticky-column sticky-col-1 text-nowrap  bg-white">
                            <div class="d-flex justify-content-between text-wrap align-items-center">
                                <div>
                                    {{ account.code ? account.code + ' | ' : '' }} {{ account.name }}
                                    {{ account.partner_id.name ? ' - ' + account.partner_id.name : '' }}
                                </div>
                                <span class="badge rounded-pill ms-1"
                                    [ngClass]="getBadgeClassForStage(account._connected_lead?.stage_id.name)">
                                    {{ account._connected_lead?.stage_id.name || 'In corso' }}
                                </span>
                            </div>
                        </td>
                        <td class="sticky-column sticky-col-2 text-center  bg-white">
                            {{ getAccountTotalHours(account) > 0 ? getAccountTotalHours(account) : '' | number: '1.0-0' }}
                        </td>

                        <!-- Hours cells -->
                        <td *ngFor="let day of periodDays" class="text-center align-middle p-0"
                            [class.sunday]="isSunday(day.date)">
                            <div class="d-flex align-items-center justify-content-center position-relative">
                                <ng-container
                                    *ngIf="getEntryForAccountAndDay(account, day.date) as entry; else newEntryInput">
                                    <input type="number"
                                        class="form-control form-control-sm border-0 text-center flex-grow-0 w-auto"
                                        [ngClass]="entry.unit_amount > 0 ? 'text-primary fw-bold' : ''"
                                        [ngModel]="entry.unit_amount"
                                        (blur)="updateHours(account, day, $event.target.value, entry)" min="0" max="24"
                                        step="0.1">


                                    <div class="dropdown ms-1 position-absolute end-0 ms-1" *ngIf="editNotes">
                                        <button class="btn btn-sm btn-link text-secondary p-0 px-1" type="button"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fa-solid fa-file-pen"></i>
                                        </button>
                                        <div class="dropdown-menu p-2" style="min-width: 250px;">
                                            <div class="mb-2">
                                                <label class="form-label small text-muted">Descrizione attività</label>
                                                <input type="text" class="form-control form-control-sm border-0"
                                                    [(ngModel)]="entry.name"
                                                    (blur)="updateLineName(account, day, entry)"
                                                    placeholder="Inserisci descrizione">
                                            </div>
                                            <small class="text-muted d-block text-end mt-1">
                                                Premi Invio o clicca fuori per salvare
                                            </small>
                                        </div>
                                    </div>
                                </ng-container>

                                <ng-template #newEntryInput>
                                    <input type="number"
                                        class="form-control form-control-sm border-0 text-center flex-grow-0 w-auto"
                                        [ngModel]="null" (blur)="updateHours(account, day, $event.target.value)" min="0"
                                        max="24" step="0.1" placeholder="">
                                </ng-template>
                            </div>
                        </td>
                        <!-- Notes column for day view -->
                        <td *ngIf="viewType === 'day'" class="text-center align-middle p-0">
                            <div class="d-flex align-items-center justify-content-center position-relative">
                                <ng-container *ngIf="getEntryForAccountAndDay(account, singleDay) as entry">
                                    <!-- Existing Entry with edit note on blur -->
                                    <input type="text" class="form-control form-control-sm border-0"
                                        [(ngModel)]="entry.name" (blur)="updateLineName(account, singleDay, entry)"
                                        placeholder="Inserisci descrizione">
                                </ng-container>
                            </div>
                        </td>
                    </tr>
                </ng-container>

                <!-- Non-confirmed accounts header -->
                <tr *ngIf="getNonConfirmedAccountsFiltered().length > 0" class="bg-light subsection-header">
                    <td class="sticky-column sticky-col-1">
                        <div class="d-flex justify-content-between align-items-center">
                            <strong>
                                <i class="fa-solid fa-circle-exclamation text-warning mx-2"></i>
                                Commesse Chiuse / Perse
                            </strong>
                            <button class="btn btn-sm btn-outline-secondary border-0"
                                (click)="nonConfirmedAccountsCollapsed = !nonConfirmedAccountsCollapsed">
                                <i class="fa"
                                    [ngClass]="nonConfirmedAccountsCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
                            </button>
                        </div>
                    </td>
                    <td class="sticky-column sticky-col-2"></td>
                    <td *ngFor="let day of periodDays" class="text-center" [class.sunday]="isSunday(day.date)">
                    </td>
                    <td *ngIf="viewType === 'day'"></td>
                </tr>

                <!-- Rows for non-confirmed accounts -->
                <ng-container *ngIf="!nonConfirmedAccountsCollapsed">
                    <tr *ngFor="let account of getNonConfirmedAccountsFiltered()"
                        [class.table-warning]="account._forEdit">
                        <td class="sticky-column sticky-col-1 text-nowrap bg-white">
                            <div class="d-flex justify-content-between text-wrap align-items-center">
                                <div>
                                    {{ account.code ? account.code + ' | ' : '' }} {{ account.name }}
                                    {{ account.partner_id.name ? ' - ' + account.partner_id.name : '' }}
                                </div>
                                <span class="badge rounded-pill ms-1"
                                    [ngClass]="getBadgeClassForStage(account._connected_lead?.stage_id.name)">
                                    {{ account._connected_lead?.stage_id.name || 'In corso' }}
                                </span>
                            </div>
                        </td>
                        <td class="sticky-column sticky-col-2 text-center bg-white">
                            {{ getAccountTotalHours(account) > 0 ? getAccountTotalHours(account) : '' | number: '1.0-0' }}
                        </td>

                        <!-- Hours cells -->
                        <td *ngFor="let day of periodDays" class="text-center align-middle p-0"
                            [class.sunday]="isSunday(day.date)">
                            <div class="d-flex align-items-center justify-content-center position-relative">
                                <ng-container
                                    *ngIf="getEntryForAccountAndDay(account, day.date) as entry; else newEntryInput">
                                    <input type="number"
                                        class="form-control form-control-sm border-0 text-center flex-grow-0 w-auto"
                                        [ngClass]="entry.unit_amount > 0 ? 'text-primary fw-bold' : ''"
                                        [ngModel]="entry.unit_amount"
                                        (blur)="updateHours(account, day, $event.target.value, entry)" min="0" max="24"
                                        step="0.1">

                                    <div class="dropdown ms-1 position-absolute end-0 ms-1" *ngIf="editNotes">
                                        <button class="btn btn-sm btn-link text-secondary p-0 px-1" type="button"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fa-solid fa-file-pen"></i>
                                        </button>
                                        <div class="dropdown-menu p-2" style="min-width: 250px;">
                                            <div class="mb-2">
                                                <label class="form-label small text-muted">Descrizione attività</label>
                                                <input type="text" class="form-control form-control-sm border-0"
                                                    [(ngModel)]="entry.name"
                                                    (blur)="updateLineName(account, day, entry)"
                                                    placeholder="Inserisci descrizione">
                                            </div>
                                            <small class="text-muted d-block text-end mt-1">
                                                Premi Invio o clicca fuori per salvare
                                            </small>
                                        </div>
                                    </div>
                                </ng-container>

                                <ng-template #newEntryInput>
                                    <input type="number"
                                        class="form-control form-control-sm border-0 text-center flex-grow-0 w-auto"
                                        [ngModel]="null" (blur)="updateHours(account, day, $event.target.value)" min="0"
                                        max="24" step="0.1" placeholder="">
                                </ng-template>
                            </div>
                        </td>
                        <!-- Notes column for day view -->
                        <td *ngIf="viewType === 'day'" class="text-center align-middle p-0">
                            <div class="d-flex align-items-center justify-content-center position-relative">
                                <ng-container *ngIf="getEntryForAccountAndDay(account, singleDay) as entry">
                                    <!-- Existing Entry with edit note on blur -->
                                    <input type="text" class="form-control form-control-sm border-0"
                                        [(ngModel)]="entry.name" (blur)="updateLineName(account, singleDay, entry)"
                                        placeholder="Inserisci descrizione">
                                </ng-container>
                            </div>
                        </td>
                    </tr>
                </ng-container>
            </ng-container>
        </tbody>
    </table>
</div>