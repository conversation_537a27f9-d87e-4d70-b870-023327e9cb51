{"name": "gali-erp3", "version": "0.0.1", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4201 --proxy-config proxy.conf.json  ", "start-local": "ng serve --host 0.0.0.0 --port 4201 --proxy-config proxy-local.conf.json ", "startTest": "ng serve --c e2e --proxy-config proxy.conf.json", "prebuild": "node ./scripts/installer.js", "build": "ng build --configuration production --output-hashing all", "test": "ng test", "lint": "ng lint", "e2e": "npx kill-port 4201  && npx kill-port 8888 && ( netlify dev -c 'yarn startTest' & sleep 20; ng e2e)"}, "engines": {"node": ">=20"}, "private": true, "dependencies": {"@angular-devkit/schematics": "^19.2.3", "@angular/animations": "^19.2.2", "@angular/cdk": "^18.0.0", "@angular/cli": "^19.2.3", "@angular/common": "^19.2.2", "@angular/compiler": "^19.2.2", "@angular/core": "^19.2.2", "@angular/forms": "^19.2.2", "@angular/platform-browser": "^19.2.2", "@angular/platform-browser-dynamic": "^19.2.2", "@angular/router": "^19.2.2", "@angular/service-worker": "^19.2.2", "@google-cloud/storage": "^7.1.0", "@ng-dnd/core": "^2.0.2", "@popperjs/core": "^2.11.6", "@primeng/themes": "^19.0.9", "@primeuix/utils": "^0.5.1", "@revolist/angular-datagrid": "^4.11.21", "@revolist/revogrid": "^4.11.21", "@types/gapi": "^0.0.39", "@types/gapi.auth2": "^0.0.52", "@types/gapi.client.drive": "^3.0.13", "@types/gapi.client.gmail": "^1.0.3", "@types/gapi.client.sheets": "^4.0.20201029", "@types/googlemaps": "^3.37.7", "@types/xmlrpc": "^1.3.7", "@undecaf/barcode-detector-polyfill": "^0.9.15", "@yuvarajv/ngx-google-places-autocomplete": "^1.0.1", "ag-grid-angular": "^33.0.4", "angular-resizable-element": "^7.0.2", "animate.css": "^4.1.1", "bootstrap": "5.3.0-alpha3", "buffer": "^6.0.3", "chart.js": "^4.3.0", "client-oauth2": "^4.2.5", "core-js": "^3.2.1", "decimal.js": "^10.4.3", "drawflow": "^0.0.59", "globby": "5.0.0", "jasmine-fail-fast": "^2.0.0", "jointjs": "^3.7.1", "jquery": "^3.4.1", "jsvat": "^2.5.3", "moment": "^2.24.0", "ng-gapi": "^0.0.94", "ngx-auth": "^5.2.0", "ngx-clipboard": "^12.2.1", "npm-run-all": "^4.1.5", "openai": "^5.8.2", "papaparse": "^5.5.3", "pinecone-client": "^1.1.0", "ping.js": "^0.3.0", "popper.js": "^1.16.1", "primeng": "^19.0.0", "querystring": "^0.2.1", "react-dnd-html5-backend": "^16.0.1", "rxjs": "~7.5.0", "scanbot-web-sdk": "^2.12.0", "socket.io-client": "^4.5.4", "sys": "^0.0.1", "trello": "^0.10.0", "ts-transformer-keys": "^0.3.5", "tslib": "^2.0.0", "typescript": "5.8.2", "xmlrpc": "^1.3.2", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.3", "@angular/compiler-cli": "^19.2.2", "@angular/language-service": "^19.2.2", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "protractor-console-plugin": "^0.1.1", "protractor-fail-fast": "^3.1.0", "ts-node": "~8.4.1", "tslint": "~6.1.0"}}