import { OdooMultiRelationship } from './odoo-multi-relationship.model';
import { IConnectable, TrelloCardEntry2 } from './trello-card';
import { OdooModel } from './odoo-model.model';
import { StockMoveLine } from './stock-move-line';
import { StockMove } from './stock-move';
import { DriveFolder } from './drive.folder';
import { Product } from './product.model';
import { SaleOrder } from './sale-order.model';
import { UomUom } from './uom-uom.model';
import { ProcurementGroup } from './procurement.group.model';
import { StockLocation } from './stock-location';
import { StockPickingType } from './stock-picking-type.model';
import { User } from './user.model';
import { OdooSerializableInterface } from '../shared/interfaces/odoo-serializable-interface';
import { OdooRelationship } from './odoo-relationship.model';
import { MailFollower } from './mail.followers';
import { MailMessage, IrAttachment, MailActivity } from './mail.message';
import { Partner } from './partner';
import { ProductTemplateAttributeValue } from './product.template.attribute.value.model';
import { ProductTemplate } from './product.template.model';
import { StockPicking } from './stock-picking';

export class MrpProduction extends OdooModel implements IConnectable<MrpProduction> {
  trello_card_ids: OdooMultiRelationship<TrelloCardEntry2>;
  drive_folder_ids: OdooMultiRelationship<DriveFolder>;
  trello_ids: OdooMultiRelationship<TrelloCardEntry2>;
  drive_ids: OdooMultiRelationship<DriveFolder>;
  public readonly ODOO_MODEL = 'mrp.production';
  
     readonly ORDER_TYPE_NAME = 'Production';
  
  // Basic Information
  id: number;
  name: string = "";                                    // Reference
  state: string = "";                                   // Status
  origin: string = "";                                  // Source
  date_planned_start: string = "";                      // Planned Date
  date_planned_finished: string = "";                   // Date of Finish Planned
  date_deadline: string = "";                           // Deadline
  date_start: string = "";                              // Date Start
  date_finished: string = "";                           // Date Finished
  product_description_variants: string = "";            // Custom Description
  create_date: string = "";                             // Date Creation
  reservation_state: string = "";                       // MO Readiness
  components_availability: string = "";                 // Components State
  components_availability_state: string = "";           // Components Availability State

  // Quantities and Products
  product_qty: number = 0;                              // Quantity to Produce
  qty_producing: number = 0;                            // Quantity Producing
  qty_produced: number;                                 // Quantity Produced
  product_uom_qty: number;                              // Total Quantity
  production_capacity: number;                          // Production Capacity
  production_duration_expected: number;                 // Expected Duration
  production_real_duration: number;                     // Actual Duration
  extra_cost: number;                                   // Extra Unit Cost

  // Related Models
  product_id: OdooRelationship<Product> = new OdooRelationship<Product>();
  product_tmpl_id: OdooRelationship<ProductTemplate> = new OdooRelationship<ProductTemplate>();
  product_uom_id: OdooRelationship<UomUom> = new OdooRelationship<UomUom>();
  product_uom_category_id: OdooRelationship<UomUom> = new OdooRelationship<UomUom>();
  bom_id: OdooRelationship<MrpBom> = new OdooRelationship<MrpBom>();
  // lot_producing_id: OdooRelationship<StockLot> = new OdooRelationship<StockLot>();
  picking_type_id: OdooRelationship<StockPickingType> = new OdooRelationship<StockPickingType>();
  user_id: OdooRelationship<User> = new OdooRelationship<User>();
  create_uid: OdooRelationship<User> = new OdooRelationship<User>();
  write_uid: OdooRelationship<User> = new OdooRelationship<User>();
  // company_id: OdooRelationship<Company> = new OdooRelationship<Company>();
  procurement_group_id: OdooRelationship<ProcurementGroup> = new OdooRelationship<ProcurementGroup>();
  // orderpoint_id: OdooRelationship<any> = new OdooRelationship<any>();                // Stock Warehouse Orderpoint
  // subcontractor_id: OdooRelationship<Partner> = new OdooRelationship<Partner>();
  location_src_id: OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>();
  location_dest_id: OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>();
  production_location_id: OdooRelationship<StockLocation> = new OdooRelationship<StockLocation>();
  // analytic_account_id: OdooRelationship<AccountAnalyticAccount> = new OdooRelationship<AccountAnalyticAccount>();
  
  // One to Many Relationships
  move_raw_ids: OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove);                 // Components
  move_finished_ids: OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove);            // Finished Products
  move_byproduct_ids: OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove);           // By-product Moves
  workorder_ids: OdooMultiRelationship<MrpWorkorder> = new OdooMultiRelationship<MrpWorkorder>(MrpWorkorder);       // Work Orders
  // unbuild_ids: OdooMultiRelationship<any> = new OdooMultiRelationship<any>(null);                                   // Unbuilds
  // scrap_ids: OdooMultiRelationship<any> = new OdooMultiRelationship<any>(null);                                     // Scraps
  move_dest_ids: OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove);                // Finished Product Inventory Moves
  move_line_raw_ids: OdooMultiRelationship<StockMoveLine> = new OdooMultiRelationship<StockMoveLine>(StockMoveLine); // Component Details
  finished_move_line_ids: OdooMultiRelationship<StockMoveLine> = new OdooMultiRelationship<StockMoveLine>(StockMoveLine); // Finished Products
  activity_ids: OdooMultiRelationship<MailActivity> = new OdooMultiRelationship<MailActivity>(MailActivity);        // Activities
  picking_ids: OdooMultiRelationship<StockPicking> = new OdooMultiRelationship<StockPicking>(StockPicking);  

  // Connections and External References
  // drive_folder_ids: OdooMultiRelationship<DriveFolder> = new OdooMultiRelationship<DriveFolder>(DriveFolder);
  // trello_card_ids: OdooMultiRelationship<TrelloCardEntry2> = new OdooMultiRelationship<TrelloCardEntry2>(TrelloCardEntry2);

  
  // Flags and Settings
  is_locked: boolean = false;
  is_planned: boolean;
  consumption: string = ""; 
  product_tracking: string = ""; 
  propagate_cancel: boolean;
  backorder_sequence: number = 0;
  allow_workorder_dependencies: boolean;
  subcontracting_has_been_recorded: boolean;
  
  // Counts and Stats
  sale_order_count: number = 0;
  scrap_count: number;
  unbuild_count: number;
  delivery_count: number;
  mrp_production_child_count: number;
  mrp_production_source_count: number;
  mrp_production_backorder_count: number;
  
  // Mail Thread Fields
  message_follower_ids: OdooMultiRelationship<MailFollower> = new OdooMultiRelationship<MailFollower>(MailFollower);
  message_ids: OdooMultiRelationship<MailMessage> = new OdooMultiRelationship<MailMessage>(MailMessage);
  message_main_attachment_id: OdooRelationship<IrAttachment> = new OdooRelationship<IrAttachment>();
  activity_type_id: OdooRelationship<any> = new OdooRelationship<any>();
  activity_user_id: OdooRelationship<User> = new OdooRelationship<User>();
  activity_state: string = ""; 
  activity_date_deadline: string = ""; 
  activity_summary: string = ""; 
  
  // Custom fields
  _sale_id: OdooRelationship<SaleOrder> = new OdooRelationship<SaleOrder>();

  create() {
    return new MrpProduction()
  }

}

export class MrpWorkorder extends OdooModel implements OdooSerializableInterface<MrpWorkorder> {

  public readonly ODOO_MODEL = 'mrp.workorder';

  // Basic Information
  name: string = '';
  state: 'pending' | 'waiting' | 'ready' | 'progress' | 'done' | 'cancel' | '' = '';
  working_state: string = '';
  
  // Relations
  workcenter_id: OdooRelationship<MrpWorkcenter> = new OdooRelationship<MrpWorkcenter>();
  production_id: OdooRelationship<MrpProduction> = new OdooRelationship<MrpProduction>();
  product_id: OdooRelationship<Product> = new OdooRelationship<Product>();
  operation_id: OdooRelationship<MrpRoutingWorkcenter> = new OdooRelationship<MrpRoutingWorkcenter>();
  product_uom_id: OdooRelationship<UomUom> = new OdooRelationship<UomUom>();
  working_user_ids: OdooMultiRelationship<User> = new OdooMultiRelationship<User>(User);
  last_working_user_id: OdooRelationship<User> = new OdooRelationship<User>();

  // Moves
  move_raw_ids: OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove);
  move_finished_ids: OdooMultiRelationship<StockMove> = new OdooMultiRelationship<StockMove>(StockMove);
  move_line_ids: OdooMultiRelationship<StockMoveLine> = new OdooMultiRelationship<StockMoveLine>(StockMoveLine);
  
  // Quantities
  qty_production: number = 0;
  qty_produced: number = 0;
  qty_producing: number = 0;
  qty_remaining: number = 0;
  
  // Timing
  date_planned_start: string = '';
  date_planned_finished: string = '';
  date_start: string = '';
  date_finished: string = '';
  duration_expected: number = 0;
  duration: number = 0;
  duration_unit: number = 0;
  duration_percent: number = 0;
  time_ids : OdooMultiRelationship<MrpWorkcenterProductivity> = new OdooMultiRelationship<MrpWorkcenterProductivity>(MrpWorkcenterProductivity);
  
  // Status and Progress
  is_user_working: boolean = false;
  is_produced: boolean = false;
  is_planned: boolean = false;
  progress: number = 0;
  production_state: string = '';
  production_availability: string = '';
  
  // Additional Info
  operation_note: string = '';
  costs_hour: number = 0;
  consumption: string = '';
  worksheet_type: string = '';
  worksheet_google_slide: string = '';
  
  constructor(id: number = null, name: string = '') {
    super();
    this.id = id;
    this.name = name;
  }

  create(): MrpWorkorder {
    return new MrpWorkorder();
  }
}

export class MrpWorkcenter extends OdooModel implements OdooSerializableInterface<MrpWorkcenter> {

  public readonly ODOO_MODEL = 'mrp.workcenter';

  // Basic Information
  name: string = '';
  code: string = '';
  active: boolean = true;
  sequence: number = 0;
  color: number;
  note: string = '';
  tz: string = '';

  // Relations
  routing_line_ids: OdooRelationship<MrpRoutingWorkcenter> = new OdooRelationship<MrpRoutingWorkcenter>();
  time_ids: OdooMultiRelationship<MrpWorkcenterProductivity> = new OdooMultiRelationship<MrpWorkcenterProductivity>(MrpWorkcenterProductivity);
  order_ids: OdooMultiRelationship<MrpProduction> = new OdooMultiRelationship<MrpProduction>(MrpProduction);
  tag_ids: OdooMultiRelationship<MrpWorkcenterTag> = new OdooMultiRelationship<MrpWorkcenterTag>(MrpWorkcenterTag);

  // Costs and Capacity
  costs_hour: number = 0;
  default_capacity: number = 0;
  time_efficiency: number = 0;
  time_start: number = 0;
  time_stop: number = 0;
  
  // Performance Metrics
  oee: number = 0;
  oee_target: number  = 0;
  performance: number = 0;
  productive_time: number = 0;
  blocked_time: number = 0;
  workcenter_load: number = 0;

  // Status and Counts
  working_state: string   = '';
  workorder_count: number = 0;
  workorder_ready_count: number = 0;
  workorder_pending_count: number = 0;
  workorder_progress_count: number = 0;
  workorder_late_count: number = 0;

  constructor(id: number = null, name: string = '') {
    super();
    this.id = id;
    this.name = name;
    this.active = true;
  }

  create(): MrpWorkcenter {
    return new MrpWorkcenter();
  }
}

export class MrpWorkcenterTag extends OdooModel implements OdooSerializableInterface<MrpWorkcenterTag> {
  public readonly ODOO_MODEL = 'mrp.workcenter.tag';

  name: string = '';
  color: number;

  constructor(id: number = null, name: string = '') {
    super();
    this.id = id;
    this.name = name;
  }

  create(): MrpWorkcenterTag {
    return new MrpWorkcenterTag();
  }
}


export class MrpRoutingWorkcenter extends OdooModel implements OdooSerializableInterface<MrpRoutingWorkcenter> {
  public readonly ODOO_MODEL = 'mrp.routing.workcenter';

  // Basic Information
  name: string = '';
  active: boolean;
  sequence: number;
  note: string = '';
  
  // Relations
  bom_id: OdooRelationship<MrpBom> = new OdooRelationship<MrpBom>();
  workcenter_id: OdooRelationship<MrpWorkcenter> = new OdooRelationship<MrpWorkcenter>();
  workorder_ids: OdooMultiRelationship<MrpWorkorder> = new OdooMultiRelationship<MrpWorkorder>(MrpWorkorder);
  blocked_by_operation_ids: OdooMultiRelationship<MrpRoutingWorkcenter> = new OdooMultiRelationship<MrpRoutingWorkcenter>(MrpRoutingWorkcenter);
  needed_by_operation_ids:  OdooMultiRelationship<MrpRoutingWorkcenter> = new OdooMultiRelationship<MrpRoutingWorkcenter>(MrpRoutingWorkcenter);
  bom_product_template_attribute_value_ids:  OdooMultiRelationship<ProductTemplateAttributeValue> = new OdooMultiRelationship<ProductTemplateAttributeValue>(ProductTemplateAttributeValue);
  possible_bom_product_template_attribute_value_ids: OdooMultiRelationship<ProductTemplateAttributeValue> = new OdooMultiRelationship<ProductTemplateAttributeValue>(ProductTemplateAttributeValue);

  // Time Management
  time_cycle: number = 0;
  time_cycle_manual: number = 0;
  time_mode: 'auto' | 'manual';
  time_mode_batch: number = 0;
  time_computed_on: string = '';
  
  // Operation Settings
  allow_operation_dependencies: boolean = false;
  workorder_count: number = 0;

  constructor(id: number = null, name: string = '') {
    super();
    this.id = id;
    this.name = name;
    this.active = true;
    this.time_mode = 'manual';
    this.time_mode_batch = 1;
  }

  create(): MrpRoutingWorkcenter {
    return new MrpRoutingWorkcenter();
  }
}

export class MrpWorkcenterProductivity extends OdooModel implements OdooSerializableInterface<MrpWorkcenterProductivity> {

  public readonly ODOO_MODEL = 'mrp.workcenter.productivity';

  // Relations
  workcenter_id: OdooRelationship<MrpWorkcenter> = new OdooRelationship<MrpWorkcenter>();
  workorder_id: OdooRelationship<MrpWorkorder> = new OdooRelationship<MrpWorkorder>();
  production_id: OdooRelationship<MrpProduction> = new OdooRelationship<MrpProduction>();
  user_id: OdooRelationship<User> = new OdooRelationship<User>();
  // loss_id: OdooRecord<MrpWorkcenterProductivityLoss>;

  // Time Tracking
  date_start: string = '';
  date_end: string = '';
  duration: number = 0;

  // Status and Description
  description: string = '';
  cost_already_recorded: boolean = false;
  loss_type: 'productive' | 'performance' | 'availability' | 'quality';

  constructor(id: number = null) {
    super();
    this.id = id;
    this.cost_already_recorded = false;
  }

  create(): MrpWorkcenterProductivity {
    return new MrpWorkcenterProductivity();
  }
}

  export class MrpBom extends  OdooModel implements OdooSerializableInterface<MrpBom> {
    public readonly ODOO_MODEL = 'mrp.bom';
  
    // Basic Information
    name: string = '';
    code: string = '';
    active: boolean;
    sequence: number;
    type: 'normal' | 'phantom';
    product_qty: number;
    consumption: 'flexible' | 'strict' | 'warning';
    ready_to_produce: 'all_available' | 'asap';
  
    // Settings
    allow_operation_dependencies: boolean;
  
    // Relations - Products
    product_id: OdooRelationship<Product> = new OdooRelationship<Product>();
    product_tmpl_id: OdooRelationship<ProductTemplate> = new OdooRelationship<ProductTemplate>();
    product_uom_id: OdooRelationship<UomUom> = new OdooRelationship<UomUom>();
    product_uom_category_id: OdooRelationship<UomUom> = new OdooRelationship<UomUom>();

    // Relations - Components and Operations
    // bom_line_ids: OdooRecord<MrpBomLine>[];
    // byproduct_ids: OdooRecord<MrpBomByproduct>[];
    // operation_ids: OdooRecord<MrpRoutingWorkcenter>[];
    
    // Relations - Other
    picking_type_id: OdooRelationship<StockPickingType> = new OdooRelationship<StockPickingType>();
    possible_product_template_attribute_value_ids:  OdooMultiRelationship<ProductTemplateAttributeValue> = new OdooMultiRelationship<ProductTemplateAttributeValue>(ProductTemplateAttributeValue);
  
    // Mail Thread Fields
    message_follower_ids: OdooMultiRelationship<MailFollower> = new OdooMultiRelationship<MailFollower>(MailFollower);
    message_ids: OdooMultiRelationship<MailMessage> = new OdooMultiRelationship<MailMessage>(MailMessage);
    message_main_attachment_id: OdooRelationship<IrAttachment> = new OdooRelationship<IrAttachment>();
    message_attachment_count: number;
    message_has_error: boolean;
    message_has_error_counter: number;
    message_has_sms_error: boolean;
    message_needaction: boolean;
    message_needaction_counter: number;
    message_is_follower: boolean;
    has_message: boolean;
  
    constructor(id: number = null, name: string = '') {
      super();
      this.id = id;
      this.name = name;
      this.active = true;
      this.product_qty = 1;
      this.type = 'normal';
      this.consumption = 'strict';
      this.ready_to_produce = 'all_available';
    }

    create(): MrpBom {
      return new MrpBom();
    }
  }
  
