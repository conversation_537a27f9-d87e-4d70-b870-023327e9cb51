import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { QuerySearchOptions, RestapiService } from '../../shared/services/rest-api.service';

@Component({
  selector: 'app-search-results',
  templateUrl: './search-results.component.html'
})
export class SearchResultsComponent implements OnInit {
  id: string

  constructor(
    private route: ActivatedRoute,
    public restapi : RestapiService,
    public router: Router
  ) { }

  ngOnInit() {
    
    this.route.params.subscribe(async params => {
      var search = params['search'];
      // this.loading = true;
      // var q : QuerySearchOptions = {
      //   table : "na.pacchi",
      //   criteria : [
      //     {column: "barcode", operator: "ilike",  value: search}
      //   ]
      // }
  
      // this.restapi.search(q).then((res) => {
      //   console.log("PACKS ", res)
      //   // if (res["na.pacchi"] && res["na.pacchi"].length) {
      //   //   this.router.navigate(['search/search-detail/' + res["na.pacchi"]]);
      //   // } else {
      //   //   alert('Trovato più di un pacco!');
      //   // }
      // })

      console.log("PP")
  })
  }

}
