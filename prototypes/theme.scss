// Options
// 
// Quickly modify global styling by enabling or disabling optional features.
  
$enable-rounded:            true !default;
$enable-shadows:            true;
$enable-transitions:        true;
$enable-hover-media-query:  false;
$enable-grid-classes:       true;
$enable-print-styles:       true;

// Variables
//
// Colors

$theme-colors: (
  primary: #f47e43,
  secondary: #7d5bd1,
  light: #efefef,
  dark: #373a3c,
  info: #ccc,
  success: #28a745,
  warning: #ffc107,
  danger: #dc3545
);

$body-bg: white;
$body-color: #333;


$body-color-inverse: invert($body-color) !default;
$link-color: #12bbad;

// Fonts
$font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
$headings-font-family: $font-family-base;
$display-font-family: $font-family-base;
$font-weight-normal: 200;
$headings-font-weight: 200;
$lead-font-size:   1.30rem;

$spacer: 1.5rem;
$font-size-base: 0.9rem;

@import 'bootstrap-4.3.1';



tbody tr.h {
  background-color:#efefef !important;
}


table.noselect {
 	user-select: none;
}

body, html {
  height: 100%;
  overflow: hidden;
}

table {
  margin-bottom: 0px !important;
}

td, th {
  white-space: nowrap;
}

.modal-overlay {
  .modal-dialog {
      top: 0px;
    position: absolute;
    right: 20px;
    width: 50%;
    min-height: 100%;
  }  
}

table {
  overflow: visible;
}

td, th {
  padding: 10px;
  position: relative;
  outline: 0;
}


.dataTables_scrollHead {
  table {
    margin-bottom: 0px;
  }
}


.panel {
  max-height: calc(100vh - 64px);
  min-height: calc(100vh - 64px);
  overflow-y: scroll;
  
  &.panel-left {
    min-width: 240px !important;
  }
  &.panel-short {
    max-height: calc(100vh - 128px);
	  min-height: calc(100vh - 128px);
  }
}



