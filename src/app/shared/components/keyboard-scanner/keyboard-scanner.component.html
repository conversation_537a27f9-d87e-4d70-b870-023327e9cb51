<form class="d-flex">
    <input 
        #input
        *ngIf="keybuffer || showKeyboard"
        class="form-control" 
        name="input" 
        [(ngModel)]="keybuffer" 
        (change)="onInput($event)">
    
    <button class="btn btn-link text-white" (click)="showKeyboard = !showKeyboard">
        <i class="fa fa-keyboard"></i>
    </button>

    <button class="btn btn-link text-white" >
        <i 
            [ngClass]="{'text-primary':listener}"
            class="fa-duotone fa-scanner-gun fa-lg"></i>
    </button>

</form>
